package com.scopely.paymentgateway.web.v1.controller;

import static com.google.common.collect.ImmutableMap.of;
import static com.scopely.paymentgateway.constants.RestApiConstants.PARAM_API_KEY;

import com.scopely.paymentgateway.exceptions.CountryUnsupportedException;
import com.scopely.paymentgateway.exceptions.CustomerIdException;
import com.scopely.paymentgateway.exceptions.InvalidCreatePaymentRequestException;
import com.scopely.paymentgateway.exceptions.LocalizedPriceNotFoundException;
import com.scopely.paymentgateway.exceptions.PaymentNotCompletedException;
import com.scopely.paymentgateway.exceptions.PaymentReversedException;
import com.scopely.paymentgateway.exceptions.model.EntityNotFoundException;
import com.scopely.paymentgateway.exceptions.payment.ExternalIdConflictException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.logging.PaymentProcess;
import com.scopely.paymentgateway.model.dto.claim.ClaimResponseDTO;
import com.scopely.paymentgateway.model.dto.claim.PaymentIdDTO;
import com.scopely.paymentgateway.model.dto.createpayment.CreatePaymentRequestDTO;
import com.scopely.paymentgateway.model.dto.createpayment.CreatePaymentResponseDTO;
import com.scopely.paymentgateway.services.payment.CreateNewPayment;
import com.scopely.paymentgateway.services.payment.ProcessPaymentClaim;
import com.scopely.paymentgateway.services.payment.RetrieveUnclaimedPayments;
import com.scopely.proteus.logging.Log;
import jakarta.ws.rs.ClientErrorException;
import jakarta.ws.rs.InternalServerErrorException;
import jakarta.ws.rs.core.Response;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class PaymentSecuredController implements PaymentSecuredEndpoint {
  private static final String PAYMENT_ID = "PAYMENT_ID";
  private final ProcessPaymentClaim processPaymentClaim;
  private final CreateNewPayment createNewPayment;

  private final RetrieveUnclaimedPayments retrieveUnclaimedPayments;

  @Inject
  public PaymentSecuredController(
      ProcessPaymentClaim processPaymentClaim,
      CreateNewPayment createNewPayment,
      RetrieveUnclaimedPayments retrieveUnclaimedPayments) {
    this.processPaymentClaim = processPaymentClaim;
    this.createNewPayment = createNewPayment;
    this.retrieveUnclaimedPayments = retrieveUnclaimedPayments;
  }

  @Override
  public CreatePaymentResponseDTO createNewPaymentEndpoint(
      String apiKey, String titanContextProperties, CreatePaymentRequestDTO createPaymentRequest) {
    try {
      CreatePaymentResponseDTO response =
          createNewPayment.execute(
              createPaymentRequest.toNewPaymentRequestData(apiKey), titanContextProperties);
      Log.withMetadata(of(PAYMENT_ID, response.paymentId()))
          .debug("Payment created successfully. Request: {}", createPaymentRequest);
      return response;
    } catch (CustomerIdException exception) {
      logException(apiKey, createPaymentRequest, exception, "Error processing customerId");
      throw new ClientErrorException(exception.getMessage(), Response.Status.CONFLICT, exception);
    } catch (CountryUnsupportedException exception) {
      logException(apiKey, createPaymentRequest, exception, "Location is not supported");
      throw new ClientErrorException(exception.getMessage(), Response.Status.CONFLICT, exception);
    } catch (LocalizedPriceNotFoundException | InvalidCreatePaymentRequestException exception) {
      logException(apiKey, createPaymentRequest, exception, exception.getMessage());
      throw new ClientErrorException(
          exception.getMessage(), Response.Status.BAD_REQUEST, exception);
    } catch (ExternalIdConflictException exception) {
      logException(apiKey, createPaymentRequest, exception, exception.getMessage());
      throw new ClientErrorException("Checkout conflict", Response.Status.CONFLICT, exception);
    } catch (Exception exception) {
      logException(apiKey, createPaymentRequest, exception, "Unhandled error requesting checkout");
      throw new InternalServerErrorException(exception.getMessage(), exception);
    }
  }

  @Override
  public ClaimResponseDTO validatePaymentIsClaimable(String apiKey, String paymentId) {
    try {
      return ClaimResponseDTO.fromPayment(processPaymentClaim.validate(apiKey, paymentId));
    } catch (PaymentNotCompletedException | PaymentReversedException exception) {
      throw new ClientErrorException(
          "The payment cannot be claimed: " + exception.getMessage(),
          Response.Status.BAD_REQUEST,
          exception);
    } catch (EntityNotFoundException exception) {
      Log.withMetadata(of(PAYMENT_ID, paymentId))
          .error(exception, "Invalid request trying to claim payment");
      throw new ClientErrorException(
          exception.getMessage(), Response.Status.BAD_REQUEST, exception);
    } catch (Exception exception) {
      Log.withMetadata(of(PAYMENT_ID, paymentId))
          .error(exception, "Unhandled error trying to claim payment");
      throw new InternalServerErrorException(exception.getMessage(), exception);
    }
  }

  @Override
  public ClaimResponseDTO claimPayment(String apiKey, String paymentId) {
    try {
      return ClaimResponseDTO.fromPayment(processPaymentClaim.execute(apiKey, paymentId));
    } catch (PaymentNotCompletedException | PaymentReversedException exception) {
      throw new ClientErrorException(
          "The payment cannot be claimed: " + exception.getMessage(),
          Response.Status.BAD_REQUEST,
          exception);
    } catch (EntityNotFoundException exception) {
      Log.withMetadata(of(PAYMENT_ID, paymentId))
          .error(exception, "Invalid request trying to claim payment");
      throw new ClientErrorException(
          exception.getMessage(), Response.Status.BAD_REQUEST, exception);
    } catch (Exception exception) {
      Log.withMetadata(of(PAYMENT_ID, paymentId))
          .error(exception, "Unhandled error trying to claim payment");
      throw new InternalServerErrorException(exception.getMessage(), exception);
    }
  }

  @Override
  public List<PaymentIdDTO> getUnclaimedPaymentsByGame(String apiKey) {
    try {
      return retrieveUnclaimedPayments.paymentsByGame(apiKey);
    } catch (Exception exception) {
      Log.withMetadata(of(PARAM_API_KEY, apiKey))
          .error(exception, "Unhandled error requesting unclaimed payments");
      throw new InternalServerErrorException(exception.getMessage(), exception);
    }
  }

  private void logException(
      String apiKey,
      CreatePaymentRequestDTO createPaymentRequestDTO,
      Exception exception,
      String message) {
    var userId = createPaymentRequestDTO.properties().user().userId();
    new PaymentGatewayLogBuilder()
        .addProcess(PaymentProcess.CHECKOUT)
        .addApiKey(apiKey)
        .addUserId(userId)
        .build()
        .error(exception, message);
  }
}
