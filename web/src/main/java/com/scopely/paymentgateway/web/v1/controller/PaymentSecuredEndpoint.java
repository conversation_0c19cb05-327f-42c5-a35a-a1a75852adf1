package com.scopely.paymentgateway.web.v1.controller;

import static com.scopely.paymentgateway.constants.RestApiConstants.HEADER_CONTEXT_PROPERTIES;
import static com.scopely.paymentgateway.constants.RestApiConstants.PARAM_API_KEY;
import static com.scopely.paymentgateway.constants.RestApiConstants.PARAM_PAYMENT_ID;
import static com.scopely.paymentgateway.constants.RestApiConstants.RESOURCE_PAYMENT_GATEWAY;
import static com.scopely.paymentgateway.constants.RestApiConstants.ROUTE_BASE;
import static com.scopely.paymentgateway.constants.RestApiConstants.ROUTE_CHECKOUT;
import static com.scopely.paymentgateway.constants.RestApiConstants.ROUTE_PAYMENT_CLAIM;
import static com.scopely.paymentgateway.constants.RestApiConstants.ROUTE_PAYMENT_CLAIM_VALIDATE;
import static com.scopely.paymentgateway.constants.RestApiConstants.ROUTE_UNCLAIMED_PAYMENTS;
import static com.scopely.paymentgateway.web.RestApiConstants.ACCESS_TOKEN;

import com.scopely.paymentgateway.model.dto.claim.ClaimResponseDTO;
import com.scopely.paymentgateway.model.dto.claim.PaymentIdDTO;
import com.scopely.paymentgateway.model.dto.createpayment.CreatePaymentRequestDTO;
import com.scopely.paymentgateway.model.dto.createpayment.CreatePaymentResponseDTO;
import com.scopely.proteus.server.annotations.RequireAppRole;
import com.scopely.proteus.server.auth.Claim;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.security.SecuritySchemes;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import java.util.List;

@Consumes({MediaType.APPLICATION_JSON})
@Produces({MediaType.APPLICATION_JSON})
@Path(ROUTE_BASE)
@SecuritySchemes({
  @SecurityScheme(
      name = "access_token",
      type = SecuritySchemeType.HTTP,
      scheme = "bearer",
      bearerFormat = "Bearer {{accessToken}}",
      description = "Access token provided by the Playgami team")
})
public interface PaymentSecuredEndpoint {

  @Path(ROUTE_CHECKOUT)
  @POST
  @Operation(
      summary = "Starts a checkout process",
      description =
          "Starts a new checkout process, sending all the required information about the player and the item to be purchased to the Playgami payments backend",
      externalDocs =
          @ExternalDocumentation(
              url =
                  "https://docs.playgami.scopely.com/v/developer-guides/developer-services/playgami-payments/playgami-payments-server-side-integration#_jpgcvk7kkmb2"),
      security = @SecurityRequirement(name = ACCESS_TOKEN),
      tags = "payment-endpoint")
  @SecurityRequirements({
    @SecurityRequirement(name = "access_token"),
  })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "The payment has been created successfully",
            content =
                @Content(
                    schema =
                        @Schema(
                            name = "CreatePaymentResponse",
                            implementation = CreatePaymentResponseDTO.class)))
      })
  @RequireAppRole(
      resource = RESOURCE_PAYMENT_GATEWAY,
      action = Claim.Action.CREATE,
      contextParameter = PARAM_API_KEY)
  CreatePaymentResponseDTO createNewPaymentEndpoint(
      @PathParam(PARAM_API_KEY) String apiKey,
      @HeaderParam(HEADER_CONTEXT_PROPERTIES) String contextProperties,
      CreatePaymentRequestDTO createPaymentRequest);

  @Path(ROUTE_PAYMENT_CLAIM_VALIDATE)
  @GET
  @Operation(
      summary = "Validates if a payment is claimable",
      description =
          "This endpoint is invoked by game backends to validate if a payment can be claimed",
      externalDocs =
          @ExternalDocumentation(
              url =
                  "https://docs.playgami.scopely.com/v/developer-guides/developer-services/playgami-payments/playgami-payments-server-side-integration#_qsmpvbzew2hb"),
      security = @SecurityRequirement(name = ACCESS_TOKEN),
      tags = "payment-endpoint")
  @SecurityRequirements({
    @SecurityRequirement(name = "access_token"),
  })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "The payment can be claimed",
            content =
                @Content(
                    schema =
                        @Schema(name = "ClaimResponse", implementation = ClaimResponseDTO.class))),
        @ApiResponse(
            responseCode = "409",
            description = "The payment is not in a status that allows it to be claimed"),
        @ApiResponse(responseCode = "404", description = "The payment does not exist"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
      })
  @RequireAppRole(
      resource = RESOURCE_PAYMENT_GATEWAY,
      action = Claim.Action.READ,
      contextParameter = PARAM_API_KEY)
  ClaimResponseDTO validatePaymentIsClaimable(
      @PathParam(PARAM_API_KEY) String apiKey, @PathParam(PARAM_PAYMENT_ID) String paymentId);

  @Path(ROUTE_PAYMENT_CLAIM)
  @PUT
  @Operation(
      summary = "Marks a payment as claimed",
      description =
          "This endpoint is invoked by game backends to mark a payment already processed as 'claimed', i.e.: that the item purchased has already been given to the player. Subsequent invocations to claim the same payment do not modify the status",
      externalDocs =
          @ExternalDocumentation(
              url =
                  "https://docs.playgami.scopely.com/v/developer-guides/developer-services/playgami-payments/playgami-payments-server-side-integration#_qsmpvbzew2hb"),
      security = @SecurityRequirement(name = ACCESS_TOKEN),
      tags = "payment-endpoint")
  @SecurityRequirements({
    @SecurityRequirement(name = "access_token"),
  })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "The payment has been successfully claimed",
            content =
                @Content(
                    schema =
                        @Schema(name = "ClaimResponse", implementation = ClaimResponseDTO.class))),
        @ApiResponse(
            responseCode = "409",
            description = "The payment is not in a status that allows it to be claimed"),
        @ApiResponse(responseCode = "404", description = "The payment does not exist"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
      })
  @RequireAppRole(
      resource = RESOURCE_PAYMENT_GATEWAY,
      action = Claim.Action.UPDATE,
      contextParameter = PARAM_API_KEY)
  ClaimResponseDTO claimPayment(
      @PathParam(PARAM_API_KEY) String apiKey, @PathParam(PARAM_PAYMENT_ID) String paymentId);

  @Path(ROUTE_UNCLAIMED_PAYMENTS)
  @GET
  @Operation(
      summary = "Lists payments pending to be claimed by a game",
      description =
          "Obtains the list of pending payments to be claimed by a given game, in the event they could not be completed through the default flow due to networking or other problems",
      externalDocs =
          @ExternalDocumentation(
              url =
                  "https://docs.playgami.scopely.com/v/developer-guides/developer-services/playgami-payments/playgami-payments-server-side-integration#_2546gyxrbbrv"),
      tags = "payment-endpoint")
  @SecurityRequirements({
    @SecurityRequirement(name = "access_token"),
  })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "The request has been successful",
            content =
                @Content(
                    array =
                        @ArraySchema(
                            schema =
                                @Schema(name = "PaymentId", implementation = PaymentIdDTO.class)))),
        @ApiResponse(responseCode = "500", description = "Internal server error")
      })
  @RequireAppRole(
      resource = RESOURCE_PAYMENT_GATEWAY,
      action = Claim.Action.READ,
      contextParameter = PARAM_API_KEY)
  List<PaymentIdDTO> getUnclaimedPaymentsByGame(@PathParam(PARAM_API_KEY) String apiKey);
}
