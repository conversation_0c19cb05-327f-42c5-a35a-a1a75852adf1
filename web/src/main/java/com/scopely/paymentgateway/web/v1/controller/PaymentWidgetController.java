package com.scopely.paymentgateway.web.v1.controller;

import com.scopely.paymentgateway.exceptions.ExpiredPaymentException;
import com.scopely.paymentgateway.exceptions.InvalidPaymentException;
import com.scopely.paymentgateway.exceptions.PaymentNotUpdatableException;
import com.scopely.paymentgateway.exceptions.PaymentProviderIntentionNotUpdatableException;
import com.scopely.paymentgateway.exceptions.RejectedPaymentException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.exceptions.model.EntityNotFoundException;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.dto.paymentinfo.PaymentInfoResponseDTO;
import com.scopely.paymentgateway.model.dto.updatepayment.UpdatePaymentRequestDTO;
import com.scopely.paymentgateway.model.dto.updatepayment.UpdatePaymentResponseDTO;
import com.scopely.paymentgateway.model.payment.BillingAddress;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.providers.digitalriver.services.countryconversion.PostalCodeManageService;
import com.scopely.paymentgateway.services.payment.GetPaymentInfo;
import com.scopely.paymentgateway.services.payment.UpdatePaymentService;
import com.scopely.proteus.logging.Log;
import jakarta.ws.rs.ClientErrorException;
import jakarta.ws.rs.InternalServerErrorException;
import jakarta.ws.rs.NotFoundException;
import jakarta.ws.rs.core.Response;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class PaymentWidgetController implements PaymentWidgetEndpoint {
  private final UpdatePaymentService updatePaymentService;
  private final GetPaymentInfo getPaymentInfo;
  private final PostalCodeManageService postalCodeManageService;

  @Inject
  public PaymentWidgetController(
      UpdatePaymentService updatePaymentService,
      GetPaymentInfo getPaymentInfo,
      PostalCodeManageService postalCodeManageService) {
    this.updatePaymentService = updatePaymentService;
    this.getPaymentInfo = getPaymentInfo;
    this.postalCodeManageService = postalCodeManageService;
  }

  public PaymentInfoResponseDTO paymentInfoEndpoint(String apiKey, String paymentId) {
    var logger = getLogger(apiKey, paymentId);
    try {
      PaymentInfoResponseDTO response = getPaymentInfo.execute(paymentId);
      logger.debug("Payment {} returned successfully.", paymentId);
      return response;
    } catch (PaymentNotFoundException exception) {
      logger.error(exception, "Payment not found");
      throw new NotFoundException(exception.getMessage(), exception);
    } catch (RejectedPaymentException exception) {
      logger.info("Payment is rejected");
      throw new ClientErrorException(
          exception.getMessage(), Response.Status.UNAUTHORIZED, exception);
    } catch (InvalidPaymentException | ExpiredPaymentException exception) {
      logger.error(exception, exception.getMessage());
      throw new ClientErrorException(
          exception.getMessage(), Response.Status.UNAUTHORIZED, exception);
    } catch (Exception exception) {
      logger.error(exception, "Unhandled error requesting payment");
      throw new InternalServerErrorException(exception.getMessage(), exception);
    }
  }

  @Override
  public PaymentInfoResponseDTO paymentInfoEndpointWithHeader(String apiKey, String paymentId) {
    return this.paymentInfoEndpoint(apiKey, paymentId);
  }

  public UpdatePaymentResponseDTO updatePaymentEndpoint(
      String apiKey, String paymentId, UpdatePaymentRequestDTO updatePaymentRequest) {
    var logger = getLogger(apiKey, paymentId);
    try {
      Payment modifiedPayment =
          this.updatePaymentService.updatePayment(
              updatePaymentRequest.toUpdatePaymentData(apiKey, paymentId));

      String postalCode =
          postalCodeManageService.managePostalCode(
              updatePaymentRequest.billingAddress().map(BillingAddress::getPostalCode).orElse(null),
              modifiedPayment.getCountry(),
              modifiedPayment.getProviderData().getProvider(),
              modifiedPayment.getApiKey());

      UpdatePaymentResponseDTO response = new UpdatePaymentResponseDTO(modifiedPayment, postalCode);
      logger.debug("Payment {} updated successfully", paymentId);
      return response;
    } catch (EntityNotFoundException exception) {
      logger.error(exception, "PaymentId" + paymentId + "not found to update");
      throw new ClientErrorException(
          "PaymentId " + paymentId + " not found", Response.Status.NOT_FOUND);
    } catch (PaymentNotUpdatableException exception) {
      logger.error(exception, "Payment status not allowed to update");
      throw new ClientErrorException(
          "PaymentId status not allowed to perform checkout update", Response.Status.CONFLICT);
    } catch (RejectedPaymentException exception) {
      logger.info("Payment rejected");
      throw new ClientErrorException(
          exception.getMessage(), Response.Status.UNAUTHORIZED, exception);
    } catch (InvalidPaymentException | ExpiredPaymentException exception) {
      logger.error(exception, "Payment already processed or expired");
      throw new ClientErrorException(
          exception.getMessage(), Response.Status.UNAUTHORIZED, exception);
    } catch (PaymentProviderIntentionNotUpdatableException exception) {
      logger.error(exception, "The provider can not to perform checkout update");
      throw new ClientErrorException(
          "The provider can not to perform checkout update", Response.Status.CONFLICT);
    } catch (RequestToProviderException exception) {
      logger.error(exception, "Unable to update payment");
      throw new ClientErrorException(
          exception.getErrorCode(), Response.Status.BAD_REQUEST, exception);
    } catch (Exception exception) {
      logger.error(exception, "Unhandled error requesting checkout");
      throw new InternalServerErrorException(exception.getMessage(), exception);
    }
  }

  @Override
  public UpdatePaymentResponseDTO updatePaymentEndpointWithHeader(
      String apiKey, String paymentId, UpdatePaymentRequestDTO updatePaymentRequest) {
    return this.updatePaymentEndpoint(apiKey, paymentId, updatePaymentRequest);
  }

  private Log.MetadataLog getLogger(String apiKey, String paymentId) {
    return new PaymentGatewayLogBuilder().addPayment(apiKey, paymentId).build();
  }
}
