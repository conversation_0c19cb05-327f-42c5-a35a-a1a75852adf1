plugins {
    id "satellite.application"
    alias(libs.plugins.swaggerGradlePlugin)
}

resolve {
    outputFileName = 'payment-gateway'
    openApiFile = file('src/main/resources/openapi-configuration.yaml')
    outputFormat = 'JSON'
    prettyPrint = 'TRUE'
    classpath = sourceSets.main.runtimeClasspath
    resourcePackages = ['com.scopely.paymentgateway.web']
    ignoredRoutes = [
            '/startup'
    ]
    outputDir = file('spec')
}

mainClassName = "com.scopely.paymentgateway.${project.name}.Main"

dependencies {
    implementation project(":core")
}
