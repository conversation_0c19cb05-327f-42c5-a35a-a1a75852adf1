{"openapi": "3.0.1", "info": {"title": "Playgami Payments API documentation", "description": "This OpenAPI file describes the API to be integrated by game backends in order to adopt the Playgami Payments product", "version": "1.0"}, "externalDocs": {"description": "Find more information in the Playgami documentation", "url": "https://docs.playgami.scopely.com/v/developer-guides/developer-services/playgami-payments/playgami-payments-server-side-integration"}, "servers": [{"url": "http://localhost:9090/", "description": "Local environment"}, {"url": "https://payment-gateway.dev.payments.playgami.scopely.com/", "description": "Integration environment"}, {"url": "https://payment-gateway.prod.payments.playgami.scopely.com/", "description": "Production environment"}], "tags": [{"name": "payment-endpoint", "description": "Manages the creation and claiming of payment operations for players"}, {"name": "user-endpoint", "description": "Shows information about payments related to a single player"}, {"name": "admin-payment", "description": "Manages the admin operations over payments"}, {"name": "admin-user", "description": "Manages the admin operations over users"}, {"name": "exchange-rate", "description": "Manages the exchange rates between currencies"}, {"name": "game-config", "description": "Manages the game configurations in parameter store"}, {"name": "payment-widget", "description": "Manage the endpoints called from the widget"}, {"name": "price-localization", "description": "Manage price localization endpoints"}], "paths": {"/v1/apps/{apiKey}/admin/payment/order/{orderId}": {"get": {"tags": ["admin-payment"], "summary": "Get payment by order ID", "operationId": "getPaymentByOrderId", "parameters": [{"name": "orderId", "in": "path", "description": "Order ID", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "Payment details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentDTO"}}}}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/admin/payment/{paymentId}": {"get": {"tags": ["admin-payment"], "summary": "Get payment by payment ID", "operationId": "getPaymentByPaymentId", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "description": "API key", "required": true, "schema": {"type": "string"}}, {"name": "paymentId", "in": "path", "description": "Payment ID", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "Payment details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentDTO"}}}}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/admin/payments/csv": {"post": {"tags": ["admin-payment"], "summary": "retrieve a summary for payments from CSV", "operationId": "getPaymentsSummaryByCSV", "requestBody": {"description": "CSV content", "content": {"text/plain": {"schema": {"type": "string"}}}, "required": true}, "responses": {"default": {"description": "Response with the summary of the payments", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentSummaryByCSVResponse"}}}}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/admin/users/block": {"patch": {"tags": ["admin-user"], "summary": "Block users", "operationId": "blockUsers", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "description": "API key", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "List of user IDs to block", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"default": {"description": "List of user information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfo"}}}}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/admin/users/provider/{provider}": {"patch": {"tags": ["admin-user"], "summary": "Change users' provider", "operationId": "changeUsersProvider", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "description": "API key", "required": true, "schema": {"type": "string"}}, {"name": "provider", "in": "path", "description": "Provider name", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "List of user IDs", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"default": {"description": "List of user information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfo"}}}}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/admin/users/{userId}/payments": {"get": {"tags": ["admin-user"], "summary": "Get payments by user ID", "operationId": "getPayments", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "description": "API key", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "List of payments", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentDTO"}}}}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/admin/users/{userId}/info": {"get": {"tags": ["admin-user"], "summary": "Get user information by user ID", "operationId": "getUser", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "description": "API key", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "User information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfo"}}}}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/admin/users/{userId}/info/{userAttributeName}": {"get": {"tags": ["admin-user"], "summary": "Get user attribute", "operationId": "getUserAttribute", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "description": "API key", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string"}}, {"name": "userAttributeName", "in": "path", "description": "User attribute name", "required": true, "schema": {"type": "string", "enum": ["userId", "<PERSON><PERSON><PERSON><PERSON>", "blocked", "provider", "other"]}}], "responses": {"default": {"description": "User attribute response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSingleAttribute"}}}}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/admin/users/users/provider/{provider}": {"post": {"tags": ["admin-user"], "summary": "Load player preferences from CSV", "operationId": "loadToPlayerPreferences", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "description": "API key", "required": true, "schema": {"type": "string"}}, {"name": "provider", "in": "path", "description": "Provider name", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "CSV content", "content": {"text/plain": {"schema": {"type": "string"}}}, "required": true}, "responses": {"default": {"description": "Response indicating the result of loading player preferences"}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/admin/users/tasks/scheduled/{scheduledTaskId}": {"post": {"tags": ["admin-user"], "summary": "Run a scheduled task", "operationId": "runScheduledTask", "parameters": [{"name": "scheduledTaskId", "in": "path", "description": "Scheduled task ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Request body", "content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"default": {"description": "Response indicating the result of running the task"}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/admin/users/unblock": {"patch": {"tags": ["admin-user"], "summary": "Unblock users", "operationId": "unblockUsers", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "description": "API key", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "List of user IDs to unblock", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"default": {"description": "List of user information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfo"}}}}}, "security": [{"access_token": []}]}}, "/v1/taxes/{country}": {"get": {"tags": ["exchange-rate"], "summary": "Get dynamic price for country", "operationId": "getDynamicPriceForCountry", "parameters": [{"name": "country", "in": "path", "description": "Country code", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "Dynamic price details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DynamicPrice"}}}}}, "security": [{"access_token": []}]}}, "/v1/taxes/load": {"post": {"tags": ["exchange-rate"], "summary": "Update country conversions", "operationId": "updateCountryConversions", "responses": {"default": {"description": "List of country conversions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CountryConversion"}}}}}, "security": [{"access_token": []}]}}, "/v1/apps/admin/config/global": {"get": {"tags": ["game-config"], "summary": "Get providers configurations", "operationId": "getGlobalConfigs", "responses": {"200": {"description": "Global configurations retrieved successfully"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}, "put": {"tags": ["game-config"], "summary": "Set global or system configuration game parameters.", "description": "Create or update global configuration game parameters given the payment provider.", "operationId": "addGlobalConfig", "requestBody": {"description": "List of global parameters configuration", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ParametersConfig"}}}}, "required": true}, "responses": {"200": {"description": "The parameter has been successfully added", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GameConfigResponse"}}}}, "400": {"description": "Request parameters not valid"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/admin/config/{apiKey}": {"get": {"tags": ["game-config"], "summary": "Get client configuration for provider", "description": "This endpoint is invoked by game to obtain actual global and their own configuration for all payment providers.", "operationId": "getConfigs", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Configurations retrieved successfully"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}, "put": {"tags": ["game-config"], "summary": "Set a list of configuration game parameters.", "description": "Create or update configuration game parameters given an apikey for the game and the payment provider.", "operationId": "addParameterConfig", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "List of parameters configuration", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ParametersConfig"}}}}, "required": true}, "responses": {"200": {"description": "The parameter has been successfully added", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GameConfigResponse"}}}}, "400": {"description": "Request parameters not valid"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/admin/config/sandbox": {"put": {"tags": ["game-config"], "summary": "Set sandbox configuration for a payment provider.", "description": "Create or update sandbox configuration for a payment provider.", "operationId": "addSandboxConfig", "requestBody": {"description": "List of sandbox parameters configuration", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ParametersConfig"}}}}, "required": true}, "responses": {"200": {"description": "The parameter has been successfully added", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GameConfigResponse"}}}}, "400": {"description": "Request parameters not valid"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/admin/config/global/{provider}/{name}": {"delete": {"tags": ["game-config"], "summary": "Remove global or system configuration parameter.", "description": "Delete the global or system configuration parameter for a payment provider given the key name.", "operationId": "deleteGlobalParameterConfig", "parameters": [{"name": "provider", "in": "path", "required": true, "schema": {"type": "string", "enum": ["PLAYGAMI_PAYMENTS", "XSOLLA", "DIGITAL_RIVER"]}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "The parameter has been successfully removed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GameConfigResponse"}}}}, "404": {"description": "The parameter does not exist"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/admin/config/{apiKey}/{provider}/{name}": {"delete": {"tags": ["game-config"], "summary": "Remove configuration parameters for a game with their apikey, payment provider and key name.", "description": "Delete the configuration parameter for a payment provider given the apikey of the game and key name. Global configuration can't be deleted.", "operationId": "deleteParameterConfig", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "provider", "in": "path", "required": true, "schema": {"type": "string", "enum": ["PLAYGAMI_PAYMENTS", "XSOLLA", "DIGITAL_RIVER"]}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "The parameter has been successfully removed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GameConfigResponse"}}}}, "404": {"description": "The parameter does not exist"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/admin/config/sandbox/{provider}/{name}": {"delete": {"tags": ["game-config"], "summary": "Remove sandbox configuration parameter.", "description": "Delete the sandbox configuration parameter for a payment provider given the key name.", "operationId": "deleteSandboxParameterConfig", "parameters": [{"name": "provider", "in": "path", "required": true, "schema": {"type": "string", "enum": ["PLAYGAMI_PAYMENTS", "XSOLLA", "DIGITAL_RIVER"]}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "The parameter has been successfully removed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GameConfigResponse"}}}}, "404": {"description": "The parameter does not exist"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/admin/config/{apiKey}/sandbox": {"get": {"tags": ["game-config"], "summary": "Get client configuration for provider on sandbox environment", "description": "This endpoint is invoked by game to obtain actual global and their own configuration for all payment providers on sandbox environment.", "operationId": "getSandboxConfigs", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Sandbox configurations retrieved successfully"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}, "put": {"tags": ["game-config"], "summary": "Enable sandbox status for an apikey game", "description": "Add or overwrite sandbox parameter for a game configuration", "operationId": "enableSandbox", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "The parameter has been successfully added", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GameConfigResponse"}}}}, "404": {"description": "The parameter does not exist"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}, "delete": {"tags": ["game-config"], "summary": "Disable sandbox status for apikey game", "description": "Delete sandbox parameter configuration by disabling sandbox status for a game", "operationId": "disableSandbox", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "The parameter has been successfully removed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GameConfigResponse"}}}}, "404": {"description": "The parameter does not exist"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/admin/config/global/sandbox": {"get": {"tags": ["game-config"], "summary": "Get providers configurations", "operationId": "getGlobalSandboxConfigs", "responses": {"200": {"description": "Global sandbox configurations retrieved successfully"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/payment/{paymentId}/claim": {"put": {"tags": ["payment-endpoint"], "summary": "Marks a payment as claimed", "description": "This endpoint is invoked by game backends to mark a payment already processed as 'claimed', i.e.: that the item purchased has already been given to the player. Subsequent invocations to claim the same payment do not modify the status", "externalDocs": {"url": "https://docs.playgami.scopely.com/v/developer-guides/developer-services/playgami-payments/playgami-payments-server-side-integration#_qsmpvbzew2hb"}, "operationId": "claimPayment", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "The payment has been successfully claimed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimResponse"}}}}, "409": {"description": "The payment is not in a status that allows it to be claimed"}, "404": {"description": "The payment does not exist"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/checkout": {"post": {"tags": ["payment-endpoint"], "summary": "Starts a checkout process", "description": "Starts a new checkout process, sending all the required information about the player and the item to be purchased to the Playgami payments backend", "externalDocs": {"url": "https://docs.playgami.scopely.com/v/developer-guides/developer-services/playgami-payments/playgami-payments-server-side-integration#_jpgcvk7kkmb2"}, "operationId": "createNewPaymentEndpoint", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "x-playgami-context-properties", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentRequest"}}}}, "responses": {"200": {"description": "The payment has been created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentResponse"}}}}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/payments/unclaimed": {"get": {"tags": ["payment-endpoint"], "summary": "Lists payments pending to be claimed by a game", "description": "Obtains the list of pending payments to be claimed by a given game, in the event they could not be completed through the default flow due to networking or other problems", "externalDocs": {"url": "https://docs.playgami.scopely.com/v/developer-guides/developer-services/playgami-payments/playgami-payments-server-side-integration#_2546gyxrbbrv"}, "operationId": "getUnclaimedPaymentsByGame", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "The request has been successful", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentId"}}}}}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/payment/{paymentId}/claim/validate": {"get": {"tags": ["payment-endpoint"], "summary": "Validates if a payment is claimable", "description": "This endpoint is invoked by game backends to validate if a payment can be claimed", "externalDocs": {"url": "https://docs.playgami.scopely.com/v/developer-guides/developer-services/playgami-payments/playgami-payments-server-side-integration#_qsmpvbzew2hb"}, "operationId": "validatePaymentIsClaimable", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "The payment can be claimed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimResponse"}}}}, "409": {"description": "The payment is not in a status that allows it to be claimed"}, "404": {"description": "The payment does not exist"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/payment/{paymentId}": {"get": {"tags": ["payment-widget"], "summary": "Get payment information by payment ID", "description": "Retrieves the payment information for a specific payment ID", "operationId": "paymentInfoEndpoint", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Payment information retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentInfoResponseDTO"}}}}, "404": {"description": "Payment not found"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/payment": {"get": {"tags": ["payment-widget"], "summary": "Get payment information by payment ID from header", "description": "Retrieves the payment information for a specific payment ID provided in the header", "operationId": "paymentInfoEndpointWithHeader", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Payment-Id", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Payment information retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentInfoResponseDTO"}}}}, "404": {"description": "Payment not found"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/payment/{paymentId}/status": {"get": {"tags": ["payment-widget"], "summary": "Get payment status by payment ID", "description": "Retrieves the payment status for a specific payment ID", "operationId": "paymentStatusByPaymentId", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Payment status retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentStatusResponse"}}}}, "404": {"description": "Payment not found"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/payment/status": {"get": {"tags": ["payment-widget"], "summary": "Get payment status by payment ID from header", "description": "Retrieves the payment status for a specific payment ID provided in the header", "operationId": "paymentStatusByPaymentIdWithHeader", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Payment-Id", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Payment status retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentStatusResponse"}}}}, "404": {"description": "Payment not found"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/payment/{paymentId}/checkout": {"put": {"tags": ["payment-widget"], "summary": "Update payment information", "description": "Updates the payment information for a specific payment ID", "operationId": "updatePaymentEndpoint", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Update payment request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentRequestDTO"}}}, "required": true}, "responses": {"200": {"description": "Payment updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentResponseDTO"}}}}, "400": {"description": "Invalid request parameters"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/v1/apps/{apiKey}/payment/checkout": {"put": {"tags": ["payment-widget"], "summary": "Update payment information with header", "description": "Updates the payment information for a specific payment ID provided in the header", "operationId": "updatePaymentEndpointWithHeader", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Payment-Id", "in": "header", "schema": {"type": "string"}}], "requestBody": {"description": "Update payment request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentRequestDTO"}}}, "required": true}, "responses": {"200": {"description": "Payment updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentResponseDTO"}}}}, "400": {"description": "Invalid request parameters"}, "500": {"description": "Internal server error"}}, "security": [{"access_token": []}]}}, "/playgami-payments/v1/apps/{apiKey}/items": {"get": {"tags": ["price-localization"], "summary": "Get a price list based on sku", "description": "Build a price list based on sku indicate the country used in the purchase", "externalDocs": {"url": "https://confluence.scopely.io/pages/viewpage.action?spaceKey=PPD&title=TDD+-+Price+Localization#TDDPriceLocalization-CodeSpecs(API)"}, "operationId": "getCountrySkuItemPrices", "parameters": [{"name": "X-Forwarded-For", "in": "header", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "country", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "A list with the localized prices", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CountryItemSkusDTO"}}}}, "404": {"description": "The localized price does not exist"}, "500": {"description": "Internal server error"}}}, "post": {"tags": ["price-localization"], "summary": "Get a price list based on sku and IP", "description": "Build a price list based on sku and IP, indicate the country used in the purchase", "externalDocs": {"url": "https://confluence.scopely.io/pages/viewpage.action?spaceKey=PPD&title=TDD+-+Price+Localization#TDDPriceLocalization-CodeSpecs(API)"}, "operationId": "getCountrySkuItemPricesWithIP", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "country", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "A list with the localized prices", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CountryItemSkusDTO"}}}}, "404": {"description": "The localized price does not exist"}, "500": {"description": "Internal server error"}}}}, "/v1/apps/{apiKey}/items/all": {"get": {"tags": ["price-localization"], "summary": "Get all price configurations", "description": "Build a price list based on sku for all countries and currencies", "externalDocs": {"url": "https://scopely.atlassian.net/wiki/spaces/PPD/pages/199430702/TDD+-+Price+Management+in+Console+v1.0"}, "operationId": "getLocalizedPrices", "parameters": [{"name": "X-Forwarded-For", "in": "header", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A list with the localized prices", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceSkuVersionDTO"}}}}, "404": {"description": "The apiKey does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyPriceSkuVersionDTO"}}}}, "500": {"description": "Internal server error"}}}}, "/v1/apps/{apiKey}/items/validate": {"post": {"tags": ["price-localization"], "summary": "Validates a CSV file provided with the localized prices configurations", "description": "Validates the CSV file provided and, if validations are passed, returns the contents of the CSV in JSON format. This endpoint will use multipart to get the contents of the file to validate.", "externalDocs": {"url": "https://scopely.atlassian.net/wiki/spaces/PPD/pages/199430702/TDD+-+Price+Management+in+Console+v1.0"}, "operationId": "validateCsvLocalizedPrices", "parameters": [{"name": "X-Forwarded-For", "in": "header", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "The CSV file to validate"}}, "required": ["file"]}}}}, "responses": {"200": {"description": "A list with the localized prices in JSON format", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ItemCountryAndCurrencyPricesDTO"}}}}}, "400": {"description": "Errors found during file validation", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "500": {"description": "Internal server error"}}}}, "/v1/apps/{apiKey}/items/upload": {"post": {"tags": ["price-localization"], "summary": "Uploads a CSV file provided with the localized prices configurations", "description": "Uploads the CSV file provided if validations are passed.", "externalDocs": {"url": "https://scopely.atlassian.net/wiki/spaces/PPD/pages/199430702/TDD+-+Price+Management+in+Console+v1.0"}, "operationId": "validateCsvLocalizedPrices", "parameters": [{"name": "X-Forwarded-For", "in": "header", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "The CSV file to validate"}}, "required": ["file"]}}}}, "responses": {"200": {"description": "A list with the localized prices in JSON format", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ItemCountryAndCurrencyPricesDTO"}}}}}, "400": {"description": "Errors found during file validation", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "500": {"description": "Internal server error"}}}}, "/v1/apps/{apiKey}/users/{userId}/payments/unclaimed": {"get": {"tags": ["user-endpoint"], "summary": "Lists payments pending to be claimed by a game player", "description": "Obtains the list of pending payments to be claimed by a given game user, in the event they could not be completed through the default flow due to networking or other problems", "externalDocs": {"url": "https://docs.playgami.scopely.com/v/developer-guides/developer-services/playgami-payments/playgami-payments-server-side-integration#_2546gyxrbbrv"}, "operationId": "getUnclaimedPayments", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "default response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentId"}}}}}}, "security": [{"access_token": []}]}}}, "components": {"schemas": {"FulfillmentItem": {"type": "object", "properties": {"quantity": {"type": "integer", "format": "int32"}, "itemId": {"type": "string"}}}, "ItemData": {"type": "object", "properties": {"name": {"type": "string"}, "productType": {"type": "string", "enum": ["IAP", "SUBSCRIPTION"]}, "internalSku": {"type": "string"}, "providerItem": {"$ref": "#/components/schemas/ProviderItem"}}}, "PaymentDTO": {"type": "object", "properties": {"paymentId": {"type": "string"}, "orderId": {"type": "string"}, "paymentStatus": {"type": "string", "enum": ["COMPLETED", "INITIATED", "PENDING", "FAILED", "REJECTED", "UNKNOWN"]}, "userId": {"type": "string"}, "deviceToken": {"type": "string"}, "apiKey": {"type": "string"}, "priceData": {"$ref": "#/components/schemas/PriceData"}, "paymentMethodUsed": {"type": "string", "enum": ["CREDIT_CARD", "PAYPAL", "GOOGLE_PAY", "APPLE_PAY", "AMAZON_PAY", "UNKNOWN", "OTHER"]}, "claimed": {"type": "boolean"}, "claimedAt": {"type": "string", "format": "date-time"}, "receiptSentAt": {"type": "string", "format": "date-time"}, "receiptId": {"type": "string"}, "country": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "trackingId": {"type": "string"}, "fulfillmentItems": {"type": "array", "items": {"$ref": "#/components/schemas/FulfillmentItem"}}, "providerStatus": {"type": "string", "enum": ["SESSION_ID_CREATED", "SOURCE_CREATED", "SOURCE_ATTACHED", "ORDER_CREATED", "PENDING_PAYMENT", "IN_REVIEW", "BLOCKED", "CANCELLED", "FULFILLED", "ACCEPTED", "COMPLETE", "RETURNED", "DISPUTE", "INITIATED", "PENDING", "FAILED", "COMPLETED"]}, "errorMessage": {"type": "string"}, "itemData": {"$ref": "#/components/schemas/ItemData"}, "sessionId": {"type": "string"}, "locale": {"type": "string"}, "email": {"type": "string"}, "providerData": {"$ref": "#/components/schemas/PaymentProviderData"}}}, "PaymentProviderData": {"type": "object", "properties": {"provider": {"type": "string", "enum": ["XSOLLA", "DIGITAL_RIVER"]}}}, "PriceData": {"type": "object", "properties": {"localPrice": {"$ref": "#/components/schemas/PriceDetail"}, "basePrice": {"$ref": "#/components/schemas/PriceDetail"}, "taxRate": {"type": "number"}, "conversionFactor": {"type": "number"}, "taxIncluded": {"type": "boolean"}}}, "PriceDetail": {"type": "object", "properties": {"currency": {"type": "string"}, "totalAmount": {"type": "number"}, "subtotalAmount": {"type": "number"}, "taxAmount": {"type": "number"}}}, "ProviderItem": {"type": "object", "properties": {"provider": {"type": "string"}, "sku": {"type": "string"}}}, "PaymentSummary": {"type": "object", "properties": {"paymentId": {"type": "string"}, "orderId": {"type": "string"}, "paymentStatus": {"type": "string", "enum": ["COMPLETED", "INITIATED", "PENDING", "FAILED", "REJECTED", "UNKNOWN"]}, "userId": {"type": "string"}, "apiKey": {"type": "string"}, "provider": {"type": "string", "enum": ["XSOLLA", "DIGITAL_RIVER"]}, "providerStatus": {"type": "string", "enum": ["SESSION_ID_CREATED", "SOURCE_CREATED", "SOURCE_ATTACHED", "ORDER_CREATED", "PENDING_PAYMENT", "IN_REVIEW", "BLOCKED", "CANCELLED", "FULFILLED", "ACCEPTED", "COMPLETE", "RETURNED", "DISPUTE", "INITIATED", "PENDING", "FAILED", "COMPLETED"]}}}, "PaymentSummaryByCSVResponse": {"type": "object", "properties": {"payments": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentSummary"}}, "failedPayments": {"type": "array", "items": {"type": "string"}}}}, "UserInfo": {"type": "object", "properties": {"userId": {"type": "string"}, "apiKey": {"type": "string"}, "blocked": {"type": "boolean"}, "blockedAt": {"type": "string", "format": "date-time"}, "blockedBy": {"type": "string"}, "blockReason": {"type": "string", "enum": ["too-many-disputes", "manually-blocked"]}, "provider": {"type": "string", "enum": ["XSOLLA", "DIGITAL_RIVER"]}, "updatedAt": {"type": "string", "format": "date-time"}}}, "UserSingleAttribute": {"type": "object", "properties": {"attribute": {"type": "object", "additionalProperties": {"type": "object"}}}}, "CountryConversion": {"type": "object", "properties": {"taxIncluded": {"type": "boolean"}, "taxRate": {"type": "number"}, "updatedAt": {"type": "string", "format": "date-time"}, "countryId": {"type": "string"}, "currencyId": {"type": "string"}, "conversionFactor": {"type": "number"}, "exchangeRate": {"type": "number"}}}, "DynamicPrice": {"type": "object", "properties": {"basePrice": {"type": "number"}, "baseCountry": {"type": "string"}, "countryConversion": {"$ref": "#/components/schemas/CountryConversion"}, "convertedPrice": {"type": "number"}}}, "GameConfigResponse": {"type": "object", "properties": {"status": {"type": "boolean"}, "message": {"type": "string"}}}, "ParametersConfig": {"type": "object", "properties": {"provider": {"type": "string", "enum": ["PLAYGAMI_PAYMENTS", "XSOLLA", "DIGITAL_RIVER"]}, "key": {"type": "string"}, "value": {"type": "object"}, "secured": {"type": "boolean"}}}, "ClientConfiguration": {"required": ["<PERSON><PERSON><PERSON><PERSON>", "paymentProviderIdentifier"], "type": "object", "properties": {"apiKey": {"type": "string"}, "postalCodes": {"type": "string"}, "segmentId": {"type": "string"}, "disclosureLinks": {"type": "string"}, "paymentProviderIdentifier": {"type": "string", "enum": ["PLAYGAMI_PAYMENTS", "XSOLLA", "DIGITAL_RIVER"]}}, "discriminator": {"propertyName": "paymentProviderIdentifier"}}, "DigitalRiverClientConfig": {"required": ["<PERSON><PERSON><PERSON><PERSON>", "conversionFees", "paymentProviderIdentifier", "public<PERSON>ey", "secret<PERSON>ey", "skuGroupId", "webhookSecretKey"], "type": "object", "allOf": [{"$ref": "#/components/schemas/ClientConfiguration"}, {"type": "object", "properties": {"publicKey": {"type": "string"}, "secretKey": {"type": "string"}, "skuGroupId": {"type": "string"}, "webhookSecretKey": {"type": "string"}, "conversionFees": {"type": "string"}, "defaultConversionFee": {"type": "string"}}}]}, "PlaygamiPaymentsClientConfig": {"required": ["<PERSON><PERSON><PERSON><PERSON>", "autoblockUsersOption", "currenciesByCountry", "emailOrderReceiptFrom", "emailOrderSenderAddress", "gameName", "paymentProviderIdentifier", "sendGridApiKey"], "type": "object", "allOf": [{"$ref": "#/components/schemas/ClientConfiguration"}, {"type": "object", "properties": {"defaultProvider": {"type": "string"}, "playerProfileProviderSelectionEnabled": {"type": "boolean"}, "testUsers": {"type": "array", "items": {"type": "string"}}, "gameName": {"type": "string"}, "sandbox": {"type": "boolean"}, "allowedLanguages": {"maxItems": **********, "minItems": 1, "type": "array", "items": {"maxLength": **********, "minLength": 1, "type": "string"}}, "currenciesByCountry": {"type": "string"}, "sendGridApiKey": {"pattern": "^SG\\..*", "type": "string"}, "emailProviders": {"maxItems": **********, "minItems": 1, "type": "array", "items": {"maxLength": **********, "minLength": 1, "type": "string"}}, "emailTemplateId": {"type": "object", "additionalProperties": {"type": "string"}}, "forcedProvider": {"type": "string"}, "defaultCurrency": {"type": "string"}, "vipSegmentId": {"type": "string"}, "flagVIPUsersEnabled": {"type": "boolean"}, "segmentationEnabled": {"type": "boolean"}, "emailOrderReceiptFrom": {"type": "string"}, "emailOrderSenderAddress": {"type": "string"}, "defaultProviderByCountry": {"type": "object", "additionalProperties": {"type": "string"}}, "backfillServiceEnabled": {"type": "boolean"}, "autoblockUsersOption": {"type": "string", "enum": ["no_block_users", "block_no_vip_users", "block_users"]}, "autoblockNumDisputes": {"type": "integer", "format": "int32"}}}]}, "XSollaClientConfig": {"required": ["<PERSON><PERSON><PERSON><PERSON>", "authApi<PERSON>ey", "paymentProviderIdentifier", "webhookSecretKey"], "type": "object", "allOf": [{"$ref": "#/components/schemas/ClientConfiguration"}, {"type": "object", "properties": {"projectId": {"type": "integer", "format": "int32"}, "authApiKey": {"type": "string"}, "merchantId": {"type": "integer", "format": "int32"}, "webhookSecretKey": {"type": "string"}, "webhookFallbackURI": {"type": "string"}, "allowedLanguages": {"maxItems": **********, "minItems": 1, "type": "array", "items": {"maxLength": **********, "minLength": 1, "type": "string"}}, "eventsToForward": {"maxItems": **********, "minItems": 1, "type": "array", "items": {"maxLength": **********, "minLength": 1, "type": "string"}}, "disableSavedMethods": {"type": "boolean"}}}]}, "ClaimItem": {"type": "object", "properties": {"sku": {"type": "string"}, "providerItem": {"$ref": "#/components/schemas/ProviderItem"}, "name": {"type": "string"}}}, "ClaimResponse": {"type": "object", "properties": {"paymentId": {"type": "string"}, "externalId": {"type": "string", "nullable": true}, "userId": {"type": "string"}, "claimedAt": {"type": "string", "format": "date-time", "nullable": true}, "item": {"$ref": "#/components/schemas/ClaimItem"}, "priceData": {"$ref": "#/components/schemas/PriceData"}, "paymentMethodUsed": {"type": "string", "nullable": true, "enum": ["CREDIT_CARD", "PAYPAL", "GOOGLE_PAY", "APPLE_PAY", "AMAZON_PAY", "UNKNOWN", "OTHER"]}, "provider": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "CreatePaymentResponse": {"type": "object", "properties": {"paymentId": {"type": "string"}, "externalId": {"type": "string"}, "userId": {"type": "string"}, "provider": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "purchaseToken": {"type": "string"}}}, "CreatePaymentRequest": {"type": "object", "properties": {"item": {"$ref": "#/components/schemas/Item"}, "properties": {"$ref": "#/components/schemas/Properties"}, "deviceToken": {"type": "string"}, "trackingId": {"type": "string"}, "externalId": {"type": "string"}, "platform": {"type": "string", "enum": ["PC", "WEB"]}}}, "Item": {"type": "object", "properties": {"sku": {"type": "string", "description": "Game's item id"}, "name": {"type": "string", "description": "Visible name of game's item"}, "price": {"type": "number", "description": "Item price in dollars. Used only if pricing mode is explicit", "format": "double", "nullable": true}, "localizedPrice": {"$ref": "#/components/schemas/LocalizedPrice"}, "providers": {"type": "array", "items": {"$ref": "#/components/schemas/ProviderItem"}}}}, "LocalizedPrice": {"type": "object", "properties": {"price": {"type": "number", "description": "Price in local currency.", "format": "double"}, "currency": {"type": "string", "description": "String code of the local currency."}}, "description": "Used only if pricing mode is explicit", "nullable": true}, "Properties": {"type": "object", "properties": {"sandbox": {"type": "boolean"}, "user": {"$ref": "#/components/schemas/User"}, "userIp": {"type": "string"}, "pricingMode": {"type": "string", "enum": ["explicit", "sku"]}, "country": {"type": "string"}, "locale": {"type": "string"}}}, "User": {"type": "object", "properties": {"userId": {"type": "string"}, "firstName": {"type": "string", "description": "User's first name", "nullable": true}, "lastName": {"type": "string", "description": "User's last name", "nullable": true}, "email": {"type": "string", "description": "User's e-mail address", "nullable": true}, "vip": {"type": "boolean"}}}, "PaymentId": {"type": "object", "properties": {"paymentId": {"type": "string"}, "externalId": {"type": "string"}, "userId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Customer": {"type": "object", "properties": {"id": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "externalId": {"type": "string"}, "firstName": {"type": "string"}, "savedCards": {"type": "array", "items": {"$ref": "#/components/schemas/SavedCreditCard"}}}}, "InitCheckoutDTO": {"type": "object", "properties": {"currency": {"type": "string"}, "country": {"type": "string"}, "defaultPostalCode": {"type": "string"}, "totalAmount": {"type": "number"}, "subtotalAmount": {"type": "number"}, "totalTax": {"type": "number"}, "taxRate": {"type": "number"}, "taxIncluded": {"type": "boolean"}, "pricingMode": {"type": "string", "enum": ["explicit", "sku"]}, "lastLocation": {"type": "string"}}}, "PaymentInfoItemDTO": {"type": "object", "properties": {"sku": {"type": "string"}, "name": {"type": "string"}}}, "PaymentInfoResponseDTO": {"type": "object", "properties": {"paymentId": {"type": "string"}, "apiKey": {"type": "string"}, "provider": {"$ref": "#/components/schemas/ProviderInfoDTO"}, "checkout": {"$ref": "#/components/schemas/InitCheckoutDTO"}, "item": {"$ref": "#/components/schemas/PaymentInfoItemDTO"}, "user": {"$ref": "#/components/schemas/Customer"}, "locale": {"type": "string"}, "sandbox": {"type": "boolean"}, "trackingId": {"type": "string"}, "gameName": {"type": "string"}}}, "ProviderInfoDTO": {"type": "object", "properties": {"providerData": {"type": "object", "additionalProperties": {"type": "string"}}, "name": {"type": "string"}, "config": {"type": "object", "additionalProperties": {"type": "object"}}}}, "SavedCreditCard": {"type": "object", "properties": {"id": {"type": "string"}, "country": {"type": "string"}, "email": {"type": "string"}, "postalCode": {"type": "string"}, "cardNetwork": {"type": "string"}, "createdTime": {"type": "string"}, "clientSecret": {"type": "string"}, "expirationMonth": {"type": "integer", "format": "int32"}, "expirationYear": {"type": "integer", "format": "int32"}, "lastFourDigits": {"type": "string"}}}, "PaymentStatusResponse": {"type": "object", "properties": {"paymentId": {"type": "string"}, "status": {"type": "string"}, "errorMessage": {"type": "string"}, "claimed": {"type": "boolean"}, "externalId": {"type": "string"}, "userId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "UpdateCheckoutDTO": {"type": "object", "properties": {"currency": {"type": "string"}, "country": {"type": "string"}, "postalCode": {"type": "string"}, "totalAmount": {"type": "number"}, "subtotalAmount": {"type": "number"}, "totalTax": {"type": "number"}, "taxRate": {"type": "number"}, "taxIncluded": {"type": "boolean"}}}, "UpdatePaymentResponseDTO": {"type": "object", "properties": {"checkout": {"$ref": "#/components/schemas/UpdateCheckoutDTO"}, "providerData": {"type": "object", "additionalProperties": {"type": "string"}}}}, "BillingAddress": {"type": "object", "properties": {"state": {"type": "string"}, "country": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "postalCode": {"type": "string"}, "city": {"type": "string"}}}, "UpdatePaymentRequestDTO": {"type": "object", "properties": {"userEmail": {"type": "string"}, "billingAddress": {"$ref": "#/components/schemas/BillingAddress"}}}, "CountryDataDTO": {"type": "object", "properties": {"code": {"type": "string"}, "currency": {"type": "string"}, "symbol": {"type": "string"}, "vatIncluded": {"type": "boolean"}}}, "PriceSkuVersionDTO": {"type": "object", "properties": {"createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/ItemCountryAndCurrencyPricesDTO"}}}}, "EmptyPriceSkuVersionDTO": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object"}}}}, "CountryItemSkusDTO": {"type": "object", "properties": {"country": {"$ref": "#/components/schemas/CountryDataDTO"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/ItemSkuPriceDTO"}}}}, "ItemSkuPriceDTO": {"type": "object", "properties": {"sku": {"type": "string"}, "price": {"type": "number"}}}, "ItemCountryAndCurrencyPricesDTO": {"type": "object", "properties": {"sku": {"type": "string"}, "regional_prices": {"type": "object", "properties": {"countryISO": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}}}, "currency_prices": {"type": "object", "properties": {"currencyCode": {"type": "object", "properties": {"amount": {"type": "number"}}}}}}}}, "securitySchemes": {"access_token": {"type": "http", "description": "Access token provided by the Playgami team", "scheme": "bearer", "bearerFormat": "Bearer {{accessToken}}"}}}}