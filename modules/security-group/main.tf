resource "aws_security_group" "security_group" {
  for_each    = { for item in var.security_groups : item.name => item }
  name        = try(each.value.name, null)
  description = try(each.value.description, null)
  name_prefix = try(each.value.name_prefix, null)

  vpc_id = each.value.vpc_id

  dynamic "ingress" {
    for_each = each.value.ingress

    content {
      description      = ingress.value.description
      from_port        = ingress.value.from_port
      to_port          = ingress.value.to_port
      protocol         = ingress.value.protocol
      self             = ingress.value.self
      cidr_blocks      = ingress.value.cidr_blocks
      ipv6_cidr_blocks = ingress.value.ipv6_cidr_blocks
      security_groups  = ingress.value.security_groups

    }
  }
  dynamic "egress" {
    for_each = each.value.egress

    content {
      description      = egress.value.description
      from_port        = egress.value.from_port
      to_port          = egress.value.to_port
      protocol         = egress.value.protocol
      self             = egress.value.self
      cidr_blocks      = egress.value.cidr_blocks
      ipv6_cidr_blocks = egress.value.ipv6_cidr_blocks
      security_groups  = egress.value.security_groups
    }
  }

  tags = merge(module.context.tags, each.value.extra_tags, { Name = each.value.name })
}