variable "security_groups" {
  type = list(object({
    name        = string
    description = optional(string)
    name_prefix = optional(string)
    vpc_id      = string
    ingress = list(object({
      description      = optional(string)
      from_port        = string
      to_port          = string
      protocol         = string
      self             = optional(bool)
      cidr_blocks      = optional(list(string))
      security_groups  = optional(list(string))
      ipv6_cidr_blocks = optional(list(string))
    }))
    egress = list(object({
      description      = optional(string)
      from_port        = string
      to_port          = string
      protocol         = string
      self             = optional(bool)
      cidr_blocks      = optional(list(string))
      security_groups  = optional(list(string))
      ipv6_cidr_blocks = optional(list(string))
    }))

    extra_tags = optional(map(string))
  }))
  description = "A list of maps defining security groups and its rules"
}

