# iac-tf-module-aws-utils/modules/security-group

A module for creating security groups and it's rules.

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.2.2 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 4.53 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | >= 4.53 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_context"></a> [context](#module\_context) | app.terraform.io/scopely-playgami/module-context/tf | 0.0.9 |

## Resources

| Name | Type |
|------|------|
| [aws_security_group.security_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_security_groups"></a> [security\_groups](#input\_security\_groups) | A list of maps defining security groups and its rules | <pre>list(object({<br>    name        = string<br>    description = optional(string)<br>    name_prefix = optional(string)<br>    vpc_id      = string<br>    ingress = list(object({<br>      description      = optional(string)<br>      from_port        = string<br>      to_port          = string<br>      protocol         = string<br>      self             = optional(bool)<br>      cidr_blocks      = optional(list(string))<br>      security_groups  = optional(list(string))<br>      ipv6_cidr_blocks = optional(list(string))<br>    }))<br>    egress = list(object({<br>      description      = optional(string)<br>      from_port        = string<br>      to_port          = string<br>      protocol         = string<br>      self             = optional(bool)<br>      cidr_blocks      = optional(list(string))<br>      security_groups  = optional(list(string))<br>      ipv6_cidr_blocks = optional(list(string))<br>    }))<br><br>    extra_tags = optional(map(string))<br>  }))</pre> | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags map | `map(string)` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_security_groups_id"></a> [security\_groups\_id](#output\_security\_groups\_id) | n/a |
<!-- END_TF_DOCS -->