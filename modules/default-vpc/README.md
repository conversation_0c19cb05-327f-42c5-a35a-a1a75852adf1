## Overview

This module is intended to standardize the configuration of default VPCs in AWS by importing
those resources and applying some basic configurations.

For using it you need to boostrap and apply the configuration for each region,
```sh
# Creates all the region folders for the default VPC configurations
# At the moment it still requires some manual configurations for the subnets
bin/configure_all_regions.sh /[...]/infrastructure-live/analyticsdev/preview/default_vpc

# Review all the changes and apply them if they're fine
cd /[...]/infrastructure-live/analyticsdev/preview/default_vpc
terragrunt run-all plan

```

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | n/a |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_context"></a> [context](#module\_context) | app.terraform.io/scopely-playgami/module-context/tf | 0.0.9 |

## Resources

| Name | Type |
|------|------|
| [aws_default_network_acl.default](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/default_network_acl) | resource |
| [aws_default_security_group.default](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/default_security_group) | resource |
| [aws_default_subnet.default](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/default_subnet) | resource |
| [aws_default_vpc.default](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/default_vpc) | resource |
| [aws_availability_zones.available](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/availability_zones) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_aws_region"></a> [aws\_region](#input\_aws\_region) | n/a | `string` | n/a | yes |
| <a name="input_aws_region_availability_zones"></a> [aws\_region\_availability\_zones](#input\_aws\_region\_availability\_zones) | The availability zones for the region when the default VPC does not have a subnet for each availability zone. By default uses all of them. | `set(string)` | `null` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags map | `map(string)` | n/a | yes |

## Outputs

No outputs.
<!-- END_TF_DOCS -->