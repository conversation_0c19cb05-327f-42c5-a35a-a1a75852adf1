#!/bin/bash

OUTPUT_DIR=$1
REGIONS=`aws ec2 describe-regions --query 'Regions[].RegionName' --output text`

for REGION in $REGIONS
do
  echo "Generating configuration for region $REGION"
  cookiecutter --no-input --overwrite-if-exists --output-dir $OUTPUT_DIR \
    **************:scopely/iac-tf-module-aws-utils.git \
    --directory modules/default-vpc/templates/default-region \
    --checkout default-vpc-v0.0.4 \
    aws_region=$REGION

  VPC_ID=`aws ec2 describe-vpcs --region $REGION --filters Name=is-default,Values=true  | jq -r '.Vpcs[0].VpcId'`
  NETWORK_ACL_ID=`aws ec2 describe-network-acls --region $REGION --filters Name=vpc-id,Values=$VPC_ID Name=default,Values=true  --query 'NetworkAcls[0].NetworkAclId'`
  SECURITY_GROUP_ID=`aws ec2 describe-security-groups --region $REGION --filters Name=vpc-id,Values=$VPC_ID Name=group-name,Values=default --query 'SecurityGroups[0].GroupId'`
  SUBNETS=`aws ec2 describe-subnets --region $REGION --filter Name=vpc-id,Values=$VPC_ID Name=default-for-az,Values=true | jq -r '.Subnets[] | "\(.AvailabilityZone);\(.SubnetId)"'`

  FILE="$OUTPUT_DIR/$REGION/region.hcl"
  echo "#Generated Automatically" > $FILE
  echo "locals {" >> $FILE
  echo "aws_region = \"$REGION\"" >> $FILE
  echo "aws_default_vpc_id = \"$VPC_ID\"" >> $FILE
  echo "aws_default_network_acl_id = $NETWORK_ACL_ID" >> $FILE
  echo "aws_default_security_group_id = $SECURITY_GROUP_ID" >> $FILE

  for SUBNET in $SUBNETS
  do
    VALUES=($(echo $SUBNET | tr ";" "\n"))
    echo "aws_default_subnet_id_for_${VALUES[0]} = \"${VALUES[1]}\"" >> $FILE
  done

  echo "}" >> $FILE
done




