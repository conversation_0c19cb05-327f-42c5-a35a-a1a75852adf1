locals {
  scopely_vars = read_terragrunt_config(find_in_parent_folders("scopely.hcl"))
  environment_vars = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  account_vars     = read_terragrunt_config(find_in_parent_folders("account.hcl"))
  common_vars     = read_terragrunt_config(find_in_parent_folders("common.hcl"))

  region_vars     = read_terragrunt_config("region.hcl")

  aws_region       = local.region_vars.locals.aws_region
  aws_account_name = local.account_vars.locals.account_name
  aws_account_id       = local.account_vars.locals.aws_account_id
  aws_provider_version = local.common_vars.locals.aws_provider_version

  module_name = "iac-tf-module-aws-utils"
  submodule_path = "modules/default-vpc"
  module_version = "default-vpc-v0.0.4"
}

terraform {
  source ="git::**************:scopely/${local.module_name}.git//${local.submodule_path}?ref=${local.module_version}"
}

inputs = {
  aws_region = local.aws_region

  allocation = "playgami-infrastructure"
  team = "playgami-devops"
  product = "networking"
  affinity = "none"
}

include {
  path = find_in_parent_folders()
}

generate "provider" {
  path      = "provider_override.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
provider "aws" {
  region = "${local.aws_region}"
  # Only these AWS Account IDs may be operated on by this template
  allowed_account_ids = ["${local.aws_account_id}"]
}

terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
      version = "${local.aws_provider_version}"
    }
  }
}
EOF
}

generate "imports" {
  path      = "imports.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
import {
  to = aws_default_vpc.default
  id = "${local.region_vars.locals.aws_default_vpc_id}"
}
import {
  to = aws_default_network_acl.default
  id = "${local.region_vars.locals.aws_default_network_acl_id}"
}
import {
  to = aws_default_security_group.default
  id = "${local.region_vars.locals.aws_default_security_group_id}"
}

# Imports cannot be created dynamically yet, check the generated region.hcl file for each
# subnet that needs to be imported. Plan should match.

import {
  to = aws_default_subnet.default["{{ cookiecutter.aws_region}}a"]
  id = "${local.region_vars.locals.aws_default_subnet_id_for_{{ cookiecutter.aws_region}}a}"
}

EOF
}