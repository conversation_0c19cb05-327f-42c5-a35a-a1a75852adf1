resource "aws_kms_key" "key" {
  for_each                 = { for item in var.kms_secrets : item.name => item }
  description              = each.value.description
  key_usage                = each.value.key_usage
  customer_master_key_spec = each.value.customer_master_key_spec
  enable_key_rotation      = true

  tags = module.context.tags
}


resource "aws_kms_alias" "key_alias" {
  for_each      = { for item, values in var.kms_secrets : item.name => item if values.alias != null }
  name          = "alias/${each.value.alias}"
  target_key_id = aws_kms_key.key[each.key].key_id
}

