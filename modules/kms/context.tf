#
# ONLY EDIT THIS FILE IN github.com/scopely/iac-tf-module-context
# All other instances of this file should be a copy of that one
#
# IMPORTANT: Ensure that this context.tf file version matches
# with the used context module version tag!
#
# Copy this file from https://github.com/scopely/iac-tf-module-context/blob/master/examples/context.tf
# and then place it in your Terraform module to automatically get
# Scopely's standard configuration inputs suitable for passing
# to Scopely modules.
#
# Modules that implement the context module only need to reference tags as module.context.tags
#
#

module "context" {
  #Remember to set the correct context module version tag after copying this file
  source  = "app.terraform.io/scopely-playgami/module-context/tf"
  version = "0.0.9"

  tags = var.tags
}

variable "tags" {
  type        = map(string)
  description = "Tags map"
}


