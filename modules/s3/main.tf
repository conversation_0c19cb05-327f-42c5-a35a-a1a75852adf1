module "s3" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "3.10.1"

  bucket                  = var.bucket_name
  block_public_acls       = true
  block_public_policy     = true
  restrict_public_buckets = true
  ignore_public_acls      = true
  policy                  = var.bucket_policy
  attach_policy           = var.bucket_attach_policy
  cors_rule               = var.cors_rule
  lifecycle_rule          = var.lifecycle_rule

  tags = module.context.tags
}