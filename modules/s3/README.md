<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

No providers.

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_context"></a> [context](#module\_context) | app.terraform.io/scopely-playgami/module-context/tf | 0.0.9 |
| <a name="module_s3"></a> [s3](#module\_s3) | terraform-aws-modules/s3-bucket/aws | 3.10.1 |

## Resources

No resources.

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_bucket_attach_policy"></a> [bucket\_attach\_policy](#input\_bucket\_attach\_policy) | Controls if a policy should be attached. Policy definition in bucket\_policy var | `bool` | `false` | no |
| <a name="input_bucket_name"></a> [bucket\_name](#input\_bucket\_name) | Name of your s3 bucket | `string` | n/a | yes |
| <a name="input_bucket_policy"></a> [bucket\_policy](#input\_bucket\_policy) | Policy to attach to the bucket | `string` | `null` | no |
| <a name="input_cors_rule"></a> [cors\_rule](#input\_cors\_rule) | List of cors rules for the S3 bucket. Use jsonencode([...]) if you are calling the module from terragrunt: https://registry.terraform.io/modules/terraform-aws-modules/s3-bucket/aws/latest#terragrunt-and-variable---type--any- | `any` | `[]` | no |
| <a name="input_lifecycle_rule"></a> [lifecycle\_rule](#input\_lifecycle\_rule) | List of lifecycle rules for the S3 bucket. Use jsonencode([...]) if you are calling the module from terragrunt: https://registry.terraform.io/modules/terraform-aws-modules/s3-bucket/aws/latest#terragrunt-and-variable---type--any- | `any` | `[]` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags map | `map(string)` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_bucket_arn"></a> [bucket\_arn](#output\_bucket\_arn) | The ARN of the bucket. Will be of format arn:aws:s3:::bucketname. |
| <a name="output_bucket_id"></a> [bucket\_id](#output\_bucket\_id) | The Name of the bucket. Will be of format bucketname. |
<!-- END_TF_DOCS -->