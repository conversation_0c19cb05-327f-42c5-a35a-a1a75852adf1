
provider "aws" {
  region              = "us-east-1"
  allowed_account_ids = ["************"]
}

variables {
  bucket_name = "test-playgami-devops-bucket-**********"

  tags = {
    environment = "preview"
    allocation  = "playgami-infrastructure"
    affinity    = "none"
    product     = "tests"
    team        = "playgami-devops"
    org_unit    = "playgami"
  }

}

run "s3_creation" {
  command = apply

  assert {
    condition     = module.s3.s3_bucket_id == var.bucket_name
    error_message = "error creating s3 bucket"
  }

  assert {
    condition     = module.s3.s3_bucket_website_endpoint == ""
    error_message = "s3 bucket has website endpoint enabled (it should be disabled by policy)"
  }

}

