variable "bucket_name" {
  type        = string
  description = "Name of your s3 bucket"
}

variable "bucket_policy" {
  type        = string
  description = "Policy to attach to the bucket"
  default     = null
}

variable "bucket_attach_policy" {
  type        = bool
  description = "Controls if a policy should be attached. Policy definition in bucket_policy var"
  default     = false
}

variable "cors_rule" {
  description = "List of cors rules for the S3 bucket. Use jsonencode([...]) if you are calling the module from terragrunt: https://registry.terraform.io/modules/terraform-aws-modules/s3-bucket/aws/latest#terragrunt-and-variable---type--any-"
  type        = any
  default     = []
}
variable "lifecycle_rule" {
  description = "List of lifecycle rules for the S3 bucket. Use jsonencode([...]) if you are calling the module from terragrunt: https://registry.terraform.io/modules/terraform-aws-modules/s3-bucket/aws/latest#terragrunt-and-variable---type--any-"
  type        = any
  default     = []
}
