variable "iam_role_name" {
  type        = string
  description = "Name of the IAM role which will contain a policies to allowing access to AWS resources through EKS cluster"
}

variable "inline_policies" {
  type        = map(string)
  description = "Map of aws_iam_policy_document"
  default     = null
}

variable "policies_arn" {
  type        = list(string)
  description = "List of policies ARNs that will be associated with the role"
  default     = []
}

variable "assume_role_policy" {
  type        = string
  description = "aws_iam_policy_document for assume role"
  default     = null
}

variable "description" {
  type        = string
  description = "Description of the IAM role"
  default     = null
}