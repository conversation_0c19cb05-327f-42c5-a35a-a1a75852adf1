resource "aws_iam_role" "iam_role" {
  name = var.iam_role_name

  assume_role_policy = var.assume_role_policy
  description        = var.description

  dynamic "inline_policy" {
    for_each = var.inline_policies == null ? {} : var.inline_policies
    content {
      name   = inline_policy.key
      policy = inline_policy.value
    }
  }

  managed_policy_arns = var.policies_arn

  tags = module.context.tags
}
