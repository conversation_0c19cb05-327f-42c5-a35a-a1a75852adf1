variable "repo_name" {
  type        = string
  description = "Name of the repository."
}

variable "image_tag_mutability" {
  type        = string
  default     = "MUTABLE"
  description = "The tag mutability setting for the repository. Must be one of: MUTABLE or IMMUTABLE."
}

variable "scan_on_push" {
  type        = bool
  default     = true
  description = "Indicates whether images are scanned after being pushed to the repository."
}

variable "force_delete" {
  type        = bool
  default     = false
  description = "If true, will delete the repository even if it contains images."
}
