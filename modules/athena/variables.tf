
variable "athena_workgroup_name" {
  type        = string
  description = "Name of the athena workgroup"
}

variable "athena_workgroup_description" {
  type        = string
  description = "Description for the Athena workgroup"
}

variable "athena_force_destroy" {
  type        = string
  description = "Delete workgroup even when named queries exist"
}

variable "athena_workgroup_configuration" {
  type = object({
    publish_cloudwatch_metrics_enabled = bool,
    enforce_workgroup_configuration    = optional(bool),
    selected_engine_version            = optional(string)
  })
  description = "Athena workgroup configuration"
}

variable "output_s3_bucket" {
  type        = string
  description = "Name of the S3 bucket to output query results"
}
