resource "aws_athena_workgroup" "athena_workgroup" {
  name          = var.athena_workgroup_name
  description   = var.athena_workgroup_description
  force_destroy = var.athena_force_destroy
  configuration {
    engine_version {
      selected_engine_version = var.athena_workgroup_configuration.selected_engine_version
    }
    publish_cloudwatch_metrics_enabled = var.athena_workgroup_configuration.publish_cloudwatch_metrics_enabled
    enforce_workgroup_configuration    = var.athena_workgroup_configuration.enforce_workgroup_configuration
    result_configuration {
      output_location = "s3://${var.output_s3_bucket}"
      encryption_configuration {
        encryption_option = "SSE_S3"
      }
    }
  }
  tags = module.context.tags
}
