<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | n/a |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_context"></a> [context](#module\_context) | app.terraform.io/scopely-playgami/module-context/tf | 0.0.9 |

## Resources

| Name | Type |
|------|------|
| [aws_athena_workgroup.athena_workgroup](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/athena_workgroup) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_athena_force_destroy"></a> [athena\_force\_destroy](#input\_athena\_force\_destroy) | Delete workgroup even when named queries exist | `string` | n/a | yes |
| <a name="input_athena_workgroup_configuration"></a> [athena\_workgroup\_configuration](#input\_athena\_workgroup\_configuration) | Athena workgroup configuration | <pre>object({<br>    publish_cloudwatch_metrics_enabled = bool,<br>    enforce_workgroup_configuration    = optional(bool),<br>    selected_engine_version            = optional(string)<br>  })</pre> | n/a | yes |
| <a name="input_athena_workgroup_description"></a> [athena\_workgroup\_description](#input\_athena\_workgroup\_description) | Description for the Athena workgroup | `string` | n/a | yes |
| <a name="input_athena_workgroup_name"></a> [athena\_workgroup\_name](#input\_athena\_workgroup\_name) | Name of the athena workgroup | `string` | n/a | yes |
| <a name="input_output_s3_bucket"></a> [output\_s3\_bucket](#input\_output\_s3\_bucket) | Name of the S3 bucket to output query results | `string` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags map | `map(string)` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_athena_workgroup_arn"></a> [athena\_workgroup\_arn](#output\_athena\_workgroup\_arn) | The ARN of the Athena workgroup |
<!-- END_TF_DOCS -->