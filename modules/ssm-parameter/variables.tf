variable "parameters_list" {
  type = set(object({
    name        = string
    value       = string
    description = optional(string)
  }))
  description = "A map containing the parameter name, value and an optional description"
}

variable "aws_account_id" {
  type        = string
  description = "The parameters AWS account id."
}

variable "aws_region" {
  type        = string
  description = "The Parameters aws region."
  default     = "us-east-1"
}

variable "access_policy_name" {
  type        = string
  description = "Name of the IAM access policy which will allow access to the parameters"
}

variable "access_policy_resource_parameters_prefix" {
  type        = string
  description = "Prefix of the parameters you want to grant access to"
  default     = ""
}
