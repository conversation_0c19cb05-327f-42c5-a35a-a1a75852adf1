<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | n/a |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_context"></a> [context](#module\_context) | app.terraform.io/scopely-playgami/module-context/tf | 0.0.9 |

## Resources

| Name | Type |
|------|------|
| [aws_iam_policy.parameters_access_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_ssm_parameter.aws_parameter](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ssm_parameter) | resource |
| [aws_iam_policy_document.access_policy_doc](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_access_policy_name"></a> [access\_policy\_name](#input\_access\_policy\_name) | Name of the IAM access policy which will allow access to the parameters | `string` | n/a | yes |
| <a name="input_access_policy_resource_parameters_prefix"></a> [access\_policy\_resource\_parameters\_prefix](#input\_access\_policy\_resource\_parameters\_prefix) | Prefix of the parameters you want to grant access to | `string` | `""` | no |
| <a name="input_aws_account_id"></a> [aws\_account\_id](#input\_aws\_account\_id) | The parameters AWS account id. | `string` | n/a | yes |
| <a name="input_aws_region"></a> [aws\_region](#input\_aws\_region) | The Parameters aws region. | `string` | `"us-east-1"` | no |
| <a name="input_parameters_list"></a> [parameters\_list](#input\_parameters\_list) | A map containing the parameter name, value and an optional description | <pre>set(object({<br>    name        = string<br>    value       = string<br>    description = optional(string)<br>  }))</pre> | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags map | `map(string)` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_access_policy_arn"></a> [access\_policy\_arn](#output\_access\_policy\_arn) | The ARN of the access policy for the parameters |
| <a name="output_parameters_arn"></a> [parameters\_arn](#output\_parameters\_arn) | The ARNs of the parameters. |
<!-- END_TF_DOCS -->