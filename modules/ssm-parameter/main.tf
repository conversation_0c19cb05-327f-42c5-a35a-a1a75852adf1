resource "aws_ssm_parameter" "aws_parameter" {
  for_each = { for parameter in var.parameters_list : parameter.name => parameter }

  name        = each.key
  value       = each.value.value
  description = each.value.description
  type        = "String"

  tags = module.context.tags
}

resource "aws_iam_policy" "parameters_access_policy" {
  name = "${var.access_policy_name}-access-policy"

  policy = data.aws_iam_policy_document.access_policy_doc.json

  tags = module.context.tags
}
