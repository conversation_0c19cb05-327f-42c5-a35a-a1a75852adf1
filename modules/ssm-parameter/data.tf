data "aws_iam_policy_document" "access_policy_doc" {
  statement {
    effect = "Allow"
    actions = [
      "kms:GenerateDataKey",
      "kms:Decrypt"
    ]
    resources = [
      "*"
    ]
    condition {
      test     = "StringEquals"
      values   = [var.aws_account_id]
      variable = "kms:CallerAccount"
    }
    condition {
      test     = "StringEquals"
      values   = ["ssmparameter.${var.aws_region}.amazonaws.com"]
      variable = "kms:ViaService"
    }
  }

  statement {
    effect = "Allow"
    actions = [
      "ssm:DescribeParameters"
    ]
    resources = ["*"]
  }

  statement {
    effect = "Allow"
    actions = [
      "ssm:GetParameter",
      "ssm:GetParameters",
      "ssm:GetParametersByPath"
    ]
    resources = [
      "arn:aws:ssm:${var.aws_region}:${var.aws_account_id}:parameter${var.access_policy_resource_parameters_prefix}*"
    ]
  }
}
