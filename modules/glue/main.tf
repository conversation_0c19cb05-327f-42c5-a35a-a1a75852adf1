resource "aws_glue_catalog_database" "aws_glue_catalog_database" {
  name       = var.glue_catalog_database_name
  catalog_id = data.aws_caller_identity.current.account_id
  #tags = module.this.tags --> only available from aws provider 4.66
}

resource "aws_glue_crawler" "aws_glue_crawler" {
  name          = var.glue_crawler_name
  description   = var.glue_crawler_description
  database_name = aws_glue_catalog_database.aws_glue_catalog_database.name

  role = aws_iam_role.glue_crawler_role.arn

  dynamic "s3_target" {
    for_each = var.glue_crawler_s3_targets
    content {
      path       = "s3://${s3_target.value.s3_bucket}/${s3_target.value.s3_bucket_path}"
      exclusions = s3_target.value.exclusions
    }
  }

  schema_change_policy {
    delete_behavior = var.glue_crawler_schema_change_policy.delete_behavior
    update_behavior = var.glue_crawler_schema_change_policy.update_behavior
  }

  depends_on = [
    aws_glue_catalog_database.aws_glue_catalog_database,
    aws_iam_role.glue_crawler_role
  ]
  tags = module.context.tags
}

resource "aws_iam_role" "glue_crawler_role" {
  name                = "${var.glue_crawler_name}_role"
  assume_role_policy  = data.aws_iam_policy_document.crawler_assume_role_policy.json
  path                = "/"
  managed_policy_arns = ["arn:aws:iam::aws:policy/service-role/AWSGlueServiceRole"]
  inline_policy {
    name   = "CrawlerComponentFunction"
    policy = data.aws_iam_policy_document.crawler_inline_policy.json
  }
  inline_policy {
    name   = "AWSKMSDecryption"
    policy = data.aws_iam_policy_document.crawler_inline_policy_kms.json
  }
  tags = module.context.tags
}

resource "aws_glue_catalog_table" "aws_glue_table" {
  name          = var.glue_catalog_table_name
  database_name = aws_glue_catalog_database.aws_glue_catalog_database.name
  catalog_id    = data.aws_caller_identity.current.account_id
  table_type    = var.glue_catalog_table_type

  storage_descriptor {
    location = "s3://${var.glue_catalog_table_storage_descriptor_location.s3_bucket}/${var.glue_catalog_table_storage_descriptor_location.s3_bucket_path}/"
    columns {
      name = var.glue_catalog_table_columns.name
      type = var.glue_catalog_table_columns.type
    }
    input_format  = var.glue_catalog_table_format.input
    output_format = var.glue_catalog_table_format.output

    ser_de_info {
      serialization_library = var.glue_catalog_table_ser_de_info.serialization_library
    }
  }

  depends_on = [aws_glue_catalog_database.aws_glue_catalog_database]
}

