
variable "glue_catalog_database_name" {
  type        = string
  description = "Name of the glue catalog database"
}

variable "glue_crawler_name" {
  type        = string
  description = "Name for the glue crawler over the glue catalog database"
}

variable "glue_crawler_description" {
  type        = string
  description = "Description for the glue crawler over the glue catalog database"
}

variable "glue_crawler_s3_targets" {
  type = map(object({
    s3_bucket      = string
    s3_bucket_path = string
    exclusions     = list(string)
  }))
  description = "s3 target configuration"
}

variable "glue_crawler_schema_change_policy" {
  type = object({
    delete_behavior = string
    update_behavior = string
  })
  description = "Glue crawler schema change policy"
}

variable "glue_catalog_table_name" {
  type        = string
  description = "Name of the glue catalog table"
}

variable "glue_catalog_table_storage_descriptor_location" {
  type = object({
    s3_bucket      = string
    s3_bucket_path = string
  })
  description = "Table Storage descriptor location s3 bucket and path"
}

variable "glue_catalog_table_type" {
  type        = string
  description = "Glue catalog table type"
}

variable "glue_catalog_table_storage_descriptor_path" {
  type        = string
  description = "Path for the storage descriptor of the glue catalog table. Should be inside the bucket defined in the variable glue_crawler_s3_target. Do not end with trailing slash."
}

variable "glue_catalog_table_columns" {
  type = object({
    name = string
    type = string
  })
  description = "Glue Catalog table column"
}

variable "glue_catalog_table_format" {
  type = object({
    input  = string
    output = string
  })
  description = "Glue Catalog table column Input and Output format"
}

variable "glue_catalog_table_ser_de_info" {
  type = object({
    serialization_library = string
  })
  description = "Glue Catalog table ser de info"
}