<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | n/a |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_context"></a> [context](#module\_context) | app.terraform.io/scopely-playgami/module-context/tf | 0.0.9 |

## Resources

| Name | Type |
|------|------|
| [aws_glue_catalog_database.aws_glue_catalog_database](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/glue_catalog_database) | resource |
| [aws_glue_catalog_table.aws_glue_table](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/glue_catalog_table) | resource |
| [aws_glue_crawler.aws_glue_crawler](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/glue_crawler) | resource |
| [aws_iam_role.glue_crawler_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_iam_policy_document.crawler_assume_role_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.crawler_inline_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.crawler_inline_policy_kms](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_glue_catalog_database_name"></a> [glue\_catalog\_database\_name](#input\_glue\_catalog\_database\_name) | Name of the glue catalog database | `string` | n/a | yes |
| <a name="input_glue_catalog_table_columns"></a> [glue\_catalog\_table\_columns](#input\_glue\_catalog\_table\_columns) | Glue Catalog table column | <pre>object({<br>    name = string<br>    type = string<br>  })</pre> | n/a | yes |
| <a name="input_glue_catalog_table_format"></a> [glue\_catalog\_table\_format](#input\_glue\_catalog\_table\_format) | Glue Catalog table column Input and Output format | <pre>object({<br>    input  = string<br>    output = string<br>  })</pre> | n/a | yes |
| <a name="input_glue_catalog_table_name"></a> [glue\_catalog\_table\_name](#input\_glue\_catalog\_table\_name) | Name of the glue catalog table | `string` | n/a | yes |
| <a name="input_glue_catalog_table_ser_de_info"></a> [glue\_catalog\_table\_ser\_de\_info](#input\_glue\_catalog\_table\_ser\_de\_info) | Glue Catalog table ser de info | <pre>object({<br>    serialization_library = string<br>  })</pre> | n/a | yes |
| <a name="input_glue_catalog_table_storage_descriptor_location"></a> [glue\_catalog\_table\_storage\_descriptor\_location](#input\_glue\_catalog\_table\_storage\_descriptor\_location) | Table Storage descriptor location s3 bucket and path | <pre>object({<br>    s3_bucket      = string<br>    s3_bucket_path = string<br>  })</pre> | n/a | yes |
| <a name="input_glue_catalog_table_storage_descriptor_path"></a> [glue\_catalog\_table\_storage\_descriptor\_path](#input\_glue\_catalog\_table\_storage\_descriptor\_path) | Path for the storage descriptor of the glue catalog table. Should be inside the bucket defined in the variable glue\_crawler\_s3\_target. Do not end with trailing slash. | `string` | n/a | yes |
| <a name="input_glue_catalog_table_type"></a> [glue\_catalog\_table\_type](#input\_glue\_catalog\_table\_type) | Glue catalog table type | `string` | n/a | yes |
| <a name="input_glue_crawler_description"></a> [glue\_crawler\_description](#input\_glue\_crawler\_description) | Description for the glue crawler over the glue catalog database | `string` | n/a | yes |
| <a name="input_glue_crawler_name"></a> [glue\_crawler\_name](#input\_glue\_crawler\_name) | Name for the glue crawler over the glue catalog database | `string` | n/a | yes |
| <a name="input_glue_crawler_s3_targets"></a> [glue\_crawler\_s3\_targets](#input\_glue\_crawler\_s3\_targets) | s3 target configuration | <pre>map(object({<br>    s3_bucket      = string<br>    s3_bucket_path = string<br>    exclusions     = list(string)<br>  }))</pre> | n/a | yes |
| <a name="input_glue_crawler_schema_change_policy"></a> [glue\_crawler\_schema\_change\_policy](#input\_glue\_crawler\_schema\_change\_policy) | Glue crawler schema change policy | <pre>object({<br>    delete_behavior = string<br>    update_behavior = string<br>  })</pre> | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags map | `map(string)` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_glue_database_name"></a> [glue\_database\_name](#output\_glue\_database\_name) | n/a |
<!-- END_TF_DOCS -->