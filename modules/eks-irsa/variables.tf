variable "aws_account_id" {
  type        = string
  description = "The AWS account id."
  default     = ""
}

variable "aws_region" {
  type        = string
  description = "The AWS region."
  default     = "us-east-1"
}

variable "eks_role_name" {
  type        = string
  description = "Name of the IAM role which will contain a policies to allowing access to AWS resources through EKS cluster"
}

variable "eks_oidc_provider" {
  type        = string
  description = "Name of the EKS OIDC provider. Format like: oidc.eks.<region>.amazonaws.com/id/<id>"
  default     = ""
}

variable "eks_cluster_name" {
  type        = string
  description = "Name of the EKS cluster where the IRSA role will be used. The OIDC provider is retrieved from this data."
  default     = null
}

variable "eks_service_account_namespace" {
  type        = string
  description = "Namespace of the EKS service account that will use the role"
}

variable "eks_service_account_name" {
  type        = string
  description = "Name of the service account that will use the role"
}

variable "eks_allow_all_stacks_affinities" {
  type        = bool
  description = "Indicates if the role will be impersonated by an app with multiple service accounts"
  default     = false
}

variable "inline_policies" {
  type        = map(string)
  description = "Map of aws_iam_policy_document"
  default     = {}
}

variable "policies_arn" {
  type        = list(string)
  description = "List of policies ARNs that will be associated with the role"
  default     = []
}

variable "additional_statements" {
  type = list(object({
    actions = list(string)
    principals = list(object({
      type        = string
      identifiers = list(string)
    }))
    conditions = list(object({
      test     = string
      variable = string
      values   = list(string)
    }))
  }))
  description = "Additional statements to be added to the assume role policy document"
  default     = []
}