data "aws_eks_cluster" "eks_cluster" {
  count = var.eks_cluster_name != null ? 1 : 0
  name  = var.eks_cluster_name
}

data "aws_iam_openid_connect_provider" "cluster_provider" {
  count = var.eks_cluster_name != null ? 1 : 0
  url   = data.aws_eks_cluster.eks_cluster[0].identity[0].oidc[0].issuer
}

data "aws_iam_policy_document" "eks_assume_role_policy_doc" {
  statement {
    effect = "Allow"
    actions = [
      "sts:AssumeRoleWithWebIdentity"
    ]
    principals {
      type        = "Federated"
      identifiers = [var.eks_cluster_name != null ? data.aws_iam_openid_connect_provider.cluster_provider[0].arn : "arn:aws:iam::${var.aws_account_id}:oidc-provider/${var.eks_oidc_provider}"]
    }
    condition {
      test     = "StringLike"
      values   = ["system:serviceaccount:${var.eks_service_account_namespace}:${var.eks_service_account_name}${var.eks_allow_all_stacks_affinities ? "-*" : ""}"]
      variable = "${var.eks_cluster_name != null ? replace(data.aws_eks_cluster.eks_cluster[0].identity[0].oidc[0].issuer, "https://", "") : var.eks_oidc_provider}:sub"
    }
    condition {
      test     = "StringLike"
      values   = ["sts.amazonaws.com"]
      variable = "${var.eks_cluster_name != null ? replace(data.aws_eks_cluster.eks_cluster[0].identity[0].oidc[0].issuer, "https://", "") : var.eks_oidc_provider}:aud"
    }
  }

  dynamic "statement" {
    for_each = var.additional_statements
    content {
      effect  = "Allow"
      actions = statement.value.actions
      dynamic "principals" {
        for_each = statement.value.principals
        content {
          type        = principals.value.type
          identifiers = principals.value.identifiers
        }
      }
      dynamic "condition" {
        for_each = statement.value.conditions
        content {
          test     = condition.value.test
          values   = condition.value.values
          variable = condition.value.variable
        }
      }
    }
  }
}