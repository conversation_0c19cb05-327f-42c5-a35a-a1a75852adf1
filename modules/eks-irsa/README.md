# iac-tf-module-aws-eks-irsa

## Overview

This module aim to cover the creation and maintenance of AWS
IRSA [IAM Roles for Service Accounts](https://docs.aws.amazon.com/emr/latest/EMR-on-EKS-DevelopmentGuide/setting-up-enable-IAM.html)

## Module features

### Definitions

#### IRSA

We handle the creation of a normal role with policies (arns or inline ones) but also add the trust relationship that
allow use the role inside a specific cluster for a specific service account

<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | n/a |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_context"></a> [context](#module\_context) | app.terraform.io/scopely-playgami/module-context/tf | 0.0.9 |

## Resources

| Name | Type |
|------|------|
| [aws_iam_role.eks_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_eks_cluster.eks_cluster](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/eks_cluster) | data source |
| [aws_iam_openid_connect_provider.cluster_provider](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_openid_connect_provider) | data source |
| [aws_iam_policy_document.eks_assume_role_policy_doc](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_additional_statements"></a> [additional\_statements](#input\_additional\_statements) | Additional statements to be added to the assume role policy document | <pre>list(object({<br>    actions = list(string)<br>    principals = list(object({<br>      type        = string<br>      identifiers = list(string)<br>    }))<br>    conditions = list(object({<br>      test     = string<br>      variable = string<br>      values   = list(string)<br>    }))<br>  }))</pre> | `[]` | no |
| <a name="input_aws_account_id"></a> [aws\_account\_id](#input\_aws\_account\_id) | The AWS account id. | `string` | `""` | no |
| <a name="input_aws_region"></a> [aws\_region](#input\_aws\_region) | The AWS region. | `string` | `"us-east-1"` | no |
| <a name="input_eks_allow_all_stacks_affinities"></a> [eks\_allow\_all\_stacks\_affinities](#input\_eks\_allow\_all\_stacks\_affinities) | Indicates if the role will be impersonated by an app with multiple service accounts | `bool` | `false` | no |
| <a name="input_eks_cluster_name"></a> [eks\_cluster\_name](#input\_eks\_cluster\_name) | Name of the EKS cluster where the IRSA role will be used. The OIDC provider is retrieved from this data. | `string` | `null` | no |
| <a name="input_eks_oidc_provider"></a> [eks\_oidc\_provider](#input\_eks\_oidc\_provider) | Name of the EKS OIDC provider. Format like: oidc.eks.<region>.amazonaws.com/id/<id> | `string` | `""` | no |
| <a name="input_eks_role_name"></a> [eks\_role\_name](#input\_eks\_role\_name) | Name of the IAM role which will contain a policies to allowing access to AWS resources through EKS cluster | `string` | n/a | yes |
| <a name="input_eks_service_account_name"></a> [eks\_service\_account\_name](#input\_eks\_service\_account\_name) | Name of the service account that will use the role | `string` | n/a | yes |
| <a name="input_eks_service_account_namespace"></a> [eks\_service\_account\_namespace](#input\_eks\_service\_account\_namespace) | Namespace of the EKS service account that will use the role | `string` | n/a | yes |
| <a name="input_inline_policies"></a> [inline\_policies](#input\_inline\_policies) | Map of aws\_iam\_policy\_document | `map(string)` | `{}` | no |
| <a name="input_policies_arn"></a> [policies\_arn](#input\_policies\_arn) | List of policies ARNs that will be associated with the role | `list(string)` | `[]` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags map | `map(string)` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_eks_role_arn"></a> [eks\_role\_arn](#output\_eks\_role\_arn) | The ARNs of role. |
<!-- END_TF_DOCS -->