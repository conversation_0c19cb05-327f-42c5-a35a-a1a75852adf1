<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_cur_report_definition.cur_report_definition](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cur_report_definition) | resource |
| [aws_s3_bucket_policy.bucket_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_policy) | resource |
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_iam_policy_document.cur_bucket_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_cost_usage_report_name"></a> [cost\_usage\_report\_name](#input\_cost\_usage\_report\_name) | Name of the report to create | `string` | n/a | yes |
| <a name="input_cur_additional_artifacts"></a> [cur\_additional\_artifacts](#input\_cur\_additional\_artifacts) | Additional artifacts applied to the CUR. Defaults to ATHENA | `list(string)` | <pre>[<br>  "ATHENA"<br>]</pre> | no |
| <a name="input_cur_time_unit"></a> [cur\_time\_unit](#input\_cur\_time\_unit) | Frequency of which report data are measured. DAILY (default), HOURLY, MONTHLY | `string` | `"DAILY"` | no |
| <a name="input_cur_versioning"></a> [cur\_versioning](#input\_cur\_versioning) | Defines how new reports are versioned (create new or overwrite) | `string` | `"OVERWRITE_REPORT"` | no |
| <a name="input_s3_bucket_name"></a> [s3\_bucket\_name](#input\_s3\_bucket\_name) | Bucket name to where CUR is saved | `string` | n/a | yes |
| <a name="input_s3_bucket_region"></a> [s3\_bucket\_region](#input\_s3\_bucket\_region) | Region where the S3 bucket is | `string` | `"us-east-1"` | no |
| <a name="input_s3_prefix"></a> [s3\_prefix](#input\_s3\_prefix) | Report path prefix | `string` | `""` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_cur_arn"></a> [cur\_arn](#output\_cur\_arn) | The ARN of the cost-usage-report |
<!-- END_TF_DOCS -->