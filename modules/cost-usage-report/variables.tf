variable "cost_usage_report_name" {
  type        = string
  description = "Name of the report to create"
}

variable "cur_time_unit" {
  type        = string
  description = "Frequency of which report data are measured. DAILY (default), HOURLY, MONTHLY"
  default     = "DAILY"
}

variable "s3_bucket_name" {
  type        = string
  description = "Bucket name to where CUR is saved"
}

variable "s3_bucket_region" {
  type        = string
  description = "Region where the S3 bucket is"
  default     = "us-east-1"
}

variable "s3_prefix" {
  type        = string
  description = "Report path prefix"
  default     = ""
}

variable "cur_additional_artifacts" {
  description = "Additional artifacts applied to the CUR. Defaults to ATHENA"
  type        = list(string)
  default     = ["ATHENA"]
}

variable "cur_versioning" {
  description = "Defines how new reports are versioned (create new or overwrite)"
  type        = string
  default     = "OVERWRITE_REPORT"
}
