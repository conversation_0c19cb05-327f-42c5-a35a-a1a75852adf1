resource "aws_cur_report_definition" "cur_report_definition" {
  report_name                = var.cost_usage_report_name
  time_unit                  = var.cur_time_unit
  format                     = "Parquet"
  compression                = "Parquet"
  additional_schema_elements = ["RESOURCES"]
  s3_bucket                  = var.s3_bucket_name
  s3_region                  = "us-east-1"
  s3_prefix                  = var.s3_prefix
  additional_artifacts       = var.cur_additional_artifacts
  report_versioning          = var.cur_versioning

  depends_on = [aws_s3_bucket_policy.bucket_policy]
}


data "aws_iam_policy_document" "cur_bucket_policy" {
  statement {
    sid = "AllowCurGetBucketS3"
    principals {
      type        = "Service"
      identifiers = ["billingreports.amazonaws.com"]
    }

    actions = [
      "s3:GetBucketAcl",
      "s3:GetBucketPolicy"
    ]

    resources = ["arn:aws:s3:::${var.s3_bucket_name}"]

    condition {
      test     = "StringEquals"
      variable = "AWS:SourceArn"
      values   = ["arn:aws:cur:us-east-1:${data.aws_caller_identity.current.account_id}:definition/*"]
    }

    condition {
      test     = "StringEquals"
      variable = "aws:SourceAccount"
      values   = [data.aws_caller_identity.current.account_id]
    }
  }
  statement {
    sid = "AllowCurPutBucketS3"
    principals {
      type        = "Service"
      identifiers = ["billingreports.amazonaws.com"]
    }

    actions = [
      "s3:PutObject"
    ]

    resources = ["arn:aws:s3:::${var.s3_bucket_name}/*"]

    condition {
      test     = "StringEquals"
      variable = "AWS:SourceArn"
      values   = ["arn:aws:cur:us-east-1:${data.aws_caller_identity.current.account_id}:definition/*"]
    }

    condition {
      test     = "StringEquals"
      variable = "aws:SourceAccount"
      values   = [data.aws_caller_identity.current.account_id]
    }
  }
}

resource "aws_s3_bucket_policy" "bucket_policy" {
  bucket = var.s3_bucket_name
  policy = data.aws_iam_policy_document.cur_bucket_policy.json
}