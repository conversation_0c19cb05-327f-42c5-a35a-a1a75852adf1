resource "aws_secretsmanager_secret" "aws_secret" {
  for_each = { for secret in var.secrets_list : secret.name => secret }

  name        = each.key
  description = each.value.description

  tags = module.context.tags
}

resource "aws_iam_policy" "secrets_access_policy" {
  name = "${var.access_policy_name}-access-policy"

  policy = data.aws_iam_policy_document.access_policy_doc.json

  tags = module.context.tags
}

