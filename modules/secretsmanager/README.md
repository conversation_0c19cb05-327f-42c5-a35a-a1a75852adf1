<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | n/a |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_context"></a> [context](#module\_context) | app.terraform.io/scopely-playgami/module-context/tf | 0.0.9 |

## Resources

| Name | Type |
|------|------|
| [aws_iam_policy.secrets_access_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_secretsmanager_secret.aws_secret](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/secretsmanager_secret) | resource |
| [aws_iam_policy_document.access_policy_doc](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_access_policy_name"></a> [access\_policy\_name](#input\_access\_policy\_name) | Name of the IAM access policy which will allow access to the secrets | `string` | n/a | yes |
| <a name="input_access_policy_resource_secrets_prefix"></a> [access\_policy\_resource\_secrets\_prefix](#input\_access\_policy\_resource\_secrets\_prefix) | Prefix of the secrets you want to grant access to | `string` | `""` | no |
| <a name="input_aws_account_id"></a> [aws\_account\_id](#input\_aws\_account\_id) | The secrets AWS account id. | `string` | n/a | yes |
| <a name="input_aws_region"></a> [aws\_region](#input\_aws\_region) | The Secrets aws region. | `string` | `"us-east-1"` | no |
| <a name="input_secrets_list"></a> [secrets\_list](#input\_secrets\_list) | A map containing the secret name and an optional description | <pre>set(object({<br>    name        = string<br>    description = optional(string)<br>  }))</pre> | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags map | `map(string)` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_access_policy_arn"></a> [access\_policy\_arn](#output\_access\_policy\_arn) | The ARN of the access policy for the secrets |
| <a name="output_secrets_arn"></a> [secrets\_arn](#output\_secrets\_arn) | The ARNs of the secrets. |
<!-- END_TF_DOCS -->