data "aws_iam_policy_document" "access_policy_doc" {
  statement {
    effect = "Allow"
    actions = [
      "kms:GenerateDataKey",
      "kms:Decrypt"
    ]
    resources = [
      "*"
    ]
    condition {
      test     = "StringEquals"
      values   = [var.aws_account_id]
      variable = "kms:CallerAccount"
    }
    condition {
      test     = "StringEquals"
      values   = ["secretsmanager.${var.aws_region}.amazonaws.com"]
      variable = "kms:ViaService"
    }
  }

  statement {
    effect = "Allow"
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      "arn:aws:secretsmanager:${var.aws_region}:${var.aws_account_id}:secret:${var.access_policy_resource_secrets_prefix}*"
    ]
  }
}