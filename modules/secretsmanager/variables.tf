variable "secrets_list" {
  type = set(object({
    name        = string
    description = optional(string)
  }))
  description = "A map containing the secret name and an optional description"
}

variable "aws_account_id" {
  type        = string
  description = "The secrets AWS account id."
}

variable "aws_region" {
  type        = string
  description = "The Secrets aws region."
  default     = "us-east-1"
}

variable "access_policy_name" {
  type        = string
  description = "Name of the IAM access policy which will allow access to the secrets"
}

variable "access_policy_resource_secrets_prefix" {
  type        = string
  description = "Prefix of the secrets you want to grant access to"
  default     = ""
}
