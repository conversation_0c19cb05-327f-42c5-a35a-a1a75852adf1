resource "aws_wafv2_rule_group" "playgami_rate_limit_filtered" {
  for_each = { for rate_limit in var.ip_rate_limits : rate_limit.name => rate_limit if rate_limit.filter != null }

  name     = "playgami-rate-limit-${each.value.name}"
  scope    = "REGIONAL"
  capacity = 55
  tags     = var.tags


  rule {
    name     = "playgami-rate-limit-${each.value.name}"
    priority = 0
    action {
      block {
        custom_response {
          response_code = "429"
        }
      }
    }
    statement {
      rate_based_statement {
        limit                 = each.value.limit
        evaluation_window_sec = each.value.period_seconds
        aggregate_key_type    = "IP"

        scope_down_statement {
          and_statement {
            statement {
              byte_match_statement {
                search_string = each.value.host
                field_to_match {
                  single_header {
                    name = "host"
                  }
                }
                positional_constraint = "EXACTLY"
                text_transformation {
                  priority = 0
                  type     = "NONE"
                }
              }
            }
            statement {
              regex_pattern_set_reference_statement {
                arn = each.value.filter.uri_include_pattern_set_arn

                field_to_match {
                  uri_path {}
                }

                text_transformation {
                  priority = 0
                  type     = "NONE"
                }
              }
            }
            statement {
              not_statement {
                statement {
                  regex_pattern_set_reference_statement {
                    arn = each.value.filter.uri_include_exclude_pattern_set_arn

                    field_to_match {
                      uri_path {}
                    }

                    text_transformation {
                      priority = 0
                      type     = "NONE"
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    rule_label {
      name = "playgami:rate-limit:filtered"
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "playgami-rate-limit-${each.value.name}"
      sampled_requests_enabled   = true
    }
  }


  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "playgami-rate-limit-${each.value.name}"
    sampled_requests_enabled   = true
  }
}