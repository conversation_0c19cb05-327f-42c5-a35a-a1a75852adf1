data "aws_caller_identity" "current" {}

data "aws_wafv2_web_acl" "security_managed_wafv2_web_acl" {
  name  = module.context.scopely_managed_regional_web_acls[data.aws_caller_identity.current.account_id]
  scope = "REGIONAL"
}

data "aws_wafv2_web_acl" "security_managed_wafv2_global_web_acl" {
  name  = module.context.scopely_managed_cloudfront_web_acls[data.aws_caller_identity.current.account_id]
  scope = "CLOUDFRONT"
}

resource "aws_wafv2_web_acl" "staging_cf_wafv2_cloudfront_web_acl" {
  name        = "staging-cf-global-security-policy"
  description = "This is a copy of the Scopely global security policy defined in ${data.aws_wafv2_web_acl.security_managed_wafv2_global_web_acl.name}. Changes should be ported manually here."
  scope       = "CLOUDFRONT"

  default_action {
    allow {}
  }

  rule {
    name     = "staging-AWSManagedRulesAmazonIpReputationList"
    priority = 0
    override_action {
      count {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAmazonIpReputationList"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "staging-cf-global-security-policy-AWSManagedRulesAmazonIpReputationList"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "staging-AWSManagedRulesCommonRuleSet"
    priority = 1
    override_action {
      count {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "staging-cf-global-security-policy-AWSManagedRulesCommonRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "staging-AWSManagedRulesKnownBadInputsRuleSet"
    priority = 2
    override_action {
      count {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "staging-cf-global-security-policy-AWSManagedRulesKnownBadInputsRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "staging-AWSManagedRulesBotControlRuleSet"
    priority = 3
    override_action {
      count {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesBotControlRuleSet"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "staging-cf-global-security-policy-AWSManagedRulesBotControlRuleSet"
      sampled_requests_enabled   = true
    }
  }


  rule {
    name     = "staging-http-floodp-protection"
    priority = 4
    override_action {
      count {}
    }
    statement {
      rule_group_reference_statement {
        arn = "arn:aws:wafv2:us-east-1:289736458775:global/rulegroup/HTTP-FloodP-Protection/e303b593-17d9-4d1c-bdb0-9e78ef499983"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "staging-cf-global-security-policy-http-floodp-protection"
      sampled_requests_enabled   = true
    }
  }


  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "staging-cf-global-security-policy"
    sampled_requests_enabled   = true
  }

  tags = var.tags
}

resource "aws_wafv2_web_acl" "staging_wafv2_web_acl" {
  depends_on = [aws_wafv2_rule_group.playgami_rate_limit_filtered, aws_wafv2_rule_group.playgami_rate_limit_simple]

  name        = "staging-global-security-policy"
  description = "This is a copy of the Scopely global security policy defined in ${data.aws_wafv2_web_acl.security_managed_wafv2_web_acl.name}. Changes should be ported manually here."
  scope       = "REGIONAL"

  default_action {
    allow {}
  }

  rule {
    name     = "staging-geo-rule-group"
    priority = 0
    override_action {
      count {}
    }
    statement {
      rule_group_reference_statement {
        arn = "arn:aws:wafv2:us-east-1:289736458775:regional/rulegroup/GlobalSecurityPolicy_regional-GeoRuleGroup/47bbf8a4-018c-49fe-8244-d561129c5441"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "staging-global-security-policy-GlobalSecurityPolicy_regional-GeoRuleGroup"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "staging-AWSManagedRulesSQLiRuleSet"
    priority = 1
    override_action {
      count {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesSQLiRuleSet"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "staging-global-security-policy-AWSManagedRulesSQLiRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "staging-AWSManagedRulesLinuxRuleSet"
    priority = 2
    override_action {
      count {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesLinuxRuleSet"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "staging-global-security-policy-AWSManagedRulesLinuxRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "staging-AWSManagedRulesUnixRuleSet"
    priority = 3
    override_action {
      count {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesUnixRuleSet"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "staging-global-security-policy-AWSManagedRulesUnixRuleSet"
      sampled_requests_enabled   = true
    }
  }

  dynamic "rule" {
    for_each = { for rate_limit in var.ip_rate_limits : rate_limit.name => rate_limit if rate_limit.filter == null }

    content {
      name     = "staging-rate-limit-${rule.value.name}"
      priority = 4 + rule.value.priority

      override_action {
        count {}
      }

      statement {
        rule_group_reference_statement {
          arn = aws_wafv2_rule_group.playgami_rate_limit_simple[rule.value.name].arn
        }
      }

      visibility_config {
        cloudwatch_metrics_enabled = true
        metric_name                = "staging-global-security-policy-playgami-rate-limit-${rule.value.name}"
        sampled_requests_enabled   = true
      }
    }
  }

  dynamic "rule" {
    for_each = { for rate_limit in var.ip_rate_limits : rate_limit.name => rate_limit if rate_limit.filter != null }

    content {
      name     = "staging-rate-limit-${rule.value.name}"
      priority = 4 + rule.value.priority

      override_action {
        count {}
      }

      statement {
        rule_group_reference_statement {
          arn = aws_wafv2_rule_group.playgami_rate_limit_filtered[rule.value.name].arn
        }
      }

      visibility_config {
        cloudwatch_metrics_enabled = true
        metric_name                = "staging-global-security-policy-playgami-rate-limit-${rule.value.name}"
        sampled_requests_enabled   = true
      }
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "staging-global-security-policy"
    sampled_requests_enabled   = true
  }

  tags = var.tags
}

module "s3" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "3.10.1"

  bucket                  = "aws-waf-logs-staging-${data.aws_caller_identity.current.account_id}"
  block_public_acls       = true
  block_public_policy     = true
  restrict_public_buckets = true
  ignore_public_acls      = true

  access_log_delivery_policy_source_accounts = [data.aws_caller_identity.current.account_id]
  lifecycle_rule = [{
    id      = "delete-old-files"
    enabled = true

    expiration = {
      days = 15
    }
  }]

  tags = var.tags
}

resource "aws_wafv2_web_acl_logging_configuration" "staging_wafv2_web_acl_logging_configuration" {
  log_destination_configs = [module.s3.s3_bucket_arn]
  resource_arn            = aws_wafv2_web_acl.staging_wafv2_web_acl.arn

  logging_filter {
    default_behavior = "DROP"

    filter {
      behavior = "KEEP"

      condition {
        action_condition {
          action = "COUNT"
        }
      }

      requirement = "MEETS_ALL"
    }
  }
}

resource "aws_wafv2_web_acl_logging_configuration" "staging_wafv2_cloudfront_web_acl_logging_configuration" {
  log_destination_configs = [module.s3.s3_bucket_arn]
  resource_arn            = aws_wafv2_web_acl.staging_cf_wafv2_cloudfront_web_acl.arn

  logging_filter {
    default_behavior = "DROP"

    filter {
      behavior = "KEEP"

      condition {
        action_condition {
          action = "COUNT"
        }
      }

      requirement = "MEETS_ALL"
    }
  }
}