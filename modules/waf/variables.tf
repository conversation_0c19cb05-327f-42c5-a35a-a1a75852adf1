variable "ip_rate_limits" {
  type = list(object({
    name  = string
    host  = string
    limit = number
    filter = optional(object({
      uri_include_pattern_set_arn         = string
      uri_include_exclude_pattern_set_arn = string
    }))
    period_seconds = optional(number, 300)
    priority       = number
  }))
  description = "Host-specific rate limits for the same IP address"
  default     = []
}

