resource "aws_wafv2_rule_group" "playgami_rate_limit_simple" {
  for_each = { for rate_limit in var.ip_rate_limits : rate_limit.name => rate_limit if rate_limit.filter == null }

  name     = "playgami-rate-limit-${each.value.name}"
  scope    = "REGIONAL"
  capacity = 5
  tags     = var.tags

  rule {
    name     = "playgami-rate-limit-${each.value.name}"
    priority = 0
    action {
      block {
        custom_response {
          response_code = "429"
        }
      }
    }
    statement {
      rate_based_statement {
        limit                 = each.value.limit
        evaluation_window_sec = each.value.period_seconds
        aggregate_key_type    = "IP"

        scope_down_statement {
          byte_match_statement {
            search_string = each.value.host
            field_to_match {
              single_header {
                name = "host"
              }
            }
            positional_constraint = "EXACTLY"
            text_transformation {
              priority = 0
              type     = "NONE"
            }
          }
        }
      }
    }
    rule_label {
      name = "playgami:rate-limit:simple"
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "playgami-rate-limit-${each.value.name}"
      sampled_requests_enabled   = true
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "playgami-rate-limit-${each.value.name}"
    sampled_requests_enabled   = true
  }
}