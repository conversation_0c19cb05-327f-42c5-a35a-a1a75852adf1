
provider "aws" {
  region              = "us-east-1"
  allowed_account_ids = ["************"]
}

variables {
  hosted_zone_name        = "test.example.scopely.io"
  hosted_zone_description = "Hosted zone for terraform testing domain"

  records_list = [
    {
      name    = "test-one.example.scopely.io"
      type    = "CNAME"
      ttl     = 300
      records = ["test-two.example.scopely.io"]
    },
    {
      name    = "test-two.example.scopely.io"
      type    = "TXT"
      ttl     = 300
      records = ["terraform-test"]
    }
  ]
  tags = {
    environment = "preview"
    allocation  = "playgami-infrastructure"
    affinity    = "none"
    product     = "tests"
    team        = "playgami-devops"
    org_unit    = "playgami"
  }

}

run "route53_plan" {
  command = plan

  assert {
    condition     = aws_route53_zone.primary.name == "test.example.scopely.io"
    error_message = "error creating route53 zone"
  }

  assert {
    condition     = aws_route53_record.records["test-one.example.scopely.io_CNAME"].name == "test-one.example.scopely.io"
    error_message = "s3 bucket has website endpoint enabled (it should be disabled by policy)"
  }

}

