variable "hosted_zone_name" {
  type        = string
  description = "Name of the hosted zone to create."
}

variable "hosted_zone_description" {
  type        = string
  description = "Description of the hosted zone to create."
}

variable "records_list" {
  description = "List of records for the zone. Warning! Alias record types conflicts with TTL and 'records' field specification."
  default     = []
  type = list(object({
    name    = string
    type    = string
    ttl     = optional(number)
    records = optional(list(string))
    alias = optional(object({
      name                   = string
      zone_id                = string
      evaluate_target_health = bool
    }))
    weight         = optional(number)
    set_identifier = optional(string)
  }))
}