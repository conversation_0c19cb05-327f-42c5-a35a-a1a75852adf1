resource "aws_route53_zone" "primary" {
  name    = var.hosted_zone_name
  comment = "${var.hosted_zone_description}. Managed by Terraform."

  tags = module.context.tags
}


resource "aws_route53_record" "records" {
  for_each = { for _, record in var.records_list : "${record.name}_${record.type}${try("_${record.set_identifier}", "")}" => record }

  zone_id = aws_route53_zone.primary.zone_id
  name    = each.value.name
  type    = each.value.type
  ttl     = each.value.ttl
  records = each.value.records

  set_identifier = each.value.set_identifier

  dynamic "weighted_routing_policy" {
    for_each = each.value.weight != null ? [1] : []
    content {
      weight = each.value.weight
    }
  }

  dynamic "alias" {
    for_each = each.value.alias != null ? [1] : []
    content {
      name                   = each.value.alias.name
      zone_id                = each.value.alias.zone_id
      evaluate_target_health = each.value.alias.evaluate_target_health
    }
  }

}