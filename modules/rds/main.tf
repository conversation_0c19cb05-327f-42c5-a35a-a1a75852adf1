module "db" {
  source  = "terraform-aws-modules/rds/aws"
  version = "5.1.0"

  identifier = var.db_identifier

  engine            = var.db_engine
  engine_version    = var.db_engine_version
  instance_class    = var.db_instance_class
  allocated_storage = var.db_allocate_storage

  db_name  = var.db_name
  username = var.db_username
  port     = var.db_port

  create_random_password = var.db_secrets_arn != null ? var.db_create_random_password : true
  password               = var.db_secrets_arn != null ? jsondecode(data.aws_secretsmanager_secret_version.db_secrets_latest[0].secret_string)["password"] : "no_effect"

  iam_database_authentication_enabled = true

  vpc_security_group_ids = var.db_vpc_security_groups_ids

  apply_immediately       = var.db_apply_immediately
  maintenance_window      = var.db_maintenance_window
  backup_retention_period = var.db_backup_retention_period
  backup_window           = var.db_backup_window

  # Enhanced Monitoring - see example for details on how to create the role
  # by yourself, in case you don't want to create it automatically
  #  monitoring_interval = "30"
  #  monitoring_role_name = "MyRDSMonitoringRole"
  #  create_monitoring_role = true

  # DB subnet group
  create_db_subnet_group = true
  subnet_ids             = var.db_subnet_ids

  # DB parameter group
  family = var.db_parameter_group_family

  # DB option group
  major_engine_version = var.db_major_engine_version

  # Database Deletion Protection
  deletion_protection = var.db_deletion_protection

  tags = module.context.tags
}