data "aws_secretsmanager_secret" "db_secrets" {
  count = var.db_secrets_arn != null ? 1 : 0

  arn = var.db_secrets_arn
}

data "aws_secretsmanager_secret_version" "db_secrets_latest" {
  count = var.db_secrets_arn != null ? 1 : 0

  secret_id = data.aws_secretsmanager_secret.db_secrets[0].id
}

#data "aws_subnet_ids" "db_vpc_private_subnet_ids" {
#  vpc_id = var.db_vpc_id
#
#  filter {
#    name   = "map-public-ip-on-launch"
#    values = ["no"] # insert values here
#  }
#}