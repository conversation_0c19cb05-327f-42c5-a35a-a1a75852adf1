variable "db_identifier" {
  type        = string
  description = "Identifier of your RDS database."
}

variable "db_engine" {
  type        = string
  description = "Engine of your RDS database"
  default     = "mysql"
}

variable "db_engine_version" {
  type        = string
  description = "Engine version of your RDS database"
  default     = "5.7.25"
}

variable "db_instance_class" {
  type        = string
  description = "Instance class of your RDS database, check the options in https://aws.amazon.com/es/rds/custom/pricing/"
  default     = "db.t4g.micro"
}

variable "db_allocate_storage" {
  type        = number
  description = "Size in GB of your RDS database"
  default     = 5
}

variable "db_name" {
  type        = string
  description = "The DB name to create. If omitted, no database is created initially"
  default     = null
}

variable "db_username" {
  type        = string
  description = "Username for the master DB user"
}

variable "db_port" {
  type        = string
  description = "The port on which the DB accepts connections	"
}

variable "db_create_random_password" {
  type        = bool
  description = "Whether to create random password for RDS primary cluster"
  default     = true
}

variable "db_secrets_arn" {
  type        = string
  description = "ARN of the secret from AWS Secrets Manger which contains the PASSWORD key with the password. The password provided will not be used if the variable create_random_password is set to true."
}

variable "db_vpc_security_groups_ids" {
  type        = list(string)
  description = "List of VPC security groups to associate"
  default     = []
}

variable "db_apply_immediately" {
  type        = bool
  description = "Specifies whether any database modifications are applied immediately, or during the next maintenance window"
  default     = false
}

variable "db_maintenance_window" {
  type        = string
  description = "The window to perform maintenance in. Syntax: 'ddd:hh24:mi-ddd:hh24:mi'. Eg: 'Mon:00:00-Mon:03:00'"
  default     = "Mon:00:00-Mon:03:00"
}

variable "db_backup_retention_period" {
  type        = number
  description = "The days to retain backups for. Must be between 0 and 35. Default is 0"
  default     = 0
}

variable "db_backup_window" {
  type        = string
  description = "The daily time range (in UTC) during which automated backups are created if they are enabled."
  default     = "03:00-06:00"
}

variable "db_subnet_ids" {
  type        = list(string)
  description = "A list of VPC subnet IDs	"
}

variable "db_parameter_group_family" {
  type        = string
  description = "The family of the DB parameter group. You can see the available ones with aws rds describe-db-engine-versions --query \"DBEngineVersions[].DBParameterGroupFamily\""
}

variable "db_major_engine_version" {
  type        = string
  description = "Specifies the major version of the engine that this option group should be associated with"
}

variable "db_deletion_protection" {
  type        = bool
  description = "The database can't be deleted when this value is set to true"
  default     = true
}