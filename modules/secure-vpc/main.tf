locals {
  allow_all_traffic_within_vpc = concat([
    {
      "action" : "allow",
      "cidr_block" : data.aws_vpc.selected.cidr_block,
      "from_port" : 0,
      "protocol" : "-1",
      "rule_no" : 100,
      "to_port" : 0
    }],
    data.aws_vpc.selected.ipv6_cidr_block != "" ? [{
      "action" : "allow",
      "ipv6_cidr_block" : data.aws_vpc.selected.ipv6_cidr_block,
      "from_port" : 0,
      "protocol" : "-1",
      "rule_no" : 101,
      "to_port" : 0
    }] : [],
    [for block in data.aws_vpc.selected.cidr_block_associations :
      {
        "action" : "allow",
        "cidr_block" : block.cidr_block,
        "from_port" : 0,
        "protocol" : "-1",
        "rule_no" : index(data.aws_vpc.selected.cidr_block_associations, block) + 102,
        "to_port" : 0
      }
  ])
  allow_http_and_https_from_anywhere = [
    {
      "action" : "allow",
      "cidr_block" : "0.0.0.0/0",
      "from_port" : 443,
      "protocol" : "6",
      "rule_no" : 200,
      "to_port" : 443
    },
    {
      "action" : "allow",
      "ipv6_cidr_block" : "::/0",
      "from_port" : 443,
      "protocol" : "6",
      "rule_no" : 201,
      "to_port" : 443
    },
    {
      "action" : "allow",
      "cidr_block" : "0.0.0.0/0",
      "from_port" : 80,
      "protocol" : "6",
      "rule_no" : 202,
      "to_port" : 80
    },
    {
      "action" : "allow",
      "ipv6_cidr_block" : "::/0",
      "from_port" : 80,
      "protocol" : "6",
      "rule_no" : 203,
      "to_port" : 80
    }
  ]
  allow_ssh_and_rdp_from_vpn = [
    {
      "action" : "allow",
      "cidr_block" : "**************/32",
      "from_port" : 22,
      "protocol" : "6",
      "rule_no" : 300,
      "to_port" : 22
    },
    {
      "action" : "allow",
      "cidr_block" : "**************/32",
      "from_port" : 3389,
      "protocol" : "6",
      "rule_no" : 301,
      "to_port" : 3389
    }
  ]
  # https://docs.aws.amazon.com/vpc/latest/userguide/vpc-network-acls.html#nacl-ephemeral-ports
  allow_upper_ephemeral_ports_from_anywhere = [
    {
      "action" : "allow",
      "cidr_block" : "0.0.0.0/0",
      "from_port" : 32678,
      "protocol" : "6",
      "rule_no" : 400,
      "to_port" : 65535
    },
    {
      "action" : "allow",
      "ipv6_cidr_block" : "::/0",
      "from_port" : 32678,
      "protocol" : "6",
      "rule_no" : 401,
      "to_port" : 65535
    }
  ]
  allow_additional_ephemeral_ports_except_rdp_from_anywhere = [
    {
      "action" : "allow",
      "cidr_block" : "0.0.0.0/0",
      "from_port" : 1024,
      "protocol" : "6",
      "rule_no" : 500,
      "to_port" : 3388
    },
    {
      "action" : "allow",
      "ipv6_cidr_block" : "::/0",
      "from_port" : 1024,
      "protocol" : "6",
      "rule_no" : 501,
      "to_port" : 3388
    },
    {
      "action" : "allow",
      "cidr_block" : "0.0.0.0/0",
      "from_port" : 3390,
      "protocol" : "6",
      "rule_no" : 502,
      "to_port" : 65535
    },
    {
      "action" : "allow",
      "ipv6_cidr_block" : "::/0",
      "from_port" : 3390,
      "protocol" : "6",
      "rule_no" : 503,
      "to_port" : 65535
    }
  ]
  deny_the_rest = [
    {
      "action" : "deny",
      "cidr_block" : "0.0.0.0/0",
      "from_port" : 0,
      "protocol" : "-1",
      "rule_no" : 1000,
      "to_port" : 0
    },
    {
      "action" : "deny",
      "ipv6_cidr_block" : "::/0",
      "from_port" : 0,
      "protocol" : "-1",
      "rule_no" : 1001,
      "to_port" : 0
    }
  ]

  allow_everything = [
    {
      "action" : "allow",
      "cidr_block" : "0.0.0.0/0",
      "from_port" : 0,
      "protocol" : "-1",
      "rule_no" : 100,
      "to_port" : 0
    },
    {
      "action" : "allow",
      "from_port" : 0,
      "ipv6_cidr_block" : "::/0",
      "protocol" : "-1",
      "rule_no" : 101,
      "to_port" : 0
    }
  ]
}

data "aws_vpc" "selected" {
  id = var.vpc_id
}

resource "aws_default_network_acl" "default" {
  // Cannot be obtained from the data block
  default_network_acl_id = var.default_network_acl_id

  subnet_ids = null

  dynamic "ingress" {
    for_each = concat(
      local.allow_all_traffic_within_vpc,
      local.allow_http_and_https_from_anywhere,
      local.allow_ssh_and_rdp_from_vpn,
      local.allow_upper_ephemeral_ports_from_anywhere,
      local.allow_additional_ephemeral_ports_except_rdp_from_anywhere,
      local.deny_the_rest
    )
    content {
      action          = ingress.value.action
      cidr_block      = lookup(ingress.value, "cidr_block", null)
      from_port       = ingress.value.from_port
      icmp_code       = lookup(ingress.value, "icmp_code", null)
      icmp_type       = lookup(ingress.value, "icmp_type", null)
      ipv6_cidr_block = lookup(ingress.value, "ipv6_cidr_block", null)
      protocol        = ingress.value.protocol
      rule_no         = ingress.value.rule_no
      to_port         = ingress.value.to_port
    }
  }
  dynamic "egress" {
    for_each = concat(
      local.allow_everything
    )
    content {
      action          = egress.value.action
      cidr_block      = lookup(egress.value, "cidr_block", null)
      from_port       = egress.value.from_port
      icmp_code       = lookup(egress.value, "icmp_code", null)
      icmp_type       = lookup(egress.value, "icmp_type", null)
      ipv6_cidr_block = lookup(egress.value, "ipv6_cidr_block", null)
      protocol        = egress.value.protocol
      rule_no         = egress.value.rule_no
      to_port         = egress.value.to_port
    }
  }

  tags = merge(
    { "Name" = "${var.vpc_name}-default" },
    module.context.tags
  )

  lifecycle {
    ignore_changes = [subnet_ids]
  }
}

resource "aws_default_security_group" "default" {
  vpc_id = var.vpc_id

  ingress = []
  egress  = []

  tags = merge(
    module.context.tags,
    { "Name" = "DO NOT USE. DO NOT ADD RULES" },
  )
}
