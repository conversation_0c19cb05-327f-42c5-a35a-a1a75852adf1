<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | n/a |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_context"></a> [context](#module\_context) | app.terraform.io/scopely-playgami/module-context/tf | 0.0.9 |

## Resources

| Name | Type |
|------|------|
| [aws_iam_instance_profile.ec2_instance_profile](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile) | resource |
| [aws_iam_role.ec2_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment.ec2_role_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_instance.instance](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/instance) | resource |
| [aws_security_group.instance_security_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_security_group_rule.egress_cidr_rules](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_security_group_rule.egress_sg_rules](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_security_group_rule.ingress_cidr_rules](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_security_group_rule.ingress_sg_rules](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_ami.instance_ami](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ami) | data source |
| [aws_subnet.instance_subnet](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/subnet) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_associate_public_access"></a> [associate\_public\_access](#input\_associate\_public\_access) | Attaches a public ip to the instance. | `bool` | `false` | no |
| <a name="input_egress_cidr_rules"></a> [egress\_cidr\_rules](#input\_egress\_cidr\_rules) | A map with the rule name as key and the ingress rule object as value. | <pre>map(object({<br>    ipv4_cidr_blocks = list(string)<br>    ipv6_cidr_blocks = optional(list(string), [])<br>    from_port        = number<br>    to_port          = number<br>    protocol         = optional(string, "tcp")<br>    description      = optional(string)<br>  }))</pre> | `{}` | no |
| <a name="input_egress_sg_rules"></a> [egress\_sg\_rules](#input\_egress\_sg\_rules) | A map with the rule name as key and the egress rule object as value. | <pre>map(object({<br>    sg_id       = string<br>    from_port   = number<br>    to_port     = number<br>    protocol    = optional(string, "tcp")<br>    description = optional(string)<br>  }))</pre> | `{}` | no |
| <a name="input_enable_detailed_monitoring"></a> [enable\_detailed\_monitoring](#input\_enable\_detailed\_monitoring) | Enables detailed monitoring on the instance. | `bool` | `false` | no |
| <a name="input_ingress_cidr_rules"></a> [ingress\_cidr\_rules](#input\_ingress\_cidr\_rules) | A map with the rule name as key and the ingress rule object as value. | <pre>map(object({<br>    ipv4_cidr_blocks = list(string)<br>    ipv6_cidr_blocks = optional(list(string), [])<br>    from_port        = number<br>    to_port          = number<br>    protocol         = optional(string, "tcp")<br>    description      = optional(string)<br>  }))</pre> | `{}` | no |
| <a name="input_ingress_sg_rules"></a> [ingress\_sg\_rules](#input\_ingress\_sg\_rules) | A map with the rule name as key and the ingress rule object as value. | <pre>map(object({<br>    sg          = string<br>    from_port   = number<br>    to_port     = number<br>    protocol    = optional(string, "tcp")<br>    description = optional(string)<br>  }))</pre> | `{}` | no |
| <a name="input_instance_additional_security_groups"></a> [instance\_additional\_security\_groups](#input\_instance\_additional\_security\_groups) | Additional security groups for the instance. | `list(string)` | `[]` | no |
| <a name="input_instance_ami_name"></a> [instance\_ami\_name](#input\_instance\_ami\_name) | The instance AMI image name. Defaults to latest Amazon Linux 2023. | `string` | `"al2023-ami-2023.*-x86_64"` | no |
| <a name="input_instance_name"></a> [instance\_name](#input\_instance\_name) | The EC2 instance name | `string` | n/a | yes |
| <a name="input_instance_root_volume_delete_on_termination"></a> [instance\_root\_volume\_delete\_on\_termination](#input\_instance\_root\_volume\_delete\_on\_termination) | Defines if the instance root EBS volume is deleted after instance termination. Defaults to true. | `bool` | `true` | no |
| <a name="input_instance_root_volume_size_gib"></a> [instance\_root\_volume\_size\_gib](#input\_instance\_root\_volume\_size\_gib) | Sets the instance root EBS volume size in GiB. Defaults to 20 GiB. | `number` | `20` | no |
| <a name="input_instance_root_volume_type"></a> [instance\_root\_volume\_type](#input\_instance\_root\_volume\_type) | Sets the instance EBS root volume type. Defaults to gp2. | `string` | `"gp2"` | no |
| <a name="input_instance_subnet_id"></a> [instance\_subnet\_id](#input\_instance\_subnet\_id) | The instance subnet to be deployed. | `string` | n/a | yes |
| <a name="input_instance_type"></a> [instance\_type](#input\_instance\_type) | The instance type. Defaults to t3.micro. | `string` | `"t3.micro"` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags map | `map(string)` | n/a | yes |
| <a name="input_user_data"></a> [user\_data](#input\_user\_data) | The user data script to execute when at instance startup. | `string` | `null` | no |
| <a name="input_user_data_replace_on_change"></a> [user\_data\_replace\_on\_change](#input\_user\_data\_replace\_on\_change) | When used in combination with user\_data will trigger a destroy and recreate when set to true. Defaults to false if not set. | `bool` | `false` | no |

## Outputs

No outputs.
<!-- END_TF_DOCS -->