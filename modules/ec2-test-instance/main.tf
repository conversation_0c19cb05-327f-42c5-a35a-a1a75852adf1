resource "aws_instance" "instance" {
  ami                         = data.aws_ami.instance_ami.id
  instance_type               = var.instance_type
  subnet_id                   = var.instance_subnet_id
  associate_public_ip_address = var.associate_public_access

  user_data                   = var.user_data
  user_data_replace_on_change = var.user_data_replace_on_change

  iam_instance_profile = aws_iam_instance_profile.ec2_instance_profile.id

  vpc_security_group_ids = concat([aws_security_group.instance_security_group.id], var.instance_additional_security_groups)

  root_block_device {
    volume_type = var.instance_root_volume_type
    volume_size = var.instance_root_volume_size_gib

    delete_on_termination = var.instance_root_volume_delete_on_termination

    tags = module.context.tags
  }

  monitoring = var.enable_detailed_monitoring

  tags = merge({ Name = var.instance_name }, module.context.tags)
}

resource "aws_security_group" "instance_security_group" {
  name_prefix = "${var.instance_name}-instance-sg"
  vpc_id      = data.aws_subnet.instance_subnet.vpc_id
  description = "security group for the ${var.instance_name} EC2 instance"

  tags = merge({
    Name = "${var.instance_name} EC2 Instance security group"
  }, module.context.tags)

}

resource "aws_security_group_rule" "ingress_cidr_rules" {
  for_each = var.ingress_cidr_rules

  security_group_id = aws_security_group.instance_security_group.id
  type              = "ingress"

  from_port        = each.value.from_port
  to_port          = each.value.to_port
  protocol         = each.value.protocol
  cidr_blocks      = each.value.ipv4_cidr_blocks
  ipv6_cidr_blocks = each.value.ipv6_cidr_blocks
}

resource "aws_security_group_rule" "ingress_sg_rules" {
  for_each = var.ingress_sg_rules

  security_group_id = aws_security_group.instance_security_group.id
  type              = "ingress"

  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  source_security_group_id = each.value.sg
}

resource "aws_security_group_rule" "egress_cidr_rules" {
  for_each = var.egress_cidr_rules

  security_group_id = aws_security_group.instance_security_group.id
  type              = "egress"

  from_port        = each.value.from_port
  to_port          = each.value.to_port
  protocol         = each.value.protocol
  cidr_blocks      = each.value.ipv4_cidr_blocks
  ipv6_cidr_blocks = each.value.ipv6_cidr_blocks
}

resource "aws_security_group_rule" "egress_sg_rules" {
  for_each = var.ingress_sg_rules

  security_group_id = aws_security_group.instance_security_group.id
  type              = "egress"

  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  source_security_group_id = each.value.sg
}


resource "aws_iam_instance_profile" "ec2_instance_profile" {
  name = "${var.instance_name}-ec2-ssm-instance-profile"

  role = aws_iam_role.ec2_role.name
}

resource "aws_iam_role" "ec2_role" {
  name = "${var.instance_name}-ec2-ssm-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ec2_role_policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
  role       = aws_iam_role.ec2_role.name
}