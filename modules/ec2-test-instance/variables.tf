variable "instance_name" {
  type        = string
  description = "The EC2 instance name"
}

variable "instance_type" {
  type        = string
  default     = "t3.micro"
  description = "The instance type. Defaults to t3.micro."
}

variable "instance_ami_name" {
  type        = string
  default     = "al2023-ami-2023.*-x86_64"
  description = "The instance AMI image name. Defaults to latest Amazon Linux 2023."
}

variable "instance_subnet_id" {
  type        = string
  description = "The instance subnet to be deployed."
}

variable "user_data" {
  type        = string
  default     = null
  description = "The user data script to execute when at instance startup."
}

variable "user_data_replace_on_change" {
  type        = bool
  default     = false
  description = "When used in combination with user_data will trigger a destroy and recreate when set to true. Defaults to false if not set."
}

variable "instance_additional_security_groups" {
  type        = list(string)
  default     = []
  description = "Additional security groups for the instance."
}

variable "enable_detailed_monitoring" {
  type        = bool
  default     = false
  description = "Enables detailed monitoring on the instance."
}

variable "associate_public_access" {
  type        = bool
  default     = false
  description = "Attaches a public ip to the instance."
}

variable "instance_root_volume_type" {
  type        = string
  default     = "gp2"
  description = "Sets the instance EBS root volume type. Defaults to gp2."
}

variable "instance_root_volume_size_gib" {
  type        = number
  default     = 20
  description = "Sets the instance root EBS volume size in GiB. Defaults to 20 GiB."
}

variable "instance_root_volume_delete_on_termination" {
  type        = bool
  default     = true
  description = "Defines if the instance root EBS volume is deleted after instance termination. Defaults to true."
}

variable "ingress_sg_rules" {
  type = map(object({
    sg          = string
    from_port   = number
    to_port     = number
    protocol    = optional(string, "tcp")
    description = optional(string)
  }))
  default     = {}
  description = "A map with the rule name as key and the ingress rule object as value."
}

variable "ingress_cidr_rules" {
  type = map(object({
    ipv4_cidr_blocks = list(string)
    ipv6_cidr_blocks = optional(list(string), [])
    from_port        = number
    to_port          = number
    protocol         = optional(string, "tcp")
    description      = optional(string)
  }))
  default     = {}
  description = "A map with the rule name as key and the ingress rule object as value."
}

variable "egress_sg_rules" {
  type = map(object({
    sg_id       = string
    from_port   = number
    to_port     = number
    protocol    = optional(string, "tcp")
    description = optional(string)
  }))
  default     = {}
  description = "A map with the rule name as key and the egress rule object as value."
}

variable "egress_cidr_rules" {
  type = map(object({
    ipv4_cidr_blocks = list(string)
    ipv6_cidr_blocks = optional(list(string), [])
    from_port        = number
    to_port          = number
    protocol         = optional(string, "tcp")
    description      = optional(string)
  }))
  default     = {}
  description = "A map with the rule name as key and the ingress rule object as value."
}