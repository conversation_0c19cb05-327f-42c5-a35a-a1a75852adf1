# iac-tf-module-template

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.1.9 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 4.13 |

## Providers

No providers.

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_this"></a> [this](#module\_this) | **************:scopely/tf-module-context.git | v0.0.8 |

## Resources

No resources.

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_additional_tag_map"></a> [additional\_tag\_map](#input\_additional\_tag\_map) | Additional tags for appending to tags\_as\_list\_of\_maps. Not added to `tags`. | `map(string)` | `{}` | no |
| <a name="input_affinity"></a> [affinity](#input\_affinity) | Contains the game, or group of games | `string` | `null` | no |
| <a name="input_allocation"></a> [allocation](#input\_allocation) | Contains a single, unique cost category, used to identify a group of services, group of components, etc. | `string` | `null` | no |
| <a name="input_context"></a> [context](#input\_context) | Single object for setting entire context at once.<br>See description of individual variables for details.<br>Leave string and numeric variables as `null` to use default value.<br>Individual variable settings (non-null) override settings in context object,<br>except for tags, and additional\_tag\_map, which are merged. | `any` | <pre>{<br>  "additional_tag_map": {},<br>  "affinity": null,<br>  "allocation": null,<br>  "enabled": true,<br>  "environment": null,<br>  "iac_repo": null,<br>  "iac_repo_path": null,<br>  "label_key_case": "lower",<br>  "label_value_case": "lower",<br>  "module_name": null,<br>  "module_version": null,<br>  "name": null,<br>  "namespace": "scopely",<br>  "org_unit": null,<br>  "product": null,<br>  "purpose": null,<br>  "regex_replace_chars": null,<br>  "service": null,<br>  "streamrole": null,<br>  "tags": {},<br>  "team": null<br>}</pre> | no |
| <a name="input_enabled"></a> [enabled](#input\_enabled) | Set to false to prevent the module from creating any resources | `bool` | `null` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | Environment, e.g. 'uw2', 'us-west-2', OR 'prod', 'staging', 'dev', 'UAT' | `string` | `null` | no |
| <a name="input_iac_repo"></a> [iac\_repo](#input\_iac\_repo) | Scopely IaC Git repo where the state definition is in | `string` | `null` | no |
| <a name="input_iac_repo_path"></a> [iac\_repo\_path](#input\_iac\_repo\_path) | Path in the 'iac\_repo' where the state definition for the module is in | `string` | `null` | no |
| <a name="input_label_key_case"></a> [label\_key\_case](#input\_label\_key\_case) | The letter case of label keys (`tag` names) (i.e. `name`, `namespace`, `environment`) to use in `tags`.<br>Possible values: `lower`, `title`, `upper`.<br>Default value: `title`. | `string` | `"lower"` | no |
| <a name="input_label_value_case"></a> [label\_value\_case](#input\_label\_value\_case) | The letter case of output label values (also used in `tags` and `id`).<br>Possible values: `lower`, `title`, `upper` and `none` (no transformation).<br>Default value: `lower`. | `string` | `"lower"` | no |
| <a name="input_module_name"></a> [module\_name](#input\_module\_name) | Scopely Module name used for creation of infrastructure | `string` | `null` | no |
| <a name="input_module_version"></a> [module\_version](#input\_module\_version) | Scopely Module version used for creation of infrastructure | `string` | `null` | no |
| <a name="input_name"></a> [name](#input\_name) | Solution name, e.g. 'app' or 'jenkins' | `string` | `null` | no |
| <a name="input_namespace"></a> [namespace](#input\_namespace) | Namespace, which could be your organization name or abbreviation, e.g. 'eg' or 'cp' | `string` | `null` | no |
| <a name="input_org_unit"></a> [org\_unit](#input\_org\_unit) | Contains Scopely's organization unit name, e.g. 'playgami' | `string` | `null` | no |
| <a name="input_product"></a> [product](#input\_product) | Contains the name of the product | `string` | `null` | no |
| <a name="input_purpose"></a> [purpose](#input\_purpose) | Contains for what was this resource created | `string` | `null` | no |
| <a name="input_regex_replace_chars"></a> [regex\_replace\_chars](#input\_regex\_replace\_chars) | Regex to replace chars with empty string in `namespace`, `environment`, `stage` and `name`.<br>If not set, `"/[^a-zA-Z0-9-]/"` is used to remove all characters other than hyphens, letters and digits. | `string` | `null` | no |
| <a name="input_service"></a> [service](#input\_service) | Contains the name of the service | `string` | `null` | no |
| <a name="input_streamrole"></a> [streamrole](#input\_streamrole) | Used for grouping the Kinesis streams | `string` | `null` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | Additional tags (e.g. `map('BusinessUnit','XYZ')` | `map(string)` | `{}` | no |
| <a name="input_team"></a> [team](#input\_team) | Used to attribute the resource to a team and to know the associated costs by areas | `string` | `null` | no |

## Outputs

No outputs.
<!-- END_TF_DOCS -->

# SonarCloud integration
After creating the project in SonarCloud you will have to create:
- Create a secret in the project to store the SONAR_TOKEN
- Add a sonar-project.properties in the root directory with the following content
    ```
    sonar.projectKey=scopely_iac-tf-module-aws-vpc
    sonar.organization=scopely
    ```