{"openapi": "3.0.1", "info": {"title": "Playgami Payments API documentation", "description": "This OpenAPI file describes the API to be integrated by game backends in order to adopt the Playgami Payments product", "version": "1.0"}, "externalDocs": {"description": "Find more information in the Playgami documentation", "url": "https://docs.playgami.scopely.com/v/developer-guides/developer-services/playgami-payments/playgami-payments-server-side-integration"}, "servers": [{"url": "http://localhost:9090/", "description": "Local environment"}, {"url": "https://payment-gateway.dev.payments.playgami.scopely.com/", "description": "Integration environment"}, {"url": "https://payment-gateway.prod.payments.playgami.scopely.com/", "description": "Production environment"}], "tags": [{"name": "payment-endpoint", "description": "Manages the creation and claiming of payment operations for players"}, {"name": "user-endpoint", "description": "Shows information about payments related to a single player"}, {"name": "admin-payment", "description": "Manages the admin operations over payments"}, {"name": "admin-user", "description": "Manages the admin operations over users"}, {"name": "exchange-rate", "description": "Manages the exchange rates between currencies"}, {"name": "game-config", "description": "Manages the game configurations in parameter store"}, {"name": "payment-widget", "description": "Manage the endpoints called from the widget"}, {"name": "price-localization", "description": "Manage price localization endpoints"}]}