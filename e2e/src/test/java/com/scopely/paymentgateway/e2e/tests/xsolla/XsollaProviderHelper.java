package com.scopely.paymentgateway.e2e.tests.xsolla;

import static java.math.BigDecimal.ZERO;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.scopely.paymentgateway.model.dto.createpayment.UserDTO;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.providers.xsolla.model.CurrencyAmountAndPercent;
import com.scopely.paymentgateway.providers.xsolla.model.CurrencyAndAmount;
import com.scopely.paymentgateway.providers.xsolla.model.PaymentDetails;
import com.scopely.paymentgateway.providers.xsolla.model.Purchase;
import com.scopely.paymentgateway.providers.xsolla.model.Transaction;
import com.scopely.paymentgateway.providers.xsolla.model.checkout.CheckoutRequest;
import com.scopely.paymentgateway.providers.xsolla.model.checkout.CheckoutResponse;
import com.scopely.paymentgateway.providers.xsolla.webhook.events.XsollaPaymentWebhookEvent;
import com.scopely.proteus.util.JacksonMapper;
import java.math.BigDecimal;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

public final class XsollaProviderHelper {
  private static final int CREDIT_CARD_METHOD_ID = 1380;
  private static final String PAYMENT_EVENT = "payment";

  private XsollaProviderHelper() {}

  public static CheckoutResponse buildCheckoutResponse() {
    return new CheckoutResponse(UUID.randomUUID().toString());
  }

  public static boolean verifyCheckoutRequest(
      CheckoutRequest checkoutRequest,
      UserDTO user,
      int quantity,
      PaymentBigDecimal price,
      String currency) {
    var checkout = checkoutRequest.purchase().checkout();
    var item = checkoutRequest.purchase().description().items().getFirst();
    return checkout.currency().equals(currency)
        && checkout.amount().compareTo(price.bigDecimal().multiply(BigDecimal.valueOf(quantity)))
            == 0
        && checkoutRequest.user().id().value().equals(user.userId())
        && item.price().amount().equals(price.bigDecimal().toPlainString());
  }

  public static String buildWebhookCompletedPaymentRequest(
      String paymentId, PaymentBigDecimal price, String currency) {
    int orderId = ThreadLocalRandom.current().nextInt();
    var amount = new CurrencyAndAmount(currency, price);
    var tax = new CurrencyAmountAndPercent(currency, new PaymentBigDecimal(ZERO), ZERO);
    var request =
        new XsollaPaymentWebhookEvent.Builder()
            .setEventName(PAYMENT_EVENT)
            .setTransaction(
                new Transaction(orderId, paymentId, null, 0, 0, null, CREDIT_CARD_METHOD_ID))
            .setPaymentDetails(
                new PaymentDetails(amount, null, null, null, null, tax, tax, tax, "0", null, null))
            .setPurchase(new Purchase(null, amount, null, null, null, null, null, null, null))
            .build();
    try {
      return JacksonMapper.MAPPER.writeValueAsString(request);
    } catch (JsonProcessingException e) {
      throw new IllegalStateException("Couldn't serialize request", e);
    }
  }
}
