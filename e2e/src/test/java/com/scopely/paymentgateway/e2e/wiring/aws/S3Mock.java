package com.scopely.paymentgateway.e2e.wiring.aws;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USD;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Uri;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

public class S3Mock {
  private static final String LOCALIZED_PRICES_JSON_TEMPLATE =
      """
        [
          {
            "sku": "%s",
            "regional_prices": {
              "PT": {
                "amount": 0.99,
                "currency": "EUR"
              },
              "ES": {
                "amount": 1.00,
                "currency": "EUR"
              }
            },
            "currency_prices": {
              "%s": {
                "amount": %.2f
              },
              "EUR": {
                "amount": 1.98
              }
            }
          }
        ]
      """;

  private final Map<S3Uri, String> files = new ConcurrentHashMap<>();

  @SuppressFBWarnings("VA_FORMAT_STRING_USES_NEWLINE")
  public static S3Client getS3Client() {
    S3Mock s3Mock = new S3Mock();
    s3Mock.putFile("playgami-payment-gateway-dev", "/translations/email.json", "{}");

    String fileContent = LOCALIZED_PRICES_JSON_TEMPLATE.formatted("psku3", USD, 1.23);

    s3Mock.putFile(
        "playgami-payment-gateway-dev",
        "localizedPrices/fc2403cd-da8e-401f-a811-4d32f4b6e99d/2024-10-15T17_02_56.429715Z.json",
        fileContent);

    S3Client client = mock(S3Client.class);
    doAnswer(
            invocation ->
                s3Mock.getObjectAsBytes(
                    invocation.getArgument(0, GetObjectRequest.class).bucket(),
                    invocation.getArgument(0, GetObjectRequest.class).key()))
        .when(client)
        .getObjectAsBytes(any(GetObjectRequest.class));

    doAnswer(
            invocation -> {
              InputStream is =
                  invocation.getArgument(1, RequestBody.class).contentStreamProvider().newStream();
              byte[] bytes = is.readAllBytes();

              String content = new String(bytes, StandardCharsets.UTF_8);
              s3Mock.putFile(
                  invocation.getArgument(0, PutObjectRequest.class).bucket(),
                  invocation.getArgument(0, PutObjectRequest.class).key(),
                  content);
              return null;
            })
        .when(client)
        .putObject(any(PutObjectRequest.class), any(RequestBody.class));

    return client;
  }

  public void putFile(String bucket, String path, String content) {
    files.put(buildS3Uri(bucket, path), content);
  }

  private ResponseBytes<?> getObjectAsBytes(String bucket, String path) {
    S3Uri uri = buildS3Uri(bucket, path);
    String content = files.get(uri);
    if (content == null) {
      throw AwsServiceException.builder().message("Key not found: " + uri).build();
    }
    return ResponseBytes.fromByteArray(
        GetObjectResponse.class, content.getBytes(StandardCharsets.UTF_8));
  }

  private S3Uri buildS3Uri(String bucket, String rawPath) {
    String path = rawPath.startsWith("/") ? rawPath : ("/" + rawPath);
    try {
      return S3Uri.builder().bucket(bucket).key(path).uri(new URI("s3://" + bucket + path)).build();
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }
}
