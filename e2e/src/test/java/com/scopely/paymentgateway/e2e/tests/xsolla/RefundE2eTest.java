package com.scopely.paymentgateway.e2e.tests.xsolla;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.API_KEY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.EUR;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.XSOLLA_API;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.XSOLLA_MERCHANT_API;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.scopely.paymentgateway.e2e.E2eTestLifecycle;
import com.scopely.paymentgateway.e2e.wiring.client.PaymentGatewayApiClientBuilder;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.web.E2ePaymentGatewayWebModule;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.model.refund.RefundRequestDTO;
import com.scopely.paymentgateway.model.refund.RefundStatus;
import com.scopely.paymentgateway.providers.xsolla.model.refund.RefundRequest;
import com.scopely.paymentgateway.providers.xsolla.model.refund.RefundResponse;
import com.scopely.paymentgateway.web.v1.controller.AdminPaymentEndpoint;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(E2eTestLifecycle.class)
public class RefundE2eTest {
  private AdminPaymentEndpoint adminPaymentClient;

  @BeforeEach
  public void setUp() {
    var port = E2ePaymentGatewayWebModule.PORT;
    adminPaymentClient =
        new PaymentGatewayApiClientBuilder<>(AdminPaymentEndpoint.class, port)
            .withClaims("*:payments:*")
            .build();
    reset(E2eServicesModule.TITAN_CLIENT, XSOLLA_API);
  }

  @Test
  public void testSuccessfulRefund() {
    String paymentId = "XS023";
    long orderId = 483021;
    var reason = RefundReason.BUYER_REMORSE;
    when(XSOLLA_MERCHANT_API.createRefund(
            anyString(), anyLong(), eq(orderId), any(RefundRequest.class)))
        .thenReturn(new RefundResponse(""));

    var request = new RefundRequestDTO(reason, "any-ticket", "any-comment");
    var result = adminPaymentClient.refundPayment(API_KEY, paymentId, request);

    assertThat(result.apiKey()).isEqualTo(API_KEY);
    assertThat(result.paymentId()).isEqualTo(paymentId);
    assertThat(result.refundId()).isNotNull();
    assertThat(result.refundReason()).isEqualTo(reason);
    assertThat(result.refundedAmount().amount()).isEqualTo("3.52");
    assertThat(result.refundedAmount().currency()).isEqualTo(EUR);
    assertThat(result.failureReason()).isNull();
    assertThat(result.status()).isEqualTo(RefundStatus.COMPLETED);
    assertThat(result.supportTicket()).isEqualTo("any-ticket");
    assertThat(result.comment()).isEqualTo("any-comment");
    verify(XSOLLA_MERCHANT_API)
        .createRefund(anyString(), anyLong(), eq(orderId), eq(new RefundRequest(reason)));
  }
}
