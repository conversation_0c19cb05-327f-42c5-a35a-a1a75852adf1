package com.scopely.paymentgateway.e2e.wiring.dagger.web;

import com.scopely.proteus.logging.Log;
import com.scopely.proteus.server.auth.ClaimSet;
import jakarta.annotation.Priority;
import jakarta.ws.rs.Priorities;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerRequestFilter;
import jakarta.ws.rs.core.SecurityContext;
import java.security.Principal;
import java.util.List;
import org.jboss.resteasy.core.ResteasyContext;
import org.jboss.resteasy.util.HttpHeaderNames;

@Priority(Priorities.AUTHENTICATION)
public class E2eJwtClaimsFilter implements ContainerRequestFilter {

  @Override
  public void filter(ContainerRequestContext requestContext) {
    String authorization = requestContext.getHeaderString(HttpHeaderNames.AUTHORIZATION);
    List<String> authorizationResult = process(authorization);
    if (authorizationResult.isEmpty()) {
      return;
    }
    String principalName = authorizationResult.getFirst();
    ClaimSet claimSet =
        ClaimSet.fromClaimTexts(authorizationResult.subList(1, authorizationResult.size()));
    requestContext.setSecurityContext(new E2eSecurityContext(principalName, claimSet));
    ResteasyContext.pushContext(ClaimSet.class, claimSet);
  }

  private List<String> process(String authorization) {
    if (authorization == null || !authorization.startsWith("Bearer ")) {
      Log.error("An unknown authentication scheme was presented: {}", authorization);
      return List.of();
    }
    return List.of(authorization.substring(7).split(","));
  }

  private static class E2eSecurityContext implements SecurityContext {
    private final String principalName;
    private final ClaimSet claimSet;

    public E2eSecurityContext(String principalName, ClaimSet claimSet) {
      this.principalName = principalName;
      this.claimSet = claimSet;
    }

    @Override
    public Principal getUserPrincipal() {
      return () -> principalName;
    }

    @Override
    public boolean isUserInRole(String role) {
      return claimSet.includes(role);
    }

    @Override
    public boolean isSecure() {
      return false;
    }

    @Override
    public String getAuthenticationScheme() {
      return "JWT";
    }
  }
}
