package com.scopely.paymentgateway.e2e.tests.xsolla.webhook;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.API_KEY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USD;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.verifyDisputeEvent;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.verifyTitanEventStreamEntry;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.REDIRECT_WEBHOOK_API;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.scopely.paymentgateway.e2e.E2eTestLifecycle;
import com.scopely.paymentgateway.e2e.db.verifier.DisputeRetriever;
import com.scopely.paymentgateway.e2e.tests.TestDataHelper;
import com.scopely.paymentgateway.e2e.wiring.client.PaymentGatewayApiClientBuilder;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.webhook.E2eWebhookXSollaModule;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeStatus;
import com.scopely.paymentgateway.providers.xsolla.model.CurrencyAndAmount;
import com.scopely.paymentgateway.providers.xsolla.model.dispute.Dispute;
import com.scopely.paymentgateway.providers.xsolla.model.dispute.DisputeTransaction;
import com.scopely.paymentgateway.providers.xsolla.webhook.events.XsollaDisputeWebhookEvent;
import com.scopely.paymentgateway.webhook.xsolla.v1.controller.WebhookXSollaEndpoint;
import com.scopely.proteus.util.JacksonMapper;
import com.scopely.titan.model.ClientEventDto;
import jakarta.ws.rs.core.MediaType;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Arrays;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import retrofit2.Response;
import rx.Observable;

@ExtendWith(E2eTestLifecycle.class)
public class DisputeWebhookE2eTest {
  private static final String USER_ID = TestDataHelper.USER_XSOLLA_1.userId();
  private static final String MISSING_PAYMENT_ID = "missing-payment-id";
  private static final BigDecimal AMOUNT = new BigDecimal("2.99");
  private static final CurrencyAndAmount XSOLLA_AMOUNT =
      new CurrencyAndAmount(USD, new PaymentBigDecimal(AMOUNT));
  private static final String DISPUTE_EVENT = "dispute";
  private static final String DISPUTE_REASON = "any-reason";
  private static final String AUTH = "any-auth-for-disputes";
  private static final Instant NOW = Instant.now();

  private WebhookXSollaEndpoint webhookApiClient;

  @BeforeEach
  public void setUp() {
    webhookApiClient =
        new PaymentGatewayApiClientBuilder<>(
                WebhookXSollaEndpoint.class, E2eWebhookXSollaModule.PORT)
            .build();
    TestDataHelper.mockSegmentationApi(API_KEY, USER_ID, segmentId -> true);
    reset(E2eServicesModule.TITAN_CLIENT);
  }

  @Test
  public void testReceiveDisputeOpenIsRedirectedWhenPaymentNotFound() {
    String redirectionResponse = "successful redirection";
    when(REDIRECT_WEBHOOK_API.redirect(anyString(), anyString(), anyString(), any()))
        .thenReturn(Observable.just(Response.success(redirectionResponse)));
    var request = buildRequest(MISSING_PAYMENT_ID, -1, "new");
    try (var response = webhookApiClient.xsollaWebhook(AUTH, API_KEY, request)) {
      assertThat(response.getStatus()).isEqualTo(200);
      assertThat(response.readEntity(String.class)).isEqualTo(redirectionResponse);
    }
    verify(REDIRECT_WEBHOOK_API)
        .redirect(eq(AUTH), eq(MediaType.APPLICATION_JSON), anyString(), any());
    verifyNoInteractions(E2eServicesModule.TITAN_CLIENT);
  }

  @Test
  public void testReceiveDisputeOpenIsSaved() {
    int orderId = 456001;
    String disputeId = "%s_%s".formatted(orderId, NOW.toEpochMilli());
    var request = buildRequest("XS001", orderId, "new");
    try (var response = webhookApiClient.xsollaWebhook(AUTH, API_KEY, request)) {
      assertThat(response.getStatus()).isEqualTo(200);
    }
    var optDisputeDao = DisputeRetriever.retrieveDisputeById(disputeId);
    assertThat(optDisputeDao).isPresent();
    var disputeDao = optDisputeDao.orElseThrow();
    assertThat(disputeDao.getAmount().getNumberStripped()).isEqualTo(AMOUNT);
    assertThat(disputeDao.getAmount().getCurrency().getCurrencyCode()).isEqualTo(USD);
    assertThat(disputeDao.getStatus()).isEqualTo(DisputeStatus.NEW);
    assertThat(disputeDao.getDisputeReason()).isEqualTo(DISPUTE_REASON);
    ClientEventDto event = verifyTitanEventStreamEntry(USER_ID);
    verifyDisputeEvent(event, disputeId, "new");
  }

  @Test
  public void testReceiveDisputeResolvedIsUpdated() {
    int orderId = 456002;
    String paymentId = "XS002";
    String disputeId = "%s_%s".formatted(orderId, NOW.toEpochMilli());

    var request1 = buildRequest(paymentId, orderId, "no_actions_required");
    try (var response = webhookApiClient.xsollaWebhook(AUTH, API_KEY, request1)) {
      assertThat(response.getStatus()).isEqualTo(200);
    }
    assertThat(DisputeRetriever.retrieveDisputeById(disputeId).orElseThrow().getStatus())
        .isEqualTo(DisputeStatus.IN_PROGRESS);
    ClientEventDto event1 = verifyTitanEventStreamEntry(USER_ID);
    verifyDisputeEvent(event1, disputeId, "in-progress");

    reset(E2eServicesModule.TITAN_CLIENT);
    var request2 = buildRequest(paymentId, orderId, "lost");
    try (var response = webhookApiClient.xsollaWebhook(AUTH, API_KEY, request2)) {
      assertThat(response.getStatus()).isEqualTo(200);
    }
    var disputeDao = DisputeRetriever.retrieveDisputeById(disputeId).orElseThrow();
    assertThat(disputeDao.getAmount().getNumberStripped()).isEqualTo(AMOUNT);
    assertThat(disputeDao.getAmount().getCurrency().getCurrencyCode()).isEqualTo(USD);
    assertThat(disputeDao.getStatus()).isEqualTo(DisputeStatus.LOST);
    assertThat(disputeDao.getDisputeReason()).isEqualTo(DISPUTE_REASON);
    ClientEventDto event2 = verifyTitanEventStreamEntry(USER_ID);
    verifyDisputeEvent(event2, disputeId, "lost");
  }

  private String buildRequest(String paymentId, int orderId, String status) {
    var xsollaStatus =
        Arrays.stream(
                com.scopely.paymentgateway.providers.xsolla.model.dispute.DisputeStatus.values())
            .filter(s -> s.getCode().equals(status))
            .findAny()
            .orElseThrow(() -> new IllegalArgumentException("Missing status: " + status));
    var request =
        new XsollaDisputeWebhookEvent.Builder()
            .setEventName(DISPUTE_EVENT)
            .setAction("")
            .setTransaction(
                new DisputeTransaction(orderId, null, XSOLLA_AMOUNT, null, null, paymentId))
            .setDispute(new Dispute(NOW, DISPUTE_REASON, null, xsollaStatus))
            .build();
    try {
      return JacksonMapper.MAPPER.writeValueAsString(request);
    } catch (JsonProcessingException e) {
      throw new IllegalStateException("Couldn't serialize request", e);
    }
  }
}
