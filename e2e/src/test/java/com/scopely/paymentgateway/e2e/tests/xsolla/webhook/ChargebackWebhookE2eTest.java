package com.scopely.paymentgateway.e2e.tests.xsolla.webhook;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.API_KEY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USD;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.REDIRECT_WEBHOOK_API;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.scopely.paymentgateway.e2e.E2eTestLifecycle;
import com.scopely.paymentgateway.e2e.db.verifier.ChargebackRetriever;
import com.scopely.paymentgateway.e2e.db.verifier.PaymentRetriever;
import com.scopely.paymentgateway.e2e.tests.TestDataHelper;
import com.scopely.paymentgateway.e2e.wiring.client.PaymentGatewayApiClientBuilder;
import com.scopely.paymentgateway.e2e.wiring.dagger.webhook.E2eWebhookXSollaModule;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.providers.xsolla.model.CurrencyAndAmount;
import com.scopely.paymentgateway.providers.xsolla.model.PaymentDetails;
import com.scopely.paymentgateway.providers.xsolla.model.Refund;
import com.scopely.paymentgateway.providers.xsolla.model.Transaction;
import com.scopely.paymentgateway.providers.xsolla.model.XsollaRefundCode;
import com.scopely.paymentgateway.providers.xsolla.webhook.events.XsollaRefundWebhookEvent;
import com.scopely.paymentgateway.webhook.xsolla.v1.controller.WebhookXSollaEndpoint;
import com.scopely.proteus.util.JacksonMapper;
import jakarta.ws.rs.core.MediaType;
import java.math.BigDecimal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import retrofit2.Response;
import rx.Observable;

@ExtendWith(E2eTestLifecycle.class)
public class ChargebackWebhookE2eTest {
  private static final String USER_ID = TestDataHelper.USER_XSOLLA_1.userId();
  private static final BigDecimal AMOUNT = new BigDecimal("2.99");
  private static final CurrencyAndAmount XSOLLA_AMOUNT =
      new CurrencyAndAmount(USD, new PaymentBigDecimal(AMOUNT));
  private static final String REFUND_EVENT = "refund";
  private static final String CHARGEBACK_REASON = "any-reason";
  private static final String AUTH = "any-auth-for-chargebacks";

  private WebhookXSollaEndpoint webhookApiClient;

  @BeforeEach
  public void setUp() {
    webhookApiClient =
        new PaymentGatewayApiClientBuilder<>(
                WebhookXSollaEndpoint.class, E2eWebhookXSollaModule.PORT)
            .build();
    TestDataHelper.mockSegmentationApi(API_KEY, USER_ID, segmentId -> true);
  }

  @Test
  public void testChargebackIsRedirectedWhenPaymentNotFound() {
    int missingOrderId = -1;
    String redirectionResponse = "successful redirection";
    when(REDIRECT_WEBHOOK_API.redirect(anyString(), anyString(), anyString(), any()))
        .thenReturn(Observable.just(Response.success(redirectionResponse)));
    var request = buildRequest(missingOrderId);
    try (var response = webhookApiClient.xsollaWebhook(AUTH, API_KEY, request)) {
      assertThat(response.getStatus()).isEqualTo(200);
      assertThat(response.readEntity(String.class)).isEqualTo(redirectionResponse);
    }
    verify(REDIRECT_WEBHOOK_API)
        .redirect(eq(AUTH), eq(MediaType.APPLICATION_JSON), anyString(), any());
  }

  @Test
  public void testChargebackIsSaved() {
    int orderId = 456001;
    String paymentId = "XS001";
    var request = buildRequest(orderId);
    try (var response = webhookApiClient.xsollaWebhook(AUTH, API_KEY, request)) {
      assertThat(response.getStatus()).isEqualTo(200);
    }
    var chargebacks = ChargebackRetriever.retrieveChargebacksByPaymentId(paymentId);
    assertThat(chargebacks).hasSize(1);
    var chargeback = chargebacks.getFirst();
    assertThat(chargeback.getAmount().getNumberStripped()).isEqualTo(AMOUNT);
    assertThat(chargeback.getAmount().getCurrency().getCurrencyCode()).isEqualTo(USD);
    assertThat(chargeback.getChargebackReason()).isEqualTo(CHARGEBACK_REASON);

    var payment = PaymentRetriever.retrievePaymentById(paymentId).orElseThrow();
    assertThat(payment.getReversedAt()).isNotNull();
  }

  @Test
  public void testSeveralChargebacksAreCreated() {
    int orderId = 456002;
    String paymentId = "XS002";

    var request1 = buildRequest(orderId);
    try (var response = webhookApiClient.xsollaWebhook(AUTH, API_KEY, request1)) {
      assertThat(response.getStatus()).isEqualTo(200);
    }
    assertThat(ChargebackRetriever.retrieveChargebacksByPaymentId(paymentId)).hasSize(1);
    var payment = PaymentRetriever.retrievePaymentById(paymentId).orElseThrow();
    assertThat(payment.getReversedAt()).isNotNull();

    var request2 = buildRequest(orderId);
    try (var response = webhookApiClient.xsollaWebhook(AUTH, API_KEY, request2)) {
      assertThat(response.getStatus()).isEqualTo(200);
    }
    assertThat(ChargebackRetriever.retrieveChargebacksByPaymentId(paymentId)).hasSize(2);
  }

  private String buildRequest(int orderId) {
    var refund =
        new Refund.Builder()
            .setCode(XsollaRefundCode.CHARGEBACK)
            .setReason(CHARGEBACK_REASON)
            .setAuthor("")
            .build();
    var paymentDetails =
        new PaymentDetails(
            XSOLLA_AMOUNT, null, null, null, null, null, null, null, "0", null, null);
    var request =
        new XsollaRefundWebhookEvent.Builder()
            .setEventName(REFUND_EVENT)
            .setTransaction(new Transaction(orderId, null, null, 0, 0, null, 0))
            .setRefund(refund)
            .setPaymentDetails(paymentDetails)
            .build();
    try {
      return JacksonMapper.MAPPER.writeValueAsString(request);
    } catch (JsonProcessingException e) {
      throw new IllegalStateException("Couldn't serialize request", e);
    }
  }
}
