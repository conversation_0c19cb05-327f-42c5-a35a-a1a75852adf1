package com.scopely.paymentgateway.e2e.wiring.dagger.web;

import com.scopely.paymentgateway.analytics.events.TitanEventService;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eCoreModule;
import com.scopely.paymentgateway.web.module.PaymentGatewayWebModule;
import com.scopely.paymentgateway.web.service.PaymentGatewayWebService;
import com.scopely.proteus.core.service.ProteusService;
import com.scopely.proteus.monitoring.StatsDHeartbeater;
import com.scopely.proteus.server.BaseHttpServiceSettings;
import com.scopely.proteus.server.providers.CorsInterceptor;
import com.scopely.proteus.server.providers.GzipFeature;
import com.scopely.proteus.server.providers.JwtClaimsFilter;
import com.scopely.proteus.server.providers.KeepAliveDisablingFilter;
import com.scopely.proteus.service.ProteusServiceConstants;
import dagger.Module;
import dagger.Provides;
import java.util.List;
import java.util.Set;
import javax.inject.Named;
import javax.inject.Singleton;
import org.jboss.resteasy.plugins.interceptors.AcceptEncodingGZIPFilter;
import org.jboss.resteasy.plugins.interceptors.GZIPDecodingInterceptor;
import org.jboss.resteasy.plugins.interceptors.GZIPEncodingInterceptor;

@Module(includes = {E2eCoreModule.class})
public class E2ePaymentGatewayWebModule {
  public static final int PORT = 7575;

  @Provides
  @Named(ProteusServiceConstants.SERVICE_NAME)
  String serviceName() {
    return "payment-gateway-web";
  }

  @Provides
  @Singleton
  ProteusService webService(
      TitanEventService titanEventService,
      StatsDHeartbeater statsDHeartbeater,
      E2ePaymentGatewayWebComponent component) {

    var settings =
        new BaseHttpServiceSettings.Builder(PORT, 10)
            .withResources(PaymentGatewayWebModule.getControllers())
            .withProviders(getE2eProviders())
            .build();

    return new E2ePaymentGatewayWebService(
        settings, titanEventService, statsDHeartbeater, component);
  }

  private static Set<Class<?>> getE2eProviders() {
    Set<Class<?>> providers = PaymentGatewayWebService.getProviders();
    List.of(
            CorsInterceptor.class,
            KeepAliveDisablingFilter.class,
            JwtClaimsFilter.class,
            GZIPDecodingInterceptor.class,
            AcceptEncodingGZIPFilter.class,
            GZIPEncodingInterceptor.class,
            GzipFeature.class)
        .forEach(providers::remove);
    providers.add(E2eJwtClaimsFilter.class);
    return providers;
  }
}
