package com.scopely.paymentgateway.e2e.wiring.dagger.webhook;

import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eProteusModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eProteusNextModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule;
import com.scopely.paymentgateway.modules.ActionsModule;
import com.scopely.paymentgateway.modules.RepositoriesModule;
import com.scopely.paymentgateway.webhook.digitalriver.PaymentGatewayWebhookDigitalRiverComponent;
import com.scopely.proteus.monitoring.MonitoringModule;
import dagger.Component;
import javax.inject.Singleton;

@Singleton
@Component(
    modules = {
      E2eWebhookDigitalRiverModule.class,
      E2eProteusModule.class,
      E2eProteusNextModule.class,
      MonitoringModule.class,
      ActionsModule.class,
      E2eServicesModule.class,
      RepositoriesModule.class
    })
public interface E2ePaymentGatewayWebhookDigitalRiverComponent
    extends PaymentGatewayWebhookDigitalRiverComponent {

  static E2ePaymentGatewayWebhookDigitalRiverComponent create() {
    return DaggerE2ePaymentGatewayWebhookDigitalRiverComponent.create();
  }
}
