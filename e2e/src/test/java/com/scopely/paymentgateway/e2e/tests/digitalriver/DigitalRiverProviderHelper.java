package com.scopely.paymentgateway.e2e.tests.digitalriver;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ENGLISH;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.US;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USD;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.US_IP;

import com.scopely.paymentgateway.model.dto.createpayment.CreatePaymentRequestDTO;
import com.scopely.paymentgateway.model.dto.createpayment.CreatePaymentResponseDTO;
import com.scopely.paymentgateway.model.dto.createpayment.ItemDTO;
import com.scopely.paymentgateway.model.dto.createpayment.PropertiesDTO;
import com.scopely.paymentgateway.model.dto.createpayment.UserDTO;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.model.payment.PlatformType;
import com.scopely.paymentgateway.model.payment.PricingMode;
import com.scopely.paymentgateway.model.payment.ProviderStatus;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.CheckoutRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.CheckoutResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.CreateCustomerResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.GetCustomerResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.Payment;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.PurchaseLocation;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.Session;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.SourceResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.UpdateCheckoutRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.order.FraudStateTransitions;
import com.scopely.paymentgateway.providers.digitalriver.model.order.OrderResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.order.StateTransitions;
import com.scopely.paymentgateway.providers.digitalriver.model.taxes.DigitalRiverCountryConversionResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.tokenization.Address;
import com.scopely.paymentgateway.providers.digitalriver.model.tokenization.Owner;
import com.scopely.paymentgateway.providers.digitalriver.model.transaction.SalesTransactionType;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookRequest;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.body.DigitalRiverWebhookOrderRequestBody;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.body.DigitalRiverWebhookSalesTransactionRequestBody;
import com.scopely.proteus.util.JacksonMapper;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public final class DigitalRiverProviderHelper {
  public static final StateTransitions STATE_TRANSITIONS =
      new StateTransitions(
          "CREATED",
          "FULFILLED",
          "ACCEPTED",
          "COMPLETE",
          "PENDING",
          "REVIEW",
          "DISPUTE",
          "CANCELLED",
          "BLOCKED",
          "RETURNED");
  public static final FraudStateTransitions FRAUD_STATE_TRANSITIONS =
      new FraudStateTransitions("PASSED", "BLOCKED", "REVIEW");

  private DigitalRiverProviderHelper() {}

  public static DigitalRiverCountryConversionResponse buildCountryConversionResponse(
      String country, String currency, BigDecimal conversionFactor, BigDecimal exchangeRate) {
    return new DigitalRiverCountryConversionResponse(
        country,
        currency,
        false,
        false,
        false,
        false,
        new PaymentBigDecimal(conversionFactor),
        new PaymentBigDecimal(exchangeRate));
  }

  public static CreateCustomerResponse buildCreateCustomerResponse() {
    return new CreateCustomerResponse(null, null, null, false, false, null, null, false);
  }

  public static GetCustomerResponse buildGetCustomerResponse(UserDTO user) {
    return new GetCustomerResponse(
        UUID.randomUUID().toString(),
        null,
        null,
        user.email(),
        false,
        false,
        null,
        null,
        Map.of("firstName", user.firstName(), "lastName", user.lastName()),
        false,
        null);
  }

  public static boolean verifyCheckoutRequest(
      CheckoutRequest checkoutRequest,
      String apiKey,
      String itemName,
      BigDecimal price,
      String currency,
      int quantity,
      CreatePaymentResponseDTO paymentGatewayResponse) {
    if (checkoutRequest.items().size() != 1) {
      return false;
    }
    var requestItem = checkoutRequest.items().getFirst();
    return checkoutRequest.currency().equals(currency)
        && checkoutRequest.applicationId().equals(apiKey)
        && requestItem.productDetails().name().equals(itemName)
        && requestItem.price().equals(price)
        && requestItem.quantity().equals(quantity)
        && checkoutRequest.upstreamId().equals(paymentGatewayResponse.paymentId())
        && checkoutRequest.metadata().userId().equals(paymentGatewayResponse.userId());
  }

  public static boolean verifyUpdateCheckoutRequest(UpdateCheckoutRequest request, UserDTO user) {
    return request.email().equals(user.email());
  }

  public static CheckoutResponse buildCheckoutResponse(
      PaymentBigDecimal price, String country, String currency) {
    return new CheckoutResponse(
        UUID.randomUUID().toString(),
        null,
        null,
        currency,
        null,
        price,
        price,
        null,
        new PaymentBigDecimal(BigDecimal.ZERO),
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "*******",
        null,
        new PurchaseLocation(country, null, null),
        null,
        null,
        null,
        new Payment(new Session(UUID.randomUUID().toString(), null, null, null, null)),
        null);
  }

  public static SourceResponse buildSourceResponse(
      UserDTO user, String country, String postalCode) {
    var address = new Address(null, null, null, postalCode, null, country);
    var owner = new Owner(null, null, user.email(), address, null);
    return new SourceResponse(
        UUID.randomUUID().toString(), null, null, null, null, false, owner, "chargeable");
  }

  public static OrderResponse buildOrderResponse(
      PaymentBigDecimal price, String currency, ProviderStatus status) {
    return new OrderResponse(
        UUID.randomUUID().toString(),
        null,
        currency,
        null,
        null,
        price,
        price,
        null,
        new PaymentBigDecimal(BigDecimal.ZERO),
        null,
        null,
        null,
        null,
        List.of(),
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        status.getDescription(),
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null);
  }

  public static DigitalRiverWebhookOrderRequestBody buildWebhookOrderRequest(
      String orderId, BigDecimal amount, String currency, String state) {
    return new DigitalRiverWebhookOrderRequestBody(
        orderId,
        null,
        null,
        currency,
        null,
        null,
        null,
        null,
        new PaymentBigDecimal(amount),
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        state,
        STATE_TRANSITIONS,
        "in_review",
        FRAUD_STATE_TRANSITIONS,
        null,
        null,
        null,
        new PaymentBigDecimal(amount),
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null);
  }

  public static DigitalRiverWebhookSalesTransactionRequestBody buildWebhookTransactionRequest(
      String chargebackId,
      String orderId,
      BigDecimal amount,
      String currency,
      SalesTransactionType type) {
    return new DigitalRiverWebhookSalesTransactionRequestBody(
        chargebackId,
        null,
        null,
        null,
        null,
        currency,
        new PaymentBigDecimal(amount),
        type,
        orderId,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        true,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null);
  }

  public static DigitalRiverWebhookRequest buildDisputeRequest(
      DigitalRiverWebhookEvent type, String orderId, BigDecimal amount, String state) {
    var requestBody = buildWebhookOrderRequest(orderId, amount, USD, state);
    return new DigitalRiverWebhookRequest.Builder()
        .setId(UUID.randomUUID().toString())
        .setType(type)
        .setData(JacksonMapper.MAPPER.valueToTree(Map.of("object", requestBody)))
        .setLiveMode("")
        .setCreatedTime("")
        .build();
  }

  public static CreatePaymentRequestDTO createNewPaymentRequestDTO(
      ItemDTO item, String trackingId, UserDTO userDTO, String externalId) {
    var pricingMode = item.price() == null ? PricingMode.SKU : PricingMode.EXPLICIT;
    return new CreatePaymentRequestDTO(
        item,
        new PropertiesDTO(true, userDTO, US_IP, pricingMode, US, ENGLISH),
        UUID.randomUUID().toString(),
        trackingId,
        externalId,
        PlatformType.WEB);
  }
}
