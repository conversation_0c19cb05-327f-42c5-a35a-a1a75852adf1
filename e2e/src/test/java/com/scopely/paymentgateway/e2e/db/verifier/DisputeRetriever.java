package com.scopely.paymentgateway.e2e.db.verifier;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBQueryExpression;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eDynamoModule;
import com.scopely.paymentgateway.repositories.daos.DisputeDAO;
import com.scopely.paymentgateway.repositories.daos.ReversalDAO;
import com.scopely.paymentgateway.utils.DynamoDBQueryHelper;
import java.util.Optional;

public class DisputeRetriever {
  private static final String DISPUTE_HEADER_PREFIX = "DISPUTE#";

  public static Optional<DisputeDAO> retrieveDisputeById(String disputeId) {
    var dynamoFilter = new DynamoDBQueryHelper();
    dynamoFilter.addKeyCondition(ReversalDAO.RANGE_KEY, DISPUTE_HEADER_PREFIX + disputeId);
    var query =
        new DynamoDBQueryExpression<DisputeDAO>()
            .withIndexName(ReversalDAO.HEADER_INDEX_NAME)
            .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
            .withFilterExpression(dynamoFilter.getFilterExpressionChain())
            .withExpressionAttributeValues(dynamoFilter.getAttributeValues())
            .withConsistentRead(false);
    return E2eDynamoModule.jsonDynamoMapper.queryAll(DisputeDAO.class, query).stream().findAny();
  }
}
