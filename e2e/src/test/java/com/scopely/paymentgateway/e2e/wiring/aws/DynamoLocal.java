package com.scopely.paymentgateway.e2e.wiring.aws;

import static com.scopely.paymentgateway.modules.CoreModule.US_EAST_REGION;

import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBClient;
import com.amazonaws.services.dynamodbv2.local.server.DynamoDBProxyServer;
import com.amazonaws.services.dynamodbv2.local.server.LocalDynamoDBRequestHandler;
import com.amazonaws.services.dynamodbv2.local.server.LocalDynamoDBServerHandler;
import java.net.URI;
import java.security.SecureRandom;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

public final class DynamoLocal {
  private static final SecureRandom SECURE_RANDOM = new SecureRandom();

  private final int port;
  private final DynamoDBProxyServer dynamoServer;

  public DynamoLocal() {
    this(SECURE_RANDOM.nextInt(3000) + 6000);
  }

  public DynamoLocal(int port) {
    this.port = port;

    LocalDynamoDBRequestHandler primaryHandler =
        new LocalDynamoDBRequestHandler(0, true, null, false, false);
    dynamoServer =
        new DynamoDBProxyServer(this.port, new LocalDynamoDBServerHandler(primaryHandler, "*"));
  }

  public AmazonDynamoDBClient buildDynamoClient() {
    AmazonDynamoDBClient dynamoClient =
        new AmazonDynamoDBClient(new DefaultAWSCredentialsProviderChain());
    dynamoClient.setEndpoint("http://localhost:" + port);
    return dynamoClient;
  }

  public DynamoDbClient buildDynamoClientV2() {
    return DynamoDbClient.builder()
        .endpointOverride(URI.create("http://localhost:" + this.port))
        .region(US_EAST_REGION)
        .credentialsProvider(
            StaticCredentialsProvider.create(
                AwsBasicCredentials.create(
                    System.getProperty("aws.accessKeyId"), System.getProperty("aws.secretKey"))))
        .build();
  }

  public void start() throws Exception {
    dynamoServer.start();
  }

  public void stop() throws Exception {
    dynamoServer.stop();
  }
}
