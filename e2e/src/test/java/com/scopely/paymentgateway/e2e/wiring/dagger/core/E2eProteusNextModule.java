package com.scopely.paymentgateway.e2e.wiring.dagger.core;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.scopely.proteus.config.ExecutionEnvironment;
import com.scopely.proteus.core.config.Config;
import com.scopely.proteus.infrastructure.LifecycleEvent;
import com.scopely.proteus.monitoring.MetricsRegistry;
import com.scopely.proteus.monitoring.MonitoringProvider;
import com.scopely.proteus.monitoring.Tracer;
import dagger.Module;
import dagger.Provides;
import java.util.Optional;
import java.util.function.Supplier;
import javax.inject.Singleton;
import rx.subjects.PublishSubject;
import rx.subjects.Subject;
import software.amazon.awssdk.services.ssm.SsmClient;

@Module
public class E2eProteusNextModule {

  @Provides
  @Singleton
  MonitoringProvider monitoringProvider() {
    return MonitoringProvider.load();
  }

  @Provides
  @Singleton
  MetricsRegistry metricsRegistry(MonitoringProvider monitoringProvider) {
    return monitoringProvider.createDefaultMetricsRegistry();
  }

  @Provides
  @Singleton
  Tracer tracer(MonitoringProvider monitoringProvider) {
    return monitoringProvider.createTracer();
  }

  @Provides
  @Singleton
  ExecutionEnvironment executionEnvironment() {
    ExecutionEnvironment executionEnvironment = mock(ExecutionEnvironment.class);
    when(executionEnvironment.getAffinity()).thenReturn(Optional.empty());
    return executionEnvironment;
  }

  @Provides
  @Singleton
  Supplier<ExecutionEnvironment> executionEnvironmentSupplier(ExecutionEnvironment executionEnv) {
    return () -> executionEnv;
  }

  @Provides
  @Singleton
  Subject<LifecycleEvent, LifecycleEvent> lifecycleObservable() {
    return PublishSubject.create();
  }

  @Provides
  @Singleton
  SsmClient ssmClient() {
    return SsmClient.create();
  }

  @Provides
  @Singleton
  Config config(ExecutionEnvironment executionEnvironment) {
    return new Config();
  }
}
