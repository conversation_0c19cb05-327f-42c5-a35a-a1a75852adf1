package com.scopely.paymentgateway.e2e.db.loader;

import com.fasterxml.jackson.core.type.TypeReference;
import com.scopely.paymentgateway.e2e.db.DataLoader;
import com.scopely.paymentgateway.repositories.daos.PriceLocalizationDAO;
import com.scopely.proteus.dynamodb.DynamoDbMappedTable;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import com.scopely.proteus.util.JacksonMapper;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import software.amazon.awssdk.enhanced.dynamodb.model.PutItemEnhancedRequest;
import software.amazon.awssdk.services.dynamodb.model.AttributeDefinition;
import software.amazon.awssdk.services.dynamodb.model.CreateTableRequest;
import software.amazon.awssdk.services.dynamodb.model.KeySchemaElement;
import software.amazon.awssdk.services.dynamodb.model.KeyType;
import software.amazon.awssdk.services.dynamodb.model.ScalarAttributeType;

public class PriceLocalizationDataLoader implements DataLoader<PriceLocalizationDAO> {
  @Override
  public Collection<PriceLocalizationDAO> generateData() throws IOException {
    return readDataFromFile().stream().toList();
  }

  private List<PriceLocalizationDAO> readDataFromFile() throws IOException {
    try (InputStream is = getClass().getResourceAsStream("localizedPrices.json")) {
      return JacksonMapper.MAPPER.readValue(is, new TypeReference<>() {});
    }
  }

  @Override
  public com.amazonaws.services.dynamodbv2.model.CreateTableRequest getCreateTableRequestV1() {
    return null;
  }

  @Override
  public CreateTableRequest getCreateTableRequest() {
    return CreateTableRequest.builder()
        .tableName(PriceLocalizationDAO.TABLE)
        .attributeDefinitions(
            AttributeDefinition.builder()
                .attributeName(PriceLocalizationDAO.HASH_KEY)
                .attributeType(ScalarAttributeType.S)
                .build(),
            AttributeDefinition.builder()
                .attributeName(PriceLocalizationDAO.RANGE_KEY)
                .attributeType(ScalarAttributeType.S)
                .build())
        .keySchema(
            KeySchemaElement.builder()
                .attributeName(PriceLocalizationDAO.HASH_KEY)
                .keyType(KeyType.HASH)
                .build(),
            KeySchemaElement.builder()
                .attributeName(PriceLocalizationDAO.RANGE_KEY)
                .keyType(KeyType.RANGE)
                .build())
        .build();
  }

  @Override
  public boolean isV1() {
    return false;
  }

  @Override
  public int saveItems(DynamoDbMapper dynamoDbMapper) throws IOException {
    Collection<PriceLocalizationDAO> items = generateData();
    DynamoDbMappedTable<PriceLocalizationDAO> dynamoDbMappedTable =
        dynamoDbMapper.getTable(PriceLocalizationDAO.class);

    items.forEach(
        item ->
            dynamoDbMappedTable.putItem(
                PutItemEnhancedRequest.builder(PriceLocalizationDAO.class).item(item).build()));

    return items.size();
  }
}
