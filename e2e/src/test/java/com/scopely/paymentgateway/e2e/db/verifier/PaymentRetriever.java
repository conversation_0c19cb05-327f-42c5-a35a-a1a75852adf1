package com.scopely.paymentgateway.e2e.db.verifier;

import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eDynamoModule;
import com.scopely.paymentgateway.repositories.daos.PaymentDAO;
import java.util.Optional;

public class PaymentRetriever {

  public static Optional<PaymentDAO> retrievePaymentById(String paymentId) {
    return E2eDynamoModule.jsonDynamoMapper.load(PaymentDAO.class, paymentId);
  }
}
