package com.scopely.paymentgateway.e2e.wiring.aws.sqs;

import java.util.Map;
import java.util.Optional;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

public class QueueMock {
  private final LinkedBlockingQueue<MessageMonitor> queue = new LinkedBlockingQueue<>();
  private final Map<String, MessageMonitor> inFlight = new ConcurrentHashMap<>();
  private final Queue<MessageMonitor> processed = new ConcurrentLinkedQueue<>();

  public void add(String message) {
    var messageMonitor = MessageMonitor.fromBody(message);
    queue.add(messageMonitor);
    processed.add(messageMonitor);
  }

  public Optional<MessageMonitor> receive() {
    MessageMonitor messageMonitor = null;
    try {
      messageMonitor = queue.poll(1, TimeUnit.SECONDS);
    } catch (InterruptedException e) {
      // Ignore
    }
    if (messageMonitor == null) {
      return Optional.empty();
    }
    inFlight.put(messageMonitor.receiptHandle(), messageMonitor);
    return Optional.of(messageMonitor);
  }

  public void delete(String receiptHandle) {
    var messageMonitor = inFlight.remove(receiptHandle);
    if (messageMonitor != null) {
      messageMonitor.notifyMessageConsumed();
    }
  }

  public Optional<MessageMonitor> getNextProcessedMessage() {
    return Optional.ofNullable(processed.poll());
  }
}
