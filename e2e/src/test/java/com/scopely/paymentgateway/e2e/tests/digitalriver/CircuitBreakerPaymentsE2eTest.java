package com.scopely.paymentgateway.e2e.tests.digitalriver;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.API_KEY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.CONTEXT_PROPERTIES;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ITEM_EXPLICIT_PRICES;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.US;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USD;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.configurePlayerProfileProvider;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildCheckoutResponse;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildCountryConversionResponse;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildCreateCustomerResponse;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.createNewPaymentRequestDTO;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.AB_TEST_CONFIG_MAPPING_API;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.DIGITAL_RIVER_API;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.FUNNEL_CIRCUIT_BREAKER_FACTORY;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.XSOLLA_API;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.scopely.circuitbreaker.CircuitBreakerState;
import com.scopely.paymentgateway.e2e.E2eTestLifecycle;
import com.scopely.paymentgateway.e2e.tests.TestDataHelper;
import com.scopely.paymentgateway.e2e.tests.xsolla.XsollaProviderHelper;
import com.scopely.paymentgateway.e2e.wiring.client.PaymentGatewayApiClientBuilder;
import com.scopely.paymentgateway.e2e.wiring.dagger.web.E2ePaymentGatewayWebModule;
import com.scopely.paymentgateway.model.dto.createpayment.CreatePaymentResponseDTO;
import com.scopely.paymentgateway.model.dto.createpayment.UserDTO;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.CheckoutRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.CreateCustomerRequest;
import com.scopely.paymentgateway.web.v1.controller.PaymentSecuredEndpoint;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(E2eTestLifecycle.class)
class CircuitBreakerPaymentsE2eTest {
  private PaymentSecuredEndpoint paymentApiClient;

  @BeforeEach
  public void setUp() {
    var port = E2ePaymentGatewayWebModule.PORT;
    paymentApiClient =
        new PaymentGatewayApiClientBuilder<>(PaymentSecuredEndpoint.class, port)
            .withClaims("*:payments:*")
            .build();
    reset(DIGITAL_RIVER_API, XSOLLA_API);
  }

  @Test
  void testCreateCheckoutCircuitBreaker() {
    testCircuitBreaker("dr-createCheckout");
  }

  @Test
  void testUpdateCheckoutCircuitBreaker() {
    testCircuitBreaker("dr-updateCheckout");
  }

  @Test
  void testGetCustomerCircuitBreaker() {
    testCircuitBreaker("dr-getCustomer");
  }

  @Test
  void testGetSourceCircuitBreaker() {
    testCircuitBreaker("dr-getSource");
  }

  @Test
  void testCreateOrderCircuitBreaker() {
    testCircuitBreaker("dr-createOrder");
  }

  @Test
  void testFulfillOrderCircuitBreaker() {
    testCircuitBreaker("dr-fulfillOrder");
  }

  private void testCircuitBreaker(String circuitBreakerName) {
    // Make successful checkout through DR to create circuit breaker for test
    var user = TestDataHelper.buildNewUser();
    var checkoutResponse = createSuccessfulCheckout(user);
    assertThat(checkoutResponse.provider())
        .isEqualTo(PaymentProviderIdentifier.DIGITAL_RIVER.name());

    // Verify that the checkout falls back to XSolla when the DR circuit breaker is open
    FUNNEL_CIRCUIT_BREAKER_FACTORY
        .getCircuitBreaker(circuitBreakerName)
        .transitionToState(CircuitBreakerState.OPEN);
    var xsollaCheckoutResponse = createCheckoutWithFallbackProvider(user);
    assertThat(xsollaCheckoutResponse.provider())
        .isEqualTo(PaymentProviderIdentifier.XSOLLA.name());

    // Verify that the checkout goes through DR again when the circuit breaker is closed
    FUNNEL_CIRCUIT_BREAKER_FACTORY
        .getCircuitBreaker(circuitBreakerName)
        .transitionToState(CircuitBreakerState.CLOSED);
    checkoutResponse = createSuccessfulCheckout(user);
    assertThat(checkoutResponse.provider())
        .isEqualTo(PaymentProviderIdentifier.DIGITAL_RIVER.name());

    verify(DIGITAL_RIVER_API, times(2)).createCheckout(anyString(), any(CheckoutRequest.class));
    verify(XSOLLA_API, times(1))
        .createCheckout(
            anyString(),
            anyInt(),
            any(com.scopely.paymentgateway.providers.xsolla.model.checkout.CheckoutRequest.class));
  }

  private CreatePaymentResponseDTO createSuccessfulCheckout(UserDTO user) {
    configurePlayerProfileProvider(API_KEY, user.userId(), PaymentProviderIdentifier.DIGITAL_RIVER);
    String trackingId = UUID.randomUUID().toString();
    var checkoutRequest = createNewPaymentRequestDTO(ITEM_EXPLICIT_PRICES, trackingId, user, null);
    var providerCheckoutResponse = buildCheckoutResponse(ITEM_EXPLICIT_PRICES.price(), US, USD);
    when(DIGITAL_RIVER_API.retrieveTaxesForSpecificCountryAndCurrency(anyString(), eq(US), eq(USD)))
        .thenReturn(buildCountryConversionResponse(US, USD, BigDecimal.ONE, BigDecimal.ONE));
    when(DIGITAL_RIVER_API.createCustomer(anyString(), any(CreateCustomerRequest.class)))
        .thenReturn(buildCreateCustomerResponse());
    when(DIGITAL_RIVER_API.createCheckout(anyString(), any(CheckoutRequest.class)))
        .thenReturn(providerCheckoutResponse);
    when(AB_TEST_CONFIG_MAPPING_API.findExperimentMappings(any(), any(), any()))
        .thenReturn(CompletableFuture.completedFuture(List.of()));
    return paymentApiClient.createNewPaymentEndpoint(API_KEY, CONTEXT_PROPERTIES, checkoutRequest);
  }

  private CreatePaymentResponseDTO createCheckoutWithFallbackProvider(UserDTO user) {
    var trackingId = UUID.randomUUID().toString();
    var failingCheckoutRequest =
        createNewPaymentRequestDTO(ITEM_EXPLICIT_PRICES, trackingId, user, null);
    var xsollaProviderResponse = XsollaProviderHelper.buildCheckoutResponse();
    when(XSOLLA_API.createCheckout(
            anyString(),
            anyInt(),
            any(com.scopely.paymentgateway.providers.xsolla.model.checkout.CheckoutRequest.class)))
        .thenReturn(xsollaProviderResponse);
    return paymentApiClient.createNewPaymentEndpoint(
        API_KEY, CONTEXT_PROPERTIES, failingCheckoutRequest);
  }
}
