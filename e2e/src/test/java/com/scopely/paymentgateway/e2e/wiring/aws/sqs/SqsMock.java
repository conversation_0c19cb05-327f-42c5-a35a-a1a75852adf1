package com.scopely.paymentgateway.e2e.wiring.aws.sqs;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import software.amazon.awssdk.services.sqs.SqsClient;

public class SqsMock {
  private final Map<String, QueueMock> queues = new ConcurrentHashMap<>();

  public SqsClient getAwsClientV2() {
    return AwsSqsClientV2Helper.getClient(this);
  }

  public QueueMock getQueue(String queueName) {
    return queues.computeIfAbsent(queueName, ignore -> new QueueMock());
  }
}
