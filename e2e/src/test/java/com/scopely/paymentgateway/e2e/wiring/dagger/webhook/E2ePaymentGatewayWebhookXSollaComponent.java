package com.scopely.paymentgateway.e2e.wiring.dagger.webhook;

import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eProteusModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eProteusNextModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule;
import com.scopely.paymentgateway.modules.ActionsModule;
import com.scopely.paymentgateway.modules.RepositoriesModule;
import com.scopely.paymentgateway.webhook.xsolla.PaymentGatewayWebhookXSollaComponent;
import com.scopely.proteus.monitoring.MonitoringModule;
import dagger.Component;
import javax.inject.Singleton;

@Singleton
@Component(
    modules = {
      E2eWebhookXSollaModule.class,
      E2eProteusModule.class,
      E2eProteusNextModule.class,
      MonitoringModule.class,
      ActionsModule.class,
      E2eServicesModule.class,
      RepositoriesModule.class
    })
public interface E2ePaymentGatewayWebhookXSollaComponent
    extends PaymentGatewayWebhookXSollaComponent {

  static E2ePaymentGatewayWebhookXSollaComponent create() {
    return DaggerE2ePaymentGatewayWebhookXSollaComponent.create();
  }
}
