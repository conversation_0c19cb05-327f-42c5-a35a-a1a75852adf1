package com.scopely.paymentgateway.e2e.tests.xsolla;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.API_KEY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.CONTEXT_PROPERTIES;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ENGLISH;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.EUR;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ITEM_EXPLICIT_PRICES;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ITEM_MULTI_CURRENCY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ITEM_SKU;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ITEM_WITH_QUANTITY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ITEM_ZERO_ENDING_PRICE;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.US;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USD;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USER_DR_BLOCKED;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USER_XSOLLA_1;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.US_IP;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.verifyTitanEventStreamEntry;
import static com.scopely.paymentgateway.e2e.tests.xsolla.XsollaProviderHelper.buildCheckoutResponse;
import static com.scopely.paymentgateway.e2e.tests.xsolla.XsollaProviderHelper.buildWebhookCompletedPaymentRequest;
import static com.scopely.paymentgateway.e2e.tests.xsolla.XsollaProviderHelper.verifyCheckoutRequest;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.DIGITAL_RIVER_API;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.XSOLLA_API;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.scopely.paymentgateway.analytics.events.EventAttributesConstants;
import com.scopely.paymentgateway.e2e.E2eTestLifecycle;
import com.scopely.paymentgateway.e2e.wiring.client.PaymentGatewayApiClientBuilder;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.web.E2ePaymentGatewayWebModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.webhook.E2eWebhookXSollaModule;
import com.scopely.paymentgateway.model.dto.createpayment.CreatePaymentRequestDTO;
import com.scopely.paymentgateway.model.dto.createpayment.ItemDTO;
import com.scopely.paymentgateway.model.dto.createpayment.PropertiesDTO;
import com.scopely.paymentgateway.model.dto.createpayment.UserDTO;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.model.payment.PaymentMethod;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PlatformType;
import com.scopely.paymentgateway.model.payment.PricingMode;
import com.scopely.paymentgateway.providers.xsolla.model.checkout.CheckoutRequest;
import com.scopely.paymentgateway.providers.xsolla.model.checkout.CheckoutResponse;
import com.scopely.paymentgateway.web.v1.controller.PaymentSecuredEndpoint;
import com.scopely.paymentgateway.web.v1.controller.PaymentWidgetEndpoint;
import com.scopely.paymentgateway.webhook.xsolla.v1.controller.WebhookXSollaEndpoint;
import com.scopely.titan.model.ClientEventDto;
import com.scopely.titan.model.EventPriority;
import com.scopely.titan.model.EventType;
import jakarta.ws.rs.ClientErrorException;
import java.math.BigDecimal;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(E2eTestLifecycle.class)
class CreditCardPaymentE2eTest {
  private static final String AUTH = "any-auth-for-webhook";
  private PaymentSecuredEndpoint paymentApiClient;
  private PaymentWidgetEndpoint widgetApiClient;
  private WebhookXSollaEndpoint webhookApiClient;

  @BeforeEach
  public void setUp() {
    var port = E2ePaymentGatewayWebModule.PORT;
    paymentApiClient =
        new PaymentGatewayApiClientBuilder<>(PaymentSecuredEndpoint.class, port)
            .withClaims("*:payments:*")
            .build();
    widgetApiClient =
        new PaymentGatewayApiClientBuilder<>(PaymentWidgetEndpoint.class, port)
            .withClaims("*:payments:*")
            .build();
    webhookApiClient =
        new PaymentGatewayApiClientBuilder<>(
                WebhookXSollaEndpoint.class, E2eWebhookXSollaModule.PORT)
            .build();
    reset(E2eServicesModule.TITAN_CLIENT, XSOLLA_API, DIGITAL_RIVER_API);
  }

  @Test
  void testSuccessfulPaymentWithExplicitPrices() {
    String trackingId = UUID.randomUUID().toString();
    var item = ITEM_EXPLICIT_PRICES;
    var providerCheckoutResponse = buildCheckoutResponse();
    String paymentId =
        testCreateNewPaymentStep(
            trackingId, item, item.localizedPrice().price(), EUR, providerCheckoutResponse);
    testWidgetRetrievesInfoStep(
        paymentId, trackingId, item, item.localizedPrice().price(), EUR, false);
    testWebhookReceivesCompletedPayment(paymentId, item.localizedPrice().price(), EUR);
    testClaimPaymentStep(paymentId, item, item.localizedPrice().price());
    verifyNoInteractions(DIGITAL_RIVER_API);
  }

  @Test
  void testSuccessfulPaymentWithMultiCurrency() {
    String trackingId = UUID.randomUUID().toString();
    var item = ITEM_MULTI_CURRENCY;
    var providerCheckoutResponse = buildCheckoutResponse();
    String paymentId =
        testCreateNewPaymentStep(trackingId, item, item.price(), USD, providerCheckoutResponse);
    testWidgetRetrievesInfoStep(paymentId, trackingId, item, item.price(), USD, true);
    testWebhookReceivesCompletedPayment(paymentId, item.price(), USD);
    testClaimPaymentStep(paymentId, item, item.price());
    verifyNoInteractions(DIGITAL_RIVER_API);
  }

  @Test
  void testSuccessfulPaymentWithLocalizedPrice() {
    String trackingId = UUID.randomUUID().toString();
    var item = ITEM_SKU;
    var itemAmount = new PaymentBigDecimal(new BigDecimal("1.23"));
    var providerCheckoutResponse = buildCheckoutResponse();
    String paymentId =
        testCreateNewPaymentStep(trackingId, item, itemAmount, USD, providerCheckoutResponse);
    testWidgetRetrievesInfoStep(paymentId, trackingId, item, itemAmount, USD, false);
    testWebhookReceivesCompletedPayment(paymentId, itemAmount, USD);
    testClaimPaymentStep(paymentId, item, itemAmount);
    verifyNoInteractions(DIGITAL_RIVER_API);
  }

  @Test
  void testRejectedPayment() {
    String trackingId = UUID.randomUUID().toString();
    var item = ITEM_EXPLICIT_PRICES;
    var price = item.localizedPrice().price();
    var checkoutRequest = createNewPaymentRequestDTO(trackingId, item, USER_DR_BLOCKED);
    var checkoutResponse =
        paymentApiClient.createNewPaymentEndpoint(API_KEY, CONTEXT_PROPERTIES, checkoutRequest);
    verifyNoInteractions(DIGITAL_RIVER_API);
    verifyNoInteractions(E2eServicesModule.XSOLLA_API);
    ClientEventDto event = verifyTitanEventStreamEntry(USER_DR_BLOCKED.userId());
    verifyBlockedUserEvent(event, trackingId);
    assertThrows(
        ClientErrorException.class,
        () ->
            testWidgetRetrievesInfoStep(
                checkoutResponse.paymentId(), trackingId, item, price, EUR, false));
  }

  @Test
  void testSuccessfulPaymentWithQuantity() {
    String trackingId = UUID.randomUUID().toString();
    var item = ITEM_WITH_QUANTITY;
    String paymentId =
        testCreateNewPaymentStep(
            trackingId, item, item.localizedPrice().price(), EUR, buildCheckoutResponse());
    testWidgetRetrievesInfoStep(
        paymentId, trackingId, item, item.localizedPrice().price(), EUR, false);
    var totalPrice =
        item.localizedPrice().price().bigDecimal().multiply(BigDecimal.valueOf(item.quantity()));
    testWebhookReceivesCompletedPayment(paymentId, new PaymentBigDecimal(totalPrice), EUR);
    testClaimPaymentStep(paymentId, item, new PaymentBigDecimal(totalPrice));
    verifyNoInteractions(DIGITAL_RIVER_API);
  }

  @Test
  void testSuccessfulPaymentWithPriceEndingInZero() {
    String trackingId = UUID.randomUUID().toString();
    var item = ITEM_ZERO_ENDING_PRICE;
    var providerCheckoutResponse = buildCheckoutResponse();
    String paymentId =
        testCreateNewPaymentStep(
            trackingId, item, item.localizedPrice().price(), EUR, providerCheckoutResponse);
    testWidgetRetrievesInfoStep(
        paymentId, trackingId, item, item.localizedPrice().price(), EUR, false);
    testWebhookReceivesCompletedPayment(paymentId, item.localizedPrice().price(), EUR);
    testClaimPaymentStep(paymentId, item, item.localizedPrice().price());
    verifyNoInteractions(DIGITAL_RIVER_API);
  }

  private void verifyBlockedUserEvent(ClientEventDto event, String trackingId) {
    assertThat(event.getName()).isEqualTo("playgami.tracking");
    assertThat(event.getEventType()).isEqualTo(EventType.GAME);
    assertThat(event.getPriority()).isEqualTo(EventPriority.MEDIUM);
    assertThat(event.getProperties())
        .containsEntry(EventAttributesConstants.TRACKING_ID, trackingId)
        .containsEntry(EventAttributesConstants.STEP_NAME, "blocked_user_checkout");
  }

  private String testCreateNewPaymentStep(
      String trackingId,
      ItemDTO item,
      PaymentBigDecimal price,
      String currency,
      CheckoutResponse providerResponse) {
    when(XSOLLA_API.createCheckout(anyString(), anyInt(), any(CheckoutRequest.class)))
        .thenReturn(providerResponse);
    var checkoutRequest = createNewPaymentRequestDTO(trackingId, item, USER_XSOLLA_1);
    var checkoutResponse =
        paymentApiClient.createNewPaymentEndpoint(API_KEY, CONTEXT_PROPERTIES, checkoutRequest);
    assertThat(checkoutResponse.provider()).isEqualTo(PaymentProviderIdentifier.XSOLLA.name());
    verify(XSOLLA_API)
        .createCheckout(
            anyString(),
            anyInt(),
            argThat(
                request ->
                    verifyCheckoutRequest(
                        request, USER_XSOLLA_1, item.quantity(), price, currency)));
    return checkoutResponse.paymentId();
  }

  private CreatePaymentRequestDTO createNewPaymentRequestDTO(
      String trackingId, ItemDTO item, UserDTO userDTO) {
    var pricingMode = item.price() == null ? PricingMode.SKU : PricingMode.EXPLICIT;
    return new CreatePaymentRequestDTO(
        item,
        new PropertiesDTO(true, userDTO, US_IP, pricingMode, US, ENGLISH),
        UUID.randomUUID().toString(),
        trackingId,
        null,
        PlatformType.WEB);
  }

  private void testWidgetRetrievesInfoStep(
      String paymentId,
      String trackingId,
      ItemDTO item,
      PaymentBigDecimal unitPrice,
      String currency,
      boolean taxIncluded) {
    var infoResponse = widgetApiClient.paymentInfoEndpointWithHeader(API_KEY, paymentId);
    var expectedPricingMode = item.price() == null ? PricingMode.SKU : PricingMode.EXPLICIT;
    assertThat(infoResponse.paymentId()).isEqualTo(paymentId);
    assertThat(infoResponse.apiKey()).isEqualTo(API_KEY);
    assertThat(infoResponse.provider().name()).isEqualTo(PaymentProviderIdentifier.XSOLLA.name());
    assertThat(infoResponse.checkout().currency()).isEqualTo(currency);
    assertThat(infoResponse.checkout().country()).isEqualTo(US);
    assertThat(infoResponse.checkout().totalAmount())
        .isEqualTo(unitPrice.bigDecimal().multiply(BigDecimal.valueOf(item.quantity())));
    assertThat(infoResponse.checkout().taxIncluded()).isEqualTo(taxIncluded);
    assertThat(infoResponse.checkout().pricingMode()).isEqualTo(expectedPricingMode);
    assertThat(infoResponse.item().sku()).isEqualTo(item.sku());
    assertThat(infoResponse.item().name()).isEqualTo(item.name());
    assertThat(infoResponse.item().quantity()).isEqualTo(item.quantity());
    assertThat(infoResponse.item().price().currency()).isEqualTo(currency);
    assertThat(infoResponse.item().price().amount()).isEqualTo(unitPrice.bigDecimal());
    assertThat(infoResponse.user()).isNull();
    assertThat(infoResponse.locale()).isEqualTo(ENGLISH);
    assertThat(infoResponse.sandbox()).isTrue();
    assertThat(infoResponse.trackingId()).isEqualTo(trackingId);
  }

  private void testWebhookReceivesCompletedPayment(
      String paymentId, PaymentBigDecimal price, String currency) {
    var request = buildWebhookCompletedPaymentRequest(paymentId, price, currency);
    try (var response = webhookApiClient.xsollaWebhook(AUTH, API_KEY, request)) {
      assertThat(response.getStatus()).isEqualTo(200);
    }
  }

  private void testClaimPaymentStep(String paymentId, ItemDTO item, PaymentBigDecimal totalPrice) {
    var claimResponse = paymentApiClient.claimPayment(API_KEY, paymentId);
    String currency = item.localizedPrice() != null ? item.localizedPrice().currency() : "USD";
    assertThat(claimResponse.paymentId()).isEqualTo(paymentId);
    assertThat(claimResponse.userId()).isEqualTo(USER_XSOLLA_1.userId());
    assertThat(claimResponse.claimedAt()).isNotNull();
    assertThat(claimResponse.item().sku()).isEqualTo(item.sku());
    assertThat(claimResponse.item().name()).isEqualTo(item.name());
    assertThat(claimResponse.item().quantity()).isEqualTo(item.quantity());
    if (item.price() != null) {
      assertThat(claimResponse.item().basePrice().amount()).isEqualTo(item.price().bigDecimal());
    }
    assertThat(claimResponse.item().basePrice().currency()).isEqualTo(USD);
    if (item.localizedPrice() != null) {
      assertThat(claimResponse.item().localPrice().amount())
          .isEqualTo(item.localizedPrice().price().bigDecimal());
    }
    assertThat(claimResponse.item().localPrice().currency()).isEqualTo(currency);
    assertThat(claimResponse.priceData().localPrice().totalAmount())
        .isEqualTo(totalPrice.bigDecimal());
    assertThat(claimResponse.priceData().localPrice().currency()).isEqualTo(currency);
    assertThat(claimResponse.priceData().taxIncluded()).isFalse();
    assertThat(claimResponse.paymentMethodUsed()).isEqualTo(PaymentMethod.CREDIT_CARD);
  }
}
