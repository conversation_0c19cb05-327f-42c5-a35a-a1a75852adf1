package com.scopely.paymentgateway.e2e.file;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

public class MultipartFormDataMock {

  public static InputStream createValidFileInputStream() {
    String validCsvFile =
        """
                   SKU,Country(Optional),Currency,Price
                   sku_1,,EUR,2.99
                   sku_2,,EUR,4.99
                   sku_3,,EUR,9.99
                   sku_1,CA,CAD,3.99
                   sku_2,CA,CAD,6.99
                   sku_3,CA,CAD,12.99
                   sku_1,FR,EUR,30.99
                   sku_2,FR,EUR,119.99
                """;
    return new ByteArrayInputStream(validCsvFile.getBytes(StandardCharsets.UTF_8));
  }

  public static InputStream createInvalidHeaderFileInputStream() {
    String csvFile =
        """
                  SKU, Currency, Price
                  sku_1,EUR,2.99
                  sku_2,EUR,4.99
                  sku_1,CAD,3.99
                  sku_2,CAD,6.99
                  sku_1,EUR,30.99
                  sku_2,EUR,119.99
              """;
    return new ByteArrayInputStream(csvFile.getBytes(StandardCharsets.UTF_8));
  }

  public static InputStream createInvalidFileWithMissingColumns() {
    String csvFile =
        """
                  SKU,Country(Optional),Currency,Price
                  sku_1,,EUR,2.99
                  sku_2,,EUR,4.99
                  sku_1,CAD,3.99
                  sku_2,CAD,6.99
                  sku_1,ES,EUR,30.99
                  sku_2,ES,EUR,119.99
              """;
    return new ByteArrayInputStream(csvFile.getBytes(StandardCharsets.UTF_8));
  }

  public static InputStream createInvalidFileWithEmptyRequiredColumns() {
    String csvFile =
        """
                  SKU,Country(Optional),Currency,Price
                  sku_1,,EUR,2.99
                  sku_2,,EUR,4.99
                  sku_1,CA,,3.99
                  sku_2,CA,,6.99
                  sku_1,ES,EUR,30.99
                  sku_2,ES,EUR,119.99
              """;
    return new ByteArrayInputStream(csvFile.getBytes(StandardCharsets.UTF_8));
  }

  public static InputStream createInvalidFileWithInvalidCountryCode() {
    String csvFile =
        """
                  SKU,Country(Optional),Currency,Price
                  sku_1,,EUR,2.99
                  sku_2,,EUR,4.99
                  sku_1,CA,CAD,3.99
                  sku_2,CA,CAD,6.99
                  sku_1,USA,USD,30.99
                  sku_2,USA,USD,119.99
              """;
    return new ByteArrayInputStream(csvFile.getBytes(StandardCharsets.UTF_8));
  }

  public static InputStream createInvalidFileWithInvalidCurrencyCode() {
    String csvFile =
        """
                  SKU,Country(Optional),Currency,Price
                  sku_1,,EUR,2.99
                  sku_2,,EUR,4.99
                  sku_1,CA,CAT,3.99
                  sku_2,CA,CAT,6.99
                  sku_1,ES,EUR,30.99
                  sku_2,ES,EUR,119.99
              """;
    return new ByteArrayInputStream(csvFile.getBytes(StandardCharsets.UTF_8));
  }

  public static InputStream createInvalidFileWithInvalidPrice() {
    String csvFile =
        """
                  SKU,Country(Optional),Currency,Price
                  sku_1,,EUR,2.99
                  sku_2,,EUR,4.99
                  sku_1,CA,CAD,3.99
                  sku_2,CA,CAD,119.999
                  sku_1,ES,EUR,-30.99
                  sku_2,ES,EUR,undefined
              """;
    return new ByteArrayInputStream(csvFile.getBytes(StandardCharsets.UTF_8));
  }

  public static InputStream createMissingDefaultSkuInputStream() {
    String csvFile =
        """
                  SKU,Country(Optional),Currency,Price
                  sku_1,,EUR,2.99
                  sku_2,,EUR,4.99
                  sku_1,,USD,2.99
                  sku_2,,USD,4.99
                  sku_1,CA,CAD,3.99
                  sku_2,CA,CAD,6.99
                  sku_3,CA,CAD,12.99
              """;
    return new ByteArrayInputStream(csvFile.getBytes(StandardCharsets.UTF_8));
  }

  public static InputStream createDefaultPriceNotConfiguredInputStream() {
    String csvFile =
        """
            SKU,Country(Optional),Currency,Price
            sku_1,CA,CAD,3.99
            sku_2,CA,CAD,6.99
            sku_3,CA,CAD,12.99
        """;
    return new ByteArrayInputStream(csvFile.getBytes(StandardCharsets.UTF_8));
  }

  public static InputStream createMultipleCurrenciesPerCountryInputStream() {
    String csvFile =
        """
                  SKU,Country(Optional),Currency,Price
                  sku_1,,EUR,2.99
                  sku_2,,EUR,4.99
                  sku_1,CA,CAD,3.99
                  sku_2,CA,USD,6.99
                  sku_1,FR,EUR,30.99
                  sku_2,FR,EUR,119.99
              """;
    return new ByteArrayInputStream(csvFile.getBytes(StandardCharsets.UTF_8));
  }

  public static InputStream createDuplicatedSkuDefinitionsInputStream() {
    String csvFile =
        """
                  SKU,Country(Optional),Currency,Price
                  sku_1,,EUR,2.99
                  sku_2,,EUR,4.99
                  sku_1,CA,CAD,3.99
                  sku_2,CA,CAD,6.99
                  sku_1,CA,USD,12.99
                  sku_2,CA,USD,25.99
                  sku_1,FR,EUR,30.99
                  sku_2,FR,EUR,119.99
              """;
    return new ByteArrayInputStream(csvFile.getBytes(StandardCharsets.UTF_8));
  }

  public static InputStream createMissingPriceSkuConfigurationInputStream() {
    String csvFile =
        """
                   SKU,Country(Optional),Currency,Price
                   sku_1,,EUR,2.99
                   sku_2,,EUR,4.99
                   sku_3,,EUR,9.99
                   sku_1,CA,CAD,3.99
                   sku_2,CA,CAD,6.99
                   sku_1,FR,EUR,30.99
                   sku_2,FR,EUR,119.99
                """;
    return new ByteArrayInputStream(csvFile.getBytes(StandardCharsets.UTF_8));
  }

  public static InputStream createEmptyFileInputStream() {
    return new ByteArrayInputStream("".getBytes(StandardCharsets.UTF_8));
  }

  public static InputStream createEmptyContentFileInputStream() {
    return new ByteArrayInputStream(" ".getBytes(StandardCharsets.UTF_8));
  }
}
