package com.scopely.paymentgateway.e2e.db.loader;

import com.amazonaws.services.dynamodbv2.model.AttributeDefinition;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.GlobalSecondaryIndex;
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement;
import com.amazonaws.services.dynamodbv2.model.KeyType;
import com.amazonaws.services.dynamodbv2.model.Projection;
import com.amazonaws.services.dynamodbv2.model.ProjectionType;
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.scopely.paymentgateway.e2e.db.DataLoader;
import com.scopely.paymentgateway.repositories.daos.PaymentDAO;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByExternalId;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByOrderId;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByUser;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import com.scopely.proteus.util.JacksonMapper;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.stream.IntStream;
import java.util.stream.Stream;

public class PaymentDataLoader implements DataLoader<PaymentDAO> {

  @Override
  public Collection<PaymentDAO> generateData() throws IOException {
    return readDataFromFile().stream().flatMap(this::generateVariants).toList();
  }

  @Override
  public int saveItems(DynamoDbMapper dynamoDbMapper) {
    return 0;
  }

  private List<PaymentDAO> readDataFromFile() throws IOException {
    try (InputStream is = getClass().getResourceAsStream("payments.json")) {
      return JacksonMapper.MAPPER.readValue(is, new TypeReference<>() {});
    }
  }

  private Stream<PaymentDAO> generateVariants(PaymentDAO payment) {
    var variants =
        IntStream.rangeClosed(1, 2).mapToObj(variantId -> generateVariant(payment, variantId));
    return Stream.concat(Stream.of(payment), variants);
  }

  private PaymentDAO generateVariant(PaymentDAO payment, int variantId) {
    return PaymentDAO.Builder.from(payment)
        .mapPaymentId(paymentId -> generateVariantId(paymentId, variantId))
        .mapOrderId(orderId -> generateVariantId(orderId, variantId))
        .build();
  }

  private String generateVariantId(String id, int variantId) {
    char[] chars = id.toCharArray();
    chars[chars.length - 2] = Character.forDigit(variantId, 10);
    return new String(chars);
  }

  @Override
  public CreateTableRequest getCreateTableRequestV1() {
    return new CreateTableRequest()
        .withTableName(PaymentDAO.TABLE)
        .withAttributeDefinitions(
            new AttributeDefinition(PaymentDAO.PAYMENT_ID, ScalarAttributeType.S),
            new AttributeDefinition(PaymentDAO.ORDER_ID, ScalarAttributeType.S),
            new AttributeDefinition(PaymentDAO.API_KEY_USER, ScalarAttributeType.S),
            new AttributeDefinition(PaymentDAO.EXTERNAL_ID, ScalarAttributeType.S),
            new AttributeDefinition(PaymentIndexByUser.GSI_RANGE_KEY, ScalarAttributeType.S))
        .withKeySchema(new KeySchemaElement(PaymentDAO.HASH_KEY, KeyType.HASH))
        .withGlobalSecondaryIndexes(
            new GlobalSecondaryIndex()
                .withIndexName(PaymentIndexByUser.GLOBAL_SECONDARY_INDEX_NAME)
                .withKeySchema(
                    new KeySchemaElement(PaymentIndexByUser.GSI_HASH_KEY, KeyType.HASH),
                    new KeySchemaElement(PaymentIndexByUser.GSI_RANGE_KEY, KeyType.RANGE))
                .withProjection(new Projection().withProjectionType(ProjectionType.ALL)),
            new GlobalSecondaryIndex()
                .withIndexName(PaymentIndexByOrderId.GLOBAL_SECONDARY_INDEX_NAME)
                .withKeySchema(
                    new KeySchemaElement(PaymentIndexByOrderId.GSI_HASH_KEY, KeyType.HASH))
                .withProjection(new Projection().withProjectionType(ProjectionType.KEYS_ONLY)),
            new GlobalSecondaryIndex()
                .withIndexName(PaymentIndexByExternalId.GLOBAL_SECONDARY_INDEX_NAME)
                .withKeySchema(
                    new KeySchemaElement(PaymentIndexByExternalId.GSI_HASH_KEY, KeyType.HASH),
                    new KeySchemaElement(PaymentIndexByExternalId.GSI_RANGE_KEY, KeyType.RANGE))
                .withProjection(new Projection().withProjectionType(ProjectionType.ALL)));
  }
}
