package com.scopely.paymentgateway.e2e.tests.digitalriver;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.API_KEY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USER_DR_3;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.mockSegmentationApi;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.verifyTitanEventStreamEntries;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.verifyUserBlockEvent;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildDisputeRequest;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eCoreModule.SQS;
import static com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent.DISPUTE_OPEN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.reset;

import com.scopely.paymentgateway.e2e.E2eTestLifecycle;
import com.scopely.paymentgateway.e2e.wiring.aws.sqs.QueueMock;
import com.scopely.paymentgateway.e2e.wiring.client.PaymentGatewayApiClientBuilder;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.web.E2ePaymentGatewayWebModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.webhook.E2eWebhookDigitalRiverModule;
import com.scopely.paymentgateway.exceptions.model.UserNotFoundException;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeEvent;
import com.scopely.paymentgateway.web.v1.controller.AdminUserEndpoint;
import com.scopely.paymentgateway.webhook.digitalriver.v1.controller.WebhookDigitalRiverEndpoint;
import com.scopely.titan.model.ClientEventDto;
import java.math.BigDecimal;
import java.time.Duration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(E2eTestLifecycle.class)
class UserAutoblockingE2eTest {
  private static final String DISPUTE_STATE = "dispute";
  private AdminUserEndpoint adminUserApiClient;
  private WebhookDigitalRiverEndpoint webhookApiClient;
  private QueueMock disputesQueue;

  @BeforeEach
  public void setUp() {
    adminUserApiClient =
        new PaymentGatewayApiClientBuilder<>(
                AdminUserEndpoint.class, E2ePaymentGatewayWebModule.PORT)
            .withClaims("*:payments:*")
            .build();
    webhookApiClient =
        new PaymentGatewayApiClientBuilder<>(
                WebhookDigitalRiverEndpoint.class, E2eWebhookDigitalRiverModule.PORT)
            .build();
    disputesQueue = SQS.getQueue("payment-gateway-disputes-processing-worker");
    reset(E2eServicesModule.TITAN_CLIENT);
  }

  @Test
  void testUserIsAutoblockedOnSecondDispute() throws UserNotFoundException, InterruptedException {
    mockSegmentationApi(API_KEY, USER_DR_3.userId(), segmentId -> false);
    assertThat(adminUserApiClient.getUser(API_KEY, USER_DR_3.userId()).blocked()).isFalse();

    webhookApiClient.digitalRiverWebhook(
        buildDisputeRequest(DISPUTE_OPEN, "123006", BigDecimal.ONE, DISPUTE_STATE));
    var message1 = disputesQueue.getNextProcessedMessage().orElseThrow();
    message1.waitForMessageToBeConsumed(Duration.ofSeconds(1));
    validateDisputeEvent(message1.deserialize(DisputeEvent.class), "DR006");
    assertThat(adminUserApiClient.getUser(API_KEY, USER_DR_3.userId()).blocked()).isFalse();

    webhookApiClient.digitalRiverWebhook(
        buildDisputeRequest(DISPUTE_OPEN, "123007", BigDecimal.ONE, DISPUTE_STATE));

    var message2 = disputesQueue.getNextProcessedMessage().orElseThrow();
    message2.waitForMessageToBeConsumed(Duration.ofSeconds(1));
    validateDisputeEvent(message2.deserialize(DisputeEvent.class), "DR007");
    assertThat(adminUserApiClient.getUser(API_KEY, USER_DR_3.userId()).blocked()).isTrue();
    ClientEventDto event =
        verifyTitanEventStreamEntries(USER_DR_3.userId())
            .filter(ev -> ev.getName().equals("playgami.payment_userblock"))
            .findFirst()
            .orElseThrow();
    verifyUserBlockEvent(event, PaymentProviderIdentifier.DIGITAL_RIVER, false);
  }

  private void validateDisputeEvent(DisputeEvent disputeEvent, String expectedPaymentId) {
    assertThat(disputeEvent.apiKey()).isEqualTo(API_KEY);
    assertThat(disputeEvent.userId()).isEqualTo(USER_DR_3.userId());
    assertThat(disputeEvent.paymentId()).isEqualTo(expectedPaymentId);
  }
}
