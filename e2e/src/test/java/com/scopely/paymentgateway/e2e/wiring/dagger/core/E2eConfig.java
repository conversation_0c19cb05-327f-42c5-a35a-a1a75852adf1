package com.scopely.paymentgateway.e2e.wiring.dagger.core;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.scopely.proteus.config.Config;
import com.scopely.proteus.config.ExecutionEnvironment;
import com.scopely.proteus.json.JsonProvider;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.Properties;
import java.util.function.Supplier;

public class E2eConfig {
  private final Properties properties;

  private E2eConfig(Properties properties) {
    this.properties = properties;
  }

  public static Config getConfig(ExecutionEnvironment executionEnvironment, String propertiesFile) {
    E2eConfig e2eConfig = new E2eConfig(readProperties(propertiesFile));
    Config config = mock(Config.class);
    when(config.getExecutionEnvironment()).thenReturn(executionEnvironment);
    when(config.get(anyString()))
        .thenAnswer(invocation -> e2eConfig.get(invocation.getArgument(0, String.class)));
    when(config.getString(anyString()))
        .thenAnswer(invocation -> e2eConfig.getString(invocation.getArgument(0, String.class)));
    when(config.getStringSupplier(anyString()))
        .thenAnswer(
            invocation -> e2eConfig.getStringSupplier(invocation.getArgument(0, String.class)));
    when(config.getList(anyString()))
        .thenAnswer(invocation -> e2eConfig.getList(invocation.getArgument(0, String.class)));
    when(config.getInt(anyString()))
        .thenAnswer(invocation -> e2eConfig.getInt(invocation.getArgument(0, String.class)));
    when(config.getLong(anyString()))
        .thenAnswer(invocation -> e2eConfig.getLong(invocation.getArgument(0, String.class)));
    when(config.getDouble(anyString()))
        .thenAnswer(invocation -> e2eConfig.getDouble(invocation.getArgument(0, String.class)));
    when(config.getBoolean(anyString()))
        .thenAnswer(invocation -> e2eConfig.getBoolean(invocation.getArgument(0, String.class)));
    return config;
  }

  private static Properties readProperties(String propertiesFile) {
    Properties properties = new Properties();
    try {
      properties.load(
          Thread.currentThread().getContextClassLoader().getResourceAsStream(propertiesFile));
    } catch (IOException e) {
      throw new IllegalArgumentException("Missing properties file: " + propertiesFile, e);
    }
    return properties;
  }

  Optional<String> get(String key) {
    return Optional.ofNullable(properties.getProperty(key));
  }

  String getString(String key) {
    return get(key).orElseThrow();
  }

  Supplier<String> getStringSupplier(String key) {
    return () -> getString(key);
  }

  List<String> getList(String key) {
    return get(key).map(this::parseJsonStringList).orElse(List.of());
  }

  private List<String> parseJsonStringList(String json) {
    try {
      return JsonProvider.defaultMapper().readValue(json, Config.STRING_LIST);
    } catch (JsonProcessingException e) {
      return List.of();
    }
  }

  Optional<Integer> getInt(String key) {
    return get(key).map(Integer::valueOf);
  }

  Optional<Long> getLong(String key) {
    return get(key).map(Long::valueOf);
  }

  Optional<Double> getDouble(String key) {
    return get(key).map(Double::valueOf);
  }

  Boolean getBoolean(String key) {
    return get(key).map(Boolean::valueOf).orElse(false);
  }
}
