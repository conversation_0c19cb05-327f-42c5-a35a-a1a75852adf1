package com.scopely.paymentgateway.e2e.wiring.dagger.web;

import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eProteusModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eProteusNextModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule;
import com.scopely.paymentgateway.modules.ActionsModule;
import com.scopely.paymentgateway.modules.RepositoriesModule;
import com.scopely.paymentgateway.web.module.PaymentGatewayWebComponent;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import com.scopely.proteus.monitoring.MonitoringModule;
import dagger.Component;
import javax.inject.Singleton;

@Singleton
@Component(
    modules = {
      E2ePaymentGatewayWebModule.class,
      E2eProteusModule.class,
      E2eProteusNextModule.class,
      MonitoringModule.class,
      RepositoriesModule.class,
      E2eServicesModule.class,
      ActionsModule.class
    })
public interface E2ePaymentGatewayWebComponent extends PaymentGatewayWebComponent {

  JsonDynamoMapper getJsonDynamoMapper();

  DynamoDbMapper getDynamoDbMapper();

  static E2ePaymentGatewayWebComponent create() {
    return DaggerE2ePaymentGatewayWebComponent.create();
  }
}
