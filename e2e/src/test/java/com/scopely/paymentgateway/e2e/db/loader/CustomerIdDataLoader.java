package com.scopely.paymentgateway.e2e.db.loader;

import com.amazonaws.services.dynamodbv2.model.AttributeDefinition;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement;
import com.amazonaws.services.dynamodbv2.model.KeyType;
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType;
import com.scopely.paymentgateway.e2e.db.DataLoader;
import com.scopely.paymentgateway.repositories.daos.CustomerIdDAO;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import java.util.Collection;
import java.util.List;

public class CustomerIdDataLoader implements DataLoader<CustomerIdDAO> {

  @Override
  public Collection<CustomerIdDAO> generateData() {
    return List.of();
  }

  @Override
  public int saveItems(DynamoDbMapper dynamoDbMapper) {
    return 0;
  }

  @Override
  public CreateTableRequest getCreateTableRequestV1() {
    return new CreateTableRequest()
        .withTableName(CustomerIdDAO.TABLE)
        .withAttributeDefinitions(
            new AttributeDefinition(CustomerIdDAO.HASH_KEY, ScalarAttributeType.S))
        .withKeySchema(new KeySchemaElement(CustomerIdDAO.HASH_KEY, KeyType.HASH));
  }
}
