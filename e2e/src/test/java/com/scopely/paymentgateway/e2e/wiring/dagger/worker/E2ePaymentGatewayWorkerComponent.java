package com.scopely.paymentgateway.e2e.wiring.dagger.worker;

import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eProteusModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eProteusNextModule;
import com.scopely.paymentgateway.modules.ActionsModule;
import com.scopely.paymentgateway.worker.PaymentGatewayWorkerComponent;
import com.scopely.proteus.monitoring.MonitoringModule;
import dagger.Component;
import javax.inject.Singleton;

@Singleton
@Component(
    modules = {
      E2eWorkerModule.class,
      E2eProteusModule.class,
      E2eProteusNextModule.class,
      MonitoringModule.class,
      ActionsModule.class
    })
public interface E2ePaymentGatewayWorkerComponent extends PaymentGatewayWorkerComponent {

  static E2ePaymentGatewayWorkerComponent create() {
    return DaggerE2ePaymentGatewayWorkerComponent.create();
  }
}
