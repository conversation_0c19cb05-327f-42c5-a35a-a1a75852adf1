package com.scopely.paymentgateway.e2e.tests.digitalriver.webhook;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.API_KEY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USD;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.verifyDisputeEvent;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.verifyTitanEventStreamEntry;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildDisputeRequest;
import static com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent.DISPUTE_OPEN;
import static com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent.DISPUTE_RESOLVED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verifyNoInteractions;

import com.scopely.paymentgateway.e2e.E2eTestLifecycle;
import com.scopely.paymentgateway.e2e.db.verifier.DisputeRetriever;
import com.scopely.paymentgateway.e2e.tests.TestDataHelper;
import com.scopely.paymentgateway.e2e.wiring.client.PaymentGatewayApiClientBuilder;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.webhook.E2eWebhookDigitalRiverModule;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeStatus;
import com.scopely.paymentgateway.webhook.digitalriver.v1.controller.WebhookDigitalRiverEndpoint;
import com.scopely.titan.model.ClientEventDto;
import jakarta.ws.rs.InternalServerErrorException;
import java.math.BigDecimal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(E2eTestLifecycle.class)
public class DisputeWebhooksE2eTest {
  private static final String USER_ID = TestDataHelper.USER_DR_1.userId();
  private static final String MISSING_ORDER_ID = "missing-order-id";
  private static final BigDecimal AMOUNT = new BigDecimal("2.99");
  private static final String DISPUTE_STATE = "dispute";
  private static final String SUPPRESSED_STATE = "suppressed";
  private WebhookDigitalRiverEndpoint webhookApiClient;

  @BeforeEach
  public void setUp() {
    webhookApiClient =
        new PaymentGatewayApiClientBuilder<>(
                WebhookDigitalRiverEndpoint.class, E2eWebhookDigitalRiverModule.PORT)
            .build();
    TestDataHelper.mockSegmentationApi(API_KEY, USER_ID, segmentId -> true);
    reset(E2eServicesModule.TITAN_CLIENT);
  }

  @Test
  public void testReceiveDisputeOpenPaymentNotFound() {
    var request = buildDisputeRequest(DISPUTE_OPEN, MISSING_ORDER_ID, AMOUNT, DISPUTE_STATE);
    assertThrows(
        InternalServerErrorException.class, () -> webhookApiClient.digitalRiverWebhook(request));
    verifyNoInteractions(E2eServicesModule.TITAN_CLIENT);
  }

  @Test
  public void testReceiveDisputeOpenIsSaved() {
    String orderId = "123001";
    String disputeId = orderId;
    var request = buildDisputeRequest(DISPUTE_OPEN, orderId, AMOUNT, DISPUTE_STATE);
    webhookApiClient.digitalRiverWebhook(request);
    var optDisputeDao = DisputeRetriever.retrieveDisputeById(disputeId);
    assertThat(optDisputeDao).isPresent();
    var disputeDao = optDisputeDao.orElseThrow();
    assertThat(disputeDao.getAmount().getNumberStripped()).isEqualTo(AMOUNT);
    assertThat(disputeDao.getAmount().getCurrency().getCurrencyCode()).isEqualTo(USD);
    assertThat(disputeDao.getStatus()).isEqualTo(DisputeStatus.NEW);
    assertThat(disputeDao.getDisputeReason()).isEqualTo("unknown");
    ClientEventDto event = verifyTitanEventStreamEntry(USER_ID);
    verifyDisputeEvent(event, disputeId, "new");
  }

  @Test
  public void testReceiveDisputeResolvedPaymentNotFound() {
    var request = buildDisputeRequest(DISPUTE_RESOLVED, MISSING_ORDER_ID, AMOUNT, SUPPRESSED_STATE);
    assertThrows(
        InternalServerErrorException.class, () -> webhookApiClient.digitalRiverWebhook(request));
    verifyNoInteractions(E2eServicesModule.TITAN_CLIENT);
  }

  @Test
  public void testReceiveDisputeResolvedIsSaved() {
    String orderId = "123002";
    String disputeId = orderId;
    var request = buildDisputeRequest(DISPUTE_RESOLVED, orderId, AMOUNT, SUPPRESSED_STATE);
    webhookApiClient.digitalRiverWebhook(request);
    var optDisputeDao = DisputeRetriever.retrieveDisputeById(disputeId);
    assertThat(optDisputeDao).isPresent();
    var disputeDao = optDisputeDao.orElseThrow();
    assertThat(disputeDao.getAmount().getNumberStripped()).isEqualTo(AMOUNT);
    assertThat(disputeDao.getAmount().getCurrency().getCurrencyCode()).isEqualTo(USD);
    assertThat(disputeDao.getStatus()).isEqualTo(DisputeStatus.LOST);
    assertThat(disputeDao.getDisputeReason()).isEqualTo("unknown");
    ClientEventDto event = verifyTitanEventStreamEntry(USER_ID);
    verifyDisputeEvent(event, disputeId, "lost");
  }

  @Test
  public void testReceiveDisputeResolvedIsUpdated() {
    String orderId = "123003";
    String disputeId = orderId;

    var request1 = buildDisputeRequest(DISPUTE_OPEN, orderId, AMOUNT, DISPUTE_STATE);
    webhookApiClient.digitalRiverWebhook(request1);
    assertThat(DisputeRetriever.retrieveDisputeById(disputeId).orElseThrow().getStatus())
        .isEqualTo(DisputeStatus.NEW);
    ClientEventDto event1 = verifyTitanEventStreamEntry(USER_ID);
    verifyDisputeEvent(event1, disputeId, "new");

    reset(E2eServicesModule.TITAN_CLIENT);
    var request2 = buildDisputeRequest(DISPUTE_RESOLVED, orderId, AMOUNT, "complete");
    webhookApiClient.digitalRiverWebhook(request2);
    var disputeDao = DisputeRetriever.retrieveDisputeById(disputeId).orElseThrow();
    assertThat(disputeDao.getAmount().getNumberStripped()).isEqualTo(AMOUNT);
    assertThat(disputeDao.getAmount().getCurrency().getCurrencyCode()).isEqualTo(USD);
    assertThat(disputeDao.getStatus()).isEqualTo(DisputeStatus.WON);
    assertThat(disputeDao.getDisputeReason()).isEqualTo("unknown");
    ClientEventDto event2 = verifyTitanEventStreamEntry(USER_ID);
    verifyDisputeEvent(event2, disputeId, "won");
  }
}
