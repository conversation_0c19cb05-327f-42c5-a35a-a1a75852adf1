package com.scopely.paymentgateway.e2e.wiring.aws.sqs;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.DeleteMessageRequest;
import software.amazon.awssdk.services.sqs.model.DeleteMessageResponse;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageResponse;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;
import software.amazon.awssdk.services.sqs.model.SendMessageResponse;

public class AwsSqsClientV2Helper {

  static SqsClient getClient(SqsMock sqsMock) {
    SqsClient client = mock(SqsClient.class);
    when(client.sendMessage(any(SendMessageRequest.class)))
        .thenAnswer(
            invocation ->
                sendMessage(sqsMock, invocation.getArgument(0, SendMessageRequest.class)));
    when(client.receiveMessage(any(ReceiveMessageRequest.class)))
        .thenAnswer(
            invocation ->
                receiveMessage(sqsMock, invocation.getArgument(0, ReceiveMessageRequest.class)));
    when(client.deleteMessage(any(DeleteMessageRequest.class)))
        .thenAnswer(
            invocation ->
                deleteMessage(sqsMock, invocation.getArgument(0, DeleteMessageRequest.class)));
    return client;
  }

  private static SendMessageResponse sendMessage(SqsMock sqsMock, SendMessageRequest request) {
    sqsMock.getQueue(request.queueUrl()).add(request.messageBody());
    return SendMessageResponse.builder().build();
  }

  private static ReceiveMessageResponse receiveMessage(
      SqsMock sqsMock, ReceiveMessageRequest request) {
    ReceiveMessageResponse.Builder response = ReceiveMessageResponse.builder();
    sqsMock
        .getQueue(request.queueUrl())
        .receive()
        .map(m -> Message.builder().body(m.body()).receiptHandle(m.receiptHandle()).build())
        .ifPresent(response::messages);
    return response.build();
  }

  private static DeleteMessageResponse deleteMessage(
      SqsMock sqsMock, DeleteMessageRequest request) {
    sqsMock.getQueue(request.queueUrl()).delete(request.receiptHandle());
    return DeleteMessageResponse.builder().build();
  }
}
