package com.scopely.paymentgateway.e2e.tests.digitalriver.webhook;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.API_KEY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USD;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.verifyDisputeEvent;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.verifyTitanEventStreamEntry;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildDisputeRequest;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildWebhookTransactionRequest;
import static com.scopely.paymentgateway.providers.digitalriver.model.transaction.SalesTransactionType.FRAUD_CHARGEBACK;
import static com.scopely.paymentgateway.providers.digitalriver.model.transaction.SalesTransactionType.NON_FRAUD_CHARGEBACK;
import static com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent.DISPUTE_RESOLVED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;

import com.scopely.paymentgateway.e2e.E2eTestLifecycle;
import com.scopely.paymentgateway.e2e.db.verifier.ChargebackRetriever;
import com.scopely.paymentgateway.e2e.db.verifier.DisputeRetriever;
import com.scopely.paymentgateway.e2e.db.verifier.PaymentRetriever;
import com.scopely.paymentgateway.e2e.tests.TestDataHelper;
import com.scopely.paymentgateway.e2e.wiring.client.PaymentGatewayApiClientBuilder;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.webhook.E2eWebhookDigitalRiverModule;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeStatus;
import com.scopely.paymentgateway.providers.digitalriver.model.transaction.SalesTransactionType;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookRequest;
import com.scopely.paymentgateway.webhook.digitalriver.v1.controller.WebhookDigitalRiverEndpoint;
import com.scopely.proteus.util.JacksonMapper;
import com.scopely.titan.model.ClientEventDto;
import jakarta.ws.rs.InternalServerErrorException;
import java.math.BigDecimal;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(E2eTestLifecycle.class)
public class ChargebackWebhookE2eTest {
  private static final String USER_ID = TestDataHelper.USER_DR_1.userId();
  private static final BigDecimal AMOUNT = new BigDecimal("2.99");
  private WebhookDigitalRiverEndpoint webhookApiClient;

  @BeforeEach
  public void setUp() {
    webhookApiClient =
        new PaymentGatewayApiClientBuilder<>(
                WebhookDigitalRiverEndpoint.class, E2eWebhookDigitalRiverModule.PORT)
            .build();
    TestDataHelper.mockSegmentationApi(API_KEY, USER_ID, segmentId -> true);
    reset(E2eServicesModule.TITAN_CLIENT);
  }

  @Test
  public void testReceivedChargebackPaymentNotFound() {
    String orderId = "missing-order-id";
    String chargebackId = "any-chargeback-id-1";
    var request = buildChargebackRequest(chargebackId, orderId, AMOUNT, FRAUD_CHARGEBACK);
    assertThrows(
        InternalServerErrorException.class, () -> webhookApiClient.digitalRiverWebhook(request));
    verifyNoInteractions(E2eServicesModule.TITAN_CLIENT);
  }

  @Test
  public void testReceivedChargebackIsSaved() {
    String orderId = "123011";
    String chargebackId = "any-chargeback-id-2";
    givenThatAPreviousDisputeExistsForPayment(orderId);

    var request = buildChargebackRequest(chargebackId, orderId, AMOUNT, FRAUD_CHARGEBACK);
    webhookApiClient.digitalRiverWebhook(request);

    var optChargebackDao = ChargebackRetriever.retrieveChargebackById(chargebackId);
    assertThat(optChargebackDao).isPresent();
    var chargebackDao = optChargebackDao.orElseThrow();
    assertThat(chargebackDao.getAmount().getNumberStripped()).isEqualTo(AMOUNT);
    assertThat(chargebackDao.getAmount().getCurrency().getCurrencyCode()).isEqualTo(USD);
    assertThat(chargebackDao.getChargebackReason()).isEqualTo("fraud_chargeback");

    var payment = PaymentRetriever.retrievePaymentById("DR011").orElseThrow();
    assertThat(payment.getReversedAt()).isNotNull();
  }

  @Test
  public void testReceivedChargebackAndDisputeAreSaved() {
    String orderId = "123014";
    String chargebackId = "any-chargeback-id-3";
    var request = buildChargebackRequest(chargebackId, orderId, AMOUNT, FRAUD_CHARGEBACK);
    webhookApiClient.digitalRiverWebhook(request);

    var optChargebackDao = ChargebackRetriever.retrieveChargebackById(chargebackId);
    assertThat(optChargebackDao).isPresent();
    var chargebackDao = optChargebackDao.orElseThrow();
    assertThat(chargebackDao.getAmount().getNumberStripped()).isEqualTo(AMOUNT);
    assertThat(chargebackDao.getAmount().getCurrency().getCurrencyCode()).isEqualTo(USD);
    assertThat(chargebackDao.getChargebackReason()).isEqualTo("fraud_chargeback");

    var payment = PaymentRetriever.retrievePaymentById("DR014").orElseThrow();
    assertThat(payment.getReversedAt()).isNotNull();

    String disputeId = orderId;
    var disputeDao = DisputeRetriever.retrieveDisputeById(disputeId).orElseThrow();
    assertThat(disputeDao.getAmount().getNumberStripped()).isEqualTo(AMOUNT);
    assertThat(disputeDao.getAmount().getCurrency().getCurrencyCode()).isEqualTo(USD);
    assertThat(disputeDao.getStatus()).isEqualTo(DisputeStatus.NON_CONTESTABLE);
    assertThat(disputeDao.getDisputeReason()).isEqualTo("unknown");
    ClientEventDto event = verifyTitanEventStreamEntry(USER_ID);
    verifyDisputeEvent(event, disputeId, "non-contestable");
  }

  @Test
  public void testReceivedChargebackIsUpdated() {
    String orderId = "123012";
    String chargebackId = "another-chargeback-id-4";
    givenThatAPreviousDisputeExistsForPayment(orderId);

    var request1 = buildChargebackRequest(chargebackId, orderId, AMOUNT, FRAUD_CHARGEBACK);
    webhookApiClient.digitalRiverWebhook(request1);
    var chargeback1 = ChargebackRetriever.retrieveChargebackById(chargebackId).orElseThrow();
    assertThat(chargeback1.getChargebackReason()).isEqualTo("fraud_chargeback");

    var payment = PaymentRetriever.retrievePaymentById("DR012").orElseThrow();
    assertThat(payment.getReversedAt()).isNotNull();

    var request2 = buildChargebackRequest(chargebackId, orderId, AMOUNT, NON_FRAUD_CHARGEBACK);
    webhookApiClient.digitalRiverWebhook(request2);
    var chargeback2 = ChargebackRetriever.retrieveChargebackById(chargebackId).orElseThrow();
    assertThat(chargeback2.getChargebackReason()).isEqualTo("non_fraud_chargeback");
  }

  @Test
  public void testReceivedChargebackIsUpdatedAndDisputeSaved() {
    String orderId = "123015";
    String chargebackId = "another-chargeback-id-5";

    var request1 = buildChargebackRequest(chargebackId, orderId, AMOUNT, FRAUD_CHARGEBACK);
    webhookApiClient.digitalRiverWebhook(request1);
    var chargeback1 = ChargebackRetriever.retrieveChargebackById(chargebackId).orElseThrow();
    assertThat(chargeback1.getChargebackReason()).isEqualTo("fraud_chargeback");

    var payment = PaymentRetriever.retrievePaymentById("DR015").orElseThrow();
    assertThat(payment.getReversedAt()).isNotNull();

    String disputeId = orderId;
    var disputeDao = DisputeRetriever.retrieveDisputeById(disputeId).orElseThrow();
    assertThat(disputeDao.getAmount().getNumberStripped()).isEqualTo(AMOUNT);
    assertThat(disputeDao.getAmount().getCurrency().getCurrencyCode()).isEqualTo(USD);
    assertThat(disputeDao.getStatus()).isEqualTo(DisputeStatus.NON_CONTESTABLE);
    assertThat(disputeDao.getDisputeReason()).isEqualTo("unknown");
    ClientEventDto event = verifyTitanEventStreamEntry(USER_ID);
    verifyDisputeEvent(event, disputeId, "non-contestable");

    var request2 = buildChargebackRequest(chargebackId, orderId, AMOUNT, NON_FRAUD_CHARGEBACK);
    webhookApiClient.digitalRiverWebhook(request2);
    var chargeback2 = ChargebackRetriever.retrieveChargebackById(chargebackId).orElseThrow();
    assertThat(chargeback2.getChargebackReason()).isEqualTo("non_fraud_chargeback");
    verifyNoMoreInteractions(E2eServicesModule.TITAN_CLIENT);
  }

  @Test
  public void testReceivedChargebackIsSavedWithPositiveAmount() {
    String orderId = "123013";
    String chargebackId = "third-chargeback-id-6";
    givenThatAPreviousDisputeExistsForPayment(orderId);
    var request = buildChargebackRequest(chargebackId, orderId, AMOUNT.negate(), FRAUD_CHARGEBACK);
    webhookApiClient.digitalRiverWebhook(request);
    var chargebackDao = ChargebackRetriever.retrieveChargebackById(chargebackId).orElseThrow();
    assertThat(chargebackDao.getAmount().getNumberStripped()).isEqualTo(AMOUNT);
  }

  private void givenThatAPreviousDisputeExistsForPayment(String orderId) {
    var disputeRequest = buildDisputeRequest(DISPUTE_RESOLVED, orderId, AMOUNT, "suppressed");
    webhookApiClient.digitalRiverWebhook(disputeRequest);
    reset(E2eServicesModule.TITAN_CLIENT);
  }

  private DigitalRiverWebhookRequest buildChargebackRequest(
      String chargebackId, String orderId, BigDecimal amount, SalesTransactionType chargebackType) {
    var requestBody =
        buildWebhookTransactionRequest(chargebackId, orderId, amount, USD, chargebackType);
    return new DigitalRiverWebhookRequest.Builder()
        .setId(UUID.randomUUID().toString())
        .setType(DigitalRiverWebhookEvent.DISPUTE_CHARGEBACK)
        .setData(JacksonMapper.MAPPER.valueToTree(Map.of("object", requestBody)))
        .setLiveMode("")
        .setCreatedTime("")
        .build();
  }
}
