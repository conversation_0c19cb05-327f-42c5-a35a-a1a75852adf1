package com.scopely.paymentgateway.e2e.wiring.dagger.web;

import com.scopely.paymentgateway.analytics.events.TitanEventService;
import com.scopely.paymentgateway.web.service.PaymentGatewayWebService;
import com.scopely.proteus.core.service.ProteusComponent;
import com.scopely.proteus.monitoring.StatsDHeartbeater;
import com.scopely.proteus.server.BaseHttpServiceSettings;

public class E2ePaymentGatewayWebService extends PaymentGatewayWebService {

  public E2ePaymentGatewayWebService(
      BaseHttpServiceSettings settings,
      TitanEventService titanEventService,
      StatsDHeartbeater statsDHeartbeater,
      ProteusComponent component) {
    super(settings, titanEventService, statsDHeartbeater, component);
  }
}
