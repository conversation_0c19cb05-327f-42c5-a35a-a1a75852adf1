package com.scopely.paymentgateway.e2e.wiring.dagger.core;

import static com.scopely.paymentgateway.modules.DynamoModule.DYNAMO_MAPPER;

import com.amazonaws.services.dynamodbv2.AmazonDynamoDB;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper;
import com.amazonaws.services.dynamodbv2.model.ConditionalCheckFailedException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.parsing.ObjectMapperFactory;
import com.scopely.paymentgateway.parsing.ObjectMapperType;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import com.scopely.proteus.dynamodb.DynamoDbMapperFactory;
import dagger.Module;
import dagger.Provides;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.concurrent.TimeUnit;
import javax.inject.Named;
import javax.inject.Singleton;
import net.jodah.failsafe.RetryPolicy;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

@Module
@SuppressFBWarnings("MS_CANNOT_BE_FINAL")
public class E2eDynamoModule {
  public static AmazonDynamoDB dynamoClientV1;
  public static DynamoDbClient dynamoDbClient;
  public static JsonDynamoMapper jsonDynamoMapper;
  public static DynamoDbMapper dynamoDbMapper;

  @Provides
  @Singleton
  AmazonDynamoDB dynamoDbClientV1() {
    return dynamoClientV1;
  }

  @Provides
  @Singleton
  DynamoDbClient dynamoDbClient() {
    return dynamoDbClient;
  }

  @Provides
  @Singleton
  @Named(value = DYNAMO_MAPPER)
  public ObjectMapper dynamoDbObjectMapper() {
    return ObjectMapperFactory.get(ObjectMapperType.DYNAMO_MAPPER);
  }

  @Provides
  public JsonDynamoMapper jsonDynamoMapper(
      @Named(DYNAMO_MAPPER) ObjectMapper defaultMapper, AmazonDynamoDB amazonDynamoDB) {
    return new JsonDynamoMapper(amazonDynamoDB, defaultMapper);
  }

  @Provides
  @Singleton
  // TODO: used for legacy purposes, it should be removed once the migration to v2 is complete
  public DynamoDBMapper dynamoDBMapperV1(AmazonDynamoDB dynamoDbClient) {
    return new DynamoDBMapper(dynamoDbClient);
  }

  @Provides
  @Singleton
  public DynamoDbMapper dynamoDBMapper(ObjectMapper objectMapper, DynamoDbClient dynamoDbClient) {
    return DynamoDbMapperFactory.newMapper(objectMapper, dynamoDbClient);
  }

  @Provides
  static RetryPolicy retryPolicy(PaymentGatewayConfig config) {
    return new RetryPolicy()
        .withMaxRetries(config.commonRetryPolicyMaxRetries())
        .withBackoff(
            config.commonRetryPolicyBackoffMillis(),
            config.commonRetryPolicyMaxDelayMillis(),
            TimeUnit.MILLISECONDS)
        .retryOn(throwable -> !(throwable instanceof ConditionalCheckFailedException));
  }
}
