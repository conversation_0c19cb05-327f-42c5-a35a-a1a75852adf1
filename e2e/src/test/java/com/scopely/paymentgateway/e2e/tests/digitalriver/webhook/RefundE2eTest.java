package com.scopely.paymentgateway.e2e.tests.digitalriver.webhook;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.API_KEY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ITEM_EXPLICIT_PRICES;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USD;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildOrderResponse;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.DIGITAL_RIVER_API;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.scopely.paymentgateway.e2e.E2eTestLifecycle;
import com.scopely.paymentgateway.e2e.wiring.client.PaymentGatewayApiClientBuilder;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.web.E2ePaymentGatewayWebModule;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.model.payment.ProviderStatus;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.model.refund.RefundRequestDTO;
import com.scopely.paymentgateway.model.refund.RefundStatus;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.CreateRefundRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.DigitalRiverProviderRefundStatus;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.RefundCreationResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.ReturnCreationRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.ReturnResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.ReturnUpdate;
import com.scopely.paymentgateway.web.v1.controller.AdminPaymentEndpoint;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(E2eTestLifecycle.class)
public class RefundE2eTest {
  private static final String PAYMENT_ID = "DR001";
  private static final String REFUND_ID = UUID.randomUUID().toString();
  private static final RefundReason REASON = RefundReason.BUYER_REMORSE;

  private AdminPaymentEndpoint adminPaymentClient;

  @BeforeEach
  public void setup() {
    var port = E2ePaymentGatewayWebModule.PORT;
    adminPaymentClient =
        new PaymentGatewayApiClientBuilder<>(AdminPaymentEndpoint.class, port)
            .withClaims("*:payments:*")
            .build();
    reset(E2eServicesModule.TITAN_CLIENT, DIGITAL_RIVER_API);
  }

  @Test
  public void testSuccessfulRefund() {
    String orderId = "456021";
    String currency = "USD";
    String failureReason = null;
    PaymentBigDecimal decimal = new PaymentBigDecimal(new BigDecimal("10"));

    RefundCreationResponse expectedResponse =
        new RefundCreationResponse(
            REFUND_ID,
            Instant.now().toString(),
            orderId,
            currency,
            decimal,
            decimal,
            RefundReason.BUYER_REMORSE,
            failureReason,
            DigitalRiverProviderRefundStatus.SUCCEEDED);

    when(DIGITAL_RIVER_API.requestRefund(anyString(), any(CreateRefundRequest.class)))
        .thenReturn(expectedResponse);
    when(DIGITAL_RIVER_API.getOrder(any(), any()))
        .thenReturn(
            buildOrderResponse(ITEM_EXPLICIT_PRICES.price(), currency, ProviderStatus.COMPLETED));
    when(DIGITAL_RIVER_API.createReturn(any(), any(ReturnCreationRequest.class)))
        .thenReturn(
            new ReturnResponse(
                REFUND_ID,
                Instant.now().toString(),
                currency,
                List.of(),
                orderId,
                REASON.toString(),
                "",
                false));
    when(DIGITAL_RIVER_API.updateReturn(any(), any(), eq(new ReturnUpdate("accepted"))))
        .thenReturn(
            new ReturnResponse(
                REFUND_ID,
                Instant.now().toString(),
                currency,
                List.of(),
                PAYMENT_ID,
                REASON.toString(),
                "",
                false));

    var request = new RefundRequestDTO(REASON, "any-ticket", "any-comment");
    var result = adminPaymentClient.refundPayment(API_KEY, PAYMENT_ID, request);

    assertThat(result.apiKey()).isEqualTo(API_KEY);
    assertThat(result.paymentId()).isEqualTo(PAYMENT_ID);
    assertThat(result.refundId()).isNotNull();
    assertThat(result.refundReason()).isEqualTo(REASON);
    assertThat(result.refundedAmount().currency()).isEqualTo(USD);
    assertThat(result.refundedAmount().amount()).isEqualTo(new BigDecimal("10"));
    assertThat(result.failureReason()).isNull();
    assertThat(result.status()).isEqualTo(RefundStatus.COMPLETED);
    assertThat(result.supportTicket()).isEqualTo("any-ticket");
    assertThat(result.comment()).isEqualTo("any-comment");

    verify(DIGITAL_RIVER_API).requestRefund(anyString(), any(CreateRefundRequest.class));
  }
}
