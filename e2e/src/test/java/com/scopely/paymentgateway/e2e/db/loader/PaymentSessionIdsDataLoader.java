package com.scopely.paymentgateway.e2e.db.loader;

import com.amazonaws.services.dynamodbv2.model.AttributeDefinition;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement;
import com.amazonaws.services.dynamodbv2.model.KeyType;
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType;
import com.scopely.paymentgateway.e2e.db.DataLoader;
import com.scopely.paymentgateway.repositories.daos.PaymentSessionIdsDAO;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import java.util.Collection;
import java.util.List;

public class PaymentSessionIdsDataLoader implements DataLoader<PaymentSessionIdsDAO> {

  @Override
  public Collection<PaymentSessionIdsDAO> generateData() {
    return List.of();
  }

  @Override
  public int saveItems(DynamoDbMapper dynamoDbMapper) {
    return 0;
  }

  @Override
  public CreateTableRequest getCreateTableRequestV1() {
    return new CreateTableRequest()
        .withTableName(PaymentSessionIdsDAO.TABLE)
        .withAttributeDefinitions(
            new AttributeDefinition(PaymentSessionIdsDAO.HASH_KEY, ScalarAttributeType.S),
            new AttributeDefinition(PaymentSessionIdsDAO.RANGE_KEY, ScalarAttributeType.S))
        .withKeySchema(
            new KeySchemaElement(PaymentSessionIdsDAO.HASH_KEY, KeyType.HASH),
            new KeySchemaElement(PaymentSessionIdsDAO.RANGE_KEY, KeyType.RANGE));
  }
}
