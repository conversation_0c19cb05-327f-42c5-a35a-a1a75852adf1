package com.scopely.paymentgateway.e2e.db;

import com.amazonaws.services.dynamodbv2.AmazonDynamoDB;
import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.e2e.db.loader.CountryConversionDataLoader;
import com.scopely.paymentgateway.e2e.db.loader.CustomerIdDataLoader;
import com.scopely.paymentgateway.e2e.db.loader.PaymentDataLoader;
import com.scopely.paymentgateway.e2e.db.loader.PaymentSessionIdsDataLoader;
import com.scopely.paymentgateway.e2e.db.loader.PriceLocalizationDataLoader;
import com.scopely.paymentgateway.e2e.db.loader.ProviderFeesDataLoader;
import com.scopely.paymentgateway.e2e.db.loader.PurchaseTokenDataLoader;
import com.scopely.paymentgateway.e2e.db.loader.RefundDataLoader;
import com.scopely.paymentgateway.e2e.db.loader.ReversalDataLoader;
import com.scopely.paymentgateway.e2e.db.loader.UserDataLoader;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import com.scopely.proteus.logging.Log;
import java.util.Collection;
import java.util.List;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.BillingMode;
import software.amazon.awssdk.services.dynamodb.model.CreateTableRequest;
import software.amazon.awssdk.services.dynamodb.model.GlobalSecondaryIndex;
import software.amazon.awssdk.services.dynamodb.model.ProvisionedThroughput;

public class DynamoPopulator {
  private final AmazonDynamoDB dynamoDbV1;
  private final DynamoDbClient dynamoDbClient;
  private final JsonDynamoMapper jsonDynamoMapper;
  private final DynamoDbMapper dynamoDbMapper;

  public DynamoPopulator(
      AmazonDynamoDB dynamoDbV1,
      DynamoDbClient dynamoDb,
      JsonDynamoMapper jsonDynamoMapper,
      DynamoDbMapper dynamoDbMapper) {
    this.dynamoDbV1 = dynamoDbV1;
    this.dynamoDbClient = dynamoDb;
    this.jsonDynamoMapper = jsonDynamoMapper;
    this.dynamoDbMapper = dynamoDbMapper;
  }

  public void populateData() {
    getDataLoaders()
        .forEach(
            dataLoader -> {
              Log.info("Loading data from {}", dataLoader.getClass().getSimpleName());
              if (dataLoader.isV1()) {
                createTableV1(dataLoader.getCreateTableRequestV1());
                storeDataV1(dataLoader);
              } else {
                createTable(dataLoader.getCreateTableRequest());
                storeData(dataLoader);
              }
            });
    Log.info("All data Loaded");
  }

  private void createTableV1(com.amazonaws.services.dynamodbv2.model.CreateTableRequest request) {
    var throughput = new com.amazonaws.services.dynamodbv2.model.ProvisionedThroughput(100L, 100L);
    if (request.getGlobalSecondaryIndexes() != null) {
      request
          .getGlobalSecondaryIndexes()
          .forEach(index -> index.setProvisionedThroughput(throughput));
    }
    dynamoDbV1.createTable(
        request
            .withBillingMode(com.amazonaws.services.dynamodbv2.model.BillingMode.PAY_PER_REQUEST)
            .withProvisionedThroughput(throughput));
  }

  private void createTable(CreateTableRequest request) {
    ProvisionedThroughput throughput =
        ProvisionedThroughput.builder().readCapacityUnits(100L).writeCapacityUnits(100L).build();

    // Update Global Secondary Indexes (if present)
    List<GlobalSecondaryIndex> globalSecondaryIndexes = request.globalSecondaryIndexes();
    if (globalSecondaryIndexes != null && !globalSecondaryIndexes.isEmpty()) {
      globalSecondaryIndexes =
          globalSecondaryIndexes.stream()
              .map(index -> index.toBuilder().provisionedThroughput(throughput).build())
              .toList();
    }

    CreateTableRequest.Builder updatedRequestBuilder =
        request.toBuilder()
            .billingMode(BillingMode.PAY_PER_REQUEST)
            .provisionedThroughput(throughput);

    if (globalSecondaryIndexes != null) {
      updatedRequestBuilder.globalSecondaryIndexes(globalSecondaryIndexes);
    }

    dynamoDbClient.createTable(updatedRequestBuilder.build());
  }

  private void storeDataV1(DataLoader<?> dataLoader) {
    try {
      Collection<?> items = dataLoader.generateData();
      items.forEach(jsonDynamoMapper::save);
      Log.info("{}: loaded {} items", dataLoader.getClass().getSimpleName(), items.size());
    } catch (Exception e) {
      Log.warn(e, "Error saving data from {}", dataLoader.getClass().getSimpleName());
    }
  }

  private void storeData(DataLoader<?> dataLoader) {
    try {
      int savedItems = dataLoader.saveItems(dynamoDbMapper);
      Log.info("{}: loaded {} items", dataLoader.getClass().getSimpleName(), savedItems);
    } catch (Exception e) {
      Log.warn(e, "Error saving data from {}", dataLoader.getClass().getSimpleName());
    }
  }

  private Collection<DataLoader<?>> getDataLoaders() {
    return List.of(
        new PaymentDataLoader(),
        new CountryConversionDataLoader(),
        new CustomerIdDataLoader(),
        new PaymentSessionIdsDataLoader(),
        new ProviderFeesDataLoader(),
        new UserDataLoader(),
        new ReversalDataLoader(),
        new RefundDataLoader(),
        new PriceLocalizationDataLoader(),
        new PurchaseTokenDataLoader());
  }
}
