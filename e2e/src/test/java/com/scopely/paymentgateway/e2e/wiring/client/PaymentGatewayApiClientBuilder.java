package com.scopely.paymentgateway.e2e.wiring.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.parsing.ObjectMapperFactory;
import jakarta.ws.rs.client.ClientRequestFilter;
import jakarta.ws.rs.ext.ContextResolver;
import org.jboss.resteasy.client.jaxrs.ResteasyClient;
import org.jboss.resteasy.client.jaxrs.ResteasyClientBuilder;
import org.jboss.resteasy.client.jaxrs.internal.ResteasyClientBuilderImpl;

public class PaymentGatewayApiClientBuilder<T> {
  private static final String ENDPOINT_FORMAT = "http://localhost:%d";
  public static final String API_USER = "<EMAIL>";
  private final Class<T> serviceInterface;
  private final String endpoint;
  private String authorizationHeaderValue;

  public PaymentGatewayApiClientBuilder(Class<T> serviceInterface, int port) {
    this.serviceInterface = serviceInterface;
    this.endpoint = ENDPOINT_FORMAT.formatted(port);
  }

  public PaymentGatewayApiClientBuilder<T> withClaims(String claims) {
    this.authorizationHeaderValue = "Bearer %s,%s".formatted(API_USER, claims);
    return this;
  }

  private ResteasyClient buildClient() {
    ResteasyClientBuilder clientBuilder = new ResteasyClientBuilderImpl();
    clientBuilder.register(new ObjectMapperContextResolver());
    if (authorizationHeaderValue != null) {
      ClientRequestFilter authenticationFilter =
          (context) -> context.getHeaders().add("Authorization", authorizationHeaderValue);
      clientBuilder.register(authenticationFilter);
    }
    return clientBuilder.build();
  }

  public T build() {
    return buildClient().target(endpoint).proxy(serviceInterface);
  }

  private static class ObjectMapperContextResolver implements ContextResolver<ObjectMapper> {

    @Override
    public ObjectMapper getContext(Class<?> type) {
      return ObjectMapperFactory.get();
    }
  }
}
