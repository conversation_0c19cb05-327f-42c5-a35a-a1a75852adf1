package com.scopely.paymentgateway.e2e.tests;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.scopely.paymentgateway.e2e.E2eTestLifecycle;
import com.scopely.paymentgateway.e2e.file.MultipartFormDataMock;
import com.scopely.paymentgateway.e2e.wiring.client.PaymentGatewayApiClientBuilder;
import com.scopely.paymentgateway.e2e.wiring.dagger.web.E2ePaymentGatewayWebModule;
import com.scopely.paymentgateway.exceptions.localized_prices.PriceLocalizationCsvParserException;
import com.scopely.paymentgateway.model.localizedprice.CurrencyPrice;
import com.scopely.paymentgateway.model.localizedprice.RegionalPrice;
import com.scopely.paymentgateway.model.localizedprice.UploadCsvFileRequestForm;
import com.scopely.paymentgateway.web.v1.controller.PriceLocalizationEndpoint;
import jakarta.ws.rs.BadRequestException;
import jakarta.ws.rs.ClientErrorException;
import jakarta.ws.rs.InternalServerErrorException;
import jakarta.ws.rs.NotFoundException;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(E2eTestLifecycle.class)
public class PriceLocalizationE2eTest {
  private static final String INVALID_API_KEY = "invalid-api-key";
  private static final String NON_EXISTENT_API_KEY = "abc1d234-566c-496d-b025-a3c60cc3073c";
  private static final String NEW_API_KEY = "new0api0-key0-this-is0a-new0api0key0";
  private static final String API_KEY_FOR_FILE_NOT_FOUND_IN_S3 = "file-not-found-in-s3";

  private PriceLocalizationEndpoint priceLocalizationEndpoint;

  @BeforeEach
  void setUp() {
    var port = E2ePaymentGatewayWebModule.PORT;
    priceLocalizationEndpoint =
        new PaymentGatewayApiClientBuilder<>(PriceLocalizationEndpoint.class, port)
            .withClaims("*:price-management:admin,*:price-management:read")
            .build();
  }

  @Test
  void testGetEmptyListOfItemsSkuPricesOnNonExistingApiKey() {
    assertThrows(
        ClientErrorException.class,
        () -> priceLocalizationEndpoint.getAllItemsSkuPrices(NON_EXISTENT_API_KEY));
  }

  @Test
  void testThrowsExceptionWhenFileNotFoundInS3() {
    assertThrows(
        ClientErrorException.class,
        () -> priceLocalizationEndpoint.getAllItemsSkuPrices(API_KEY_FOR_FILE_NOT_FOUND_IN_S3));
  }

  @Test
  void testGetPricesByCountryAndIpSuccessfully() {
    var response = priceLocalizationEndpoint.getCountrySkuItemPricesWithIP(API_KEY, US, US_IP);
    assertThat(response.country().code()).isEqualTo(US);
    assertThat(response.items()).isNotEmpty();
    assertThat(response.items().getFirst().sku()).isEqualTo(ITEM_SKU.providers().getFirst().sku());
    assertThat(response.items().getFirst().price()).isEqualTo(new BigDecimal("1.23"));
  }

  @Test
  void testGetPricesByCountrySuccessfully() {
    var responseNew = priceLocalizationEndpoint.getCountrySkuItemPrices(null, API_KEY, US);
    var responseCurrent = priceLocalizationEndpoint.getCountrySkuItemPrices(null, API_KEY, US);
    assertThat(responseCurrent).isEqualTo(responseNew);
  }

  @Test
  void testGetPricesByCountryWithInvalidCountryCode() {
    assertThrows(
        ClientErrorException.class,
        () -> priceLocalizationEndpoint.getCountrySkuItemPricesWithIP(API_KEY, "INVALID", US_IP));
  }

  @Test
  void testGetPricesByCountryWithInvalidApiKey() {
    assertThrows(
        ClientErrorException.class,
        () -> priceLocalizationEndpoint.getCountrySkuItemPricesWithIP(INVALID_API_KEY, US, US_IP));
  }

  @Test
  void testGetPricesByCountryWithNonExistentApiKey() {
    assertThrows(
        ClientErrorException.class,
        () ->
            priceLocalizationEndpoint.getCountrySkuItemPricesWithIP(
                NON_EXISTENT_API_KEY, US, US_IP));
  }

  @Test
  void testValidateCsvItemsSkuPrices() {
    var frRegionalPrices = new RegionalPrice(new BigDecimal("30.99"), "EUR");
    var eurCurrencyPrices = new CurrencyPrice(new BigDecimal("2.99"));

    final InputStream fileValue = MultipartFormDataMock.createValidFileInputStream();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    var response = priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form);
    assertThat(response).isNotNull();
    assertThat(response.prices().getFirst().sku()).isEqualTo("sku_1");
    assertThat(response.prices().getFirst().regionalPrices().get("FR")).isEqualTo(frRegionalPrices);
    assertThat(response.prices().getFirst().currencyPrices().get("EUR"))
        .isEqualTo(eurCurrencyPrices);
  }

  @Test
  void testShouldFailWhenInvalidCsvHeader() {
    final InputStream fileValue = MultipartFormDataMock.createInvalidHeaderFileInputStream();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));

    // Check if the message matches the expected error
    String exceptionMessage = getErrorMessage(exception);

    assertEquals(
        "Header verification missing$The uploaded file must include the following columns: Sku, Country, Currency, Price. Please check and try again.$$",
        exceptionMessage);
  }

  @Test
  void testShouldFailWhenMissingColumnsInCsvFile() {
    final InputStream fileValue = MultipartFormDataMock.createInvalidFileWithMissingColumns();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));

    // Check if the message matches the expected error
    String exceptionMessage = getErrorMessage(exception);

    assertEquals(
        "Mandatory fields missing$Each SKU must include the required fields: SKU, Currency, and Price. Please ensure all necessary fields are provided and try again.$4$",
        exceptionMessage);
  }

  @Test
  void testShouldFailWhenEmptyRequiredColumnsInCsvFile() {
    final InputStream fileValue = MultipartFormDataMock.createInvalidFileWithEmptyRequiredColumns();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));

    // Check if the message matches the expected error
    String exceptionMessage = getErrorMessage(exception);

    assertEquals(
        "Mandatory fields missing$Each SKU must include the required fields: SKU, Currency, and Price. Please ensure all necessary fields are provided and try again.$4$",
        exceptionMessage);
  }

  @Test
  void testShouldThrowBadRequestWhenInvalidCountryCode() {
    final InputStream fileValue = MultipartFormDataMock.createInvalidFileWithInvalidCountryCode();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));

    // Check if the message matches the expected error
    String exceptionMessage = getErrorMessage(exception);

    assertEquals(
        "Invalid country code$The country code provided is not valid. Please enter a valid country code and try again.$6#7$",
        exceptionMessage);
  }

  @Test
  void testShouldThrowBadRequestWhenInvalidCurrencyCode() {
    final InputStream fileValue = MultipartFormDataMock.createInvalidFileWithInvalidCurrencyCode();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));

    // Check if the message matches the expected error
    String exceptionMessage = getErrorMessage(exception);

    assertEquals(
        "Invalid currency code$The currency code specified is not valid. Please enter a valid currency code and try again.$4#5$",
        exceptionMessage);
  }

  @Test
  void testShouldThrowBadRequestWhenInvalidPrice() {
    final InputStream fileValue = MultipartFormDataMock.createInvalidFileWithInvalidPrice();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));

    // Check if the message matches the expected error
    String exceptionMessage = getErrorMessage(exception);

    assertEquals(
        "Invalid price$The provided price does not have a valid number format. Please enter a valid price and try again.$5#6#7$",
        exceptionMessage);
  }

  @Test
  void testShouldThrowBadRequestWhenMissingDefaultSkuPrice() {
    final InputStream fileValue = MultipartFormDataMock.createMissingDefaultSkuInputStream();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));

    // Check if the message matches the expected error
    String exceptionMessage = getErrorMessage(exception);

    assertEquals(
        "Default price SKU not configured$The default price SKU must be configured for all price points.$$SKU sku_3 is missing for the following currencies: EUR, USD",
        exceptionMessage);
  }

  @Test
  void testShouldThrowBadRequestWhenDefaultPriceIsNotConfigured() {
    final InputStream fileValue =
        MultipartFormDataMock.createDefaultPriceNotConfiguredInputStream();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));

    // Check if the message matches the expected error
    String exceptionMessage = getErrorMessage(exception);

    assertEquals(
        "Default price SKU not configured$The default price SKU must be configured. Please set it up and try again.$$",
        exceptionMessage);
  }

  @Test
  void testShouldThrowBadRequestWhenMultipleCurrenciesPerCountry() {
    final InputStream fileValue =
        MultipartFormDataMock.createMultipleCurrenciesPerCountryInputStream();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));

    // Check if the message matches the expected error
    String exceptionMessage = getErrorMessage(exception);

    assertEquals(
        "Single currency configuration required$Only one currency can be configured per country. Please review and try again.$5$",
        exceptionMessage);
  }

  @Test
  void testShouldTrowBadRequestWhenDuplicatedSkuDefinitions() {
    final InputStream fileValue = MultipartFormDataMock.createDuplicatedSkuDefinitionsInputStream();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));

    // Check if the message matches the expected error
    List<String> exceptionMessages =
        Arrays.asList(
            getErrorMessage(exception).split(PriceLocalizationCsvParserException.ERROR_SEPARATOR));

    assertTrue(
        exceptionMessages.contains(
            "Duplicated price SKU configuration$There are multiple definitions of the same price SKU in the same county. Please review and try again.$6#7$"));
    assertTrue(
        exceptionMessages.contains(
            "Single currency configuration required$Only one currency can be configured per country. Please review and try again.$6#7$"));
  }

  @Test
  void testShouldThrowBadRequestWhenMissingPriceConfiguration() {
    final InputStream fileValue =
        MultipartFormDataMock.createMissingPriceSkuConfigurationInputStream();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));

    // Check if the message matches the expected error
    String exceptionMessage = getErrorMessage(exception);

    assertEquals(
        "Missing price SKU configuration$A corresponding price SKU configuration must exist for all available currency and country/currency combinations. Please review and try again.$$SKU sku_3 is missing for the following countries: CA",
        exceptionMessage);
  }

  @Test
  void testShouldThrowBadRequestIfProvidedFileContentIsEmpty() {
    final InputStream fileValue = MultipartFormDataMock.createEmptyContentFileInputStream();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));

    // Check if the message matches the expected error
    String exceptionMessage = getErrorMessage(exception);

    assertEquals(
        "Header verification missing$The uploaded file must include the following columns: Sku, Country, Currency, Price. Please check and try again.$$",
        exceptionMessage);
  }

  @Test
  void testShouldThrowBadRequestIfProvidedFileIsEmpty() {
    final InputStream fileValue = MultipartFormDataMock.createEmptyFileInputStream();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    assertThrows(
        BadRequestException.class,
        () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));
  }

  @Test
  void testShouldTrowInternalServerErrorWhenFileNotProvided() {
    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();

    assertThrows(
        InternalServerErrorException.class,
        () -> priceLocalizationEndpoint.validateCsvItemsSkuPrices(API_KEY, form));
  }

  private String getErrorMessage(BadRequestException exception) {
    // Read all bytes from the ByteArrayInputStream
    ByteArrayInputStream brais = (ByteArrayInputStream) exception.getResponse().getEntity();
    byte[] bytes = brais.readAllBytes();

    // Convert bytes to String
    String jsonString = new String(bytes, StandardCharsets.UTF_8);

    // Parse the JSON string to get the error message
    return jsonString.split("\"error\":\"")[1].split("\"")[0].replaceAll("\\\\n", "\n");
  }

  @Test
  void testShouldUploadANewVersionSuccessfullyWhenTheApiKeyDidntExists() {
    assertThrows(
        NotFoundException.class, () -> priceLocalizationEndpoint.getAllItemsSkuPrices(NEW_API_KEY));

    final InputStream fileValue = MultipartFormDataMock.createValidFileInputStream();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    var uploadedLocalizedPrices =
        priceLocalizationEndpoint.uploadCsvItemsSkuPrices(NEW_API_KEY, form);

    // Now the version should exist
    var savedLocalizedPrices = priceLocalizationEndpoint.getAllItemsSkuPrices(NEW_API_KEY);
    assertThat(uploadedLocalizedPrices).isEqualTo(savedLocalizedPrices);
  }

  @Test
  void testShouldThrowBadRequestWhenFileToUpdateIsNotValid() {
    final InputStream fileValue = MultipartFormDataMock.createInvalidHeaderFileInputStream();

    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();
    form.setPriceConfigurationFile(fileValue);

    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () -> priceLocalizationEndpoint.uploadCsvItemsSkuPrices(API_KEY, form));

    // Check if the message matches the expected error
    String exceptionMessage = getErrorMessage(exception);

    assertEquals(
        "Header verification missing$The uploaded file must include the following columns: Sku, Country, Currency, Price. Please check and try again.$$",
        exceptionMessage);
  }

  @Test
  void testShouldTrowInternalServerErrorWhenFileToUploadNotProvided() {
    UploadCsvFileRequestForm form = new UploadCsvFileRequestForm();

    assertThrows(
        InternalServerErrorException.class,
        () -> priceLocalizationEndpoint.uploadCsvItemsSkuPrices(API_KEY, form));
  }
}
