package com.scopely.paymentgateway.e2e.wiring.dagger.core;

import static com.scopely.paymentgateway.modules.ServicesModule.DIGITAL_RIVER_CIRCUIT_BREAKER_CONFIG;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.scopely.arche.client.api.ABTestConfigMappingApi;
import com.scopely.arche.client.grpc.SegmentationClient;
import com.scopely.circuitbreaker.CircuitBreakerConfig;
import com.scopely.circuitbreaker.IntervalFunction;
import com.scopely.geofencing.client.service.GeofencingService;
import com.scopely.paymentgateway.config.ABTestingConfig;
import com.scopely.paymentgateway.config.DigitalRiverConfig;
import com.scopely.paymentgateway.config.FraudPreventionConfig;
import com.scopely.paymentgateway.config.MailChimpTransactionalApiConfig;
import com.scopely.paymentgateway.config.MetricsCatalogConfig;
import com.scopely.paymentgateway.config.ParameterStoreConfig;
import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.config.PlayerProfileConfig;
import com.scopely.paymentgateway.config.RedirectWebhookConfig;
import com.scopely.paymentgateway.config.SegmentationConfig;
import com.scopely.paymentgateway.config.TitanConfig;
import com.scopely.paymentgateway.config.XSollaConfig;
import com.scopely.paymentgateway.e2e.wiring.aws.S3Mock;
import com.scopely.paymentgateway.providers.digitalriver.api.CircuitBreakerDigitalRiverApiClient;
import com.scopely.paymentgateway.providers.digitalriver.api.DigitalRiverApi;
import com.scopely.paymentgateway.providers.digitalriver.services.DigitalRiverPaymentProviderService;
import com.scopely.paymentgateway.providers.digitalriver.services.DigitalRiverPaymentProviderServiceImpl;
import com.scopely.paymentgateway.providers.xsolla.api.XSollaApi;
import com.scopely.paymentgateway.providers.xsolla.api.XsollaMerchantApi;
import com.scopely.paymentgateway.providers.xsolla.services.XSollaPaymentProviderService;
import com.scopely.paymentgateway.providers.xsolla.services.XsollaPaymentProviderServiceImpl;
import com.scopely.paymentgateway.providers.xsolla.webhook.processors.RedirectWebhookProcessor;
import com.scopely.paymentgateway.repositories.DynamoFeesByProviderRepository;
import com.scopely.paymentgateway.repositories.FeesByProviderRepository;
import com.scopely.paymentgateway.services.api.ApiFactory;
import com.scopely.paymentgateway.services.api.GenericApiCallsExecutor;
import com.scopely.paymentgateway.services.api.MailChimpTransactionalApi;
import com.scopely.paymentgateway.services.api.MetricsCatalogApi;
import com.scopely.paymentgateway.services.api.RedirectWebhookApi;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationCacheService;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.clientconfig.ParameterStoreService;
import com.scopely.paymentgateway.services.countryconversion.CountryInfoService;
import com.scopely.paymentgateway.services.email.EmailService;
import com.scopely.paymentgateway.services.email.PaymentEmailService;
import com.scopely.paymentgateway.services.email.RefundEmailService;
import com.scopely.paymentgateway.services.email.mailchimp.MailChimpEmailService;
import com.scopely.paymentgateway.services.email.mailchimp.MailchimpEmailTemplateNameRetriever;
import com.scopely.paymentgateway.services.fees.DefaultProviderFeesLoader;
import com.scopely.paymentgateway.services.fees.FeesByProviderService;
import com.scopely.paymentgateway.services.geolocation.GeolocationPaymentService;
import com.scopely.paymentgateway.services.geolocation.GeolocationPaymentServiceImpl;
import com.scopely.paymentgateway.services.segmentation.GRPCSegmentationService;
import com.scopely.paymentgateway.services.segmentation.SegmentationService;
import com.scopely.paymentgateway.services.startup.WarmUpService;
import com.scopely.paymentgateway.utils.ApiCallsExecutor;
import com.scopely.playerprofile.client.v2.api.PlayerProfileApi;
import com.scopely.playerprofile.v1.dto.PlayerAttributesApiDTO;
import com.scopely.playerprofile.v1.dto.PlayerProfileInformationDTO;
import com.scopely.proteus.client.interceptors.retry.JakartaRetryPredicate;
import com.scopely.proteus.config.ConfigBackedValuesSupplier;
import com.scopely.proteus.config.ConfigObjectAssembler;
import com.scopely.proteus.email.sendgrid.SendGridMailSender;
import com.scopely.proteus.util.ApiConfig;
import com.scopely.titan.TitanClient;
import com.scopely.titan.common.util.AppCache;
import com.timgroup.statsd.NoOpStatsDClient;
import com.timgroup.statsd.StatsDClient;
import dagger.Module;
import dagger.Provides;
import java.time.Duration;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import javax.inject.Named;
import javax.inject.Singleton;
import software.amazon.awssdk.services.s3.S3Client;

@Module
public class E2eServicesModule {
  public static final DigitalRiverApi DIGITAL_RIVER_API = mock(DigitalRiverApi.class);
  public static final XSollaApi XSOLLA_API = mock(XSollaApi.class);
  public static final XsollaMerchantApi XSOLLA_MERCHANT_API = mock(XsollaMerchantApi.class);
  public static final ABTestConfigMappingApi AB_TEST_CONFIG_MAPPING_API =
      mock(ABTestConfigMappingApi.class);
  public static final PlayerProfileApi PLAYER_PROFILE_API = mock(PlayerProfileApi.class);
  public static final MetricsCatalogApi METRICS_CATALOG_API = mock(MetricsCatalogApi.class);
  public static final MailChimpTransactionalApi MAILCHIMP_API =
      mock(MailChimpTransactionalApi.class);
  public static final ABTestConfigMappingApi ARCHE_ABTEST_API = mock(ABTestConfigMappingApi.class);
  public static final RedirectWebhookApi REDIRECT_WEBHOOK_API = mock(RedirectWebhookApi.class);
  public static final SegmentationClient SEGMENTATION_API = mock(SegmentationClient.class);
  public static final WarmUpService READINESS_PROBE_SERVICE = mock(WarmUpService.class);
  public static final TitanClient TITAN_CLIENT = mock(TitanClient.class);
  public static final SendGridMailSender SENDGRID_MAIL_SENDER = mock(SendGridMailSender.class);
  public static final E2eFunnelCircuitBreakerFactory FUNNEL_CIRCUIT_BREAKER_FACTORY =
      new E2eFunnelCircuitBreakerFactory(new NoOpStatsDClient());

  @Provides
  @Singleton
  public AppCache appCache() {
    return mock(AppCache.class);
  }

  @Provides
  @Singleton
  public WarmUpService readinessProbeService() {
    return READINESS_PROBE_SERVICE;
  }

  @Provides
  @Singleton
  public DigitalRiverConfig digitalRiverConfig(
      ConfigBackedValuesSupplier configBackedValuesSupplier) {
    return new ConfigObjectAssembler(configBackedValuesSupplier)
        .newInstanceOf(DigitalRiverConfig.class);
  }

  @Provides
  @Singleton
  public ApiCallsExecutor genericExecutor(GenericApiCallsExecutor genericApiCallsExecutor) {
    return genericApiCallsExecutor;
  }

  @Provides
  @Singleton
  public XSollaConfig xSollaConfig(ConfigBackedValuesSupplier configBackedValuesSupplier) {
    return new ConfigObjectAssembler(configBackedValuesSupplier).newInstanceOf(XSollaConfig.class);
  }

  @Provides
  @Singleton
  public ABTestingConfig abTestingConfig(ConfigBackedValuesSupplier configBackedValuesSupplier) {
    return new ConfigObjectAssembler(configBackedValuesSupplier)
        .newInstanceOf(ABTestingConfig.class);
  }

  @Provides
  @Singleton
  public PlayerProfileConfig playerProfileConfig(
      ConfigBackedValuesSupplier configBackedValuesSupplier) {
    return new ConfigObjectAssembler(configBackedValuesSupplier)
        .newInstanceOf(PlayerProfileConfig.class);
  }

  @Provides
  @Singleton
  public MetricsCatalogConfig metricsCatalogConfig(
      ConfigBackedValuesSupplier configBackedValuesSupplier) {
    return new ConfigObjectAssembler(configBackedValuesSupplier)
        .newInstanceOf(MetricsCatalogConfig.class);
  }

  @Provides
  @Singleton
  public RedirectWebhookConfig redirectWebhookConfig(
      ConfigBackedValuesSupplier configBackedValuesSupplier) {
    return new ConfigObjectAssembler(configBackedValuesSupplier)
        .newInstanceOf(RedirectWebhookConfig.class);
  }

  @Provides
  @Singleton
  @Named(DIGITAL_RIVER_CIRCUIT_BREAKER_CONFIG)
  public CircuitBreakerConfig digitalRiverCircuitBreakerConfig(DigitalRiverConfig config) {
    return new CircuitBreakerConfig.Builder()
        .setSlidingWindowType(CircuitBreakerConfig.SlidingWindowType.COUNT_BASED)
        .setSlidingWindowSize(config.circuitBreakerSlidingWindowSize())
        .setFailureRateThreshold(config.circuitBreakerFailureRateThresholdPercent())
        .setMinimumNumberOfCalls(config.circuitBreakerMinimumNumberOfCalls())
        .setSlowCallRateThreshold(config.circuitBreakerSlowCallRateThresholdPercent())
        .setSlowCallDurationThreshold(
            Duration.ofMillis(config.circuitBreakerSlowCallThresholdMillis()))
        .setWaitIntervalFunctionInOpenState(
            IntervalFunction.of(Duration.ofSeconds(config.circuitBreakerWaitInOpenStateSeconds())))
        .setPermittedNumberOfCallsInHalfOpenState(
            config.circuitBreakerPermittedCallsInHalfOpenState())
        .setMaxWaitDurationInHalfOpenState(
            Duration.ofSeconds(config.circuitBreakerMaxWaitInHalfOpenStateSeconds()))
        .setRecordExceptionPredicate(new JakartaRetryPredicate())
        .build();
  }

  @Provides
  @Singleton
  public DigitalRiverApi digitalRiverApi(
      @Named(DIGITAL_RIVER_CIRCUIT_BREAKER_CONFIG) CircuitBreakerConfig circuitBreakerConfig) {
    return new CircuitBreakerDigitalRiverApiClient(
        DIGITAL_RIVER_API, circuitBreakerConfig, FUNNEL_CIRCUIT_BREAKER_FACTORY);
  }

  @Provides
  @Singleton
  public XSollaApi xSollaApi() {
    return XSOLLA_API;
  }

  @Provides
  @Singleton
  public XsollaMerchantApi xsollaMerchantApi() {
    return XSOLLA_MERCHANT_API;
  }

  @Provides
  @Singleton
  public PlayerProfileApi playerProfileApi() {
    when(PLAYER_PROFILE_API.saveAttributes(
            anyString(), anyString(), any(PlayerAttributesApiDTO.class)))
        .thenReturn(
            CompletableFuture.completedFuture(new PlayerProfileInformationDTO.Builder().build()));
    return PLAYER_PROFILE_API;
  }

  @Provides
  @Singleton
  public ABTestConfigMappingApi aBTestConfigMappingApi() {
    return AB_TEST_CONFIG_MAPPING_API;
  }

  @Provides
  @Singleton
  public DigitalRiverPaymentProviderService paymentProviderDigitalRiverService(
      DigitalRiverPaymentProviderServiceImpl digitalRiverPaymentProviderServiceImpl) {
    return digitalRiverPaymentProviderServiceImpl;
  }

  @Provides
  @Singleton
  public XSollaPaymentProviderService paymentProviderXSollaService(
      XsollaPaymentProviderServiceImpl xsollaPaymentProviderServiceImpl) {
    return xsollaPaymentProviderServiceImpl;
  }

  @Provides
  @Singleton
  public GeolocationPaymentService geolocationPaymentService(
      StatsDClient statsDClient,
      GeofencingService geofencingService,
      CountryInfoService countryInfoService) {
    return new GeolocationPaymentServiceImpl(statsDClient, geofencingService, countryInfoService);
  }

  @Provides
  @Singleton
  public FraudPreventionConfig fraudPreventionConfig(
      ConfigBackedValuesSupplier configBackedValuesSupplier) {
    return new ConfigObjectAssembler(configBackedValuesSupplier)
        .newInstanceOf(FraudPreventionConfig.class);
  }

  @Provides
  @Singleton
  public FeesByProviderService getFeesByProviderService(
      FeesByProviderRepository feesByProviderRepository,
      MetricsCatalogApi metricCatalogApi,
      ApiCallsExecutor apiCallsExecutor,
      CountryInfoService countryInfoService,
      PaymentGatewayConfig paymentGatewayConfig,
      DefaultProviderFeesLoader defaultProviderFeesLoader,
      StatsDClient statsDClient) {
    return new FeesByProviderService(
        feesByProviderRepository,
        metricCatalogApi,
        apiCallsExecutor,
        countryInfoService,
        paymentGatewayConfig,
        defaultProviderFeesLoader,
        statsDClient);
  }

  @Provides
  @Singleton
  public DefaultProviderFeesLoader getDefaultProviderFeesLoader() {
    return new DefaultProviderFeesLoader();
  }

  @Provides
  @Singleton
  public FeesByProviderRepository getFeesByProviderRepo(
      DynamoFeesByProviderRepository dynamoFeesByProviderRepo) {
    return dynamoFeesByProviderRepo;
  }

  @Provides
  @Singleton
  public MetricsCatalogApi getMetricsCatalogApi() {
    return METRICS_CATALOG_API;
  }

  @Provides
  @Singleton
  public ClientConfigurationService clientConfigurationService(
      ParameterStoreConfig parameterStoreConfig, ParameterStoreService parameterStoreService) {
    return new ClientConfigurationCacheService(parameterStoreConfig, parameterStoreService);
  }

  @Provides
  @Singleton
  public SegmentationConfig segmentationConfig(
      ConfigBackedValuesSupplier configBackedValuesSupplier) {
    return new ConfigObjectAssembler(configBackedValuesSupplier)
        .newInstanceOf(SegmentationConfig.class);
  }

  @Provides
  @Singleton
  public SegmentationClient segmentationClient() {
    return SEGMENTATION_API;
  }

  @Provides
  @Singleton
  public SegmentationService segmentationService(GRPCSegmentationService grpcSegmentationService) {
    return grpcSegmentationService;
  }

  @Provides
  public S3Client s3Client() {
    return S3Mock.getS3Client();
  }

  @Provides
  @Singleton
  MailChimpTransactionalApiConfig mailChimpTransactionalApiConfig(
      ConfigBackedValuesSupplier configBackedValuesSupplier) {
    return new ConfigObjectAssembler(configBackedValuesSupplier)
        .newInstanceOf(MailChimpTransactionalApiConfig.class);
  }

  @Provides
  @Singleton
  MailChimpTransactionalApi mailChimpTransactionalApi() {
    return MAILCHIMP_API;
  }

  @Provides
  @Singleton
  TitanConfig titanConfig(ConfigBackedValuesSupplier configBackedValuesSupplier) {
    return new ConfigObjectAssembler(configBackedValuesSupplier).newInstanceOf(TitanConfig.class);
  }

  @Provides
  @Singleton
  static TitanClient titanClient() {
    return TITAN_CLIENT;
  }

  @Provides
  @Singleton
  static RedirectWebhookProcessor redirectWebhookProcessor(
      ClientConfigurationService clientConfigurationService,
      StatsDClient statsDClient,
      ApiCallsExecutor apiCallsExecutor) {
    var apiConfig = mock(ApiConfig.class);
    var apifactory = mock(ApiFactory.class);
    when(apifactory.buildNullable(eq(RedirectWebhookApi.class), anyString(), eq(apiConfig)))
        .thenReturn(REDIRECT_WEBHOOK_API);
    return new RedirectWebhookProcessor(
        clientConfigurationService, statsDClient, apiCallsExecutor, apiConfig, apifactory);
  }

  @Provides
  MailChimpEmailService sendEmailOrder(
      MailChimpTransactionalApi mailChimpTransactionalApi,
      MailChimpTransactionalApiConfig mailChimpTransactionalApiConfig,
      MailchimpEmailTemplateNameRetriever mailchimpEmailTemplateNameRetriever,
      ApiCallsExecutor apiCallsExecutor,
      ClientConfigurationService clientConfigurationService) {
    return new MailChimpEmailService(
        mailChimpTransactionalApi,
        mailChimpTransactionalApiConfig,
        mailchimpEmailTemplateNameRetriever,
        apiCallsExecutor,
        clientConfigurationService);
  }

  @Provides
  @Named(RefundEmailService.REFUND_EMAIL_SERVICE)
  EmailService refundEmailService(RefundEmailService refundEmailService) {
    return refundEmailService;
  }

  @Provides
  @Named(PaymentEmailService.PAYMENT_EMAIL_SERVICE)
  EmailService paymentEmailService(PaymentEmailService paymentEmailService) {
    return paymentEmailService;
  }

  @Provides
  Set<EmailService> emailServices(
      PaymentEmailService paymentEmailService, RefundEmailService refundEmailService) {
    return Set.of(paymentEmailService, refundEmailService);
  }

  @Provides
  @Singleton
  SendGridMailSender sendGridEmailService() {
    return SENDGRID_MAIL_SENDER;
  }
}
