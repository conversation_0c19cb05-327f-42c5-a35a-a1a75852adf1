package com.scopely.paymentgateway.e2e.wiring.dagger.webhook;

import com.scopely.paymentgateway.webhook.xsolla.service.WebhookXSollaService;
import com.scopely.proteus.core.service.ProteusComponent;
import com.scopely.proteus.monitoring.StatsDHeartbeater;
import com.scopely.proteus.server.providers.CorsInterceptor;
import com.scopely.proteus.server.providers.GzipFeature;
import com.scopely.proteus.server.providers.KeepAliveDisablingFilter;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.jboss.resteasy.plugins.interceptors.AcceptEncodingGZIPFilter;
import org.jboss.resteasy.plugins.interceptors.GZIPDecodingInterceptor;
import org.jboss.resteasy.plugins.interceptors.GZIPEncodingInterceptor;
import org.jetbrains.annotations.NotNull;

public class E2eWebhookXSollaService extends WebhookXSollaService {

  public E2eWebhookXSollaService(
      int port, int threadCount, StatsDHeartbeater statsDHeartbeater, ProteusComponent component) {
    super(port, threadCount, getE2eProviders(), statsDHeartbeater, component);
  }

  @NotNull
  protected static Set<Class<?>> getE2eProviders() {
    Set<Class<?>> providers = new HashSet<>(WebhookXSollaService.getProviders());
    List.of(
            CorsInterceptor.class,
            KeepAliveDisablingFilter.class,
            GZIPDecodingInterceptor.class,
            AcceptEncodingGZIPFilter.class,
            GZIPEncodingInterceptor.class,
            GzipFeature.class)
        .forEach(providers::remove);
    return providers;
  }
}
