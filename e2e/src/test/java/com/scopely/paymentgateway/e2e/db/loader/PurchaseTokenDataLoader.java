package com.scopely.paymentgateway.e2e.db.loader;

import com.amazonaws.services.dynamodbv2.model.AttributeDefinition;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement;
import com.amazonaws.services.dynamodbv2.model.KeyType;
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType;
import com.scopely.paymentgateway.e2e.db.DataLoader;
import com.scopely.paymentgateway.repositories.daos.PurchaseTokenPaymentInfoDAO;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

public class PurchaseTokenDataLoader implements DataLoader<PurchaseTokenPaymentInfoDAO> {

  @Override
  public Collection<PurchaseTokenPaymentInfoDAO> generateData() {
    return List.of();
  }

  @Override
  public int saveItems(DynamoDbMapper dynamoDbMapper) throws IOException {
    return 0;
  }

  @Override
  public CreateTableRequest getCreateTableRequestV1() {
    return new CreateTableRequest()
        .withTableName(PurchaseTokenPaymentInfoDAO.TABLE)
        .withAttributeDefinitions(
            new AttributeDefinition(PurchaseTokenPaymentInfoDAO.HASH_KEY, ScalarAttributeType.S))
        .withKeySchema(new KeySchemaElement(PurchaseTokenPaymentInfoDAO.HASH_KEY, KeyType.HASH));
  }
}
