package com.scopely.paymentgateway.e2e.db.loader;

import com.amazonaws.services.dynamodbv2.model.AttributeDefinition;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement;
import com.amazonaws.services.dynamodbv2.model.KeyType;
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType;
import com.scopely.paymentgateway.e2e.db.DataLoader;
import com.scopely.paymentgateway.repositories.daos.CountryConversionDAO;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import java.util.Collection;
import java.util.List;

public class CountryConversionDataLoader implements DataLoader<CountryConversionDAO> {

  @Override
  public Collection<CountryConversionDAO> generateData() {
    return List.of();
  }

  @Override
  public int saveItems(DynamoDbMapper dynamoDbMapper) {
    return 0;
  }

  @Override
  public CreateTableRequest getCreateTableRequestV1() {
    return new CreateTableRequest()
        .withTableName(CountryConversionDAO.TABLE)
        .withAttributeDefinitions(
            new AttributeDefinition(CountryConversionDAO.HASH_KEY, ScalarAttributeType.S))
        .withKeySchema(new KeySchemaElement(CountryConversionDAO.HASH_KEY, KeyType.HASH));
  }
}
