package com.scopely.paymentgateway.e2e.wiring.dagger.webhook;

import com.scopely.paymentgateway.webhook.digitalriver.PaymentGatewayWebhookDigitalRiverComponent;
import com.scopely.paymentgateway.webhook.digitalriver.authentication.digitalriver.DigitalRiverAuthenticationFilter;
import com.scopely.paymentgateway.webhook.digitalriver.service.WebhookDigitalRiverService;
import com.scopely.proteus.monitoring.StatsDHeartbeater;
import com.scopely.proteus.server.providers.CorsInterceptor;
import com.scopely.proteus.server.providers.GzipFeature;
import com.scopely.proteus.server.providers.KeepAliveDisablingFilter;
import java.util.List;
import java.util.Set;
import org.jboss.resteasy.plugins.interceptors.AcceptEncodingGZIPFilter;
import org.jboss.resteasy.plugins.interceptors.GZIPDecodingInterceptor;
import org.jboss.resteasy.plugins.interceptors.GZIPEncodingInterceptor;
import org.jetbrains.annotations.NotNull;

public class E2eWebhookDigitalRiverService extends WebhookDigitalRiverService {

  public E2eWebhookDigitalRiverService(
      int port,
      int threadCount,
      StatsDHeartbeater statsDHeartbeater,
      PaymentGatewayWebhookDigitalRiverComponent component) {
    super(port, threadCount, getE2eProviders(), statsDHeartbeater, component);
  }

  @NotNull
  protected static Set<Class<?>> getE2eProviders() {
    Set<Class<?>> providers = WebhookDigitalRiverService.getProviders();
    List.of(
            CorsInterceptor.class,
            KeepAliveDisablingFilter.class,
            DigitalRiverAuthenticationFilter.class,
            GZIPDecodingInterceptor.class,
            AcceptEncodingGZIPFilter.class,
            GZIPEncodingInterceptor.class,
            GzipFeature.class)
        .forEach(providers::remove);
    return providers;
  }
}
