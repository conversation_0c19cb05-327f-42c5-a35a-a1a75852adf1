package com.scopely.paymentgateway.e2e.wiring.dagger.webhook;

import static org.mockito.Mockito.mock;

import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eCoreModule;
import com.scopely.paymentgateway.webhook.xsolla.PaymentGatewayWebhookXSollaComponent;
import com.scopely.paymentgateway.webhook.xsolla.authentication.filter.XsollaAuthenticationValidator;
import com.scopely.paymentgateway.webhook.xsolla.service.WebhookXSollaService;
import com.scopely.proteus.monitoring.StatsDHeartbeater;
import com.scopely.proteus.service.ProteusServiceConstants;
import dagger.Module;
import dagger.Provides;
import javax.inject.Named;
import javax.inject.Singleton;

@Module(includes = {E2eCoreModule.class})
public class E2eWebhookXSollaModule {
  public static final int PORT = 7577;

  @Provides
  @Named(ProteusServiceConstants.SERVICE_NAME)
  String serviceName() {
    return "payment-gateway-webhook-xsolla";
  }

  @Provides
  @Singleton
  public WebhookXSollaService webhookXSollaService(
      StatsDHeartbeater statsDHeartbeater, E2ePaymentGatewayWebhookXSollaComponent component) {
    return new E2eWebhookXSollaService(PORT, 10, statsDHeartbeater, component);
  }

  @Provides
  @Singleton
  public XsollaAuthenticationValidator xsollaAuthenticationValidator() {
    return mock(XsollaAuthenticationValidator.class);
  }

  @Provides
  @Singleton
  public PaymentGatewayWebhookXSollaComponent paymentGatewayWebhookXSollaComponent() {
    return mock(PaymentGatewayWebhookXSollaComponent.class);
  }
}
