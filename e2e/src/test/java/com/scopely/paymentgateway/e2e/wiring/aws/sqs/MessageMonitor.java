package com.scopely.paymentgateway.e2e.wiring.aws.sqs;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.scopely.proteus.util.JacksonMapper;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.time.Duration;
import java.util.UUID;

public record MessageMonitor(String body, String receiptHandle) {

  public static MessageMonitor fromBody(String body) {
    return new MessageMonitor(body, UUID.randomUUID().toString());
  }

  @SuppressFBWarnings("NN_NAKED_NOTIFY")
  synchronized void notifyMessageConsumed() {
    notifyAll();
  }

  @SuppressFBWarnings("SWL_SLEEP_WITH_LOCK_HELD")
  public synchronized void waitForMessageToBeConsumed(Duration timeout)
      throws InterruptedException {
    wait(timeout.toMillis());
    // Wait some extra time to allow processor finish its work
    Thread.sleep(500);
  }

  public <T> T deserialize(Class<T> clazz) {
    try {
      return JacksonMapper.MAPPER.readValue(body, clazz);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }
}
