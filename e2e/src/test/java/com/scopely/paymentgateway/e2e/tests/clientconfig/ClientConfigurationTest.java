package com.scopely.paymentgateway.e2e.tests.clientconfig;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.API_KEY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.DIGITAL_RIVER;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.PLAYGAMI_PAYMENTS;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.XSOLLA;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.scopely.paymentgateway.e2e.E2eTestLifecycle;
import com.scopely.paymentgateway.e2e.wiring.client.PaymentGatewayApiClientBuilder;
import com.scopely.paymentgateway.e2e.wiring.dagger.web.E2ePaymentGatewayWebModule;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;
import com.scopely.paymentgateway.model.gameconfig.GameConfigRespone;
import com.scopely.paymentgateway.model.gameconfig.ParametersConfig;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.providers.xsolla.config.XSollaClientConfig;
import com.scopely.paymentgateway.web.v1.controller.GameConfigEndpoint;
import jakarta.ws.rs.BadRequestException;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(E2eTestLifecycle.class)
public class ClientConfigurationTest {
  private GameConfigEndpoint gameConfigApiClient;

  @BeforeEach
  public void setUp() {
    gameConfigApiClient =
        new PaymentGatewayApiClientBuilder<>(
                GameConfigEndpoint.class, E2ePaymentGatewayWebModule.PORT)
            .withClaims("*:payments:admin")
            .build();
  }

  @Test
  public void testGetAndUpdateClientConfiguration() {
    var clientConfigMap = gameConfigApiClient.getGameConfig(API_KEY);
    assertThat(clientConfigMap).isNotNull();
    assertThat(clientConfigMap).containsKeys(PLAYGAMI_PAYMENTS, XSOLLA, DIGITAL_RIVER);

    // Verify each provider's configuration
    verifyPlaygamiPaymentsConfig(clientConfigMap.get(PLAYGAMI_PAYMENTS));
    verifyDigitalRiverConfig(clientConfigMap.get(DIGITAL_RIVER));
    verifyXsollaConfig(clientConfigMap.get(XSOLLA));

    // Update valid configuration
    var response =
        updateParameterConfig(
            ConfigurationProviderIdentifier.PLAYGAMI_PAYMENTS, "emailOrderReceiptFrom", "Playgami");
    assertThat(response.status()).isEqualTo(true);
    assertThat(response.message()).isEqualTo("Parameters added correctly.");

    // Update invalid configuration
    BadRequestException exception =
        assertThrows(
            BadRequestException.class,
            () ->
                updateParameterConfig(
                    ConfigurationProviderIdentifier.DIGITAL_RIVER,
                    "conversionFees",
                    "{\"EUR\"asd2:\"0.005\"}"));

    assertThat(exception.getResponse().getStatus()).isEqualTo(400);
    assertThat(exception.getResponse().readEntity(String.class))
        .contains("Invalid json conversion fees");
  }

  private void verifyPlaygamiPaymentsConfig(Object config) {
    var playgamiConfig = (PlaygamiPaymentsClientConfig) config;
    assertThat(playgamiConfig).isNotNull();
    assertThat(playgamiConfig.getPaymentProviderIdentifier())
        .isEqualTo(ConfigurationProviderIdentifier.PLAYGAMI_PAYMENTS);
    assertThat(playgamiConfig.getApiKey()).isEqualTo("fc2403cd-da8e-401f-a811-4d32f4b6e99d");
    assertThat(playgamiConfig.getEmailOrderReceiptFrom()).isEqualTo("Scopely");
    assertThat(playgamiConfig.getAllowedLanguages()).isInstanceOf(List.class);
    assertThat(playgamiConfig.getAllowedLanguages())
        .contains(
            "en", "fr", "de", "it", "es", "pt", "tr", "ja", "ko", "th", "zh", "zh_Hant", "nl", "ar",
            "pl", "ms");
    assertThat(playgamiConfig.getPostalCodes()).isNull();
  }

  private void verifyDigitalRiverConfig(Object config) {
    var digitalRiverConfig = (DigitalRiverClientConfig) config;
    assertThat(digitalRiverConfig).isNotNull();
    assertThat(digitalRiverConfig.getPaymentProviderIdentifier())
        .isEqualTo(ConfigurationProviderIdentifier.DIGITAL_RIVER);
    assertThat(digitalRiverConfig.getPostalCodes()).isNotNull();
    assertThat(digitalRiverConfig.getDisclosureLinks()).isNotNull();
    assertThat(digitalRiverConfig.getConversionFees()).isNotNull();
  }

  private void verifyXsollaConfig(Object config) {
    var xsollaConfig = (XSollaClientConfig) config;
    assertThat(xsollaConfig).isNotNull();
    assertThat(xsollaConfig.getPaymentProviderIdentifier())
        .isEqualTo(ConfigurationProviderIdentifier.XSOLLA);
    assertThat(xsollaConfig.getAllowedLanguages()).isInstanceOf(List.class);
    assertThat(xsollaConfig.getAllowedLanguages())
        .contains(
            "en", "ar", "bg", "cn", "tw", "cs", "fr", "de", "he", "it", "ja", "ko", "pl", "pt",
            "ro", "ru", "es", "th", "tr", "vi");
    assertThat(xsollaConfig.getProjectId()).isEqualTo(187557);
    assertThat(xsollaConfig.getMerchantId()).isEqualTo(175134);
  }

  private GameConfigRespone updateParameterConfig(
      ConfigurationProviderIdentifier provider, String key, String value) {
    ParametersConfig requestConfig = new ParametersConfig(provider, key, value);
    return gameConfigApiClient.addParameterConfig(API_KEY, List.of(requestConfig));
  }
}
