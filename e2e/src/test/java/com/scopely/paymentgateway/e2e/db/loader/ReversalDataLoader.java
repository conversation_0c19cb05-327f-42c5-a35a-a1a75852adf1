package com.scopely.paymentgateway.e2e.db.loader;

import com.amazonaws.services.dynamodbv2.model.AttributeDefinition;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.GlobalSecondaryIndex;
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement;
import com.amazonaws.services.dynamodbv2.model.KeyType;
import com.amazonaws.services.dynamodbv2.model.Projection;
import com.amazonaws.services.dynamodbv2.model.ProjectionType;
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType;
import com.scopely.paymentgateway.e2e.db.DataLoader;
import com.scopely.paymentgateway.repositories.daos.ReversalDAO;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import java.util.Collection;
import java.util.List;

public class ReversalDataLoader implements DataLoader<ReversalDAO> {

  @Override
  public Collection<ReversalDAO> generateData() {
    return List.of();
  }

  @Override
  public int saveItems(DynamoDbMapper dynamoDbMapper) {
    return 0;
  }

  @Override
  public CreateTableRequest getCreateTableRequestV1() {
    return new CreateTableRequest()
        .withTableName(ReversalDAO.TABLE)
        .withAttributeDefinitions(
            new AttributeDefinition(ReversalDAO.HASH_KEY, ScalarAttributeType.S),
            new AttributeDefinition(ReversalDAO.RANGE_KEY, ScalarAttributeType.S),
            new AttributeDefinition(ReversalDAO.API_KEY_USER, ScalarAttributeType.S))
        .withKeySchema(
            new KeySchemaElement(ReversalDAO.HASH_KEY, KeyType.HASH),
            new KeySchemaElement(ReversalDAO.RANGE_KEY, KeyType.RANGE))
        .withGlobalSecondaryIndexes(
            new GlobalSecondaryIndex()
                .withIndexName(ReversalDAO.HEADER_INDEX_NAME)
                .withKeySchema(new KeySchemaElement(ReversalDAO.RANGE_KEY, KeyType.HASH))
                .withProjection(new Projection().withProjectionType(ProjectionType.ALL)),
            new GlobalSecondaryIndex()
                .withIndexName(ReversalDAO.APIKEY_USER_INDEX_NAME)
                .withKeySchema(new KeySchemaElement(ReversalDAO.API_KEY_USER, KeyType.HASH))
                .withProjection(new Projection().withProjectionType(ProjectionType.ALL)));
  }
}
