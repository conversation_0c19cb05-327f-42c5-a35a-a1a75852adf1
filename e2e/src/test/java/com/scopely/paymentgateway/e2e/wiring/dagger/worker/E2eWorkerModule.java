package com.scopely.paymentgateway.e2e.wiring.dagger.worker;

import static com.scopely.paymentgateway.worker.module.SqsQueueModule.EMAIL_QUEUE_CONSUMERS;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.affinity.AffinityHelper;
import com.scopely.paymentgateway.config.WorkerQueuesConfig;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eCoreModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeEvent;
import com.scopely.paymentgateway.modules.RepositoriesModule;
import com.scopely.paymentgateway.worker.CommandBasedQueueMessageProcessor;
import com.scopely.paymentgateway.worker.model.ForwarderQueueMessageParser;
import com.scopely.paymentgateway.worker.module.SqsQueueModule;
import com.scopely.paymentgateway.worker.module.WorkerModule;
import com.scopely.paymentgateway.worker.services.ForwarderQueueMessageProcessor;
import com.scopely.paymentgateway.worker.services.disputesmanagement.UserDisputesManagementTaskProcessor;
import com.scopely.proteus.service.ProteusServiceConstants;
import com.scopely.proteus.sqs.consumer.FailedMessageHandlerResult;
import com.scopely.proteus.sqs.consumer.QueueConsumerService;
import com.scopely.proteus.sqs.consumer.QueueConsumerServiceBuilder;
import com.timgroup.statsd.StatsDClient;
import dagger.Module;
import dagger.Provides;
import dagger.multibindings.IntoSet;
import java.time.Duration;
import java.util.Set;
import javax.inject.Named;
import javax.inject.Singleton;
import software.amazon.awssdk.services.sqs.SqsClient;

@Module(
    includes = {
      E2eCoreModule.class,
      SqsQueueModule.class,
      E2eServicesModule.class,
      RepositoriesModule.class
    })
public class E2eWorkerModule {

  @Provides
  @Named(ProteusServiceConstants.SERVICE_NAME)
  String serviceName() {
    return "payment-gateway-worker";
  }

  @Provides
  static QueueConsumerService<QueueMessage> createEmailQueueForwarder(
      WorkerQueuesConfig queueConfig,
      StatsDClient statsDClient,
      SqsClient sqsClient,
      @Named(WorkerModule.EMAIL_QUEUE_MESSAGE_FORWARDER)
          ForwarderQueueMessageProcessor forwarderQueueMessageProcessor,
      ObjectMapper mapper,
      AffinityHelper affinityHelper) {
    return new QueueConsumerServiceBuilder<QueueMessage>()
        .withQueueName(
            affinityHelper.getQueueNameForCurrentAffinity(queueConfig.emailQueueUrlPattern()))
        .withMessageParser(new ForwarderQueueMessageParser(statsDClient, mapper))
        .withMessageReadBatchSize(10)
        .withMessageReadWaitTime(Duration.ofSeconds(20))
        .withKeepInFlightTime(Duration.ofMinutes(15))
        .withNumProcessingThreads(5)
        .withProcessWorkQueueSize(20)
        .withTerminationGracePeriod(Duration.ofSeconds(5))
        .withSqsClient(sqsClient)
        .withMessageProcessor(forwarderQueueMessageProcessor)
        .build();
  }

  @Provides
  @Named(WorkerModule.EMAIL_QUEUE_MESSAGE_FORWARDER)
  static ForwarderQueueMessageProcessor emailForwarderProcessor(
      StatsDClient statsDClient,
      @Named(EMAIL_QUEUE_CONSUMERS)
          Set<CommandBasedQueueMessageProcessor<QueueMessage>> consumers) {
    return new ForwarderQueueMessageProcessor(consumers, statsDClient);
  }

  @Provides
  @Singleton
  @IntoSet
  static QueueConsumerService<?> disputeQueueMessageProcessor(
      WorkerQueuesConfig config,
      ObjectMapper objectMapper,
      SqsClient sqsClient,
      UserDisputesManagementTaskProcessor processor,
      AffinityHelper affinityHelper) {
    return new QueueConsumerServiceBuilder<DisputeEvent>()
        .withQueueName(
            affinityHelper.getQueueNameForCurrentAffinity(config.disputeQueueUrlPattern()))
        .withMessageReadBatchSize(10)
        .withMessageReadWaitTime(Duration.ofSeconds(20))
        .withKeepInFlightTime(Duration.ofMinutes(15))
        .withNumProcessingThreads(5)
        .withProcessWorkQueueSize(20)
        .withTerminationGracePeriod(Duration.ofSeconds(5))
        .withJsonMessageParser(objectMapper, DisputeEvent.class)
        .withSqsClient(sqsClient)
        .withMessageProcessor(processor)
        .withFailedMessageHandler((body, receipt) -> FailedMessageHandlerResult.SHOULD_KEEP)
        .build();
  }
}
