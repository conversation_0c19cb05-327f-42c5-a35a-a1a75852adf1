package com.scopely.paymentgateway.e2e.wiring.dagger.core;

import com.scopely.circuitbreaker.CircuitBreaker;
import com.scopely.circuitbreaker.CircuitBreakerConfig;
import com.scopely.circuitbreaker.FunnelCircuitBreakerFactory;
import com.timgroup.statsd.StatsDClient;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public class E2eFunnelCircuitBreakerFactory implements FunnelCircuitBreakerFactory {
  private final FunnelCircuitBreakerFactory internalFactory;
  private ConcurrentMap<String, CircuitBreaker> circuitBreakers;

  public E2eFunnelCircuitBreakerFactory(StatsDClient statsDClient) {
    this.internalFactory = FunnelCircuitBreakerFactory.newInstance(statsDClient);
    this.circuitBreakers = new ConcurrentHashMap<>();
  }

  @Override
  public CircuitBreaker forEntryStep(String name, CircuitBreakerConfig circuitBreakerConfig) {
    var circuitBreaker = internalFactory.forEntryStep(name, circuitBreakerConfig);
    circuitBreakers.put(name, circuitBreaker);
    return circuitBreaker;
  }

  @Override
  public CircuitBreaker forNonEntryStep(String name, CircuitBreakerConfig circuitBreakerConfig) {
    var circuitBreaker = internalFactory.forNonEntryStep(name, circuitBreakerConfig);
    circuitBreakers.put(name, circuitBreaker);
    return circuitBreaker;
  }

  public CircuitBreaker getCircuitBreaker(String name) {
    return circuitBreakers.get(name);
  }
}
