package com.scopely.paymentgateway.e2e.tests;

import static com.scopely.paymentgateway.services.abtest.ABTestService.USER_COUNTRY;
import static com.scopely.paymentgateway.services.abtest.ABTestService.USER_PROVIDER;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.common.util.concurrent.Futures;
import com.scopely.arche.abtest.v2.dto.ExperimentMappingDTO;
import com.scopely.arche.abtest.v2.dto.ValuesForUserRequestDTO;
import com.scopely.arche.grpc.segmentation.AllSegmentsUserIn;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule;
import com.scopely.paymentgateway.model.dto.createpayment.ItemDTO;
import com.scopely.paymentgateway.model.dto.createpayment.LocalizedPriceDTO;
import com.scopely.paymentgateway.model.dto.createpayment.ProviderItemDTO;
import com.scopely.paymentgateway.model.dto.createpayment.UserDTO;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.user.UserBlockActionType;
import com.scopely.playerprofile.v1.dto.PlayerProfileInformationDTO;
import com.scopely.titan.model.BuildType;
import com.scopely.titan.model.ClientEventDto;
import com.scopely.titan.model.EventPriority;
import com.scopely.titan.model.EventStreamEntry;
import com.scopely.titan.model.EventType;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.mockito.ArgumentCaptor;

public final class TestDataHelper {
  public static final String PROVIDER_PP_ATTR = "payment_provider";
  public static final String API_KEY = "fc2403cd-da8e-401f-a811-4d32f4b6e99d";
  public static final UserDTO USER_DR_1 =
      new UserDTO("DR1", "drone", "user", "<EMAIL>", true);
  public static final UserDTO USER_DR_BLOCKED =
      new UserDTO("DR2", "drtwo", "user", "<EMAIL>", false);
  public static final UserDTO USER_DR_3 =
      new UserDTO("DR3", "drthree", "user", "<EMAIL>", false);
  public static final UserDTO USER_XSOLLA_1 =
      new UserDTO("XS1", "xsone", "user", "<EMAIL>", true);
  public static final UserDTO USER_XSOLLA_BLOCKED =
      new UserDTO("XS2", "xstwo", "user", "<EMAIL>", false);
  public static final UserDTO USER_1 =
      new UserDTO("USER1", "userone", "user", "<EMAIL>", false);
  public static final ItemDTO ITEM_EXPLICIT_PRICES =
      new ItemDTO(
          "sku1", "item1", 1, buildPrice("20.99"), buildLocalizedPrice("20.45"), null, null);
  public static final ItemDTO ITEM_ZERO_ENDING_PRICE =
      new ItemDTO("sku1", "item1", 1, buildPrice("8.99"), buildLocalizedPrice("10"), null, null);
  public static final ItemDTO ITEM_WITH_QUANTITY =
      new ItemDTO(
          "sku1", "item1", 5, buildPrice("20.99"), buildLocalizedPrice("20.45"), null, null);
  public static final ItemDTO ITEM_MULTI_CURRENCY =
      new ItemDTO("sku2", "item2", 1, buildPrice("4.99"), null, null, null);
  public static final ItemDTO ITEM_SKU =
      new ItemDTO(
          "sku3", "item3", 1, null, null, null, List.of(new ProviderItemDTO("playgami", "psku3")));
  public static final String US_IP = "**********";
  public static final String US = "US";
  public static final String USD = "USD";
  public static final String EUR = "EUR";
  public static final String ENGLISH = Locale.ENGLISH.getLanguage();
  public static final String POSTAL_CODE = "90232";
  public static final String XSOLLA = "XSOLLA";
  public static final String DIGITAL_RIVER = "DIGITAL_RIVER";
  public static final String PLAYGAMI_PAYMENTS = "PLAYGAMI_PAYMENTS";
  public static final String CONTEXT_PROPERTIES = "TEST_CONTEXT_PROPERTIES";

  private TestDataHelper() {}

  private static LocalizedPriceDTO buildLocalizedPrice(String decimalValue) {
    return new LocalizedPriceDTO(buildPrice(decimalValue), EUR);
  }

  private static PaymentBigDecimal buildPrice(String decimalValue) {
    return new PaymentBigDecimal(new BigDecimal(decimalValue));
  }

  public static UserDTO buildNewUser() {
    return buildNewUser(UUID.randomUUID().toString());
  }

  public static UserDTO buildNewUser(String id) {
    return new UserDTO(id, "drone", "user", "<EMAIL>", true);
  }

  public static void configurePlayerProfileProvider(
      String apiKey, String userId, PaymentProviderIdentifier provider) {
    var response =
        new PlayerProfileInformationDTO.Builder()
            .putAttributes(PROVIDER_PP_ATTR, provider.getDescription())
            .build();
    when(E2eServicesModule.PLAYER_PROFILE_API.getAttribute(apiKey, userId, PROVIDER_PP_ATTR))
        .thenReturn(CompletableFuture.completedFuture(response));
  }

  public static ClientEventDto verifyTitanEventStreamEntry(String expectedUserId) {
    return verifyTitanEventStreamEntries(expectedUserId).findFirst().orElseThrow();
  }

  public static Stream<ClientEventDto> verifyTitanEventStreamEntries(String expectedUserId) {
    var eventStreamCaptor = ArgumentCaptor.forClass(EventStreamEntry.class);
    var titanClient = E2eServicesModule.TITAN_CLIENT;
    await()
        .atMost(Duration.ofSeconds(5))
        .untilAsserted(
            () -> verify(titanClient, atLeastOnce()).sendEvent(eventStreamCaptor.capture()));
    List<EventStreamEntry> entries = eventStreamCaptor.getAllValues();
    for (EventStreamEntry entry : entries) {
      assertThat(entry.getApiKey()).isEqualTo(TestDataHelper.API_KEY);
      assertThat(entry.getClientIp()).isNull();
      assertThat(entry.getEventPayload().getBuildType()).isEqualTo(BuildType.PRODUCTION);
      assertThat(entry.getEventPayload().getContext().getTenant()).isEqualTo(expectedUserId);
      assertThat(entry.getEventPayload().getEvents()).hasSize(1);
    }
    return entries.stream().map(entry -> entry.getEventPayload().getEvents().getFirst());
  }

  public static void verifyDisputeEvent(
      ClientEventDto titanEvent, String expectedDisputeId, String expectedStatus) {
    assertThat(titanEvent.getName()).isEqualTo("playgami.payment_dispute");
    assertThat(titanEvent.getEventType()).isEqualTo(EventType.GAME);
    assertThat(titanEvent.getPriority()).isEqualTo(EventPriority.MEDIUM);
    assertThat(titanEvent.getProperties())
        .containsEntry("dispute_id", expectedDisputeId)
        .containsEntry("status", expectedStatus);
  }

  public static void verifyUserBlockEvent(
      ClientEventDto titanEvent, PaymentProviderIdentifier expectedProvider, boolean expectedVip) {
    assertThat(titanEvent.getName()).isEqualTo("playgami.payment_userblock");
    assertThat(titanEvent.getEventType()).isEqualTo(EventType.GAME);
    assertThat(titanEvent.getPriority()).isEqualTo(EventPriority.MEDIUM);
    assertThat(titanEvent.getProperties())
        .containsEntry("reason", "too-many-disputes")
        .containsEntry("is_blocked", true)
        .containsEntry("payment_provider", expectedProvider.name())
        .containsEntry("is_vip", expectedVip)
        .containsEntry("action_type", UserBlockActionType.AUTO.getDescription());
  }

  public static void mockSegmentationApi(
      String apiKey, String userId, Predicate<String> userInSegment) {
    when(E2eServicesModule.SEGMENTATION_API.getAllSegmentsUserIn(
            eq(apiKey), anySet(), eq(userId), anyMap(), eq(false)))
        .thenAnswer(
            invocation -> {
              Set<String> requestSegments = invocation.getArgument(1);
              Set<String> responseSegments =
                  requestSegments.stream().filter(userInSegment).collect(Collectors.toSet());
              var response =
                  AllSegmentsUserIn.newBuilder().addAllSegmentId(responseSegments).build();
              return Futures.immediateFuture(response);
            });
  }

  public static void mockArcheABtestApiResponse(
      String apiKey, String userId, String country, PaymentProviderIdentifier provider) {
    CompletionStage<List<ExperimentMappingDTO>> response =
        CompletableFuture.completedFuture(List.of());
    var requestDto =
        new ValuesForUserRequestDTO.Builder()
            .putAllUserProperties(
                Map.of(
                    USER_COUNTRY, country,
                    USER_PROVIDER, provider))
            .setSourceId("")
            .build();
    when(E2eServicesModule.ARCHE_ABTEST_API.findExperimentMappings(apiKey, userId, requestDto))
        .thenReturn(response);
  }
}
