package com.scopely.paymentgateway.e2e.wiring.aws;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.amazonaws.services.simplesystemsmanagement.AWSSimpleSystemsManagement;
import com.amazonaws.services.simplesystemsmanagement.model.DeleteParameterRequest;
import com.amazonaws.services.simplesystemsmanagement.model.DeleteParameterResult;
import com.amazonaws.services.simplesystemsmanagement.model.GetParametersByPathRequest;
import com.amazonaws.services.simplesystemsmanagement.model.GetParametersByPathResult;
import com.amazonaws.services.simplesystemsmanagement.model.Parameter;
import com.amazonaws.services.simplesystemsmanagement.model.ParameterType;
import com.amazonaws.services.simplesystemsmanagement.model.PutParameterRequest;
import com.amazonaws.services.simplesystemsmanagement.model.PutParameterResult;
import java.io.IOException;
import java.util.Properties;

public class ParameterStoreMock {
  private static final String LIST_MARK = "@LIST;";
  private final Properties properties;

  private ParameterStoreMock(Properties properties) {
    this.properties = properties;
  }

  public static AWSSimpleSystemsManagement getClient(String propertiesFile) {
    ParameterStoreMock store = new ParameterStoreMock(readProperties(propertiesFile));
    AWSSimpleSystemsManagement ssmClient = mock(AWSSimpleSystemsManagement.class);
    when(ssmClient.getParametersByPath(any(GetParametersByPathRequest.class)))
        .thenAnswer(
            invocation ->
                store.getParametersByPath(
                    invocation.getArgument(0, GetParametersByPathRequest.class)));
    when(ssmClient.putParameter(any(PutParameterRequest.class)))
        .thenAnswer(
            invocation -> store.putParameter(invocation.getArgument(0, PutParameterRequest.class)));
    when(ssmClient.deleteParameter(any(DeleteParameterRequest.class)))
        .thenAnswer(
            invocation ->
                store.deleteParameter(invocation.getArgument(0, DeleteParameterRequest.class)));
    return ssmClient;
  }

  private static Properties readProperties(String propertiesFile) {
    Properties properties = new Properties();
    try {
      properties.load(
          Thread.currentThread().getContextClassLoader().getResourceAsStream(propertiesFile));
    } catch (IOException e) {
      throw new IllegalArgumentException("Missing properties file: " + propertiesFile, e);
    }
    return properties;
  }

  private GetParametersByPathResult getParametersByPath(GetParametersByPathRequest request) {
    var params =
        properties.stringPropertyNames().stream()
            .filter(key -> key.startsWith(request.getPath()))
            .map(this::buildParameter)
            .toList();
    return new GetParametersByPathResult().withParameters(params);
  }

  private Parameter buildParameter(String key) {
    String rawValue = properties.getProperty(key);
    if (rawValue != null && rawValue.startsWith(LIST_MARK)) {
      return new Parameter()
          .withName(key)
          .withType(ParameterType.StringList)
          .withValue(rawValue.substring(LIST_MARK.length()));
    }
    return new Parameter().withName(key).withType(ParameterType.String).withValue(rawValue);
  }

  private PutParameterResult putParameter(PutParameterRequest req) {
    Object previousValue;
    if (ParameterType.StringList.toString().equals(req.getType()) && req.getValue() != null) {
      previousValue = properties.setProperty(req.getName(), LIST_MARK + req.getValue());
    } else {
      previousValue = properties.setProperty(req.getName(), req.getValue());
    }
    return new PutParameterResult().withVersion(previousValue == null ? 1L : 2L);
  }

  private DeleteParameterResult deleteParameter(DeleteParameterRequest request) {
    Object previousValue = properties.remove(request.getName());
    DeleteParameterResult result = mock(DeleteParameterResult.class);
    when(result.toString()).thenReturn(previousValue == null ? "ERROR" : "{}");
    return result;
  }
}
