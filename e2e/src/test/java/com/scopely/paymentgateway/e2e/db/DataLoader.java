package com.scopely.paymentgateway.e2e.db;

import com.scopely.proteus.dynamodb.DynamoDbMapper;
import java.io.IOException;
import java.util.Collection;
import software.amazon.awssdk.services.dynamodb.model.CreateTableRequest;

public interface DataLoader<T> {

  Collection<T> generateData() throws IOException;

  // TODO: Remove this method when all the repositories are migrated to V2
  default boolean isV1() {
    return true;
  }

  // returns the number of items saved
  int saveItems(DynamoDbMapper dynamoDbMapper) throws IOException;

  // TODO: Remove this method when all the repositories are migrated to V2
  com.amazonaws.services.dynamodbv2.model.CreateTableRequest getCreateTableRequestV1();

  // TODO: Don't make this method default when all the repositories are migrated to V2
  default CreateTableRequest getCreateTableRequest() {
    return null;
  }
}
