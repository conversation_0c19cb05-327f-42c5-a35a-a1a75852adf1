package com.scopely.paymentgateway.e2e.wiring.dagger.core;

import static com.scopely.paymentgateway.modules.CoreModule.AB_TEST_TOKEN_SUPPLIER;
import static com.scopely.paymentgateway.modules.CoreModule.METRICS_CATALOG_TOKEN_SUPPLIER;
import static com.scopely.paymentgateway.modules.CoreModule.PLAYER_PROFILE_TOKEN_SUPPLIER;
import static org.mockito.Mockito.mock;

import com.amazonaws.services.simplesystemsmanagement.AWSSimpleSystemsManagement;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.geofencing.client.service.GeofencingService;
import com.scopely.paymentgateway.config.ABTestingConfig;
import com.scopely.paymentgateway.config.AWSConfig;
import com.scopely.paymentgateway.config.MetricsCatalogConfig;
import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.config.PlayerProfileConfig;
import com.scopely.paymentgateway.e2e.wiring.aws.ParameterStoreMock;
import com.scopely.paymentgateway.e2e.wiring.aws.sqs.SqsMock;
import com.scopely.paymentgateway.parsing.ObjectMapperFactory;
import com.scopely.proteus.config.Config;
import com.scopely.proteus.config.ConfigBackedValuesSupplier;
import com.scopely.proteus.config.ConfigObjectAssembler;
import com.scopely.proteus.identity.SatelliteClientTokenSupplier;
import com.scopely.proteus.infrastructure.AWSClient;
import com.scopely.proteus.monitoring.StatsDClientSupplier;
import dagger.Module;
import dagger.Provides;
import java.time.Clock;
import javax.inject.Named;
import javax.inject.Singleton;
import software.amazon.awssdk.services.sqs.SqsClient;

@Module(includes = {E2eDynamoModule.class})
public class E2eCoreModule {
  public static final SqsMock SQS = new SqsMock();
  public static final GeofencingService GEOFENCING_SERVICE = mock(GeofencingService.class);

  @Provides
  @Singleton
  ObjectMapper jacksonMapper() {
    return ObjectMapperFactory.get();
  }

  @Singleton
  @Provides
  @Named(AB_TEST_TOKEN_SUPPLIER)
  SatelliteClientTokenSupplier satelliteClientTokenSupplierForArche(
      ABTestingConfig abTestingConfig,
      Config config,
      StatsDClientSupplier statsDClientSupplier,
      AWSClient awsClient) {
    return new SatelliteClientTokenSupplier(
        config, statsDClientSupplier, awsClient, abTestingConfig::archeJWT);
  }

  @Singleton
  @Provides
  @Named(PLAYER_PROFILE_TOKEN_SUPPLIER)
  SatelliteClientTokenSupplier satelliteClientTokenSupplierForPlayerProfile(
      PlayerProfileConfig playerProfileConfig,
      Config config,
      StatsDClientSupplier statsDClientSupplier,
      AWSClient awsClient) {
    return new SatelliteClientTokenSupplier(
        config, statsDClientSupplier, awsClient, playerProfileConfig::playerProfileJWT);
  }

  @Singleton
  @Provides
  @Named(METRICS_CATALOG_TOKEN_SUPPLIER)
  SatelliteClientTokenSupplier satelliteClientTokenSupplierForMEtricsCatalog(
      MetricsCatalogConfig metricsCatalogConfig,
      Config config,
      StatsDClientSupplier statsDClientSupplier,
      AWSClient awsClient) {
    return new SatelliteClientTokenSupplier(
        config, statsDClientSupplier, awsClient, metricsCatalogConfig::metricsCatalogJWT);
  }

  @Provides
  @Singleton
  PaymentGatewayConfig config(ConfigBackedValuesSupplier configBackedValuesSupplier) {
    return new ConfigObjectAssembler(configBackedValuesSupplier)
        .newInstanceOf(PaymentGatewayConfig.class);
  }

  @Provides
  Clock clock() {
    return Clock.systemDefaultZone();
  }

  @Provides
  @Singleton
  AWSSimpleSystemsManagement ssmClient() {
    return ParameterStoreMock.getClient("ssm.properties");
  }

  @Provides
  @Singleton
  AWSConfig awsConfig(ConfigBackedValuesSupplier configBackedValuesSupplier) {
    return new ConfigObjectAssembler(configBackedValuesSupplier).newInstanceOf(AWSConfig.class);
  }

  @Provides
  SqsClient sqsClient() {
    return SQS.getAwsClientV2();
  }

  @Provides
  @Singleton
  GeofencingService geofencingService() {
    return GEOFENCING_SERVICE;
  }
}
