package com.scopely.paymentgateway.e2e.tests.digitalriver;

import static com.scopely.paymentgateway.analytics.events.EventName.PLAYGAMI_PAYMENT_TRANSACTION;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.API_KEY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.CONTEXT_PROPERTIES;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ENGLISH;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.EUR;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ITEM_EXPLICIT_PRICES;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ITEM_MULTI_CURRENCY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ITEM_SKU;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.ITEM_WITH_QUANTITY;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.POSTAL_CODE;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.PROVIDER_PP_ATTR;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.US;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USD;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USER_1;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USER_DR_BLOCKED;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.configurePlayerProfileProvider;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.verifyTitanEventStreamEntries;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.verifyTitanEventStreamEntry;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildCheckoutResponse;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildCountryConversionResponse;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildCreateCustomerResponse;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildGetCustomerResponse;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildOrderResponse;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.buildSourceResponse;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.createNewPaymentRequestDTO;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.verifyCheckoutRequest;
import static com.scopely.paymentgateway.e2e.tests.digitalriver.DigitalRiverProviderHelper.verifyUpdateCheckoutRequest;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.AB_TEST_CONFIG_MAPPING_API;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.DIGITAL_RIVER_API;
import static com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule.XSOLLA_API;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.clearInvocations;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.scopely.paymentgateway.analytics.events.EventAttributesConstants;
import com.scopely.paymentgateway.e2e.E2eTestLifecycle;
import com.scopely.paymentgateway.e2e.tests.TestDataHelper;
import com.scopely.paymentgateway.e2e.wiring.client.PaymentGatewayApiClientBuilder;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eServicesModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.web.E2ePaymentGatewayWebModule;
import com.scopely.paymentgateway.model.dto.createpayment.CreatePaymentRequestDTO;
import com.scopely.paymentgateway.model.dto.createpayment.CreatePaymentResponseDTO;
import com.scopely.paymentgateway.model.dto.createpayment.ItemDTO;
import com.scopely.paymentgateway.model.dto.createpayment.UserDTO;
import com.scopely.paymentgateway.model.dto.updatepayment.UpdatePaymentRequestDTO;
import com.scopely.paymentgateway.model.payment.BillingAddress;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.model.payment.PaymentMethod;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import com.scopely.paymentgateway.model.payment.PricingMode;
import com.scopely.paymentgateway.model.payment.ProviderStatus;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.CheckoutRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.CheckoutResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.CreateCustomerRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.SourceCreationRequestData;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.UpdateCheckoutRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.UpdateCustomerRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.order.OrderCreationRequest;
import com.scopely.paymentgateway.web.v1.controller.DigitalRiverEndpoint;
import com.scopely.paymentgateway.web.v1.controller.PaymentSecuredEndpoint;
import com.scopely.paymentgateway.web.v1.controller.PaymentWidgetEndpoint;
import com.scopely.proteus.client.ApiClientException;
import com.scopely.titan.model.ClientEventDto;
import com.scopely.titan.model.EventPriority;
import com.scopely.titan.model.EventType;
import jakarta.ws.rs.ClientErrorException;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.jboss.resteasy.client.exception.WebApplicationExceptionWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(E2eTestLifecycle.class)
class CreditCardPaymentE2eTest {
  private PaymentSecuredEndpoint paymentApiClient;
  private PaymentWidgetEndpoint widgetApiClient;
  private DigitalRiverEndpoint digitalRiverSourceApiClient;

  @BeforeEach
  public void setUp() {
    var port = E2ePaymentGatewayWebModule.PORT;
    paymentApiClient =
        new PaymentGatewayApiClientBuilder<>(PaymentSecuredEndpoint.class, port)
            .withClaims("*:payments:*")
            .build();
    widgetApiClient =
        new PaymentGatewayApiClientBuilder<>(PaymentWidgetEndpoint.class, port)
            .withClaims("*:payments:*")
            .build();
    digitalRiverSourceApiClient =
        new PaymentGatewayApiClientBuilder<>(DigitalRiverEndpoint.class, port).build();
    reset(E2eServicesModule.TITAN_CLIENT, XSOLLA_API, DIGITAL_RIVER_API);
  }

  @Test
  void testSuccessfulPaymentWithExplicitPrices() {
    String trackingId = UUID.randomUUID().toString();
    var user = TestDataHelper.buildNewUser();
    var item = ITEM_EXPLICIT_PRICES;
    configurePlayerProfileProvider(API_KEY, user.userId(), PaymentProviderIdentifier.DIGITAL_RIVER);
    var paymentRequest = createNewPaymentRequestDTO(item, trackingId, user, null);
    var providerCheckoutResponse = buildCheckoutResponse(ITEM_EXPLICIT_PRICES.price(), US, USD);
    var createPaymentResponseDTO =
        testCreateNewPaymentStepForNewUser(paymentRequest, providerCheckoutResponse, item);
    testWidgetRetrievesInfoStep(createPaymentResponseDTO.paymentId(), trackingId, user, item, EUR);
    testWidgetUpdatesPaymentStep(
        createPaymentResponseDTO.paymentId(), providerCheckoutResponse, user, item.price(), EUR);
    testCreateSourceStep(
        createPaymentResponseDTO.paymentId(), providerCheckoutResponse, user, item, item.price());
    testClaimPaymentStep(createPaymentResponseDTO.paymentId(), user, item, item.price(), EUR);
    verifyNoInteractions(XSOLLA_API);
  }

  @Test
  void testSuccessfulPaymentWithQuantity() {
    String trackingId = UUID.randomUUID().toString();
    var user = TestDataHelper.buildNewUser();
    var item = ITEM_WITH_QUANTITY;
    var unitPrice = item.localizedPrice().price();
    configurePlayerProfileProvider(API_KEY, user.userId(), PaymentProviderIdentifier.DIGITAL_RIVER);
    var paymentRequest = createNewPaymentRequestDTO(item, trackingId, user, null);
    var providerCheckoutResponse = buildCheckoutResponse(unitPrice, US, EUR);
    var createPaymentResponseDTO =
        testCreateNewPaymentStepForNewUser(paymentRequest, providerCheckoutResponse, item);
    var paymentId = createPaymentResponseDTO.paymentId();
    testWidgetRetrievesInfoStep(paymentId, trackingId, user, item, unitPrice.bigDecimal(), EUR);
    testWidgetUpdatesPaymentStep(paymentId, providerCheckoutResponse, user, unitPrice, EUR);
    var totalPrice =
        new PaymentBigDecimal(unitPrice.bigDecimal().multiply(BigDecimal.valueOf(item.quantity())));
    testCreateSourceStep(paymentId, providerCheckoutResponse, user, item, totalPrice);
    testClaimPaymentStep(paymentId, user, item, totalPrice, EUR);
    verifyNoInteractions(XSOLLA_API);
  }

  @Test
  void testSuccessfulPaymentWithMultiCurrency() {
    String trackingId = UUID.randomUUID().toString();
    var user = TestDataHelper.buildNewUser();
    var item = ITEM_MULTI_CURRENCY;
    var price = new PaymentBigDecimal(new BigDecimal("3.45"));
    var currency = USD;
    configurePlayerProfileProvider(API_KEY, user.userId(), PaymentProviderIdentifier.DIGITAL_RIVER);
    var paymentRequest = createNewPaymentRequestDTO(item, trackingId, user, null);
    var providerCheckoutResponse = buildCheckoutResponse(price, US, currency);
    var itemName = item.name();
    var itemAmount = item.price().bigDecimal();
    var createPaymentResponseDTO =
        testCreateNewPaymentStepForNewUser(
            paymentRequest,
            providerCheckoutResponse,
            itemName,
            itemAmount,
            currency,
            item.quantity());
    testWidgetRetrievesInfoStep(
        createPaymentResponseDTO.paymentId(), trackingId, user, item, price.bigDecimal(), currency);
    testWidgetUpdatesPaymentStep(
        createPaymentResponseDTO.paymentId(), providerCheckoutResponse, user, price, currency);
    testCreateSourceStep(
        createPaymentResponseDTO.paymentId(), providerCheckoutResponse, user, item, item.price());
    testClaimPaymentStep(createPaymentResponseDTO.paymentId(), user, item, item.price(), currency);
    verifyNoInteractions(XSOLLA_API);
  }

  @Test
  void testSuccessfulPaymentWithLocalizedPrice() {
    String trackingId = UUID.randomUUID().toString();
    var user = TestDataHelper.buildNewUser();
    var item = ITEM_SKU;
    var itemName = item.name();
    var itemAmount = new BigDecimal("1.23");
    var price = new PaymentBigDecimal(itemAmount);
    var currency = USD;
    configurePlayerProfileProvider(API_KEY, user.userId(), PaymentProviderIdentifier.DIGITAL_RIVER);
    var paymentRequest = createNewPaymentRequestDTO(item, trackingId, user, null);
    var providerCheckoutResponse = buildCheckoutResponse(price, US, currency);
    var createPaymentResponseDTO =
        testCreateNewPaymentStepForNewUser(
            paymentRequest,
            providerCheckoutResponse,
            itemName,
            itemAmount,
            currency,
            item.quantity());
    testWidgetRetrievesInfoStep(
        createPaymentResponseDTO.paymentId(), trackingId, user, item, itemAmount, currency);
    testWidgetUpdatesPaymentStep(
        createPaymentResponseDTO.paymentId(), providerCheckoutResponse, user, price, currency);
    testCreateSourceStep(
        createPaymentResponseDTO.paymentId(), providerCheckoutResponse, user, item, price);
    testClaimPaymentStep(createPaymentResponseDTO.paymentId(), user, item, price, currency);
    verifyNoInteractions(XSOLLA_API);
  }

  @Test
  void testRejectedPayment() {
    String trackingId = UUID.randomUUID().toString();
    var item = ITEM_EXPLICIT_PRICES;
    var checkoutRequest = createNewPaymentRequestDTO(item, trackingId, USER_DR_BLOCKED, null);
    var checkoutResponse =
        paymentApiClient.createNewPaymentEndpoint(API_KEY, CONTEXT_PROPERTIES, checkoutRequest);
    verifyNoInteractions(DIGITAL_RIVER_API);
    verifyNoInteractions(E2eServicesModule.XSOLLA_API);
    ClientEventDto event = verifyTitanEventStreamEntry(USER_DR_BLOCKED.userId());
    verifyBlockedUserEvent(event, trackingId);
    assertThrows(
        ClientErrorException.class,
        () ->
            testWidgetRetrievesInfoStep(
                checkoutResponse.paymentId(), trackingId, USER_DR_BLOCKED, item, EUR));
  }

  @Test
  void testSuccessPaymentCreationWithExternalIdWithNewUser() {
    String trackingId = UUID.randomUUID().toString();
    String externalId = UUID.randomUUID().toString();
    var user = TestDataHelper.buildNewUser();
    var item = ITEM_EXPLICIT_PRICES;
    configurePlayerProfileProvider(API_KEY, user.userId(), PaymentProviderIdentifier.DIGITAL_RIVER);
    var paymentRequest = createNewPaymentRequestDTO(item, trackingId, user, externalId);
    var providerCheckoutResponse = buildCheckoutResponse(item.price(), US, USD);
    var createPaymentResponseDTO =
        testCreateNewPaymentStepForNewUser(paymentRequest, providerCheckoutResponse, item);
    assertThat(createPaymentResponseDTO.externalId()).isEqualTo(externalId);

    testWidgetRetrievesInfoStep(createPaymentResponseDTO.paymentId(), trackingId, user, item, EUR);
    testWidgetUpdatesPaymentStep(
        createPaymentResponseDTO.paymentId(), providerCheckoutResponse, user, item.price(), EUR);
    testCreateSourceStep(
        createPaymentResponseDTO.paymentId(), providerCheckoutResponse, user, item, item.price());
    testClaimPaymentStep(createPaymentResponseDTO.paymentId(), user, item, item.price(), EUR);
  }

  @Test
  void testFailedPaymentCreationDueToCompletedPaymentWithExternalId() {
    String trackingId = UUID.randomUUID().toString();
    var externalId = "DR008-external-id";
    var userDto = TestDataHelper.buildNewUser("DR-with-external-id");
    var paymentRequest =
        createNewPaymentRequestDTO(ITEM_EXPLICIT_PRICES, trackingId, userDto, externalId);
    assertThrows(
        ClientErrorException.class,
        () ->
            paymentApiClient.createNewPaymentEndpoint(API_KEY, CONTEXT_PROPERTIES, paymentRequest));
    verifyNoInteractions(DIGITAL_RIVER_API);
    verifyNoInteractions(E2eServicesModule.XSOLLA_API);
  }

  @Test
  void testFailedPaymentDueToConflictWithExternalId() {
    String trackingId = UUID.randomUUID().toString();
    String externalId = UUID.randomUUID().toString();
    var item = ITEM_EXPLICIT_PRICES;
    var user = TestDataHelper.buildNewUser();
    configurePlayerProfileProvider(API_KEY, user.userId(), PaymentProviderIdentifier.DIGITAL_RIVER);
    var paymentRequest = createNewPaymentRequestDTO(item, trackingId, user, externalId);
    var providerCheckoutResponse = buildCheckoutResponse(item.price(), US, USD);

    var createFirstPaymentResponseDTO =
        testCreateNewPaymentStepForNewUser(paymentRequest, providerCheckoutResponse, item);
    assertThat(createFirstPaymentResponseDTO.externalId()).isEqualTo(externalId);

    var createSecondPaymentResponseDTO =
        testCreateNewPaymentForOldUserStep(paymentRequest, providerCheckoutResponse);
    assertThat(createSecondPaymentResponseDTO.externalId()).isEqualTo(externalId);

    var firstPaymentId = createFirstPaymentResponseDTO.paymentId();
    var secondPaymentId = createSecondPaymentResponseDTO.paymentId();
    testWidgetRetrievesInfoStep(firstPaymentId, trackingId, user, item, EUR);
    testWidgetRetrievesInfoStep(secondPaymentId, trackingId, user, item, EUR);

    testWidgetUpdatesPaymentStep(firstPaymentId, providerCheckoutResponse, user, item.price(), EUR);
    testWidgetUpdatesPaymentStep(
        secondPaymentId, providerCheckoutResponse, user, item.price(), EUR);

    testCreateSourceStep(firstPaymentId, providerCheckoutResponse, user, item, item.price());

    clearInvocations(DIGITAL_RIVER_API);
    var sourceRequest =
        new SourceCreationRequestData(
            secondPaymentId,
            "any-source-id",
            PaymentMethod.CREDIT_CARD.getDescription(),
            false,
            false);
    assertThrows(
        ClientErrorException.class,
        () -> digitalRiverSourceApiClient.digitalRiverSourceCreation(sourceRequest));
  }

  @Test
  void testProviderConfiguredInPlayerProfile() {
    configurePlayerProfileProvider(
        API_KEY, USER_1.userId(), PaymentProviderIdentifier.DIGITAL_RIVER);
    String trackingId = UUID.randomUUID().toString();
    var providerCheckoutResponse = buildCheckoutResponse(ITEM_EXPLICIT_PRICES.price(), US, USD);
    String paymentId = testCreateNewPaymentStep(trackingId, providerCheckoutResponse, USER_1);
    verifyNoInteractions(XSOLLA_API);
    assertThat(paymentId).isNotNull();
    verify(E2eServicesModule.PLAYER_PROFILE_API)
        .getAttribute(API_KEY, USER_1.userId(), PROVIDER_PP_ATTR);
  }

  @Test
  void testFailedOrderInsufficientFunds() {
    String trackingId = UUID.randomUUID().toString();
    var item = ITEM_EXPLICIT_PRICES;
    var user = TestDataHelper.buildNewUser();
    var paymentRequest = createNewPaymentRequestDTO(item, trackingId, user, null);
    var providerCheckoutResponse = buildCheckoutResponse(item.price(), US, USD);
    var createPaymentResponseDTO =
        testCreateNewPaymentStepForNewUser(paymentRequest, providerCheckoutResponse, item);
    testWidgetRetrievesInfoStep(createPaymentResponseDTO.paymentId(), trackingId, user, item, EUR);
    testWidgetUpdatesPaymentStep(
        createPaymentResponseDTO.paymentId(), providerCheckoutResponse, user, item.price(), EUR);
    testCreateSourceStepWithFailedOrder(
        createPaymentResponseDTO.paymentId(), providerCheckoutResponse, user);
    verifyTitanEventStreamEntries(user.userId())
        .filter(e -> e.getName().equals(PLAYGAMI_PAYMENT_TRANSACTION.getName()))
        .findAny()
        .ifPresent(event -> verifyTransactionErrorEvent(event, trackingId));
    verifyNoInteractions(XSOLLA_API);
  }

  private void verifyTransactionErrorEvent(ClientEventDto event, String trackingId) {
    assertThat(event.getName()).isEqualTo(PLAYGAMI_PAYMENT_TRANSACTION.getName());
    assertThat(event.getEventType()).isEqualTo(EventType.GAME);
    assertThat(event.getPriority()).isEqualTo(EventPriority.MEDIUM);
    assertThat(event.getProperties())
        .containsEntry(EventAttributesConstants.TRACKING_ID, trackingId)
        .containsEntry(EventAttributesConstants.STATUS, "failed")
        .containsEntry(EventAttributesConstants.STATUS_MESSAGE, "insufficient_funds");
  }

  private void verifyBlockedUserEvent(ClientEventDto event, String trackingId) {
    assertThat(event.getName()).isEqualTo("playgami.tracking");
    assertThat(event.getEventType()).isEqualTo(EventType.GAME);
    assertThat(event.getPriority()).isEqualTo(EventPriority.MEDIUM);
    assertThat(event.getProperties())
        .containsEntry(EventAttributesConstants.TRACKING_ID, trackingId)
        .containsEntry(EventAttributesConstants.STEP_NAME, "blocked_user_checkout");
    assertThat(event.getProperties().get("process_attributes"))
        .asString()
        .contains("\"game_sku\":\"sku1\"")
        .contains("\"is_vip\":\"false\"");
  }

  private String testCreateNewPaymentStep(
      String trackingId, CheckoutResponse providerResponse, UserDTO userDTO) {
    when(DIGITAL_RIVER_API.retrieveTaxesForSpecificCountryAndCurrency(anyString(), eq(US), eq(USD)))
        .thenReturn(buildCountryConversionResponse(US, USD, BigDecimal.ONE, BigDecimal.ONE));
    when(DIGITAL_RIVER_API.createCustomer(anyString(), any(CreateCustomerRequest.class)))
        .thenReturn(buildCreateCustomerResponse());
    when(DIGITAL_RIVER_API.createCheckout(anyString(), any(CheckoutRequest.class)))
        .thenReturn(providerResponse);
    var checkoutRequest =
        createNewPaymentRequestDTO(ITEM_EXPLICIT_PRICES, trackingId, userDTO, null);

    var checkoutResponse =
        paymentApiClient.createNewPaymentEndpoint(API_KEY, CONTEXT_PROPERTIES, checkoutRequest);
    assertThat(checkoutResponse.provider())
        .isEqualTo(PaymentProviderIdentifier.DIGITAL_RIVER.name());
    verify(DIGITAL_RIVER_API)
        .createCustomer(
            anyString(), argThat(request -> verifyCreateCustomerRequest(request, userDTO)));
    var itemName = ITEM_EXPLICIT_PRICES.name();
    var itemAmount = ITEM_EXPLICIT_PRICES.localizedPrice().price().bigDecimal();
    var quantity = ITEM_EXPLICIT_PRICES.quantity();
    verify(DIGITAL_RIVER_API)
        .createCheckout(
            anyString(),
            argThat(
                request ->
                    verifyCheckoutRequest(
                        request, API_KEY, itemName, itemAmount, EUR, quantity, checkoutResponse)));
    return checkoutResponse.paymentId();
  }

  private CreatePaymentResponseDTO testCreateNewPaymentStepForNewUser(
      CreatePaymentRequestDTO request, CheckoutResponse response, ItemDTO item) {
    var itemName = item.name();
    var itemAmount = item.localizedPrice().price().bigDecimal();
    var currency = item.localizedPrice().currency();
    var quantity = item.quantity();
    return testCreateNewPaymentStepForNewUser(
        request, response, itemName, itemAmount, currency, quantity);
  }

  private CreatePaymentResponseDTO testCreateNewPaymentStepForNewUser(
      CreatePaymentRequestDTO createPaymentRequestDTO,
      CheckoutResponse providerResponse,
      String itemName,
      BigDecimal price,
      String currency,
      int quantity) {
    when(DIGITAL_RIVER_API.retrieveTaxesForSpecificCountryAndCurrency(anyString(), eq(US), eq(USD)))
        .thenReturn(buildCountryConversionResponse(US, USD, BigDecimal.ONE, BigDecimal.ONE));
    when(DIGITAL_RIVER_API.createCustomer(anyString(), any(CreateCustomerRequest.class)))
        .thenReturn(buildCreateCustomerResponse());
    when(DIGITAL_RIVER_API.createCheckout(anyString(), any(CheckoutRequest.class)))
        .thenReturn(providerResponse);
    when(AB_TEST_CONFIG_MAPPING_API.findExperimentMappings(any(), any(), any()))
        .thenReturn(CompletableFuture.completedFuture(List.of()));
    var checkoutResponse =
        paymentApiClient.createNewPaymentEndpoint(
            API_KEY, CONTEXT_PROPERTIES, createPaymentRequestDTO);
    assertThat(checkoutResponse.provider())
        .isEqualTo(PaymentProviderIdentifier.DIGITAL_RIVER.name());
    verify(DIGITAL_RIVER_API)
        .createCheckout(
            anyString(),
            argThat(
                request ->
                    verifyCheckoutRequest(
                        request, API_KEY, itemName, price, currency, quantity, checkoutResponse)));
    verifyNoInteractions(E2eServicesModule.XSOLLA_API);
    return checkoutResponse;
  }

  private CreatePaymentResponseDTO testCreateNewPaymentForOldUserStep(
      CreatePaymentRequestDTO createPaymentRequestDTO, CheckoutResponse providerResponse) {
    clearInvocations(DIGITAL_RIVER_API);
    when(DIGITAL_RIVER_API.retrieveTaxesForSpecificCountryAndCurrency(anyString(), eq(US), eq(USD)))
        .thenReturn(buildCountryConversionResponse(US, USD, BigDecimal.ONE, BigDecimal.ONE));
    when(DIGITAL_RIVER_API.updateCustomer(
            anyString(), anyString(), any(UpdateCustomerRequest.class)))
        .thenReturn(buildCreateCustomerResponse());
    when(DIGITAL_RIVER_API.createCheckout(anyString(), any(CheckoutRequest.class)))
        .thenReturn(providerResponse);
    var checkoutResponse =
        paymentApiClient.createNewPaymentEndpoint(
            API_KEY, CONTEXT_PROPERTIES, createPaymentRequestDTO);
    assertThat(checkoutResponse.provider())
        .isEqualTo(PaymentProviderIdentifier.DIGITAL_RIVER.name());
    var itemName = ITEM_EXPLICIT_PRICES.name();
    var itemAmount = ITEM_EXPLICIT_PRICES.localizedPrice().price().bigDecimal();
    var quantity = ITEM_EXPLICIT_PRICES.quantity();
    var currency = ITEM_EXPLICIT_PRICES.localizedPrice().currency();
    verify(DIGITAL_RIVER_API)
        .createCheckout(
            anyString(),
            argThat(
                request ->
                    verifyCheckoutRequest(
                        request,
                        API_KEY,
                        itemName,
                        itemAmount,
                        currency,
                        quantity,
                        checkoutResponse)));
    verifyNoInteractions(E2eServicesModule.XSOLLA_API);
    return checkoutResponse;
  }

  private boolean verifyCreateCustomerRequest(CreateCustomerRequest request, UserDTO userDTO) {
    return request.email().equals(userDTO.email())
        && request.metadata().get("apiKey").equals(API_KEY)
        && request.metadata().get("userId").equals(userDTO.userId())
        && request.metadata().get("firstName").equals(userDTO.firstName())
        && request.metadata().get("lastName").equals(userDTO.lastName())
        && request.type().equals("individual")
        && request.enabled();
  }

  private void testWidgetRetrievesInfoStep(
      String paymentId, String trackingId, UserDTO userDTO, ItemDTO item, String currency) {
    testWidgetRetrievesInfoStep(
        paymentId, trackingId, userDTO, item, item.price().bigDecimal(), currency);
  }

  private void testWidgetRetrievesInfoStep(
      String paymentId,
      String trackingId,
      UserDTO userDTO,
      ItemDTO item,
      BigDecimal price,
      String currency) {
    clearInvocations(DIGITAL_RIVER_API);
    when(DIGITAL_RIVER_API.getCustomer(anyString(), anyString()))
        .thenReturn(buildGetCustomerResponse(userDTO));
    var infoResponse = widgetApiClient.paymentInfoEndpointWithHeader(API_KEY, paymentId);
    var expectedPricingMode = item.price() == null ? PricingMode.SKU : PricingMode.EXPLICIT;
    assertThat(infoResponse.paymentId()).isEqualTo(paymentId);
    assertThat(infoResponse.apiKey()).isEqualTo(API_KEY);
    assertThat(infoResponse.provider().name())
        .isEqualTo(PaymentProviderIdentifier.DIGITAL_RIVER.name());
    assertThat(infoResponse.checkout().currency()).isEqualTo(currency);
    assertThat(infoResponse.checkout().country()).isEqualTo(US);
    assertThat(infoResponse.checkout().totalAmount()).isEqualTo(price);
    assertThat(infoResponse.checkout().taxIncluded()).isFalse();
    assertThat(infoResponse.checkout().pricingMode()).isEqualTo(expectedPricingMode);
    assertThat(infoResponse.item().sku()).isEqualTo(item.sku());
    assertThat(infoResponse.item().name()).isEqualTo(item.name());
    assertThat(infoResponse.user().getId()).isEqualTo(userDTO.userId());
    assertThat(infoResponse.user().getEmail()).isEqualTo(userDTO.email());
    assertThat(infoResponse.user().getFirstName()).isEqualTo(userDTO.firstName());
    assertThat(infoResponse.user().getLastName()).isEqualTo(userDTO.lastName());
    assertThat(infoResponse.locale()).isEqualTo(ENGLISH);
    assertThat(infoResponse.sandbox()).isTrue();
    assertThat(infoResponse.trackingId()).isEqualTo(trackingId);
    verify(DIGITAL_RIVER_API).getCustomer(anyString(), anyString());
  }

  private void testWidgetUpdatesPaymentStep(
      String paymentId,
      CheckoutResponse providerResponse,
      UserDTO userDTO,
      PaymentBigDecimal price,
      String currency) {
    clearInvocations(DIGITAL_RIVER_API);
    when(DIGITAL_RIVER_API.updateCheckout(
            anyString(), anyString(), any(UpdateCheckoutRequest.class)))
        .thenReturn(providerResponse);
    var billingAddress =
        new BillingAddress.Builder().setPostalCode(POSTAL_CODE).setCountry(US).build();
    var updateResponse =
        widgetApiClient.updatePaymentEndpointWithHeader(
            API_KEY,
            paymentId,
            new UpdatePaymentRequestDTO(
                Optional.ofNullable(userDTO.email()), Optional.of(billingAddress)));
    assertThat(updateResponse.checkout().currency()).isEqualTo(currency);
    assertThat(updateResponse.checkout().country()).isEqualTo(US);
    assertThat(updateResponse.checkout().postalCode()).isEqualTo(POSTAL_CODE);
    assertThat(updateResponse.checkout().totalAmount()).isEqualTo(price.bigDecimal());
    assertThat(updateResponse.checkout().taxIncluded()).isFalse();
    verify(DIGITAL_RIVER_API)
        .updateCheckout(
            anyString(),
            anyString(),
            argThat(request -> verifyUpdateCheckoutRequest(request, userDTO)));
  }

  private void testCreateSourceStep(
      String paymentId,
      CheckoutResponse providerCheckoutResponse,
      UserDTO userDTO,
      ItemDTO item,
      PaymentBigDecimal price) {
    var sourceId = mockGetSourceResponse(userDTO);
    when(DIGITAL_RIVER_API.createOrder(anyString(), any(OrderCreationRequest.class)))
        .thenReturn(buildOrderResponse(price, USD, ProviderStatus.COMPLETED));
    var sourceRequest =
        new SourceCreationRequestData(
            paymentId, sourceId, PaymentMethod.CREDIT_CARD.getDescription(), false, false);
    var sourceResponse = digitalRiverSourceApiClient.digitalRiverSourceCreation(sourceRequest);
    assertThat(sourceResponse.userId()).isEqualTo(userDTO.userId());
    assertThat(sourceResponse.email()).isEqualTo(userDTO.email());
    assertThat(sourceResponse.paymentId()).isEqualTo(paymentId);
    assertThat(sourceResponse.status()).isEqualTo(PaymentStatus.COMPLETED.name());
    assertThat(sourceResponse.errorMessage()).isNull();
    assertThat(sourceResponse.items()).hasSize(1);
    assertThat(sourceResponse.items().getFirst().itemName()).isEqualTo(item.name());
    assertThat(sourceResponse.country()).isEqualTo(US);
    assertThat(sourceResponse.totalAmount()).isEqualTo(price.bigDecimal());
    assertThat(sourceResponse.taxIncluded()).isFalse();
    assertThat(sourceResponse.claimed()).isFalse();
    verify(DIGITAL_RIVER_API)
        .createOrder(
            anyString(),
            argThat(request -> request.checkoutId().equals(providerCheckoutResponse.id())));
  }

  private void testCreateSourceStepWithFailedOrder(
      String paymentId, CheckoutResponse providerCheckoutResponse, UserDTO userDTO) {
    var sourceId = mockGetSourceResponse(userDTO);
    var errorResponse =
        Response.status(Status.CONFLICT)
            .entity(
                """
                {
                  "type": "conflict",
                  "errors": [
                    {
                      "code": "insufficient_funds",
                      "message": "The card has insufficient funds to complete the purchase"
                    }
                  ]
                }
                """)
            .build();
    var resteasyException =
        WebApplicationExceptionWrapper.wrap(new WebApplicationException(errorResponse));
    when(DIGITAL_RIVER_API.createOrder(anyString(), any(OrderCreationRequest.class)))
        .thenThrow(new ApiClientException(resteasyException.getMessage(), resteasyException));
    var sourceRequest =
        new SourceCreationRequestData(
            paymentId, sourceId, PaymentMethod.CREDIT_CARD.getDescription(), false, false);
    assertThrows(
        ClientErrorException.class,
        () -> digitalRiverSourceApiClient.digitalRiverSourceCreation(sourceRequest));
    verify(DIGITAL_RIVER_API)
        .createOrder(
            anyString(),
            argThat(request -> request.checkoutId().equals(providerCheckoutResponse.id())));
  }

  private String mockGetSourceResponse(UserDTO userDTO) {
    var providerSourceResponse = buildSourceResponse(userDTO, US, POSTAL_CODE);
    String sourceId = providerSourceResponse.id();
    when(DIGITAL_RIVER_API.getSource(anyString(), eq(sourceId))).thenReturn(providerSourceResponse);
    return sourceId;
  }

  private void testClaimPaymentStep(
      String paymentId, UserDTO userDTO, ItemDTO item, PaymentBigDecimal price, String currency) {
    var claimResponse = paymentApiClient.claimPayment(API_KEY, paymentId);
    assertThat(claimResponse.paymentId()).isEqualTo(paymentId);
    assertThat(claimResponse.userId()).isEqualTo(userDTO.userId());
    assertThat(claimResponse.claimedAt()).isNotNull();
    assertThat(claimResponse.item().sku()).isEqualTo(item.sku());
    assertThat(claimResponse.item().name()).isEqualTo(item.name());
    assertThat(claimResponse.priceData().localPrice().totalAmount()).isEqualTo(price.bigDecimal());
    assertThat(claimResponse.priceData().localPrice().currency()).isEqualTo(currency);
    assertThat(claimResponse.priceData().taxIncluded()).isFalse();
    assertThat(claimResponse.paymentMethodUsed()).isEqualTo(PaymentMethod.CREDIT_CARD);
  }
}
