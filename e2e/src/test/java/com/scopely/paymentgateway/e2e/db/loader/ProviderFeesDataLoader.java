package com.scopely.paymentgateway.e2e.db.loader;

import com.amazonaws.services.dynamodbv2.model.AttributeDefinition;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.GlobalSecondaryIndex;
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement;
import com.amazonaws.services.dynamodbv2.model.KeyType;
import com.amazonaws.services.dynamodbv2.model.Projection;
import com.amazonaws.services.dynamodbv2.model.ProjectionType;
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType;
import com.scopely.paymentgateway.e2e.db.DataLoader;
import com.scopely.paymentgateway.repositories.daos.ProviderFeesDAO;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import java.util.Collection;
import java.util.List;

public class ProviderFeesDataLoader implements DataLoader<ProviderFeesDAO> {

  @Override
  public Collection<ProviderFeesDAO> generateData() {
    return List.of();
  }

  @Override
  public int saveItems(DynamoDbMapper dynamoDbMapper) {
    return 0;
  }

  @Override
  public CreateTableRequest getCreateTableRequestV1() {
    return new CreateTableRequest()
        .withTableName(ProviderFeesDAO.TABLE)
        .withAttributeDefinitions(
            new AttributeDefinition(ProviderFeesDAO.HASH_KEY, ScalarAttributeType.S),
            new AttributeDefinition(ProviderFeesDAO.RANGE_KEY, ScalarAttributeType.S))
        .withKeySchema(
            new KeySchemaElement(ProviderFeesDAO.HASH_KEY, KeyType.HASH),
            new KeySchemaElement(ProviderFeesDAO.RANGE_KEY, KeyType.RANGE))
        .withGlobalSecondaryIndexes(
            new GlobalSecondaryIndex()
                .withIndexName("GSI_Payment_Header")
                .withKeySchema(new KeySchemaElement(ProviderFeesDAO.RANGE_KEY, KeyType.HASH))
                .withProjection(new Projection().withProjectionType(ProjectionType.ALL)));
  }
}
