package com.scopely.paymentgateway.e2e.db.loader;

import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USER_DR_1;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USER_DR_3;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USER_DR_BLOCKED;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USER_XSOLLA_1;
import static com.scopely.paymentgateway.e2e.tests.TestDataHelper.USER_XSOLLA_BLOCKED;
import static com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier.DIGITAL_RIVER;
import static com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier.XSOLLA;

import com.amazonaws.services.dynamodbv2.model.AttributeDefinition;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement;
import com.amazonaws.services.dynamodbv2.model.KeyType;
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType;
import com.scopely.paymentgateway.e2e.db.DataLoader;
import com.scopely.paymentgateway.e2e.tests.TestDataHelper;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.repositories.daos.UserDAO;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import java.time.Instant;
import java.util.Collection;
import java.util.List;

public class UserDataLoader implements DataLoader<UserDAO> {

  @Override
  public Collection<UserDAO> generateData() {
    return List.of(
        buildUser(USER_DR_1.userId(), DIGITAL_RIVER).build(),
        buildUser(USER_DR_3.userId(), DIGITAL_RIVER).build(),
        buildUser(USER_DR_BLOCKED.userId(), DIGITAL_RIVER).setBlocked(true).build(),
        buildUser(USER_XSOLLA_1.userId(), XSOLLA).build(),
        buildUser(USER_XSOLLA_BLOCKED.userId(), XSOLLA).setBlocked(true).build());
  }

  @Override
  public int saveItems(DynamoDbMapper dynamoDbMapper) {
    return 0;
  }

  private UserDAO.Builder buildUser(String userId, PaymentProviderIdentifier provider) {
    return new UserDAO.Builder()
        .setApiKey(TestDataHelper.API_KEY)
        .setUserId(userId)
        .setBlocked(false)
        .setProvider(provider)
        .setUpdatedAt(Instant.now());
  }

  @Override
  public CreateTableRequest getCreateTableRequestV1() {
    return new CreateTableRequest()
        .withTableName(UserDAO.TABLE)
        .withAttributeDefinitions(new AttributeDefinition(UserDAO.HASH_KEY, ScalarAttributeType.S))
        .withKeySchema(new KeySchemaElement(UserDAO.HASH_KEY, KeyType.HASH));
  }
}
