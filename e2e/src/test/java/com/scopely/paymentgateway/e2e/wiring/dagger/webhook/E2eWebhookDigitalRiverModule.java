package com.scopely.paymentgateway.e2e.wiring.dagger.webhook;

import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eCoreModule;
import com.scopely.paymentgateway.webhook.digitalriver.service.WebhookDigitalRiverService;
import com.scopely.proteus.monitoring.StatsDHeartbeater;
import com.scopely.proteus.service.ProteusServiceConstants;
import dagger.Module;
import dagger.Provides;
import javax.inject.Named;
import javax.inject.Singleton;

@Module(includes = {E2eCoreModule.class})
public class E2eWebhookDigitalRiverModule {
  public static final int PORT = 7576;

  @Provides
  @Named(ProteusServiceConstants.SERVICE_NAME)
  String serviceName() {
    return "payment-gateway-webhook-digitalriver";
  }

  @Provides
  @Singleton
  public WebhookDigitalRiverService webhookDigitalRiverService(
      StatsDHeartbeater statsDHeartbeater,
      E2ePaymentGatewayWebhookDigitalRiverComponent paymentGatewayWebhookDigitalRiverComponent) {
    return new E2eWebhookDigitalRiverService(
        PORT, 10, statsDHeartbeater, paymentGatewayWebhookDigitalRiverComponent);
  }
}
