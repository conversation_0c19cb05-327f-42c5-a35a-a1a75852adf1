package com.scopely.paymentgateway.e2e.db.verifier;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBQueryExpression;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eDynamoModule;
import com.scopely.paymentgateway.repositories.daos.ChargebackDAO;
import com.scopely.paymentgateway.repositories.daos.ReversalDAO;
import com.scopely.paymentgateway.utils.DynamoDBQueryHelper;
import com.scopely.paymentgateway.utils.DynamoDBQueryHelper.Comparator;
import java.util.List;
import java.util.Optional;

public class ChargebackRetriever {
  private static final String CHARGEBACK_HEADER_PREFIX = "REVERSAL#CHARGEBACK#";

  public static List<ChargebackDAO> retrieveChargebacksByPaymentId(String paymentId) {
    var dynamoFilter = new DynamoDBQueryHelper();
    dynamoFilter.addKeyCondition(ReversalDAO.HASH_KEY, paymentId);
    dynamoFilter.addKeyCondition(
        Comparator.BEGINS, ReversalDAO.RANGE_KEY, CHARGEBACK_HEADER_PREFIX);
    var query =
        new DynamoDBQueryExpression<ChargebackDAO>()
            .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
            .withFilterExpression(dynamoFilter.getFilterExpressionChain())
            .withExpressionAttributeValues(dynamoFilter.getAttributeValues());
    return E2eDynamoModule.jsonDynamoMapper.queryAll(ChargebackDAO.class, query);
  }

  public static Optional<ChargebackDAO> retrieveChargebackById(String chargebackId) {
    var dynamoFilter = new DynamoDBQueryHelper();
    dynamoFilter.addKeyCondition(ReversalDAO.RANGE_KEY, CHARGEBACK_HEADER_PREFIX + chargebackId);
    var query =
        new DynamoDBQueryExpression<ChargebackDAO>()
            .withIndexName(ReversalDAO.HEADER_INDEX_NAME)
            .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
            .withFilterExpression(dynamoFilter.getFilterExpressionChain())
            .withExpressionAttributeValues(dynamoFilter.getAttributeValues())
            .withConsistentRead(false);
    return E2eDynamoModule.jsonDynamoMapper.queryAll(ChargebackDAO.class, query).stream().findAny();
  }
}
