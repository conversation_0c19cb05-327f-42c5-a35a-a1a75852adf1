package com.scopely.paymentgateway.e2e.db.loader;

import com.amazonaws.services.dynamodbv2.model.AttributeDefinition;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.GlobalSecondaryIndex;
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement;
import com.amazonaws.services.dynamodbv2.model.KeyType;
import com.amazonaws.services.dynamodbv2.model.Projection;
import com.amazonaws.services.dynamodbv2.model.ProjectionType;
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType;
import com.scopely.paymentgateway.e2e.db.DataLoader;
import com.scopely.paymentgateway.repositories.daos.RefundDAO;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

public class RefundDataLoader implements DataLoader<RefundDAO> {

  @Override
  public Collection<RefundDAO> generateData() throws IOException {
    return List.of();
  }

  @Override
  public int saveItems(DynamoDbMapper dynamoDbMapper) {
    return 0;
  }

  @Override
  public CreateTableRequest getCreateTableRequestV1() {
    return new CreateTableRequest()
        .withTableName(RefundDAO.TABLE)
        .withAttributeDefinitions(
            new AttributeDefinition(RefundDAO.HASH_KEY, ScalarAttributeType.S),
            new AttributeDefinition(RefundDAO.RANGE_KEY, ScalarAttributeType.S))
        .withKeySchema(
            new KeySchemaElement(RefundDAO.HASH_KEY, KeyType.HASH),
            new KeySchemaElement(RefundDAO.RANGE_KEY, KeyType.RANGE))
        .withGlobalSecondaryIndexes(
            new GlobalSecondaryIndex()
                .withIndexName(RefundDAO.GSI_REFUND_ID)
                .withKeySchema(new KeySchemaElement(RefundDAO.RANGE_KEY, KeyType.HASH))
                .withProjection(new Projection().withProjectionType(ProjectionType.ALL)));
  }
}
