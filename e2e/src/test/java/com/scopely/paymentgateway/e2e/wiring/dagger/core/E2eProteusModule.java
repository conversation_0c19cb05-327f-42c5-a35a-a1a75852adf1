package com.scopely.paymentgateway.e2e.wiring.dagger.core;

import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import com.scopely.proteus.config.Config;
import com.scopely.proteus.config.ExecutionEnvironment;
import dagger.Module;
import dagger.Provides;
import javax.inject.Singleton;

@Module
public class E2eProteusModule {

  @Provides
  @Singleton
  AWSCredentialsProvider awsCredentialsProvider() {
    return new DefaultAWSCredentialsProviderChain();
  }

  @Provides
  @Singleton
  Config config(ExecutionEnvironment executionEnvironment) {
    return E2eConfig.getConfig(executionEnvironment, "e2e.properties");
  }
}
