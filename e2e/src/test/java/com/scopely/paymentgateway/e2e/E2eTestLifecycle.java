package com.scopely.paymentgateway.e2e;

import static org.junit.jupiter.api.extension.ExtensionContext.Namespace.GLOBAL;

import com.scopely.paymentgateway.e2e.db.DynamoPopulator;
import com.scopely.paymentgateway.e2e.wiring.aws.DynamoLocal;
import com.scopely.paymentgateway.e2e.wiring.dagger.core.E2eDynamoModule;
import com.scopely.paymentgateway.e2e.wiring.dagger.web.E2ePaymentGatewayWebComponent;
import com.scopely.paymentgateway.e2e.wiring.dagger.webhook.E2ePaymentGatewayWebhookDigitalRiverComponent;
import com.scopely.paymentgateway.e2e.wiring.dagger.webhook.E2ePaymentGatewayWebhookXSollaComponent;
import com.scopely.paymentgateway.e2e.wiring.dagger.worker.E2ePaymentGatewayWorkerComponent;
import com.scopely.proteus.core.service.ProteusService;
import com.scopely.proteus.core.service.ServiceTrampoline;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.ExtensionContext;

public class E2eTestLifecycle
    implements BeforeAllCallback, ExtensionContext.Store.CloseableResource {
  private DynamoLocal dynamoLocal;
  private ProteusService webService;
  private ProteusService digitalRiverWebhookService;
  private ProteusService xsollaWebhookService;
  private ProteusService workerService;

  @Override
  public void beforeAll(ExtensionContext context) throws Exception {
    String uniqueKey = this.getClass().getName();
    if (context.getRoot().getStore(GLOBAL).get(uniqueKey) == null) {
      context.getRoot().getStore(GLOBAL).put(uniqueKey, this);
      setUp();
    }
  }

  private void setUp() throws Exception {
    setUpInfrastructure();
    setupModules();
    setupData();
  }

  private void setUpInfrastructure() throws Exception {
    System.setProperty("aws.accessKeyId", "fakeAccessKey");
    System.setProperty("aws.secretKey", "fakeSecretKey");
    dynamoLocal = new DynamoLocal();
    dynamoLocal.start();
  }

  private void setupModules() {
    E2eDynamoModule.dynamoClientV1 = dynamoLocal.buildDynamoClient();
    E2eDynamoModule.dynamoDbClient = dynamoLocal.buildDynamoClientV2();
    var webComponent = E2ePaymentGatewayWebComponent.create();
    var drWebhookComponent = E2ePaymentGatewayWebhookDigitalRiverComponent.create();
    var xsollaWebhookComponent = E2ePaymentGatewayWebhookXSollaComponent.create();
    var workerComponent = E2ePaymentGatewayWorkerComponent.create();
    E2eDynamoModule.jsonDynamoMapper = webComponent.getJsonDynamoMapper();
    E2eDynamoModule.dynamoDbMapper = webComponent.getDynamoDbMapper();
    webService = ServiceTrampoline.startService("payment-gateway-web", webComponent).getService();
    digitalRiverWebhookService =
        ServiceTrampoline.startService("payment-gateway-webhook-digitalriver", drWebhookComponent)
            .getService();
    xsollaWebhookService =
        ServiceTrampoline.startService("payment-gateway-webhook-xsolla", xsollaWebhookComponent)
            .getService();
    workerService =
        ServiceTrampoline.startService("payment-gateway-worker", workerComponent).getService();
  }

  private static void setupData() {
    new DynamoPopulator(
            E2eDynamoModule.dynamoClientV1,
            E2eDynamoModule.dynamoDbClient,
            E2eDynamoModule.jsonDynamoMapper,
            E2eDynamoModule.dynamoDbMapper)
        .populateData();
  }

  @Override
  public void close() throws Exception {
    teardownModules();
    teardownInfrastructure();
  }

  private void teardownModules() {
    webService.stop();
    digitalRiverWebhookService.stop();
    xsollaWebhookService.stop();
    workerService.stop();
  }

  private void teardownInfrastructure() throws Exception {
    dynamoLocal.stop();
  }
}
