plugins {
    id 'satellite.module'
}

dependencies {
    testImplementation project(':core')
    testImplementation project(':web')
    testImplementation project(':webhook-digitalriver')
    testImplementation project(':webhook-xsolla')
    testImplementation project(':worker')
}

configurations {
    // This is necessary to avoid an error to connect parameter store in e2e tests
    testImplementation {
        exclude group:"com.scopely.proteus", module:"proteus-config-aws-ssm-shim"
    }
}
