import groovy.json.JsonSlurper

plugins {
    id 'satellite.library'
}

static def findAwsCli() {
    try {
        def proc = ['which', 'aws'].execute()
        proc.waitFor()
        if (proc.exitValue() != 0) {
            throw new GradleException("AWS CLI not found in PATH.")
        }
        return proc.in.text.trim()
    } catch (Exception e) {
        throw new GradleException("Failed to locate aws CLI. Is it installed and available in PATH?", e)
    }
}

static def getSSMParameter(String name) {
    def region = System.env['AWS_REGION'] ?: 'us-east-1'
    def proc = [findAwsCli(), 'ssm', 'get-parameter', '--name', name, '--with-decryption', '--region', region].execute()
    proc.waitFor()
    // If the command fails, return the preview path
    if (proc.exitValue() != 0) {
        println "Error: ${proc.err.text}"
        println "Failed to get SSM parameter: $name. Using default path."
        return "s3://payments-preview-geofencing/city/geoip.mmdb"
    }

    def json = new JsonSlurper().parseText(proc.in.text)
    return json?.Parameter?.Value
}

tasks.register('downloadGeoFencingDB') {
    var dbResourceFilePath = "src/main/resources/geoip.mmdb"
    //Check the s3 bucket with the app parameter "geofencing.s3.database.url" or in geofencing-lib project check more info in the readme
    var s3url = System.env['GEO_FENCING_DB_S3_URL'] == null ?
            getSSMParameter("/proteus/geofencing.s3.database.url") :
            System.env['GEO_FENCING_DB_S3_URL']
    onlyIf {
        s3url != null && !file("./$dbResourceFilePath").exists()
    }
    doFirst {
        exec {
            commandLine findAwsCli(), 's3', 'cp', s3url, dbResourceFilePath
        }
    }
}

tasks.named('processResources') {
    dependsOn tasks.named('downloadGeoFencingDB')
}