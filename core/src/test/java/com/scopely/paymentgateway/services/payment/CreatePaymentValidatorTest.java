package com.scopely.paymentgateway.services.payment;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_CONTEXT_PROPERTIES_SENT;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_LOAD_EMAIL_INVALID;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;
import static utils.TestMocks.API_KEY;
import static utils.TestMocks.ITEM_NAME;
import static utils.TestMocks.SKU_ID;
import static utils.TestMocks.USD;

import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.model.checkout.Item;
import com.scopely.paymentgateway.model.createpayment.CreatePaymentRequestData;
import com.scopely.paymentgateway.model.payment.PlatformType;
import com.scopely.paymentgateway.model.payment.PricingMode;
import com.scopely.paymentgateway.model.payment.Properties;
import com.scopely.paymentgateway.model.payment.User;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.timgroup.statsd.StatsDClient;
import java.math.BigDecimal;
import org.javamoney.moneta.Money;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CreatePaymentValidatorTest {
  @Mock private PaymentGatewayConfig paymentGatewayConfig;
  @Mock private StatsDClient statsDClient;
  @InjectMocks private CreatePaymentValidator validator;

  private static final Item VALID_ITEM =
      new Item.Builder()
          .setName(ITEM_NAME)
          .setSku(SKU_ID)
          .setBasePrice(Money.of(BigDecimal.ONE, USD))
          .build();

  private static final User.Builder USER_BUILDER =
      new User.Builder()
          .setUserId("123")
          .setEmail("<EMAIL>")
          .setFirstName("Dummy")
          .setLastName("User")
          .setVip(false);

  @Test
  void succeedsOnNonStandardGmailAddresses() {
    var user = USER_BUILDER.setEmail("<EMAIL>").build();
    var request = buildCreatePaymentRequestData(VALID_ITEM, user, PricingMode.EXPLICIT);
    assertDoesNotThrow(() -> validator.validateRequest(request));
    verifyNoInteractions(statsDClient);
  }

  @Test
  void sendsMetricOnInvalidEmail() {
    var user = USER_BUILDER.setEmail("invalid.email").build();
    var request = buildCreatePaymentRequestData(VALID_ITEM, user, PricingMode.EXPLICIT);
    assertDoesNotThrow(() -> validator.validateRequest(request));
    verify(statsDClient)
        .increment(DD_PAYMENT_LOAD_EMAIL_INVALID, MetricsUtils.buildTags(TAG_API_KEY, API_KEY));
  }

  @Test
  void failsOnInvalidQuantity() {
    var item = Item.Builder.from(VALID_ITEM).setQuantity(0).build();
    var request = buildCreatePaymentRequestData(item, USER_BUILDER.build(), PricingMode.EXPLICIT);
    assertThrows(IllegalArgumentException.class, () -> validator.validateRequest(request));
  }

  @Test
  void succeedsValidateContextProperties(){
    var contextProperties = "testContextProperties";
    var apiKey = "testApiKey";
    when(paymentGatewayConfig.contextPropertiesMaxLength()).thenReturn(1000);
    assertDoesNotThrow(() -> validator.validateContextProperties(contextProperties, apiKey));
    verify(statsDClient)
            .increment(DD_PAYMENT_CONTEXT_PROPERTIES_SENT, MetricsUtils.buildTags(TAG_API_KEY, apiKey));
  }

  private CreatePaymentRequestData buildCreatePaymentRequestData(
      Item item, User user, PricingMode pricingMode) {
    return new CreatePaymentRequestData.Builder()
        .setApiKey(API_KEY)
        .setDeviceToken("a-device-token")
        .setItem(item)
        .setProperties(
            new Properties.Builder()
                .setSandbox(false)
                .setUserIp("************")
                .setUser(user)
                .setPricingMode(pricingMode))
        .setPlatform(PlatformType.WEB)
        .build();
  }
}
