package com.scopely.paymentgateway.services.payment;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static utils.TestMocks.API_KEY;
import static utils.TestMocks.BLOCKED_USER;
import static utils.TestMocks.PAYMENT;
import static utils.TestMocks.PAYMENTS_CLIENT_CONFIG;
import static utils.TestMocks.PAYMENT_ID;
import static utils.TestMocks.PAYMENT_LOCATION;
import static utils.TestMocks.PAYMENT_REQUEST_DATA_EXPLICIT_WITHOUT_EXCHANGE;
import static utils.TestMocks.USER;
import static utils.TestMocks.USER_IP;

import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.exceptions.payment.DuplicatedPaymentException;
import com.scopely.paymentgateway.exceptions.payment.UnableToSavePaymentException;
import com.scopely.paymentgateway.model.dto.createpayment.CreatePaymentResponseDTO;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.geolocation.GeolocationPaymentService;
import com.scopely.paymentgateway.services.translations.TranslationsService;
import com.scopely.paymentgateway.services.user.UserService;
import com.timgroup.statsd.StatsDClient;
import java.time.Clock;
import java.time.Instant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import utils.TestMocks;

@ExtendWith(MockitoExtension.class)
class CreateNewPaymentTest {
  private static final String NEW_PAYMENT_ID = "NEW_PAYMENT_ID";
  private static final int DUPLICATE_MAX_INTENT = 2;
  private static final Instant NOW = Instant.now();
  private static final Payment NEW_PAYMENT =
      Payment.Builder.from(PAYMENT).setPaymentId(NEW_PAYMENT_ID).build();
  @Mock private ClientConfigurationService clientConfigurationService;
  @Mock private GeolocationPaymentService geolocationPaymentService;

  @Mock private StatsDClient statsDClient; // this is needed although it says never used
  @Mock private Clock clock;
  @Mock private UserService userService;
  @Mock private PaymentGatewayConfig paymentGatewayConfig;
  @Mock private PaymentTransactionIdGenerator paymentTransactionIdGenerator;
  @Mock private CreatePaymentService createPaymentService;
  @Mock private CreatePaymentValidator createPaymentValidator;
  @Mock private TranslationsService translationsService;
  @InjectMocks private CreateNewPayment createNewPayment;

  @BeforeEach
  void setUp() throws Exception {
    lenient().when(clock.instant()).thenReturn(NOW);
    lenient().when(paymentTransactionIdGenerator.generateId()).thenReturn(PAYMENT_ID);
    lenient().when(paymentGatewayConfig.createPaymentRetries()).thenReturn(DUPLICATE_MAX_INTENT);
    lenient()
        .when(geolocationPaymentService.getPaymentLocation(any(), eq(USER_IP)))
        .thenReturn(PAYMENT_LOCATION);
    lenient()
        .when(clientConfigurationService.getConfiguration(eq(API_KEY)))
        .thenReturn(PAYMENTS_CLIENT_CONFIG);
    lenient()
        .when(createPaymentService.updatePaymentDataBySandbox(any(), any()))
        .thenReturn(PAYMENT_REQUEST_DATA_EXPLICIT_WITHOUT_EXCHANGE);
    lenient()
        .when(createPaymentService.updatePaymentDataForVIP(any(), any()))
        .thenReturn(PAYMENT_REQUEST_DATA_EXPLICIT_WITHOUT_EXCHANGE);
  }

  @Test
  void shouldCreateNewPaymentWhenPaymentIdAlreadyExists() throws Exception {
    when(userService.getUserPayerInfo(any(), any())).thenReturn(BLOCKED_USER);
    // The first call with exception the second one saving the payment
    when(createPaymentService.createRejectedPayment(any(PaymentCreationContext.class)))
        .thenThrow(DuplicatedPaymentException.class)
        .thenReturn(new CreatePaymentResponseDTO(NEW_PAYMENT));

    CreatePaymentResponseDTO createPaymentResponse =
        createNewPayment.execute(
            PAYMENT_REQUEST_DATA_EXPLICIT_WITHOUT_EXCHANGE, TestMocks.CONTEXT_PROPERTIES);

    assertThat(createPaymentResponse.paymentId()).isEqualTo(NEW_PAYMENT_ID);
    assertThat(createPaymentResponse.provider())
        .isEqualTo(PaymentProviderIdentifier.DIGITAL_RIVER.name());
  }

  @Test
  void shouldFailCreatingNewPaymentWhenPaymentIdAlreadyAndReachMaxIntents() throws Exception {
    when(userService.getUserPayerInfo(any(), any())).thenReturn(BLOCKED_USER);
    when(createPaymentService.createRejectedPayment(any(PaymentCreationContext.class)))
        .thenThrow(DuplicatedPaymentException.class);

    assertThrows(
        UnableToSavePaymentException.class,
        () ->
            createNewPayment.execute(
                PAYMENT_REQUEST_DATA_EXPLICIT_WITHOUT_EXCHANGE, TestMocks.CONTEXT_PROPERTIES));
  }

  @Test
  void shouldCreateValidPayment() throws Exception {
    when(userService.getUserPayerInfo(any(), any())).thenReturn(USER);
    when(createPaymentService.createPayment(any(PaymentCreationContext.class)))
        .thenReturn(new CreatePaymentResponseDTO(PAYMENT));

    CreatePaymentResponseDTO createPaymentResponse =
        createNewPayment.execute(
            PAYMENT_REQUEST_DATA_EXPLICIT_WITHOUT_EXCHANGE, TestMocks.CONTEXT_PROPERTIES);

    assertThat(createPaymentResponse.paymentId()).isEqualTo(PAYMENT_ID);
    verify(createPaymentService, never()).createRejectedPayment(any(PaymentCreationContext.class));
  }

  @Test
  void shouldCreateRejectedPayment() throws Exception {
    when(userService.getUserPayerInfo(any(), any())).thenReturn(BLOCKED_USER);
    when(createPaymentService.createRejectedPayment(any(PaymentCreationContext.class)))
        .thenReturn(new CreatePaymentResponseDTO(PAYMENT));

    CreatePaymentResponseDTO createPaymentResponse =
        createNewPayment.execute(
            PAYMENT_REQUEST_DATA_EXPLICIT_WITHOUT_EXCHANGE, TestMocks.CONTEXT_PROPERTIES);

    assertThat(createPaymentResponse.paymentId()).isEqualTo(PAYMENT_ID);
    verify(createPaymentService, never()).createPayment(any(PaymentCreationContext.class));
  }

  @Test
  void shouldFailCreatingNewPaymentWithInvalidRequest() throws Exception {
    var request = PAYMENT_REQUEST_DATA_EXPLICIT_WITHOUT_EXCHANGE;
    doThrow(IllegalArgumentException.class).when(createPaymentValidator).validateRequest(request);

    assertThrows(
        IllegalArgumentException.class,
        () -> createNewPayment.execute(request, TestMocks.CONTEXT_PROPERTIES));
    verify(createPaymentService, never()).createPayment(any(PaymentCreationContext.class));
  }
}
