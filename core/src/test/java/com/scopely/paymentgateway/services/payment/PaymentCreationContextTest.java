package com.scopely.paymentgateway.services.payment;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.scopely.paymentgateway.model.checkout.Item;
import com.scopely.paymentgateway.model.checkout.ProviderItem;
import com.scopely.paymentgateway.model.createpayment.CreatePaymentRequestData;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PlatformType;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PriceProvider;
import com.scopely.paymentgateway.model.payment.PricingMode;
import com.scopely.paymentgateway.model.payment.ProductType;
import com.scopely.paymentgateway.model.payment.Properties;
import com.scopely.paymentgateway.model.user.User;
import java.time.Instant;
import java.util.List;
import org.javamoney.moneta.Money;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import utils.TestMocks;

@ExtendWith(MockitoExtension.class)
class PaymentCreationContextTest {
  @Mock private Item item;
  @Mock private Properties properties;
  @Mock private CreatePaymentRequestData requestData;
  @Mock private User payer;
  private PaymentCreationContext context;

  @BeforeEach
  public void setUp() {
    when(requestData.getItem()).thenReturn(item);
    lenient().when(requestData.getProperties()).thenReturn(properties);
    context =
        new PaymentCreationContext(
            TestMocks.PAYMENT_ID,
            requestData,
            payer,
            TestMocks.PAYMENT_LOCATION,
            TestMocks.LOCALE,
            TestMocks.CONTEXT_PROPERTIES);
  }

  @Test
  void testGetPaymentId() {
    assertThat(context.getPaymentId()).isEqualTo(TestMocks.PAYMENT_ID);
  }

  @Test
  void testGetApiKey() {
    when(requestData.getApiKey()).thenReturn(TestMocks.API_KEY);
    assertThat(context.getApiKey()).isEqualTo(TestMocks.API_KEY);
  }

  @Test
  void testGetUserId() {
    when(payer.getUserId()).thenReturn(TestMocks.USER_ID);
    assertThat(context.getUserId()).isEqualTo(TestMocks.USER_ID);
  }

  @Test
  void testGetRequestLocale() {
    var requestLocale = "any-request-locale";
    when(properties.getLocale()).thenReturn(requestLocale);
    assertThat(context.getRequestLocale()).isEqualTo(requestLocale);
  }

  @Test
  void testGetUiLocale() {
    assertThat(context.getUiLocale()).isEqualTo(TestMocks.LOCALE);
  }

  @Test
  void testGetAssignedProviderIsEmpty() {
    assertThat(context.getAssignedProvider()).isEmpty();
  }

  @Test
  void testGetAssignedProviderContainsValue() {
    when(payer.getProvider()).thenReturn(PaymentProviderIdentifier.XSOLLA);
    assertThat(context.getAssignedProvider()).contains(PaymentProviderIdentifier.XSOLLA);
  }

  @Test
  void testGetPlatform() {
    when(requestData.getPlatform()).thenReturn(PlatformType.WEB);
    assertThat(context.getPlatform()).isEqualTo(PlatformType.WEB);
  }

  @Test
  void testGetExternalId() {
    when(requestData.getExternalId()).thenReturn(TestMocks.EXTERNAL_ID);
    assertThat(context.getExternalId()).isEqualTo(TestMocks.EXTERNAL_ID);
  }

  @Test
  void testIsSandbox() {
    when(properties.isSandbox()).thenReturn(true);
    assertThat(context.isSandbox()).isTrue();
  }

  @Test
  void testGetPaymentLocation() {
    assertThat(context.getPaymentLocation()).isEqualTo(TestMocks.PAYMENT_LOCATION);
  }

  @Test
  void testSetPaymentLocation() {
    context.setPaymentLocation(TestMocks.PAYMENT_LOCATION_ES);
    assertThat(context.getPaymentLocation()).isEqualTo(TestMocks.PAYMENT_LOCATION_ES);
  }

  @Test
  void testGetRequestUserInfo() {
    var requestUser = mock(com.scopely.paymentgateway.model.payment.User.class);
    when(properties.getUser()).thenReturn(requestUser);
    assertThat(context.getRequestUserInfo()).isEqualTo(requestUser);
  }

  @Test
  void testIsVipUser() {
    var requestUser = mock(com.scopely.paymentgateway.model.payment.User.class);
    when(properties.getUser()).thenReturn(requestUser);
    when(requestUser.isVip()).thenReturn(true);
    assertThat(context.isVipUser()).isTrue();
  }

  @Test
  void testGetPricingMode() {
    when(properties.getPricingMode()).thenReturn(PricingMode.EXPLICIT);
    assertThat(context.getPricingMode()).isEqualTo(PricingMode.EXPLICIT);
  }

  @Test
  void testGetQuantity() {
    when(item.getQuantity()).thenReturn(3);
    assertThat(context.getQuantity()).isEqualTo(3);
  }

  @Test
  void testGetTotalBasePriceThrowsErrorWhenNoPriceDefined() {
    var exception = assertThrows(NullPointerException.class, context::getTotalBasePrice);
    assertThat(exception.getMessage()).isEqualTo("Missing base price");
  }

  @Test
  void testGetTotalBasePriceSucceedsWhenPriceDefined() {
    when(properties.getPricingMode()).thenReturn(PricingMode.EXPLICIT);
    when(item.getQuantity()).thenReturn(3);
    when(item.getBasePrice()).thenReturn(Money.of(2, TestMocks.USD));
    context =
        new PaymentCreationContext(
            TestMocks.PAYMENT_ID,
            requestData,
            payer,
            TestMocks.PAYMENT_LOCATION,
            TestMocks.LOCALE,
            TestMocks.CONTEXT_PROPERTIES);
    assertThat(context.getTotalBasePrice()).isEqualTo(Money.of(6, TestMocks.USD));
  }

  @Test
  void testSetItemPrice() {
    when(properties.getPricingMode()).thenReturn(PricingMode.SKU);
    when(item.getQuantity()).thenReturn(4);
    context.setItemPrice(Money.of(2, TestMocks.USD), Money.of(3, TestMocks.EUR));
    assertThat(context.getTotalBasePrice()).isEqualTo(Money.of(8, TestMocks.USD));
    assertThat(context.getItemData().getUnitBasePrice()).isEqualTo(Money.of(2, TestMocks.USD));
    assertThat(context.getItemData().getUnitLocalPrice()).isEqualTo(Money.of(3, TestMocks.EUR));
    assertThat(context.getTotalPriceData().getOriginalBasePrice())
        .isEqualTo(Money.of(8, TestMocks.USD));
    assertThat(context.getTotalPriceData().getOriginalLocalPrice())
        .isEqualTo(Money.of(12, TestMocks.EUR));
    assertThat(context.getTotalPriceData().getPricingMode()).isEqualTo(PricingMode.SKU);
    assertThat(context.getTotalPriceData().isTaxIncluded()).isFalse();
  }

  @Test
  void testSetItemPriceWithPriceData() {
    when(properties.getPricingMode()).thenReturn(PricingMode.EXPLICIT);
    when(item.getQuantity()).thenReturn(4);
    var priceData =
        new PriceData.Builder()
            .setBasePrice(Money.of(2, TestMocks.USD))
            .setLocalPrice(Money.of(3, TestMocks.EUR))
            .setTaxIncluded(true)
            .build();
    context.setItemPrice(priceData);
    assertThat(context.getTotalBasePrice()).isEqualTo(Money.of(8, TestMocks.USD));
    assertThat(context.getItemData().getUnitBasePrice()).isEqualTo(Money.of(2, TestMocks.USD));
    assertThat(context.getItemData().getUnitLocalPrice()).isEqualTo(Money.of(3, TestMocks.EUR));
    assertThat(context.getTotalPriceData().getOriginalBasePrice())
        .isEqualTo(Money.of(8, TestMocks.USD));
    assertThat(context.getTotalPriceData().getOriginalLocalPrice())
        .isEqualTo(Money.of(12, TestMocks.EUR));
    assertThat(context.getTotalPriceData().getPricingMode()).isEqualTo(PricingMode.EXPLICIT);
    assertThat(context.getTotalPriceData().isTaxIncluded()).isTrue();
  }

  @Test
  void testIsMultiCurrency() {
    when(properties.getPricingMode()).thenReturn(PricingMode.SKU);
    assertThat(context.isMultiCurrency()).isFalse();

    when(properties.getPricingMode()).thenReturn(PricingMode.EXPLICIT);
    assertThat(context.isMultiCurrency()).isTrue();

    when(item.getLocalizedPrice()).thenReturn(Money.of(1, TestMocks.USD));
    assertThat(context.isMultiCurrency()).isFalse();
  }

  @Test
  void testGetProviderSkuIsEmpty() {
    assertThat(context.getProviderSku()).isEmpty();
  }

  @Test
  void testGetProviderSkuWithValue() {
    var sku = "any-sku";
    var providerItem =
        new ProviderItem.Builder().setProvider(PriceProvider.PLAYGAMI).setSku(sku).build();
    when(item.getProviders()).thenReturn(List.of(providerItem));
    assertThat(context.getProviderSku()).contains(sku);
  }

  @Test
  void testGetItemDataThrowsWhenNoPriceDefined() {
    var exception = assertThrows(NullPointerException.class, context::getItemData);
    assertThat(exception.getMessage()).isEqualTo("Missing base price");
  }

  @Test
  void testGetItemDataSucceedsWhenPriceDefined() {
    var providerItem =
        new ProviderItem.Builder()
            .setProvider(PriceProvider.PLAYGAMI)
            .setSku("any-provider-sku")
            .build();
    var unitBasePrice = Money.of(2, TestMocks.USD);
    var unitLocalPrice = Money.of(3, TestMocks.EUR);
    when(item.getProviders()).thenReturn(List.of(providerItem));
    when(item.getSku()).thenReturn("any-internal-sku");
    when(item.getName()).thenReturn("any-name");
    when(item.getQuantity()).thenReturn(3);
    when(properties.getPricingMode()).thenReturn(PricingMode.SKU);
    context.setItemPrice(unitBasePrice, unitLocalPrice);

    var itemData = context.getItemData();

    assertThat(itemData.getInternalSku()).isEqualTo("any-internal-sku");
    assertThat(itemData.getProviderItem().getProvider()).isEqualTo(PriceProvider.PLAYGAMI);
    assertThat(itemData.getProviderItem().getSku()).isEqualTo("any-provider-sku");
    assertThat(itemData.getName()).isEqualTo("any-name");
    assertThat(itemData.getProductType()).isEqualTo(ProductType.IAP);
    assertThat(itemData.getQuantity()).isEqualTo(3);
    assertThat(itemData.getUnitBasePrice()).isEqualTo(unitBasePrice);
    assertThat(itemData.getUnitLocalPrice()).isEqualTo(unitLocalPrice);
  }

  @Test
  void testGetLogger() {
    assertThat(context.getLogger()).isNotNull();
  }

  @Test
  void testGetBaseNewPayment() {
    var requestUser = mock(com.scopely.paymentgateway.model.payment.User.class);
    when(requestUser.isVip()).thenReturn(true);
    when(properties.getUser()).thenReturn(requestUser);
    when(properties.isSandbox()).thenReturn(true);
    when(payer.getUserId()).thenReturn(TestMocks.USER_ID);
    when(requestData.getApiKey()).thenReturn(TestMocks.API_KEY);
    when(requestData.getDeviceToken()).thenReturn(TestMocks.DEVICE_TOKEN);
    when(requestData.getTrackingId()).thenReturn(TestMocks.TRACKING_ID);
    when(requestData.getExternalId()).thenReturn(TestMocks.EXTERNAL_ID);
    when(requestData.getPlatform()).thenReturn(PlatformType.WEB);
    var now = Instant.now();

    var paymentBuilder = context.getBaseNewPayment(now);

    assertThat(paymentBuilder.getPaymentId()).isEqualTo(TestMocks.PAYMENT_ID);
    assertThat(paymentBuilder.getClaimed()).isFalse();
    assertThat(paymentBuilder.getCreatedAt()).isEqualTo(now);
    assertThat(paymentBuilder.getUpdatedAt()).isEqualTo(now);
    assertThat(paymentBuilder.getApiKey()).isEqualTo(TestMocks.API_KEY);
    assertThat(paymentBuilder.getUserId()).isEqualTo(TestMocks.USER_ID);
    assertThat(paymentBuilder.getDeviceToken()).isEqualTo(TestMocks.DEVICE_TOKEN);
    assertThat(paymentBuilder.getTrackingId()).isEqualTo(TestMocks.TRACKING_ID);
    assertThat(paymentBuilder.isVip()).isTrue();
    assertThat(paymentBuilder.getExternalId()).isEqualTo(TestMocks.EXTERNAL_ID);
    assertThat(paymentBuilder.isSandbox()).isTrue();
    assertThat(paymentBuilder.getPlatform()).isEqualTo(PlatformType.WEB);
  }

  @Test
  void testContextProperties() {
    assertThat(context.getTitanContextProperties()).isEqualTo(TestMocks.CONTEXT_PROPERTIES);
  }
}
