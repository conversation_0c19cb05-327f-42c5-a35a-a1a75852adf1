package com.scopely.paymentgateway.providers.xsolla.services;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;
import static utils.TestMocks.PAYMENT_REQUEST_DATA_EXPLICIT_WITHOUT_EXCHANGE;
import static utils.TestMocks.PRICE_DATA;

import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.model.payment.PaymentLocation;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PriceDetail;
import com.scopely.paymentgateway.model.provider.ProviderCheckout;
import com.scopely.paymentgateway.model.refund.ProviderRefundInformation;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.model.user.User;
import com.scopely.paymentgateway.providers.xsolla.api.XSollaApi;
import com.scopely.paymentgateway.providers.xsolla.api.XsollaMerchantApi;
import com.scopely.paymentgateway.providers.xsolla.config.XSollaClientConfig;
import com.scopely.paymentgateway.providers.xsolla.model.checkout.CheckoutResponse;
import com.scopely.paymentgateway.providers.xsolla.model.refund.RefundResponse;
import com.scopely.paymentgateway.services.payment.PaymentCreationContext;
import com.scopely.paymentgateway.services.translations.TranslationsService;
import jakarta.ws.rs.BadRequestException;
import java.util.List;
import org.javamoney.moneta.Money;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class XsollaPaymentProviderServiceImplTest {
  private static final String API_KEY = "API_KEY";
  private static final String PAYMENT_ID = "PAYMENT_ID";
  private static final String COUNTRY = "US";
  private static final String CURRENCY = "USD";
  private static final String LOCALE = "en";
  private static final String CONTEXT_PROPERTIES = "TEST_CONTEXT_PROPERTIES";
  private static final Money TOTAL = Money.of(11, CURRENCY);
  private static final String TOKEN_ID = "TOKEN";
  private static final String AUTHORIZATION_KEY = "AUTHORIZATION_KEY";
  private static final Integer PROJECT_ID = 123;
  private static final Integer MERCHANT_ID = 456;
  private static final String USER_IP = "USER_IP";
  private static final String CUSTOMER_ID = "CUSTOMER_ID";
  public static final String ORDER_ID = "123";

  @Mock private XSollaApi api;
  @Mock private XsollaMerchantApi merchantApi;
  @Mock private TranslationsService translationsService;
  @Mock private User user;
  private XsollaPaymentProviderServiceImpl xSollaService;

  @BeforeEach
  public void setUp() {
    xSollaService = new XsollaPaymentProviderServiceImpl(api, merchantApi, translationsService);
  }

  public static final String WEBHOOK_FALLBACK_URI = "WEBHOOK_FALLBACK_URI";
  private static final ClientConfiguration CLIENT_CONFIG =
      new XSollaClientConfig.Builder()
          .setApiKey(API_KEY)
          .setPaymentProviderIdentifier(ConfigurationProviderIdentifier.XSOLLA)
          .setProjectId(PROJECT_ID)
          .setMerchantId(MERCHANT_ID)
          .setAuthApiKey(AUTHORIZATION_KEY)
          .setWebhookSecretKey(AUTHORIZATION_KEY)
          .setWebhookFallbackURI(WEBHOOK_FALLBACK_URI)
          .addAllAllowedLanguages(List.of(LOCALE))
          .build();

  private static final PaymentLocation PAYMENT_LOCATION =
      new PaymentLocation.Builder()
          .setIpAddress(USER_IP)
          .setCountry(COUNTRY)
          .setCurrency(CURRENCY)
          .setContinent("EU")
          .build();

  @Test
  void shouldCreateCheckout() throws RequestToProviderException {
    CheckoutResponse checkoutResponse = createCheckoutResponseMock();
    when(api.createCheckout(any(), anyInt(), any())).thenReturn(checkoutResponse);
    when(translationsService.getValidateLocale(anyString(), anyList(), anyString()))
        .thenReturn(LOCALE);
    var context =
        new PaymentCreationContext(
            PAYMENT_ID,
            PAYMENT_REQUEST_DATA_EXPLICIT_WITHOUT_EXCHANGE,
            user,
            PAYMENT_LOCATION,
            LOCALE,
            CONTEXT_PROPERTIES);
    context.setItemPrice(PRICE_DATA);

    ProviderCheckout checkout =
        xSollaService.createProviderCheckout(context, CLIENT_CONFIG, CUSTOMER_ID);

    assertThat(checkout.getCountry()).isEqualTo(COUNTRY);
    assertThat(checkout.getPriceData().getBasePrice().getCurrency()).isEqualTo(CURRENCY);
    assertThat(
            new PaymentBigDecimal(checkout.getPriceData().getLocalPrice().getTotalAmount())
                .getMoneyValue(checkout.getPriceData().getLocalPrice().getCurrency()))
        .isEqualTo(TOTAL);
    assertThat(checkout.getContextProperties()).isEqualTo(CONTEXT_PROPERTIES);
  }

  @Test
  void shouldFailCheckout() {
    when(api.createCheckout(any(), anyInt(), any())).thenThrow(new RuntimeException(""));
    when(translationsService.getValidateLocale(anyString(), anyList(), anyString()))
        .thenReturn(LOCALE);
    var context =
        new PaymentCreationContext(
            PAYMENT_ID,
            PAYMENT_REQUEST_DATA_EXPLICIT_WITHOUT_EXCHANGE,
            user,
            PAYMENT_LOCATION,
            LOCALE,
            CONTEXT_PROPERTIES);

    assertThrows(
        RequestToProviderException.class,
        () -> xSollaService.createProviderCheckout(context, CLIENT_CONFIG, CUSTOMER_ID));
  }

  @Test
  void shouldCreateRefund() throws RequestToProviderException {
    doReturn(new RefundResponse("Refund response"))
        .when(merchantApi)
        .createRefund(any(), anyLong(), anyLong(), any());
    var payment = Mockito.mock(Payment.class);
    when(payment.getOrderId()).thenReturn(ORDER_ID);
    when(payment.getPriceData()).thenReturn(Mockito.mock(PriceData.class));
    when(payment.getPriceData().getLocalPrice()).thenReturn(Mockito.mock(PriceDetail.class));
    when(payment.getPriceData().getBasePrice()).thenReturn(Mockito.mock(PriceDetail.class));
    when(payment.getPriceData().getLocalPrice().getTotalAmount())
        .thenReturn(TOTAL.getNumberStripped());
    when(payment.getPriceData().getBasePrice().getTotalAmount())
        .thenReturn(TOTAL.getNumberStripped());
    when(payment.getPriceData().getLocalPrice().getCurrency()).thenReturn(CURRENCY);
    when(payment.getPriceData().getBasePrice().getCurrency()).thenReturn(CURRENCY);
    ProviderRefundInformation refundInformation =
        xSollaService.createFullRefundRequest(
            payment, RefundReason.ACCIDENTAL_PURCHASE, CLIENT_CONFIG);

    assertNotNull(refundInformation);
  }

  @Test
  void shouldFailRefundByRequestToProviderException() {
    when(merchantApi.createRefund(any(), anyLong(), anyLong(), any()))
        .thenThrow(new BadRequestException("Request Error"));
    var payment = Mockito.mock(Payment.class);
    when(payment.getOrderId()).thenReturn(ORDER_ID);
    assertThrows(
        RequestToProviderException.class,
        () ->
            xSollaService.createFullRefundRequest(
                payment, RefundReason.ACCIDENTAL_PURCHASE, CLIENT_CONFIG));
  }

  @Test
  void shouldFailRefundByUnknownException() {
    when(merchantApi.createRefund(any(), anyInt(), anyInt(), any()))
        .thenThrow(new RuntimeException());
    var payment = Mockito.mock(Payment.class);
    when(payment.getOrderId()).thenReturn(ORDER_ID);
    when(payment.getPriceData()).thenReturn(Mockito.mock(PriceData.class));
    when(payment.getPriceData().getLocalPrice()).thenReturn(Mockito.mock(PriceDetail.class));
    when(payment.getPriceData().getLocalPrice().getTotalAmount())
        .thenReturn(TOTAL.getNumberStripped());
    when(payment.getPriceData().getLocalPrice().getCurrency()).thenReturn(CURRENCY);
    assertThrows(
        RequestToProviderException.class,
        () ->
            xSollaService.createFullRefundRequest(
                payment, RefundReason.ACCIDENTAL_PURCHASE, CLIENT_CONFIG));
  }

  private CheckoutResponse createCheckoutResponseMock() {
    return new CheckoutResponse(TOKEN_ID);
  }
}
