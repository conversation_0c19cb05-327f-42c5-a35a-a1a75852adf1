# logging
error.level=info
logging.appender=stdout
logging.layout=json

payment-gateway.affinities=["tophat", "prime", "msf"]

#Auth supplier
security.public.jwk_issuer.list = ["https://scopelyplaygami.okta.com/oauth2/aus3tgcy7ndxfYGHt697", "https://scopelyplaygami.okta.com/oauth2/aus3tge16o4ZbK2QF697", "https://scopelyplaygami.okta.com/oauth2/aus4k5whj0zaaouZL697"]

#web
jwt.payment-gateway-web.okta_issuer=https://scopelyplaygami.okta.com/oauth2/aus3tge16o4ZbK2QF697
jwt.payment-gateway-web.okta_client_id=0oa5g5rei8M9MRTUZ697
jwt.payment-gateway-web.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read

#web-internal
jwt.payment-gateway-web-internal.okta_issuer=https://scopelyplaygami.okta.com/oauth2/aus3tge16o4ZbK2QF697
jwt.payment-gateway-web-internal.okta_client_id=0oa5g5rei8M9MRTUZ697
jwt.payment-gateway-web-internal.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read

#webhook xsolla
jwt.payment-gateway-webhook-xsolla.okta_issuer=https://scopelyplaygami.okta.com/oauth2/aus3tge16o4ZbK2QF697
jwt.payment-gateway-webhook-xsolla.okta_client_id=0oa5g5rei8M9MRTUZ697
jwt.payment-gateway-webhook-xsolla.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read

#webhook DR
jwt.payment-gateway-webhook-digitalriver.okta_issuer=https://scopelyplaygami.okta.com/oauth2/aus3tge16o4ZbK2QF697
jwt.payment-gateway-webhook-digitalriver.okta_client_id=0oa5g5rei8M9MRTUZ697
jwt.payment-gateway-webhook-digitalriver.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read

#worker
jwt.payment-gateway-worker.okta_issuer=https://scopelyplaygami.okta.com/oauth2/aus3tge16o4ZbK2QF697
jwt.payment-gateway-worker.okta_client_id=0oa5g5rei8M9MRTUZ697
jwt.payment-gateway-worker.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read alert_sender_create alert_sender_system

#s3
payment-gateway.s3.bucket=playgami-payment-gateway-prod

#API configs
payment-gateway.ab.testing.http.url=https://arche-abtest.aprod.scopely.io
payment-gateway.metricscatalog.http.url=https://metricscatalog.aprod.scopely.io
payment-gateway.alertsender.http.url=https://alert-sender.aprod.scopely.io
payment-gateway.console.http.url=https://console.playgami.scopely.com/%s/payments/refunds
payment-gateway.arche.grpc.address=arche-grpc-default.aprod.scopely.io
# Before adding a new affinity GRPC address below, you need to:
#   1. create a private link to that address in the infrastructure-live repo
#   2. accept the new private link in the payments-infrastructure-live repo
payment-gateway.arche.grpc.affinity.addresses=["arche-grpc-dahtzee.aprod.scopely.io", "arche-grpc-wwe.aprod.scopely.io", "arche-grpc-bingo-bash.aprod.scopely.io", "arche-grpc-spacetime.aprod.scopely.io", "arche-grpc-tophat.aprod.scopely.io", "arche-grpc-msf.aprod.scopely.io", "arche-grpc-casino.aprod.scopely.io"]

# titan admin config
payment-gateway.admin.endpoint=https://titan-admin.scopely.io/
payment-gateway.titan.sqs.endpoint=https://sqs.us-east-1.amazonaws.com/095669871260/titan-collector-payments

#metrics catalog
metrics-catalog.apikey=fc2403cd-da8e-401f-a811-4d32f4b6e99d