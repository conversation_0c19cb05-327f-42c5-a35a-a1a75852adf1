# logging
kinesis.log.format=plain
error.level=debug

#payment-gateway.dynamodb.url=http://localhost:4566
#payment-gateway.sqs.url=http://localhost:4566

#Auth supplier
security.public.jwk_issuer.list = ["https://scopely.oktapreview.com/oauth2/aus1bjulqgqsG1Dke0h8", "https://scopely.oktapreview.com/oauth2/aus19hux5ke1s8GrB0h8"]

#web
jwt.payment-gateway-web.okta_issuer=https://scopely.oktapreview.com/oauth2/aus19hux5ke1s8GrB0h8
jwt.payment-gateway-web.okta_client_id=0oa1neyo52cO0UE4t0h8
jwt.payment-gateway-web.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read

#web-internal
jwt.payment-gateway-web-internal.okta_issuer=https://scopely.oktapreview.com/oauth2/aus19hux5ke1s8GrB0h8
jwt.payment-gateway-web-internal.okta_client_id=0oa1neyo52cO0UE4t0h8
jwt.payment-gateway-web-internal.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read

#worker
jwt.payment-gateway-worker.okta_issuer=https://scopely.oktapreview.com/oauth2/aus19hux5ke1s8GrB0h8
jwt.payment-gateway-worker.okta_client_id=0oa1neyo52cO0UE4t0h8
jwt.payment-gateway-worker.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read alert_sender_create alert_sender_system

#webhook xsolla
jwt.payment-gateway-webhook-xsolla.okta_issuer=https://scopely.oktapreview.com/oauth2/aus19hux5ke1s8GrB0h8
jwt.payment-gateway-webhook-xsolla.okta_client_id=0oa1neyo52cO0UE4t0h8
jwt.payment-gateway-webhook-xsolla.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read

#webhook DR
jwt.payment-gateway-webhook-digitalriver.okta_issuer=https://scopely.oktapreview.com/oauth2/aus19hux5ke1s8GrB0h8
jwt.payment-gateway-webhook-digitalriver.okta_client_id=0oa1neyo52cO0UE4t0h8
jwt.payment-gateway-webhook-digitalriver.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read

#API configs
payment-gateway.playerprofile.http.url=https://player-profile.adev.scopely.io
payment-gateway.metricscatalog.http.url=https://metricscatalog.adev.scopely.io
payment-gateway.alertsender.http.url=https://alert-sender.adev.scopely.io/
payment-gateway.console.http.url=https://latest.portal.adev.scopely.io/%s/payments/refunds
payment-gateway.ab.testing.http.url=https://arche-abtest.adev.scopely.io
payment-gateway.arche.grpc.address=arche-grpc-default.adev.scopely.io

# titan admin config
payment-gateway.admin.endpoint=https://preview-titan-admin.scopely.io/
#payment-gateway.titan.sqs.endpoint=http://localhost:4566/000000000000/titan-collector-payments

payment-gateway.ab.testing.dry-run=true

#s3
payment-gateway.s3.bucket=playgami-payment-gateway-dev

# statsD
metrics.statsd.host=/var/run/datadog/dsd.socket
metrics.statsd.port=0

#metrics catalog
metrics-catalog.apikey=9037498c-6c37-4eca-be19-bcc50a4e70ff
payment-gateway.use.mock.fraud.data=true

# CORS configuration
cors.allowed.domains=[ \
"http://localhost:3000", \
"http://localhost:9141", \
"http://localhost:9142", \
"https://editor.swagger.io" \
]

cors.allowed.dynamic.domains=[ \
"https?://((.*)\\\\.)?playgami\\\\.scopely\\\\.com", \
"https?://((.*)\\\\.)?scopely\\\\.io", \
"https?://((.*)\\\\.)?withbuddies\\\\.com", \
"https?://((.*)\\\\.)?docker\\\\.internal(:\\\\d+)?" \
]
