# logging
error.level=debug
logging.appender=stdout
logging.layout=json

#Auth supplier
security.public.jwk_issuer.list = ["https://scopely.oktapreview.com/oauth2/aus1bjulqgqsG1Dke0h8", "https://scopely.oktapreview.com/oauth2/aus19hux5ke1s8GrB0h8"]

#web
jwt.payment-gateway-web.okta_issuer=https://scopely.oktapreview.com/oauth2/aus19hux5ke1s8GrB0h8
jwt.payment-gateway-web.okta_client_id=0oa1neyo52cO0UE4t0h8
jwt.payment-gateway-web.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read

#web-internal
jwt.payment-gateway-web-internal.okta_issuer=https://scopely.oktapreview.com/oauth2/aus19hux5ke1s8GrB0h8
jwt.payment-gateway-web-internal.okta_client_id=0oa1neyo52cO0UE4t0h8
jwt.payment-gateway-web-internal.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read

#worker
jwt.payment-gateway-worker.okta_issuer=https://scopely.oktapreview.com/oauth2/aus19hux5ke1s8GrB0h8
jwt.payment-gateway-worker.okta_client_id=0oa1neyo52cO0UE4t0h8
jwt.payment-gateway-worker.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read alert_sender_create alert_sender_system

#webhook xsolla
jwt.payment-gateway-webhook-xsolla.okta_issuer=https://scopely.oktapreview.com/oauth2/aus19hux5ke1s8GrB0h8
jwt.payment-gateway-webhook-xsolla.okta_client_id=0oa1neyo52cO0UE4t0h8
jwt.payment-gateway-webhook-xsolla.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read

#webhook DR
jwt.payment-gateway-webhook-digitalriver.okta_issuer=https://scopely.oktapreview.com/oauth2/aus19hux5ke1s8GrB0h8
jwt.payment-gateway-webhook-digitalriver.okta_client_id=0oa1neyo52cO0UE4t0h8
jwt.payment-gateway-webhook-digitalriver.okta_client_scope=config arche_segments_read player_profile_write metrics_catalog_read

#API configs
payment-gateway.playerprofile.http.url=https://player-profile.adev.scopely.io
payment-gateway.metricscatalog.http.url=https://metricscatalog.adev.scopely.io
payment-gateway.alertsender.http.url=https://alert-sender.adev.scopely.io
payment-gateway.console.http.url=https://latest.portal.adev.scopely.io/%s/payments/refunds
payment-gateway.ab.testing.http.url=https://arche-abtest.adev.scopely.io
payment-gateway.arche.grpc.address=arche-grpc-default.adev.scopely.io

#s3
payment-gateway.s3.bucket=playgami-payment-gateway-dev

# titan admin config
payment-gateway.admin.endpoint=https://preview-titan-admin.scopely.io/
payment-gateway.titan.sqs.endpoint=https://sqs.us-east-1.amazonaws.com/821676841978/titan-collector-payments

#metrics catalog
metrics-catalog.apikey=9037498c-6c37-4eca-be19-bcc50a4e70ff
payment-gateway.use.mock.fraud.data=true

# CORS configuration
cors.allowed.domains=[ \
"https://apollo.scopely.io", \
"http://apollo.scopely.io", \
"https://staging-apollo.scopely.io", \
"http://staging-apollo.scopely.io", \
"https://pr-apollo.scopely.io", \
"http://pr-apollo.scopely.io", \
"http://staging-portal.scopely.io", \
"https://staging-portal.scopely.io", \
"https://portal.scopely.io", \
"http://portal.scopely.io", \
"http://development.galaxy.divshot.io", \
"http://staging.galaxy.divshot.io", \
"http://production.galaxy.divshot.io", \
"https://staging.wwechampions.com", \
"https://wwechampions.com", \
"https://dev.wwechampions.com", \
"https://facebook-dice.withbuddies.com", \
"https://preview-api.withbuddies.com", \
"http://pr-portal.scopely.io", \
"https://pr-portal.scopely.io", \
"https://inside.scopely.io", \
"https://portal.adev.scopely.io", \
"https://portal-realtime-events-explorer.adev.scopely.io", \
"http://localhost:9000-9200", \
"http://localhost:9090", \
"http://localhost:3025", \
"http://localhost:3474", \
"http://localhost:9030", \
"http://localhost:9020", \
"http://localhost:9080", \
"http://localhost:9142", \
"http://localhost:9141", \
"http://localhost:65007", \
"http://localhost:3024", \
"http://localhost:3000", \
"http://localhost:8000", \
"http://localhost:8080", \
"https://localhost:9000-9200", \
"https://localhost:9090", \
"https://localhost:3025", \
"https://localhost:3474", \
"https://localhost:9030", \
"https://localhost:9020", \
"https://localhost:9080", \
"https://localhost:9142", \
"https://localhost:65007", \
"https://localhost:3024", \
"https://localhost:3000", \
"https://localhost:8000", \
"https://localhost:8092", \
"http://test.cat", \
"https://web-sample-app.adev.scopely.io", \
"http://web-sample-app.adev.scopely.io", \
"https://payments-widget.adev.scopely.io", \
"https://payments-widget.aprod.scopely.io", \
"https://widget.dev.payments.playgami.scopely.com", \
"https://widget.prod.payments.playgami.scopely.com", \
"https://qtpcuymdvb.us-west-2.awsapprunner.com", \
"https://sandbox.dev.jnpr.io", \
"https://dev.playyahtzee.com", \
"https://staging.playyahtzee.com", \
"https://qa.playyahtzee.com", \
"https://sample-widget.dev.payments.playgami.scopely.com", \
"https://webportal.dev.tophat.withbuddies.com", \
"https://stage.webportal.tophat.withbuddies.com", \
"https://qa.webportal.tophat.withbuddies.com", \
"http://loadtest.tophat.withbuddies.com", \
"https://dev.classiccasino.com", \
"https://qa.classiccasino.com", \
"https://classiccasino.com", \
"http://dev.bingobash.com", \
"http://www.bingobash.com", \
"https://seam-dev-tunnel-490428612.us-east-1.elb.amazonaws.com", \
"https://3d44-91-126-33-188.ngrok-free.app", \
"https://canvas-dev.classiccasino.com", \
"https://canvas.classiccasino.com", \
"https://marvelstrikeforce.com", \
"https://wwedomination.com", \
"http://*************", \
"https://msf.geargames.com", \
"http://*************:3000", \
"http://*************:3000", \
"https://*************:3001", \
"https://*************:3001", \
"https://wheeloffortunemobile.com", \
"https://qa.wheeloffortunemobile.com", \
"https://dev.wheeloffortunemobile.com", \
"https://localhost:5123", \
"http://localhost:5123" \
]

cors.allowed.dynamic.domains=[ \
"https?://((.*)\\\\.)?playgami\\\\.scopely\\\\.com", \
"https?://((.*)\\\\.)?scopely\\\\.io", \
"https?://((.*)\\\\.)?playscrabble\\\\.com", \
"https?://((.*)\\\\.)?scrabble\\\\.com", \
"https?://((.*)\\\\.)?twdrts\\\\.com", \
"https?://((.*)\\\\.)?marvelstrikeforce\\\\.com", \
"https?://((.*)\\\\.)?msf\\\\.gg", \
"https?://((.*)\\\\.)?m3\\\\.scopelypv\\\\.com", \
"https?://((.*)\\\\.)?msf\\\\.geargames\\\\.com", \
"https?://((.*)\\\\.)?withbuddies\\\\.com", \
"https?://((.*)\\\\.)?startrekfleetcommand\\\\.com", \
"https?://((.*)\\\\.)?bingobash\\\\.com", \
"https?://((.*)\\\\.)?classiccasino\\\\.com", \
"https?://((.*)\\\\.)?wwechampions\\\\.com", \
"https?://((.*)\\\\.)?wwedomination\\\\.com", \
"https?://((.*)\\\\.)?digitgaming\\\\.com", \
"https?://((.*)\\\\.)?docker\\\\.internal(:\\\\d+)?" \
]
