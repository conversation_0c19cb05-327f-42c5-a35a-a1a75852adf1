package com.scopely.paymentgateway.config;

import com.scopely.proteus.config.ConfigKey;
import com.scopely.proteus.config.Default;
import java.util.List;
import java.util.Optional;

public interface PaymentGatewayConfig {

  @ConfigKey("payment-gateway.web.service.port")
  @Default.Integer(9090)
  Integer webServicePort();

  @ConfigKey("payment-gateway.web.service.thread.count")
  @Default.Integer(75)
  Integer webServiceThreadCount();

  @ConfigKey("payment-gateway.web.service.termination.grace.period.seconds")
  @Default.Integer(60)
  Integer terminationGracePeriodSeconds();

  @ConfigKey("payment-gateway.dynamodb.url")
  Optional<String> dynamoDBUrl();

  @Default.Long(15)
  @ConfigKey("payment-gateway.expiration.time.minutes")
  long expirationTimeMinutes();

  @Default.Long(15)
  @Config<PERSON>ey("payment-gateway.keepalive.cancelation.time.seconds")
  long cancelationTimeSeconds();

  @Default.Integer(3)
  @ConfigKey("payment-gateway.common.retry.policy.max.retries")
  int commonRetryPolicyMaxRetries();

  @Default.Long(500)
  @ConfigKey("payment-gateway.common.retry.policy.backoff.millis")
  long commonRetryPolicyBackoffMillis();

  @Default.Long(2000)
  @ConfigKey("payment-gateway.common.retry.policy.max.delay.millis")
  long commonRetryPolicyMaxDelayMillis();

  @Default.Integer(120)
  @ConfigKey("payment-gateway.geolocation.refresh.cache.seconds")
  int geolocationRefreshCacheSeconds();

  @Default.Long(1000)
  @ConfigKey("payment-gateway.geolocation.maximum.size.cache")
  Long geolocationMaximumSizeCache();

  @Default.Integer(5)
  @ConfigKey("payment-gateway.refresh.apps.cache.interval.minutes")
  int refreshAppsCacheIntervalMinutes();

  @Default.Integer(8)
  @ConfigKey("payment-gateway.payment.id.generator.length")
  int paymentIdLength();

  @Default.String("ABCDEFGHJKLMNPQRSTUVWXYZ23456789")
  @ConfigKey("payment-gateway.payment.id.generator.alphabet")
  String paymentIdAlphabet();

  @Default.String("payments")
  @ConfigKey("payment-gateway.payment.id.generator.salt")
  String paymentIdSalt();

  @Default.Long(120)
  @ConfigKey("payment-gateway.locking.conflict.exception.count.cache.ttl")
  long lockingConflictExceptionCountCacheTtl();

  @Default.Long(900)
  @ConfigKey("payment-gateway.provider.fees.expiration.cache.ttl")
  long providerFeesExpirationCacheTtl();

  @Default.Long(43200)
  @ConfigKey("payment-gateway.country.conversion.expiration.time.seconds")
  long countryConversionExpirationTimeSeconds();

  @ConfigKey("payment-gateway.s3.bucket")
  String gatewayS3Bucket();

  @Default.Long(900)
  @ConfigKey("payment-gateway.localizedprice.cache.ttl.seconds")
  long localizedPricesExpirationTimeSeconds();

  @Default.Integer(32)
  @ConfigKey("payment-gateway.purchasetoken.length")
  int purchaseTokenLength();

  @Default.Long(86400)
  @ConfigKey("payment-gateway.translations.cache.ttl.seconds")
  long translationsExpirationTimeSeconds();

  @Default.Long(900)
  @ConfigKey("payment-gateway.features.cache.ttl.seconds")
  long featuresExpirationTimeSeconds();

  @Default.Integer(3)
  @ConfigKey("payment-gateway.web.create.payment.retries")
  int createPaymentRetries();

  @Default.Long(10)
  @ConfigKey("payment-gateway.unclaimed.payments.delay.seconds")
  long unclaimedPaymentsDelaySeconds();

  @Default.String("[]")
  @ConfigKey("payment-gateway.affinities")
  List<String> affinities();

  @ConfigKey("payment-gateway.console.http.url")
  String consoleUrlTemplate();

  @ConfigKey("metrics-catalog.apikey")
  String metricsCatalogApiKey();

  @Default.Boolean(false)
  @ConfigKey("payment-gateway.use.mock.fraud.data")
  Boolean useMockFraudData();

  @Default.Integer(1000)
  @ConfigKey("payment-gateway.titan.context.properties.max.length")
  int titanContextPropertiesMaxLength();
}
