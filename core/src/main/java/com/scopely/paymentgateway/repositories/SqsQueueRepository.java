package com.scopely.paymentgateway.repositories;

import static com.scopely.proteus.logging.Log.debug;
import static com.scopely.proteus.logging.Log.error;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.exceptions.InvalidTaskInSQSQueueException;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import javax.inject.Singleton;
import net.jodah.failsafe.Failsafe;
import net.jodah.failsafe.RetryPolicy;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;

@Singleton
public class SqsQueueRepository implements QueueRepository {
  private final SqsClient sqsClient;
  private final ObjectMapper objectMapper;

  @Inject
  public SqsQueueRepository(SqsClient sqsClient, ObjectMapper objectMapper) {
    this.sqsClient = sqsClient;
    this.objectMapper = objectMapper;
  }

  @Override
  public void queueTask(String queueUrl, QueueMessage taskToQueue) {
    queueTask(queueUrl, null, taskToQueue);
  }

  @Override
  public void queueTask(String queueUrl, String id, QueueMessage taskToQueue) {
    String messageBody;
    try {
      messageBody = this.objectMapper.writeValueAsString(taskToQueue);
    } catch (JsonProcessingException e) {
      throw new InvalidTaskInSQSQueueException(e.getMessage(), e);
    }

    SendMessageRequest request =
        SendMessageRequest.builder()
            .queueUrl(queueUrl)
            .messageBody(messageBody)
            .messageGroupId(id)
            .build();

    sendWithRetryPolicy(queueUrl, messageBody, request);
  }

  private void sendWithRetryPolicy(
      String queueUrl, String messageBody, SendMessageRequest request) {
    RetryPolicy retryPolicy =
        new RetryPolicy()
            .withBackoff(2, 16, TimeUnit.SECONDS)
            .withMaxDuration(1, TimeUnit.MINUTES)
            .retryOn(
                throwable -> {
                  error(throwable, "Failed to queue task on queue {}", queueUrl);
                  return true;
                });

    Failsafe.with(retryPolicy)
        .onSuccess(sqsRequest -> debug("Sent task {} to queue {}", messageBody, queueUrl))
        .run(() -> sqsClient.sendMessage(request));
  }
}
