package com.scopely.paymentgateway.repositories.daos.mapper;

import com.scopely.paymentgateway.model.provider.fees.ProviderFees;
import com.scopely.paymentgateway.repositories.daos.ProviderFeesDAO;
import java.util.List;

public class ProviderFeeMapper {

  private ProviderFeeMapper() {}

  public static ProviderFeesDAO providerFeeToProviderFeeDAO(ProviderFees fees) {
    return new ProviderFeesDAO.Builder()
        .setApiKey(fees.getApiKey())
        .setZone(fees.getZone())
        .setCurrency(fees.getCurrency())
        .setHeader(
            ProviderFeesDAO.buildHeader(fees.getProvider(), fees.getZone(), fees.getCurrency()))
        .setProvider(fees.getProvider())
        .addAllPercentages(fees.getPercentages())
        .setFlatFee(fees.getFlatFee())
        .build();
  }

  public static ProviderFees providerFeeDAOToProviderFee(ProviderFeesDAO fee) {
    return ProviderFees.Builder.from(
        fee.getApiKey(),
        fee.getZone(),
        fee.getCurrency(),
        fee.getProvider(),
        fee.getPercentages(),
        fee.getFlatFee());
  }

  public static List<ProviderFees> providerFeeDAOsToProviderFees(List<ProviderFeesDAO> fees) {
    return fees.stream().map(ProviderFeeMapper::providerFeeDAOToProviderFee).toList();
  }
}
