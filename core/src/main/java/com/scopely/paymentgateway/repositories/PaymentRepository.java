package com.scopely.paymentgateway.repositories;

import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.repositories.daos.BackfillPage;
import com.scopely.paymentgateway.repositories.daos.PaginationToken;
import com.scopely.paymentgateway.repositories.daos.PaymentDAO;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByExternalId;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByUnclaimed;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByUser;
import java.util.List;
import java.util.Optional;

public interface PaymentRepository {

  Payment save(Payment payment) throws LockingConflictException;

  Optional<Payment> getPayment(String paymentId);

  /**
   * @deprecated Use PaymentService.getPayment instead
   */
  @Deprecated
  Payment getPaymentUnchecked(String paymentId) throws PaymentNotFoundException;

  List<PaymentIndexByExternalId> getPaymentsByExternalId(
      String apiKey, String userId, String externalId);

  Optional<Payment> getPaymentByOrderId(String orderId);

  Optional<Payment> getPaymentBySessionId(String providerId, String providerIdentifier);

  List<PaymentIndexByUnclaimed> getUnclaimedPaymentsByUser(String apiKey, String userId);

  List<PaymentIndexByUnclaimed> getUnclaimedPaymentsByGame(String apiKey);

  List<PaymentIndexByUser> getPaymentsByUser(String apiKey, String userId);

  BackfillPage<PaymentDAO> getUnclaimedPaymentsPaginated(PaginationToken token);

  BackfillPage<PaymentIndexByUnclaimed> getUnclaimedPaymentsFromIndexPaginated(
      PaginationToken token);
}
