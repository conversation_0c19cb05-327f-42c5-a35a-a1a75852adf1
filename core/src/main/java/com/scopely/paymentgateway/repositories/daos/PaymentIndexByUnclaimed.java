package com.scopely.paymentgateway.repositories.daos;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.Instant;
import javax.annotation.Nullable;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = PaymentIndexByUnclaimed.Builder.class)
@DynamoDBTable(tableName = PaymentIndexByUnclaimed.TABLE)
public interface PaymentIndexByUnclaimed {

  String TABLE = "PaymentGateway_Payment";

  String GLOBAL_SECONDARY_INDEX_NAME = "GSI_Payment_Unclaimed_Completed";

  String HASH_KEY = "paymentId";

  String GSI_HASH_KEY = "unclaimedCompleted";

  String GSI_RANGE_KEY = "userId";

  @DynamoDBHashKey(attributeName = HASH_KEY)
  String getPaymentId();

  @DynamoDBIndexHashKey(
      attributeName = GSI_HASH_KEY,
      globalSecondaryIndexName = GLOBAL_SECONDARY_INDEX_NAME)
  @JsonProperty(GSI_HASH_KEY)
  String getUnclaimedCompleted();

  @DynamoDBIndexRangeKey(
      attributeName = GSI_RANGE_KEY,
      globalSecondaryIndexName = GLOBAL_SECONDARY_INDEX_NAME)
  String getUserId();

  @Nullable
  String getExternalId();

  Instant getCreatedAt();

  Instant getUpdatedAt();

  class Builder extends PaymentIndexByUnclaimed_Builder {

    public Builder() {}
  }
}
