package com.scopely.paymentgateway.repositories;

import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.model.RefundNotFoundException;
import com.scopely.paymentgateway.model.refund.Refund;
import java.util.List;
import java.util.Optional;

public interface RefundRepository {

  Refund save(Refund refund) throws LockingConflictException;

  List<Refund> getRefunds(String paymentId);

  Optional<Refund> getRefundByRefundId(String refundId);

  Refund getRefundByRefundIdUnchecked(String refundId) throws RefundNotFoundException;

  void deleteRefund(Refund refund);

  void deleteReturn(Refund refund);
}
