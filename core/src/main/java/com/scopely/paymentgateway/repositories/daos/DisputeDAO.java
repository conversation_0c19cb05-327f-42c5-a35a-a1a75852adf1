package com.scopely.paymentgateway.repositories.daos;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeStatus;
import com.scopely.paymentgateway.utils.DynamoDBUtils;
import org.inferred.freebuilder.FreeBuilder;
import org.javamoney.moneta.Money;

@FreeBuilder
@JsonDeserialize(builder = DisputeDAO.Builder.class)
@DynamoDBTable(tableName = ReversalDAO.TABLE)
public interface DisputeDAO extends ReversalDAO {

  String getDisputeReason();

  DisputeStatus getStatus();

  class Builder extends DisputeDAO_Builder {

    public Builder() {
      setLocalAmount(Money.of(0, "USD"));
    }

    @Override
    public DisputeDAO build() {
      if (getLocalAmount().isZero()) {
        setLocalAmount(getAmount());
      }
      setApiKeyAndUserId(DynamoDBUtils.buildCompositeAttribute(getApiKey(), getUserId()));
      return super.build();
    }
  }
}
