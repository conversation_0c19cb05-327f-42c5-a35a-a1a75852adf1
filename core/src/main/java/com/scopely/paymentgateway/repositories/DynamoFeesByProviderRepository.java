package com.scopely.paymentgateway.repositories;

import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SERVICE;
import static com.scopely.paymentgateway.repositories.daos.ProviderFeesDAO.HASH_KEY;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBQueryExpression;
import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.CustomerIdRepositoryException;
import com.scopely.paymentgateway.exceptions.FeeException;
import com.scopely.paymentgateway.logging.MetadataKeys;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.provider.fees.ProviderFees;
import com.scopely.paymentgateway.repositories.daos.ProviderFeesDAO;
import com.scopely.paymentgateway.repositories.daos.mapper.ProviderFeeMapper;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.DynamoDBQueryHelper;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import java.util.Map;
import javax.inject.Inject;
import javax.inject.Singleton;
import net.jodah.failsafe.Failsafe;
import net.jodah.failsafe.RetryPolicy;
import net.jodah.failsafe.function.CheckedConsumer;

@Singleton
public class DynamoFeesByProviderRepository implements FeesByProviderRepository {

  private final JsonDynamoMapper dynamoMapper;
  private final RetryPolicy retryPolicy;
  private final StatsDClient statsDClient;

  @Inject
  public DynamoFeesByProviderRepository(
      JsonDynamoMapper dynamoMapper, RetryPolicy retryPolicy, StatsDClient statsDClient) {
    this.dynamoMapper = dynamoMapper;
    this.retryPolicy = retryPolicy;
    this.statsDClient = statsDClient;
  }

  public List<ProviderFees> getFeesByApiKey(String apiKey) {
    return ProviderFeeMapper.providerFeeDAOsToProviderFees(
        Failsafe.with(retryPolicy)
            .withFallback(
                (CheckedConsumer<Throwable>)
                    t -> {
                      new PaymentGatewayLogBuilder()
                          .addApiKey(apiKey)
                          .build()
                          .error(t, "Unable to query PK(" + apiKey + ") to retrieve the fees");

                      statsDClient.increment(
                          StatsConstants.DD_REPOSITORY_OPERATION_GET,
                          MetricsUtils.buildTags(
                              List.of(TAG_SERVICE, TAG_RESULT),
                              List.of(
                                  ProviderFeesDAO.TABLE,
                                  StatsConstants.OperationResults.UNHANDLED_ERROR)));
                      throw new FeeException(t.getMessage());
                    })
            .onSuccess(
                (List<ProviderFeesDAO> fees) -> {
                  new PaymentGatewayLogBuilder()
                      .addApiKey(apiKey)
                      .build()
                      .debug("Query succeeded for pk " + apiKey);
                })
            .get(
                () -> {
                  var dynamoFilter = new DynamoDBQueryHelper();
                  dynamoFilter.addKeyCondition(HASH_KEY, apiKey);
                  var query =
                      new DynamoDBQueryExpression<ProviderFeesDAO>()
                          .withConsistentRead(false)
                          .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
                          .withFilterExpression(dynamoFilter.getFilterExpressionChain())
                          .withExpressionAttributeValues(dynamoFilter.getAttributeValues());
                  return dynamoMapper.queryAll(ProviderFeesDAO.class, query);
                }));
  }

  public List<ProviderFees> getFeesByCountryAndCurrencyAndProvider(
      String apiKey, PaymentProviderIdentifier provider, String country, String currency) {
    return ProviderFeeMapper.providerFeeDAOsToProviderFees(
        Failsafe.with(retryPolicy)
            .withFallback(
                (CheckedConsumer<Throwable>)
                    t -> {
                      new PaymentGatewayLogBuilder()
                          .addApiKey(apiKey)
                          .build()
                          .error(t, "Unable to query PK(" + apiKey + ") to retrieve the fees");
                      statsDClient.increment(
                          StatsConstants.DD_REPOSITORY_OPERATION_GET,
                          MetricsUtils.buildTags(
                              List.of(TAG_SERVICE, TAG_RESULT),
                              List.of(
                                  ProviderFeesDAO.TABLE,
                                  StatsConstants.OperationResults.UNHANDLED_ERROR)));
                      throw new FeeException(t.getMessage());
                    })
            .onSuccess(
                (List<ProviderFeesDAO> fees) -> {
                  new PaymentGatewayLogBuilder()
                      .addApiKey(apiKey)
                      .build()
                      .debug("Query succeeded for pk " + apiKey);
                })
            .get(
                () ->
                    dynamoMapper
                        .load(
                            ProviderFeesDAO.class,
                            apiKey,
                            ProviderFeesDAO.buildHeader(provider, country, currency))
                        .stream()
                        .toList()));
  }

  @Override
  public void save(ProviderFees fees) {
    this.save(ProviderFeeMapper.providerFeeToProviderFeeDAO(fees));
  }

  private void save(ProviderFeesDAO feeDao) {
    Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  new PaymentGatewayLogBuilder()
                      .addApiKey(feeDao.getApiKey())
                      .addMetadata(
                          Map.of(
                              MetadataKeys.COUNTRY.toString(),
                              feeDao.getZone(),
                              MetadataKeys.CURRENCY.toString(),
                              feeDao.getCurrency() != null ? feeDao.getCurrency() : ""))
                      .build()
                      .error(
                          t,
                          "Unable to save fee for "
                              + feeDao.getApiKey()
                              + " and country "
                              + feeDao.getZone()
                              + " and currency "
                              + feeDao.getCurrency()
                              + "to dynamo");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_SAVE,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              ProviderFeesDAO.TABLE,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new CustomerIdRepositoryException(t);
                })
        .run(() -> dynamoMapper.save(feeDao));
  }
}
