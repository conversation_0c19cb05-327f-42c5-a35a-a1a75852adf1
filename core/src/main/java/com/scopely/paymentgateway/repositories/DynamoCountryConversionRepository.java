package com.scopely.paymentgateway.repositories;

import static com.google.common.collect.ImmutableMap.of;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SERVICE;

import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.CountryExchangeRatesRepositoryException;
import com.scopely.paymentgateway.model.countryconversion.CountryConversion;
import com.scopely.paymentgateway.repositories.daos.CountryConversionDAO;
import com.scopely.paymentgateway.repositories.daos.mapper.CountryConversionDAOMapper;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;
import net.jodah.failsafe.Failsafe;
import net.jodah.failsafe.RetryPolicy;
import net.jodah.failsafe.function.CheckedConsumer;

@Singleton
public class DynamoCountryConversionRepository {
  private final JsonDynamoMapper dynamoMapper;
  private final RetryPolicy retryPolicy;
  private final StatsDClient statsDClient;

  private static final String COUNTRY_ID = "countryId";

  @Inject
  public DynamoCountryConversionRepository(
      JsonDynamoMapper dynamoMapper, RetryPolicy retryPolicy, StatsDClient statsDClient) {
    this.dynamoMapper = dynamoMapper;
    this.retryPolicy = retryPolicy;
    this.statsDClient = statsDClient;
  }

  public void save(CountryConversion countryConversion) {
    CountryConversionDAO dao =
        CountryConversionDAOMapper.countryExchangeRateToCountryExchangeRateDAO(countryConversion);
    Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(COUNTRY_ID, dao.getCountryId()))
                      .error(t, "Unable to save country conversion rate");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_SAVE,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              CountryConversionDAO.TABLE,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new CountryExchangeRatesRepositoryException(t);
                })
        .run(() -> dynamoMapper.save(dao));
  }

  public Optional<CountryConversion> getCountryConversionForCurrency(String countryId) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(COUNTRY_ID, countryId))
                      .error(t, "Unable to load paymentSessionIds");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              CountryConversionDAO.TABLE,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new CountryExchangeRatesRepositoryException(t);
                })
        .onSuccess(
            (Optional<CountryConversion> optionalCountryExchangeRate) -> {
              if (optionalCountryExchangeRate.isEmpty()) {
                Log.withMetadata(of(COUNTRY_ID, countryId))
                    .debug("Any conversion rate found for this country");
              }
            })
        .get(() -> dynamoMapper.load(CountryConversionDAO.class, countryId))
        .map(CountryConversionDAOMapper::countryExchangeRateDAOToCountryExchangeRate);
  }
}
