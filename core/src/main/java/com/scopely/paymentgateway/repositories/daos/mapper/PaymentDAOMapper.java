package com.scopely.paymentgateway.repositories.daos.mapper;

import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import com.scopely.paymentgateway.repositories.daos.PaymentDAO;

public class PaymentDAOMapper {

  public static PaymentDAO paymentToPaymentDao(Payment payment) {
    return new PaymentDAO.Builder()
        .setPaymentId(payment.getPaymentId())
        .setStatus(payment.getPaymentStatus().name())
        .setUserId(payment.getUserId())
        .setDeviceToken(payment.getDeviceToken())
        .setApiKey(payment.getApiKey())
        .setPriceData(payment.getPriceData())
        .setPaymentMethodUsed(payment.getPaymentMethodUsed())
        .setClaimed(payment.getClaimed())
        .setClaimedAt(payment.getClaimedAt())
        .setReversedAt(payment.getReversedAt())
        .setReceiptSentAt(payment.getReceiptSentAt())
        .setReceiptId(payment.getReceiptId())
        .setCountry(payment.getCountry())
        .setCreatedAt(payment.getCreatedAt())
        .setUpdatedAt(payment.getUpdatedAt())
        .setOrderId(payment.getOrderId())
        .setVersion(payment.getVersion())
        .setTrackingId(payment.getTrackingId())
        .setProviderStatus(payment.getProviderStatus())
        .setErrorMessage(payment.getErrorMessage())
        .setItemData(payment.getItemData())
        .setSavedPaymentMethod(payment.isSavedPaymentMethod())
        .setVip(payment.isVip())
        .setProviderFallback(payment.isProviderFallback())
        .setProviderData(payment.getProviderData())
        .setLocale(payment.getLocale())
        .setSandbox(payment.isSandbox())
        .setExternalId(payment.getExternalId())
        .setPlatform(payment.getPlatform())
        .setPurchaseToken(payment.getPurchaseToken())
        .addAllFeatures(payment.getFeatures())
        .setContextProperties(payment.getContextProperties())
        .build();
  }

  public static Payment paymentDaoToPayment(PaymentDAO paymentDAO) {
    return new Payment.Builder()
        .setPaymentId(paymentDAO.getPaymentId())
        .setPaymentStatus(PaymentStatus.valueOf(paymentDAO.getStatus()))
        .setUserId(paymentDAO.getUserId())
        .setDeviceToken(paymentDAO.getDeviceToken())
        .setApiKey(paymentDAO.getApiKey())
        .setPriceData(paymentDAO.getPriceData())
        .setPaymentMethodUsed(paymentDAO.getPaymentMethodUsed())
        .setClaimed(paymentDAO.getClaimed())
        .setClaimedAt(paymentDAO.getClaimedAt())
        .setReversedAt(paymentDAO.getReversedAt())
        .setReceiptSentAt(paymentDAO.getReceiptSentAt())
        .setReceiptId(paymentDAO.getReceiptId())
        .setCountry(paymentDAO.getCountry())
        .setCreatedAt(paymentDAO.getCreatedAt())
        .setUpdatedAt(paymentDAO.getUpdatedAt())
        .setOrderId(paymentDAO.getOrderId())
        .setVersion(paymentDAO.getVersion())
        .setTrackingId(paymentDAO.getTrackingId())
        .setProviderStatus(paymentDAO.getProviderStatus())
        .setErrorMessage(paymentDAO.getErrorMessage())
        .setItemData(paymentDAO.getItemData())
        .setSavedPaymentMethod(paymentDAO.isSavedPaymentMethod())
        .setVip(paymentDAO.isVip())
        .setProviderFallback(paymentDAO.isProviderFallback())
        .setProviderData(paymentDAO.getProviderData())
        .setLocale(paymentDAO.getLocale())
        .setSandbox(paymentDAO.isSandbox())
        .setExternalId(paymentDAO.getExternalId())
        .setPlatform(paymentDAO.getPlatform())
        .addAllFeatures(paymentDAO.getFeatures())
        .setContextProperties(paymentDAO.getContextProperties())
        .build();
  }
}
