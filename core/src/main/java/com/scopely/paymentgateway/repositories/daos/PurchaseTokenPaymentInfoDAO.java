package com.scopely.paymentgateway.repositories.daos;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = PurchaseTokenPaymentInfoDAO.Builder.class)
@DynamoDBTable(tableName = PurchaseTokenPaymentInfoDAO.TABLE)
public interface PurchaseTokenPaymentInfoDAO {

  String TABLE = "PaymentGateway_PurchaseTokenPaymentInfo";
  String PURCHASE_TOKEN = "purchaseToken";
  String PAYMENT_ID = "paymentId";
  String HASH_KEY = PURCHASE_TOKEN;

  @DynamoDBHashKey(attributeName = HASH_KEY)
  String getPurchaseToken();

  String getPaymentId();

  Long getLastUpdateAt();

  Long getExpireAt();

  boolean isUsed();

  class Builder extends PurchaseTokenPaymentInfoDAO_Builder {
    public Builder() {
      setUsed(false);
    }
  }
}
