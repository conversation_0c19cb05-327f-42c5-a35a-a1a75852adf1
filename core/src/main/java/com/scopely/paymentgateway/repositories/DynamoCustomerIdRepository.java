package com.scopely.paymentgateway.repositories;

import static com.google.common.collect.ImmutableMap.of;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SERVICE;

import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.CustomerIdRepositoryException;
import com.scopely.paymentgateway.model.customer.CustomerId;
import com.scopely.paymentgateway.repositories.daos.CustomerIdDAO;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;
import net.jodah.failsafe.Failsafe;
import net.jodah.failsafe.RetryPolicy;
import net.jodah.failsafe.function.CheckedConsumer;

@Singleton
public class DynamoCustomerIdRepository implements CustomerIdRepository {

  private final JsonDynamoMapper dynamoMapper;
  private final RetryPolicy retryPolicy;
  private final StatsDClient statsDClient;

  private static final String INTERNAL_CUSTOMER_ID = "internalCustomerId";

  @Inject
  public DynamoCustomerIdRepository(
      JsonDynamoMapper dynamoMapper, RetryPolicy retryPolicy, StatsDClient statsDClient) {
    this.dynamoMapper = dynamoMapper;
    this.retryPolicy = retryPolicy;
    this.statsDClient = statsDClient;
  }

  @Override
  public void save(CustomerId customerId) {
    CustomerIdDAO dao =
        new CustomerIdDAO.Builder()
            .setExternalId(customerId.getExternalId())
            .setInternalId(customerId.getInternalId())
            .build();
    Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(INTERNAL_CUSTOMER_ID, dao.getInternalId()))
                      .error(t, "Unable to save customerId");

                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_SAVE,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              CustomerIdDAO.TABLE,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new CustomerIdRepositoryException(t);
                })
        .run(() -> dynamoMapper.save(dao));
  }

  @Override
  public Optional<CustomerId> getCustomerId(String internalCustomerId) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(INTERNAL_CUSTOMER_ID, internalCustomerId))
                      .error(t, "Unable to load customerId");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              CustomerIdDAO.TABLE,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new CustomerIdRepositoryException(t);
                })
        .onSuccess(
            (Optional<CustomerIdDAO> optionalCustomerIdDAO) -> {
              if (optionalCustomerIdDAO.isEmpty()) {
                Log.withMetadata(of(INTERNAL_CUSTOMER_ID, internalCustomerId))
                    .debug("CustomerId not found");
              }
            })
        .get(() -> dynamoMapper.load(CustomerIdDAO.class, internalCustomerId))
        .map(
            (optional) ->
                new CustomerId.Builder()
                    .setExternalId(optional.getExternalId())
                    .setInternalId(optional.getInternalId())
                    .build());
  }
}
