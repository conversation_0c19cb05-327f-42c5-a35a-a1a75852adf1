package com.scopely.paymentgateway.repositories.daos;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = CustomerIdDAO.Builder.class)
@DynamoDBTable(tableName = CustomerIdDAO.TABLE)
public interface CustomerIdDAO {
  String TABLE = "PaymentGateway_CustomerId";
  String HASH_KEY = "internalId";

  @DynamoDBHashKey(attributeName = HASH_KEY)
  String getInternalId();

  String getExternalId();

  class Builder extends CustomerIdDAO_Builder {}
}
