package com.scopely.paymentgateway.repositories;

import com.scopely.paymentgateway.exceptions.repository.ReversalRepositoryException;
import com.scopely.paymentgateway.model.reversal.Reversal;
import java.util.List;

public interface ReversalRepository {

  List<Reversal> getReversalsByPayment(String paymentId) throws ReversalRepositoryException;

  List<Reversal> getReversalsByUser(String apiKey, String userId)
      throws ReversalRepositoryException;
}
