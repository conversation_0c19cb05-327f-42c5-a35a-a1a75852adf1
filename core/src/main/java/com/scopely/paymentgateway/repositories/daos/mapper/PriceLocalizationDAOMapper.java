package com.scopely.paymentgateway.repositories.daos.mapper;

import com.scopely.paymentgateway.model.localizedprice.PriceLocalization;
import com.scopely.paymentgateway.repositories.daos.PriceLocalizationDAO;

public class PriceLocalizationDAOMapper {
  public static PriceLocalization priceLocalizationDaoToPriceLocalization(
      PriceLocalizationDAO priceLocalizationDAO) {
    return PriceLocalization.Builder.from(
        priceLocalizationDAO.getApiKey(),
        priceLocalizationDAO.getRangeKey(),
        priceLocalizationDAO.getPriceFilePath(),
        priceLocalizationDAO.getCreatedBy(),
        priceLocalizationDAO.getCreatedAt());
  }

  public static PriceLocalizationDAO localizedPriceVersionToPriceLocalizationDAO(
      PriceLocalization priceLocalization) {
    return new PriceLocalizationDAO.Builder()
        .setApiKey(priceLocalization.getApiKey())
        .setRangeKey(priceLocalization.getRangeKey())
        .setPriceFilePath(priceLocalization.getPriceFilePath())
        .setCreatedBy(priceLocalization.getCreatedBy())
        .setCreatedAt(priceLocalization.getCreatedAt())
        .build();
  }
}
