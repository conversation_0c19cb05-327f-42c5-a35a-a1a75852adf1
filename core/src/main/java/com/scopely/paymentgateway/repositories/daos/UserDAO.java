package com.scopely.paymentgateway.repositories.daos;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBVersionAttribute;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.user.UserBlockedReason;
import com.scopely.paymentgateway.utils.DynamoDBUtils;
import java.time.Instant;
import javax.annotation.Nullable;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = UserDAO.Builder.class)
@DynamoDBTable(tableName = UserDAO.TABLE)
public interface UserDAO {
  String TABLE = "PaymentGateway_User";

  String HASH_KEY = "apiKeyUserId";

  @DynamoDBHashKey(attributeName = HASH_KEY)
  String getApiKeyUserId();

  String getApiKey();

  String getUserId();

  boolean isBlocked();

  @Nullable
  Instant getBlockedAt();

  @Nullable
  String getBlockedBy();

  @Nullable
  UserBlockedReason getBlockedReason();

  @Nullable
  PaymentProviderIdentifier getProvider();

  Instant getUpdatedAt();

  @Nullable
  @DynamoDBVersionAttribute(attributeName = "version")
  Integer getVersion();

  class Builder extends UserDAO_Builder {
    public Builder() {}

    @Override
    public UserDAO build() {
      this.setApiKeyUserId(DynamoDBUtils.buildCompositeAttribute(getApiKey(), getUserId()));
      return super.build();
    }
  }
}
