package com.scopely.paymentgateway.repositories;

import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SERVICE;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBQueryExpression;
import com.amazonaws.services.dynamodbv2.model.ConditionalCheckFailedException;
import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.repository.DisputeRepositoryException;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import com.scopely.paymentgateway.repositories.daos.DisputeDAO;
import com.scopely.paymentgateway.repositories.daos.ReversalDAO;
import com.scopely.paymentgateway.repositories.daos.mapper.DisputeDAOMapper;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.DynamoDBQueryHelper;
import com.scopely.paymentgateway.utils.DynamoDBQueryHelper.Comparator;
import com.scopely.paymentgateway.utils.DynamoDBUtils;
import com.timgroup.statsd.StatsDClient;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DynamoDisputeRepository implements DisputeRepository {
  private final JsonDynamoMapper dynamoMapper;
  private final StatsDClient statsDClient;
  private final DisputeDAOMapper disputeDAOMapper;

  @Inject
  public DynamoDisputeRepository(
      JsonDynamoMapper dynamoMapper, StatsDClient statsDClient, DisputeDAOMapper disputeDAOMapper) {
    this.dynamoMapper = dynamoMapper;
    this.statsDClient = statsDClient;
    this.disputeDAOMapper = disputeDAOMapper;
  }

  @Override
  public Dispute save(Dispute dispute) throws LockingConflictException, DisputeRepositoryException {
    DisputeDAO disputeDAO = disputeDAOMapper.disputeToDisputeDao(dispute);
    try {
      DisputeDAO saved = dynamoMapper.saveAndGet(disputeDAO);
      return disputeDAOMapper.disputeDaoToDispute(saved);
    } catch (ConditionalCheckFailedException exception) {
      throw new LockingConflictException(exception);
    } catch (RuntimeException e) {
      statsDClient.increment(
          StatsConstants.DD_REPOSITORY_OPERATION_SAVE,
          MetricsUtils.buildTags(
              List.of(TAG_API_KEY, TAG_SERVICE, TAG_RESULT),
              List.of(
                  dispute.getApiKey(),
                  ReversalDAO.TABLE,
                  StatsConstants.OperationResults.UNHANDLED_ERROR)));
      throw new DisputeRepositoryException(e);
    }
  }

  @Override
  public List<Dispute> getByPayment(String paymentId) throws DisputeRepositoryException {
    var dynamoFilter = new DynamoDBQueryHelper();
    dynamoFilter.addKeyCondition(ReversalDAO.HASH_KEY, paymentId);
    dynamoFilter.addKeyCondition(
        Comparator.BEGINS, ReversalDAO.RANGE_KEY, disputeDAOMapper.getHeaderPrefix());
    var query =
        new DynamoDBQueryExpression<DisputeDAO>()
            .withConsistentRead(false)
            .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
            .withFilterExpression(dynamoFilter.getFilterExpressionChain())
            .withExpressionAttributeValues(dynamoFilter.getAttributeValues());
    try {
      var disputeDaos = dynamoMapper.queryAll(DisputeDAO.class, query);
      return disputeDaos.stream().map(disputeDAOMapper::disputeDaoToDispute).toList();
    } catch (RuntimeException e) {
      statsDClient.increment(
          StatsConstants.DD_REPOSITORY_OPERATION_GET,
          MetricsUtils.buildTags(
              List.of(TAG_SERVICE, TAG_RESULT),
              List.of(ReversalDAO.TABLE, StatsConstants.OperationResults.UNHANDLED_ERROR)));
      throw new DisputeRepositoryException(e);
    }
  }

  @Override
  public List<Dispute> getByUser(String apiKey, String userId) throws DisputeRepositoryException {
    var dynamoFilter = new DynamoDBQueryHelper();
    dynamoFilter.addKeyCondition(
        ReversalDAO.API_KEY_USER, DynamoDBUtils.buildCompositeAttribute(apiKey, userId));
    dynamoFilter.addFilterCondition(
        Comparator.BEGINS, ReversalDAO.RANGE_KEY, disputeDAOMapper.getHeaderPrefix());
    return getDisputes(dynamoFilter);
  }

  @Override
  public List<Dispute> getByUserFromDate(String apiKey, String userId, Instant dateFrom)
      throws DisputeRepositoryException {
    var dynamoFilter = new DynamoDBQueryHelper();
    dynamoFilter.addKeyCondition(
        ReversalDAO.API_KEY_USER, DynamoDBUtils.buildCompositeAttribute(apiKey, userId));
    dynamoFilter.addFilterCondition(
        Comparator.BEGINS, ReversalDAO.RANGE_KEY, disputeDAOMapper.getHeaderPrefix());
    dynamoFilter.addFilterCondition(
        Comparator.GREATER_THAN, ReversalDAO.DATE_KEY, dateFrom.toString());

    return getDisputes(dynamoFilter);
  }

  private List<Dispute> getDisputes(DynamoDBQueryHelper dynamoFilter) {
    var query =
        new DynamoDBQueryExpression<DisputeDAO>()
            .withConsistentRead(false)
            .withIndexName(ReversalDAO.APIKEY_USER_INDEX_NAME)
            .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
            .withExpressionAttributeValues(dynamoFilter.getAttributeValues())
            .withFilterExpression(dynamoFilter.getFilterExpressionChain());
    try {
      var disputeDaos = dynamoMapper.queryAll(DisputeDAO.class, query);
      return disputeDaos.stream().map(disputeDAOMapper::disputeDaoToDispute).toList();
    } catch (RuntimeException e) {
      statsDClient.increment(
          StatsConstants.DD_REPOSITORY_OPERATION_GET,
          MetricsUtils.buildTags(
              List.of(TAG_SERVICE, TAG_RESULT),
              List.of(
                  ReversalDAO.APIKEY_USER_INDEX_NAME,
                  StatsConstants.OperationResults.UNHANDLED_ERROR)));
      throw new DisputeRepositoryException(e);
    }
  }

  @Override
  public Optional<Dispute> getByDisputeId(String disputeId) throws DisputeRepositoryException {
    var dynamoFilter = new DynamoDBQueryHelper();
    dynamoFilter.addKeyCondition(
        ReversalDAO.RANGE_KEY, disputeDAOMapper.getHeaderFromDisputeId(disputeId));
    var query =
        new DynamoDBQueryExpression<DisputeDAO>()
            .withConsistentRead(false)
            .withIndexName(ReversalDAO.HEADER_INDEX_NAME)
            .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
            .withFilterExpression(dynamoFilter.getFilterExpressionChain())
            .withExpressionAttributeValues(dynamoFilter.getAttributeValues());
    try {
      var daos = dynamoMapper.queryAll(DisputeDAO.class, query);
      return daos.stream().map(disputeDAOMapper::disputeDaoToDispute).findFirst();
    } catch (RuntimeException e) {
      statsDClient.increment(
          StatsConstants.DD_REPOSITORY_OPERATION_GET,
          MetricsUtils.buildTags(
              List.of(TAG_SERVICE, TAG_RESULT),
              List.of(
                  ReversalDAO.HEADER_INDEX_NAME, StatsConstants.OperationResults.UNHANDLED_ERROR)));
      throw new DisputeRepositoryException(e);
    }
  }
}
