package com.scopely.paymentgateway.repositories;

import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SERVICE;

import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.PriceLocalizationException;
import com.scopely.paymentgateway.model.localizedprice.PriceLocalization;
import com.scopely.paymentgateway.repositories.daos.PriceLocalizationDAO;
import com.scopely.paymentgateway.repositories.daos.mapper.PriceLocalizationDAOMapper;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.dynamodb.DynamoDbMappedTable;
import com.scopely.proteus.dynamodb.DynamoDbMapper;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.GetItemEnhancedRequest;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

@Singleton
public class DynamoPriceLocalizationRepository implements PriceLocalizationRepository {

  private final StatsDClient statsDClient;
  private final DynamoDbMappedTable<PriceLocalizationDAO> priceLocalizationTable;
  private static final String ACTIVE_ITEM = "ACTIVE";
  private static final String HISTORIC_ITEM = "HISTORIC";

  @Inject
  public DynamoPriceLocalizationRepository(
      DynamoDbMapper dynamoDbMapper, StatsDClient statsDClient) {
    this.statsDClient = statsDClient;
    this.priceLocalizationTable = dynamoDbMapper.getTable(PriceLocalizationDAO.class);
  }

  @Override
  public Optional<PriceLocalization> getActivePriceLocalizationByApiKey(String apiKey)
      throws PriceLocalizationException {
    try {
      PriceLocalizationDAO saved =
          priceLocalizationTable.getItem(
              GetItemEnhancedRequest.builder()
                  .key(
                      Key.builder()
                          .partitionValue(apiKey)
                          .sortValue(PriceLocalizationDAO.ACTIVE)
                          .build())
                  .consistentRead(false)
                  .build());

      return Optional.ofNullable(saved)
          .map(PriceLocalizationDAOMapper::priceLocalizationDaoToPriceLocalization);

    } catch (RuntimeException e) {
      statsDClient.increment(
          StatsConstants.DD_REPOSITORY_OPERATION_GET,
          MetricsUtils.buildTags(
              List.of(TAG_SERVICE, TAG_RESULT),
              List.of(
                  PriceLocalizationDAO.TABLE, StatsConstants.OperationResults.UNHANDLED_ERROR)));
      throw new PriceLocalizationException(e);
    }
  }

  @Override
  public PriceLocalization saveNewPriceLocalizationVersion(PriceLocalization priceLocalization)
      throws LockingConflictException, PriceLocalizationException {
    try {
      statsDClient.increment(
          StatsConstants.DD_SAVE_LOCALIZATION_PRICES_HITS,
          MetricsUtils.buildTags(List.of(TAG_API_KEY), List.of(priceLocalization.getApiKey())));

      PriceLocalization activePriceLocalization =
          PriceLocalization.Builder.from(
              priceLocalization.getApiKey(),
              PriceLocalizationDAO.ACTIVE,
              priceLocalization.getPriceFilePath(),
              priceLocalization.getCreatedBy(),
              priceLocalization.getCreatedAt());

      PriceLocalizationDAO activeItemDAO =
          PriceLocalizationDAOMapper.localizedPriceVersionToPriceLocalizationDAO(
              activePriceLocalization);
      PriceLocalizationDAO historicItemDAO =
          PriceLocalizationDAOMapper.localizedPriceVersionToPriceLocalizationDAO(priceLocalization);

      // First it saves the historic item. If it fails, the active one won't be modified.
      // If the active one fails, the historic one will be kept in the database as it doesn't affect
      // the current state.
      putItem(historicItemDAO, HISTORIC_ITEM);
      putItem(activeItemDAO, ACTIVE_ITEM);

      return PriceLocalizationDAOMapper.priceLocalizationDaoToPriceLocalization(activeItemDAO);

    } catch (ConditionalCheckFailedException exception) {
      throw new LockingConflictException(exception);
    } catch (RuntimeException e) {
      statsDClient.increment(
          StatsConstants.DD_REPOSITORY_OPERATION_SAVE,
          MetricsUtils.buildTags(
              List.of(TAG_API_KEY, TAG_SERVICE, TAG_RESULT),
              List.of(
                  priceLocalization.getApiKey(),
                  PriceLocalizationDAO.TABLE,
                  StatsConstants.OperationResults.UNHANDLED_ERROR)));
      throw new PriceLocalizationException(e);
    }
  }

  private void putItem(PriceLocalizationDAO item, String itemType) {
    try {
      priceLocalizationTable.putItem(item);
    } catch (RuntimeException e) {
      String metric =
          switch (itemType) {
            case ACTIVE_ITEM -> StatsConstants.DD_SAVE_LOCALIZATION_PRICES_ERROR_ACTIVE;
            case HISTORIC_ITEM -> StatsConstants.DD_SAVE_LOCALIZATION_PRICES_ERROR_HISTORIC;
            default -> StatsConstants.DD_SAVE_LOCALIZATION_PRICES_ERROR;
          };

      statsDClient.increment(
          metric, MetricsUtils.buildTags(List.of(TAG_API_KEY), List.of(item.getApiKey())));
      throw e;
    }
  }
}
