package com.scopely.paymentgateway.repositories.daos;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBVersionAttribute;
import java.time.Instant;
import javax.annotation.Nullable;
import org.javamoney.moneta.Money;

public interface ReversalDAO {
  String TABLE = "PaymentGateway_Reversal";
  String HASH_KEY = "paymentId";
  String RANGE_KEY = "header";
  String DATE_KEY = "updatedAt";
  String API_KEY_USER = "apiKeyAndUserId";
  String APIKEY_USER_INDEX_NAME = "GSI_Reversal_User";
  String HEADER_INDEX_NAME = "GSI_Reversal_Header";

  @DynamoDBHashKey(attributeName = HASH_KEY)
  String getPaymentId();

  @DynamoDBRangeKey(attributeName = RANGE_KEY)
  String getHeader();

  @DynamoDBAttribute(attributeName = API_KEY_USER)
  String getApiKeyAndUserId();

  String getApiKey();

  String getUserId();

  Instant getCreatedAt();

  Instant getUpdatedAt();

  Money getAmount();

  Money getLocalAmount();

  @Nullable
  @DynamoDBVersionAttribute(attributeName = "version")
  Integer getVersion();
}
