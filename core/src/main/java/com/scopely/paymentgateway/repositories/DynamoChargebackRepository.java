package com.scopely.paymentgateway.repositories;

import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SERVICE;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBQueryExpression;
import com.amazonaws.services.dynamodbv2.model.ConditionalCheckFailedException;
import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.repository.ChargebackRepositoryException;
import com.scopely.paymentgateway.model.reversal.Chargeback;
import com.scopely.paymentgateway.repositories.daos.ChargebackDAO;
import com.scopely.paymentgateway.repositories.daos.ReversalDAO;
import com.scopely.paymentgateway.repositories.daos.mapper.ChargebackDAOMapper;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.DynamoDBQueryHelper;
import com.scopely.paymentgateway.utils.DynamoDBUtils;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DynamoChargebackRepository implements ChargebackRepository {
  private final JsonDynamoMapper dynamoMapper;
  private final StatsDClient statsDClient;
  private final ChargebackDAOMapper chargebackDAOMapper;

  @Inject
  public DynamoChargebackRepository(
      JsonDynamoMapper dynamoMapper, StatsDClient statsDClient, ChargebackDAOMapper daoMapper) {
    this.dynamoMapper = dynamoMapper;
    this.statsDClient = statsDClient;
    this.chargebackDAOMapper = daoMapper;
  }

  @Override
  public Chargeback save(Chargeback chargeback)
      throws LockingConflictException, ChargebackRepositoryException {
    ChargebackDAO chargebackDAO = chargebackDAOMapper.chargebackToChargebackDao(chargeback);
    try {
      ChargebackDAO saved = dynamoMapper.saveAndGet(chargebackDAO);
      return chargebackDAOMapper.chargebackDaoToChargeback(saved);
    } catch (ConditionalCheckFailedException exception) {
      throw new LockingConflictException(exception);
    } catch (RuntimeException e) {
      statsDClient.increment(
          StatsConstants.DD_REPOSITORY_OPERATION_SAVE,
          MetricsUtils.buildTags(
              List.of(TAG_API_KEY, TAG_SERVICE, TAG_RESULT),
              List.of(
                  chargeback.getApiKey(),
                  ReversalDAO.TABLE,
                  StatsConstants.OperationResults.UNHANDLED_ERROR)));
      throw new ChargebackRepositoryException(e);
    }
  }

  @Override
  public Optional<Chargeback> getById(String chargebackId) throws ChargebackRepositoryException {
    var dynamoFilter = new DynamoDBQueryHelper();
    dynamoFilter.addKeyCondition(
        ReversalDAO.RANGE_KEY, chargebackDAOMapper.getHeaderFromChargebackId(chargebackId));
    var query =
        new DynamoDBQueryExpression<ChargebackDAO>()
            .withConsistentRead(false)
            .withIndexName(ReversalDAO.HEADER_INDEX_NAME)
            .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
            .withFilterExpression(dynamoFilter.getFilterExpressionChain())
            .withExpressionAttributeValues(dynamoFilter.getAttributeValues());
    try {
      var daos = dynamoMapper.queryAll(ChargebackDAO.class, query);
      return daos.stream().map(chargebackDAOMapper::chargebackDaoToChargeback).findFirst();
    } catch (RuntimeException e) {
      statsDClient.increment(
          StatsConstants.DD_REPOSITORY_OPERATION_GET,
          MetricsUtils.buildTags(
              List.of(TAG_SERVICE, TAG_RESULT),
              List.of(
                  ReversalDAO.HEADER_INDEX_NAME, StatsConstants.OperationResults.UNHANDLED_ERROR)));
      throw new ChargebackRepositoryException(e);
    }
  }

  @Override
  public List<Chargeback> getByUser(String apiKey, String userId)
      throws ChargebackRepositoryException {
    var dynamoFilter = new DynamoDBQueryHelper();
    dynamoFilter.addKeyCondition(
        ReversalDAO.API_KEY_USER, DynamoDBUtils.buildCompositeAttribute(apiKey, userId));
    dynamoFilter.addFilterCondition(
        DynamoDBQueryHelper.Comparator.BEGINS,
        ReversalDAO.RANGE_KEY,
        chargebackDAOMapper.getHeaderPrefix());
    var query =
        new DynamoDBQueryExpression<ChargebackDAO>()
            .withConsistentRead(false)
            .withIndexName(ReversalDAO.APIKEY_USER_INDEX_NAME)
            .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
            .withExpressionAttributeValues(dynamoFilter.getAttributeValues())
            .withFilterExpression(dynamoFilter.getFilterExpressionChain());
    try {
      var chargebacksDaos = dynamoMapper.queryAll(ChargebackDAO.class, query);
      return chargebacksDaos.stream().map(chargebackDAOMapper::chargebackDaoToChargeback).toList();
    } catch (RuntimeException e) {
      statsDClient.increment(
          StatsConstants.DD_REPOSITORY_OPERATION_GET,
          MetricsUtils.buildTags(
              List.of(TAG_SERVICE, TAG_RESULT),
              List.of(
                  ReversalDAO.APIKEY_USER_INDEX_NAME,
                  StatsConstants.OperationResults.UNHANDLED_ERROR)));
      throw new ChargebackRepositoryException(e);
    }
  }
}
