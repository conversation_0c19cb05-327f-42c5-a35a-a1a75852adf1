package com.scopely.paymentgateway.repositories.daos.mapper;

import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.repositories.daos.RefundDAO;

public class RefundDAOMapper {

  public static RefundDAO refundToRefundDao(Refund refund) {
    return new RefundDAO.Builder()
        .setApiKey(refund.getApiKey())
        .setPaymentId(refund.getPaymentId())
        .setRefundId(refund.getRefundId())
        .setRefundReason(refund.getRefundReason())
        .setCreatedAt(refund.getCreatedAt())
        .setUpdatedAt(refund.getUpdatedAt())
        .setRefundedAmount(refund.getRefundedAmount())
        .setRefundedLocalAmount(refund.getRefundedLocalAmount())
        .setFailureReason(refund.getFailureReason())
        .setStatus(refund.getStatus())
        .setVersion(refund.getVersion())
        .setProviderData(refund.getProviderData())
        .setPaymentMethodUsed(refund.getPaymentMethodUsed())
        .setRefundType(refund.getRefundType())
        .setReceiptSentAt(refund.getReceiptSentAt())
        .setReceiptId(refund.getReceiptId())
        .setSupportTicket(refund.getSupportTicket())
        .setComment(refund.getComment())
        .build();
  }

  public static Refund refundDaoToRefund(RefundDAO refundDAO) {
    return new Refund.Builder()
        .setApiKey(refundDAO.getApiKey())
        .setPaymentId(refundDAO.getPaymentId())
        .setRefundId(refundDAO.getRefundId())
        .setRefundReason(refundDAO.getRefundReason())
        .setCreatedAt(refundDAO.getCreatedAt())
        .setUpdatedAt(refundDAO.getUpdatedAt())
        .setRefundedAmount(refundDAO.getRefundedAmount())
        .setRefundedLocalAmount(refundDAO.getRefundedLocalAmount())
        .setFailureReason(refundDAO.getFailureReason())
        .setStatus(refundDAO.getStatus())
        .setVersion(refundDAO.getVersion())
        .setProviderData(refundDAO.getProviderData())
        .setPaymentMethodUsed(refundDAO.getPaymentMethodUsed())
        .setRefundType(refundDAO.getRefundType())
        .setReceiptSentAt(refundDAO.getReceiptSentAt())
        .setReceiptId(refundDAO.getReceiptId())
        .setSupportTicket(refundDAO.getSupportTicket())
        .setComment(refundDAO.getComment())
        .build();
  }
}
