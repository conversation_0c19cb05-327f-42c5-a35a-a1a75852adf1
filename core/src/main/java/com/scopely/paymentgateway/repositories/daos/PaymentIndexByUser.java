package com.scopely.paymentgateway.repositories.daos;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import java.time.Instant;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = PaymentIndexByUser.Builder.class)
@DynamoDBTable(tableName = PaymentIndexByUser.TABLE)
public interface PaymentIndexByUser {
  String TABLE = "PaymentGateway_Payment";

  String GLOBAL_SECONDARY_INDEX_NAME = "GSI_Payment_UserId";

  String HASH_KEY = "paymentId";

  String GSI_HASH_KEY = "apiKeyPaymentUserGSIPK";

  String GSI_RANGE_KEY = "statusCreatedAtGSIPK";

  @DynamoDBHashKey(attributeName = HASH_KEY)
  String getPaymentId();

  @DynamoDBIndexHashKey(
      attributeName = GSI_HASH_KEY,
      globalSecondaryIndexName = GLOBAL_SECONDARY_INDEX_NAME)
  String getApiKeyPaymentUserGSIPK();

  @DynamoDBIndexRangeKey(
      attributeName = GSI_RANGE_KEY,
      globalSecondaryIndexName = GLOBAL_SECONDARY_INDEX_NAME)
  String getStatusCreatedAtGSIPK();

  String getUserId();

  PaymentStatus getStatus();

  String getCountry();

  Boolean getClaimed();

  Instant getCreatedAt();

  class Builder extends PaymentIndexByUser_Builder {

    public Builder() {}
  }
}
