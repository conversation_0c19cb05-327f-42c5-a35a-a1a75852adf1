package com.scopely.paymentgateway.repositories;

import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentFilterCriteria;
import java.util.List;

public interface PaymentFilterRepository {

  /**
   * Retrieve all payments matching the specific criteria
   *
   * @param criteria
   * @return
   */
  List<Payment> getAll(PaymentFilterCriteria criteria);
}
