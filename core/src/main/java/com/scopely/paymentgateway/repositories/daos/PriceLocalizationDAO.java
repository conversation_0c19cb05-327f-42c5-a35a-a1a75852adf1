package com.scopely.paymentgateway.repositories.daos;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.proteus.dynamodb.DynamoDbMappedBean;
import java.time.Instant;
import org.inferred.freebuilder.FreeBuilder;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

@FreeBuilder
@JsonDeserialize(builder = PriceLocalizationDAO.Builder.class)
@DynamoDbMappedBean(table = PriceLocalizationDAO.TABLE)
public interface PriceLocalizationDAO {
  String TABLE = "PaymentGateway_PriceLocalization";
  String HASH_KEY = "apiKey";
  String RANGE_KEY = "rangeKey";
  String ACTIVE = "active";

  @DynamoDbPartitionKey
  String getApiKey();

  @DynamoDbSortKey
  String getRangeKey();

  String getPriceFilePath();

  String getCreatedBy();

  Instant getCreatedAt();

  class Builder extends PriceLocalizationDAO_Builder {}
}
