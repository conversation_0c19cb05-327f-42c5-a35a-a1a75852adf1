package com.scopely.paymentgateway.repositories.daos;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = PaymentIndexByOrderId.Builder.class)
@DynamoDBTable(tableName = PaymentIndexByOrderId.TABLE)
public interface PaymentIndexByOrderId {
  String TABLE = "PaymentGateway_Payment";

  String GLOBAL_SECONDARY_INDEX_NAME = "GSI_Payment_OrderId";

  String HASH_KEY = "paymentId";

  String GSI_HASH_KEY = "orderId";

  @DynamoDBHashKey(attributeName = HASH_KEY)
  String getPaymentId();

  @DynamoDBIndexHashKey(
      attributeName = GSI_HASH_KEY,
      globalSecondaryIndexName = GLOBAL_SECONDARY_INDEX_NAME)
  String getOrderId();

  class Builder extends PaymentIndexByOrderId_Builder {

    public Builder() {}

    public Builder(String paymentId, String orderId) {
      this.setPaymentId(paymentId).setOrderId(orderId);
    }
  }
}
