package com.scopely.paymentgateway.repositories.daos.mapper;

import com.scopely.paymentgateway.model.purchasetoken.PaymentInfoToken;
import com.scopely.paymentgateway.repositories.daos.PurchaseTokenPaymentInfoDAO;
import java.time.Instant;

public class PaymentInfoDAOMapper {

  public static PurchaseTokenPaymentInfoDAO paymentInfoToPaymentDataDao(
      PaymentInfoToken paymentInfoToken) {
    return new PurchaseTokenPaymentInfoDAO.Builder()
        .setPaymentId(paymentInfoToken.getPaymentId())
        .setLastUpdateAt(paymentInfoToken.getLastUpdateAt().getEpochSecond())
        .setPurchaseToken(paymentInfoToken.getPurchaseToken())
        .setUsed(paymentInfoToken.isUsed())
        .setExpireAt(paymentInfoToken.getExpireAt())
        .build();
  }

  public static PaymentInfoToken paymentInfoDaoToPaymentData(
      PurchaseTokenPaymentInfoDAO paymentDataDAO) {
    return new PaymentInfoToken.Builder()
        .setPaymentId(paymentDataDAO.getPaymentId())
        .setLastUpdateAt(Instant.ofEpochSecond(paymentDataDAO.getLastUpdateAt()))
        .setPurchaseToken(paymentDataDAO.getPurchaseToken())
        .setUsed(paymentDataDAO.isUsed())
        .setExpireAt(paymentDataDAO.getExpireAt())
        .build();
  }
}
