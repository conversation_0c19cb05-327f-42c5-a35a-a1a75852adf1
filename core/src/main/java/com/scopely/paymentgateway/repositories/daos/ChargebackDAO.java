package com.scopely.paymentgateway.repositories.daos;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.utils.DynamoDBUtils;
import org.inferred.freebuilder.FreeBuilder;
import org.javamoney.moneta.Money;

@FreeBuilder
@JsonDeserialize(builder = ChargebackDAO.Builder.class)
@DynamoDBTable(tableName = ReversalDAO.TABLE)
public interface ChargebackDAO extends ReversalDAO {

  String getChargebackReason();

  class Builder extends ChargebackDAO_Builder {

    public Builder() {
      setLocalAmount(Money.of(0, "USD"));
    }

    @Override
    public ChargebackDAO build() {
      if (getLocalAmount().isZero()) {
        setLocalAmount(getAmount());
      }
      setApiKeyAndUserId(DynamoDBUtils.buildCompositeAttribute(getApi<PERSON>ey(), getUserId()));
      return super.build();
    }
  }
}
