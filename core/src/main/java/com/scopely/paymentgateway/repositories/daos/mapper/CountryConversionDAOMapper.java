package com.scopely.paymentgateway.repositories.daos.mapper;

import com.scopely.paymentgateway.model.countryconversion.CountryConversion;
import com.scopely.paymentgateway.repositories.daos.CountryConversionDAO;

public class CountryConversionDAOMapper {

  public static CountryConversionDAO countryExchangeRateToCountryExchangeRateDAO(
      CountryConversion countryConversion) {
    return new CountryConversionDAO.Builder()
        .setCountryId(countryConversion.getCountryId())
        .setCurrencyId(countryConversion.getCurrencyId())
        .setExchangeRate(countryConversion.getExchangeRate())
        .setConversionFactor(countryConversion.getConversionFactor())
        .setTaxRate(countryConversion.getTaxRate())
        .setTaxIncluded(countryConversion.isTaxIncluded())
        .setUpdatedAt(countryConversion.getUpdatedAt())
        .build();
  }

  public static CountryConversion countryExchangeRateDAOToCountryExchangeRate(
      CountryConversionDAO countryConversionDAO) {
    return new CountryConversion.Builder()
        .setCountryId(countryConversionDAO.getCountryId())
        .setCurrencyId(countryConversionDAO.getCurrencyId())
        .setExchangeRate(countryConversionDAO.getExchangeRate())
        .setConversionFactor(countryConversionDAO.getConversionFactor())
        .setTaxIncluded(countryConversionDAO.isTaxIncluded())
        .setTaxRate(countryConversionDAO.getTaxRate())
        .setUpdatedAt(countryConversionDAO.getUpdatedAt())
        .build();
  }
}
