package com.scopely.paymentgateway.repositories.daos;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import java.math.BigDecimal;
import java.util.List;
import javax.annotation.Nullable;
import org.apache.commons.lang.StringUtils;
import org.inferred.freebuilder.FreeBuilder;
import org.javamoney.moneta.Money;

@FreeBuilder
@DynamoDBTable(tableName = ProviderFeesDAO.TABLE)
@JsonDeserialize(builder = ProviderFeesDAO.Builder.class)
public interface ProviderFeesDAO {

  String TABLE = "PaymentGateway_Fees";
  String HASH_KEY = "apiKey";
  String RANGE_KEY = "header";
  String HEADER_SEPARATOR = "#";

  static String buildHeader(PaymentProviderIdentifier provider, String country, String currency) {
    if (StringUtils.isEmpty(currency)) {
      return String.join(HEADER_SEPARATOR, provider.getDescription(), country);
    }
    return String.join(HEADER_SEPARATOR, provider.getDescription(), country, currency);
  }

  @DynamoDBHashKey(attributeName = HASH_KEY)
  String getApiKey();

  @DynamoDBRangeKey(attributeName = RANGE_KEY)
  String getHeader();

  PaymentProviderIdentifier getProvider();

  String getZone();

  @Nullable
  String getCurrency();

  List<BigDecimal> getPercentages();

  @Nullable
  Money getFlatFee();

  class Builder extends ProviderFeesDAO_Builder {}
}
