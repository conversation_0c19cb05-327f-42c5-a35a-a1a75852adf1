package com.scopely.paymentgateway.repositories;

import static com.google.common.collect.ImmutableMap.of;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SERVICE;

import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.PurchaseTokenRepositoryException;
import com.scopely.paymentgateway.model.purchasetoken.PaymentInfoToken;
import com.scopely.paymentgateway.repositories.daos.PurchaseTokenPaymentInfoDAO;
import com.scopely.paymentgateway.repositories.daos.mapper.PaymentInfoDAOMapper;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import net.jodah.failsafe.Failsafe;
import net.jodah.failsafe.RetryPolicy;
import net.jodah.failsafe.function.CheckedConsumer;

public class DynamoPurchaseTokenPaymentInfoRepository
    implements PurchaseTokenPaymentInfoRepository {

  private final JsonDynamoMapper dynamoMapper;
  private final RetryPolicy retryPolicy;
  private final StatsDClient statsDClient;

  private static final String PURCHASE_TOKEN_PAYMENT_INFO = "PurchaseTokenPaymentInfo";
  private static final String PAYMENT_ID = "paymentId";
  public static final String PURCHASE_TOKEN = "PURCHASE_TOKEN";

  public DynamoPurchaseTokenPaymentInfoRepository(
      JsonDynamoMapper dynamoMapper, RetryPolicy retryPolicy, StatsDClient statsDClient1) {
    this.dynamoMapper = dynamoMapper;
    this.retryPolicy = retryPolicy;
    this.statsDClient = statsDClient1;
  }

  @Override
  public Optional<PaymentInfoToken> getPaymentInfo(String purchaseToken) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(PURCHASE_TOKEN_PAYMENT_INFO, purchaseToken))
                      .error(t, "Unable to get Payment data from purchase token");

                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              PurchaseTokenPaymentInfoDAO.TABLE,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new PurchaseTokenRepositoryException(t);
                })
        .get(() -> dynamoMapper.load(PurchaseTokenPaymentInfoDAO.class, purchaseToken))
        .filter(
            dao -> {
              long currentTime = Instant.now().getEpochSecond();
              return dao.getExpireAt() > currentTime; // Filter out expired items
            })
        .map(PaymentInfoDAOMapper::paymentInfoDaoToPaymentData);
  }

  @Override
  public void savePaymentInfo(PaymentInfoToken paymentInfoToken) {
    PurchaseTokenPaymentInfoDAO purchaseTokenPaymentInfoDAO =
        PaymentInfoDAOMapper.paymentInfoToPaymentDataDao(paymentInfoToken);
    Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(
                          of(
                              PURCHASE_TOKEN,
                              paymentInfoToken.getPurchaseToken(),
                              PAYMENT_ID,
                              paymentInfoToken.getPaymentId()))
                      .error(t, "Unable to save Payment data with purchase token");

                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_SAVE,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              PurchaseTokenPaymentInfoDAO.TABLE,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new PurchaseTokenRepositoryException(t);
                })
        .get(() -> dynamoMapper.save(purchaseTokenPaymentInfoDAO));
  }
}
