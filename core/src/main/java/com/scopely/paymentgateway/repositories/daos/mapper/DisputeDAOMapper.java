package com.scopely.paymentgateway.repositories.daos.mapper;

import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import com.scopely.paymentgateway.repositories.daos.DisputeDAO;
import com.scopely.paymentgateway.utils.DynamoDBUtils;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DisputeDAOMapper {
  private static final String HEADER_PREFIX = "DISPUTE";

  @Inject
  public DisputeDAOMapper() {}

  public DisputeDAO disputeToDisputeDao(Dispute dispute) {
    return new DisputeDAO.Builder()
        .setApiKey(dispute.getApiKey())
        .setUserId(dispute.getUserId())
        .setPaymentId(dispute.getPaymentId())
        .setHeader(getHeaderFromDisputeId(dispute.getDisputeId()))
        .setDisputeReason(dispute.getDisputeReason())
        .setCreatedAt(dispute.getCreatedAt())
        .setUpdatedAt(dispute.getUpdatedAt())
        .setAmount(dispute.getAmount())
        .setLocalAmount(dispute.getLocalAmount())
        .setStatus(dispute.getStatus())
        .setVersion(dispute.getVersion())
        .build();
  }

  public Dispute disputeDaoToDispute(DisputeDAO disputeDAO) {
    return new Dispute.Builder()
        .setApiKey(disputeDAO.getApiKey())
        .setUserId(disputeDAO.getUserId())
        .setPaymentId(disputeDAO.getPaymentId())
        .setDisputeId(getDisputeIdFromHeader(disputeDAO.getHeader()))
        .setDisputeReason(disputeDAO.getDisputeReason())
        .setCreatedAt(disputeDAO.getCreatedAt())
        .setUpdatedAt(disputeDAO.getUpdatedAt())
        .setAmount(disputeDAO.getAmount())
        .setLocalAmount(disputeDAO.getLocalAmount())
        .setStatus(disputeDAO.getStatus())
        .setVersion(disputeDAO.getVersion())
        .build();
  }

  public String getHeaderFromDisputeId(String disputeId) {
    return DynamoDBUtils.buildCompositeAttribute(HEADER_PREFIX, disputeId);
  }

  public String getHeaderPrefix() {
    return getHeaderFromDisputeId("");
  }

  public String getDisputeIdFromHeader(String header) {
    return header.split(DynamoDBUtils.DELIMITER)[1];
  }
}
