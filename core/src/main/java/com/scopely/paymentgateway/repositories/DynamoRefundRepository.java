package com.scopely.paymentgateway.repositories;

import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SERVICE;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBQueryExpression;
import com.amazonaws.services.dynamodbv2.model.ConditionalCheckFailedException;
import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.RefundException;
import com.scopely.paymentgateway.exceptions.RefundIndexQueryException;
import com.scopely.paymentgateway.exceptions.model.RefundNotFoundException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.repositories.daos.PaymentDAO;
import com.scopely.paymentgateway.repositories.daos.RefundDAO;
import com.scopely.paymentgateway.repositories.daos.mapper.RefundDAOMapper;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.DynamoDBQueryHelper;
import com.scopely.proteus.cache.InMemoryObjectCache;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;
import net.jodah.failsafe.Failsafe;
import net.jodah.failsafe.RetryPolicy;
import net.jodah.failsafe.function.CheckedConsumer;
import org.jetbrains.annotations.NotNull;

@Singleton
public class DynamoRefundRepository implements RefundRepository {
  private final JsonDynamoMapper dynamoMapper;
  private final RetryPolicy retryPolicy;
  private final StatsDClient statsDClient;
  private final InMemoryObjectCache<Integer> lockingConflictExceptionCountCache;

  @Inject
  public DynamoRefundRepository(
      JsonDynamoMapper dynamoMapper,
      RetryPolicy retryPolicy,
      StatsDClient statsDClient,
      InMemoryObjectCache<Integer> lockingConflictExceptionCountCache) {
    this.dynamoMapper = dynamoMapper;
    this.retryPolicy = retryPolicy;
    this.statsDClient = statsDClient;
    this.lockingConflictExceptionCountCache = lockingConflictExceptionCountCache;
  }

  @Override
  public Refund save(Refund refund) throws LockingConflictException {
    RefundDAO refundDAO = RefundDAOMapper.refundToRefundDao(refund);
    try {
      RefundDAO saved =
          Failsafe.with(retryPolicy)
              .withFallback(
                  (CheckedConsumer<Throwable>)
                      t -> {
                        if (t instanceof ConditionalCheckFailedException) {
                          throw (ConditionalCheckFailedException) t;
                        }
                        new PaymentGatewayLogBuilder()
                            .addPayment(refund.getApiKey(), refund.getPaymentId())
                            .build()
                            .error(t, "Unable to save Refund (" + refundDAO.getRefundId() + ")");
                        statsDClient.increment(
                            StatsConstants.DD_REPOSITORY_OPERATION_SAVE,
                            MetricsUtils.buildTags(
                                List.of(TAG_SERVICE, TAG_RESULT),
                                List.of(
                                    RefundDAO.TABLE,
                                    StatsConstants.OperationResults.UNHANDLED_ERROR)));
                        throw new RefundException(t);
                      })
              .get(() -> dynamoMapper.saveAndGet(refundDAO));

      return RefundDAOMapper.refundDaoToRefund(saved);
    } catch (ConditionalCheckFailedException exception) {
      String key =
          refundDAO.getPaymentId() + "#" + refundDAO.getRefundId() + "#" + refundDAO.getVersion();

      int lockingExceptionCount = lockingConflictExceptionCountCache.get(key).orElse(0);

      if (lockingExceptionCount <= retryPolicy.getMaxRetries()) {
        lockingExceptionCount += 1;
        lockingConflictExceptionCountCache.put(key, lockingExceptionCount);
        throw new LockingConflictException(exception);
      }
      new PaymentGatewayLogBuilder()
          .addPayment(refund.getApiKey(), refund.getPaymentId())
          .build()
          .error(
              exception,
              "Unable to save Refund ("
                  + refundDAO.getRefundId()
                  + ") after validate the LockingConflictException");
      statsDClient.increment(
          StatsConstants.DD_REPOSITORY_OPERATION_SAVE,
          MetricsUtils.buildTags(
              List.of(TAG_API_KEY, TAG_SERVICE, TAG_RESULT),
              List.of(
                  refund.getApiKey(),
                  RefundDAO.TABLE,
                  StatsConstants.OperationResults.UNHANDLED_ERROR)));

      throw new RefundException(exception);
    }
  }

  @Override
  public List<Refund> getRefunds(String paymentId) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  new PaymentGatewayLogBuilder()
                      .addPayment(paymentId)
                      .build()
                      .error(t, "Unable to query PK(" + paymentId + ") to retrieve payments");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              RefundDAO.TABLE, StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new RefundException(t);
                })
        .get(
            () -> {
              var dynamoFilter = new DynamoDBQueryHelper();
              dynamoFilter.addKeyCondition(RefundDAO.HASH_KEY, paymentId);
              var query =
                  new DynamoDBQueryExpression<RefundDAO>()
                      .withConsistentRead(false)
                      .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
                      .withFilterExpression(dynamoFilter.getFilterExpressionChain())
                      .withExpressionAttributeValues(dynamoFilter.getAttributeValues());
              return dynamoMapper.queryAll(RefundDAO.class, query);
            })
        .stream()
        .map(RefundDAOMapper::refundDaoToRefund)
        .collect(Collectors.toList());
  }

  @NotNull
  @Override
  public Refund getRefundByRefundIdUnchecked(String refundId) throws RefundNotFoundException {
    return getRefundByRefundId(refundId).orElseThrow(() -> new RefundNotFoundException(refundId));
  }

  @Override
  public Optional<Refund> getRefundByRefundId(String refundId) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  new PaymentGatewayLogBuilder()
                      .addRefund(refundId)
                      .build()
                      .error(t, "Unable to load refund");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              RefundDAO.GSI_REFUND_ID,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new RefundIndexQueryException(t);
                })
        .get(
            () -> {
              var dynamoFilter = new DynamoDBQueryHelper();
              dynamoFilter.addKeyCondition(RefundDAO.RANGE_KEY, refundId);

              var query =
                  new DynamoDBQueryExpression<PaymentDAO>()
                      .withIndexName(RefundDAO.GSI_REFUND_ID)
                      .withConsistentRead(false)
                      .withScanIndexForward(false)
                      .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
                      .withFilterExpression(dynamoFilter.getFilterExpressionChain())
                      .withExpressionAttributeValues(dynamoFilter.getAttributeValues());

              return Optional.ofNullable(dynamoMapper.queryAll(RefundDAO.class, query))
                  .filter(l -> !l.isEmpty())
                  .map(l -> RefundDAOMapper.refundDaoToRefund(l.get(0)));
            });
  }

  @Override
  public void deleteRefund(Refund refund) {
    try {
      dynamoMapper.delete(RefundDAO.class, refund.getPaymentId(), refund.getRefundId());
    } catch (Exception e) {
      new PaymentGatewayLogBuilder()
          .addRefund(refund.getApiKey(), refund.getRefundId())
          .build()
          .error(e, "Unable to delete refund item");
    }
  }

  @Override
  public void deleteReturn(Refund refund) {
    try {
      // only deleting return processes that are in the refund table
      if (refund.isReturn()) {
        this.deleteRefund(refund);
      }
    } catch (Exception e) {
      new PaymentGatewayLogBuilder()
          .addRefund(refund.getApiKey(), refund.getRefundId())
          .build()
          .error(e, "Unable to delete return");
    }
  }
}
