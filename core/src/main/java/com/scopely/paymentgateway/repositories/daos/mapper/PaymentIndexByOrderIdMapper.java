package com.scopely.paymentgateway.repositories.daos.mapper;

import com.scopely.paymentgateway.repositories.daos.PaymentDAO;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByOrderId;

public class PaymentIndexByOrderIdMapper {

  public static PaymentIndexByOrderId paymentDaoToPaymentByOrderId(PaymentDAO paymentDAO) {
    return new PaymentIndexByOrderId.Builder()
        .setPaymentId(paymentDAO.getPaymentId())
        .setOrderId(paymentDAO.getOrderId())
        .build();
  }
}
