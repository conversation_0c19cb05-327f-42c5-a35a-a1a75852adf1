package com.scopely.paymentgateway.repositories;

import static com.google.common.collect.ImmutableMap.of;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SERVICE;
import static com.scopely.paymentgateway.repositories.daos.PaymentDAO.API_KEY_ID;
import static com.scopely.paymentgateway.repositories.daos.PaymentDAO.ORDER_ID;
import static com.scopely.paymentgateway.repositories.daos.PaymentDAO.PAYMENT_ID;
import static com.scopely.paymentgateway.repositories.daos.PaymentDAO.SESSION_ID;
import static com.scopely.paymentgateway.repositories.daos.PaymentDAO.USER_ID;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBQueryExpression;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBScanExpression;
import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.ConditionalCheckFailedException;
import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.PaymentException;
import com.scopely.paymentgateway.exceptions.PaymentHistoryException;
import com.scopely.paymentgateway.exceptions.PaymentIndexQueryException;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import com.scopely.paymentgateway.repositories.daos.BackfillPage;
import com.scopely.paymentgateway.repositories.daos.PaginationToken;
import com.scopely.paymentgateway.repositories.daos.PaymentDAO;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByExternalId;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByOrderId;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByUnclaimed;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByUser;
import com.scopely.paymentgateway.repositories.daos.PaymentSessionIdsDAO;
import com.scopely.paymentgateway.repositories.daos.mapper.PaymentDAOMapper;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.DynamoDBQueryHelper;
import com.scopely.paymentgateway.utils.DynamoDBUtils;
import com.scopely.proteus.cache.InMemoryObjectCache;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import java.time.Clock;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;
import net.jodah.failsafe.Failsafe;
import net.jodah.failsafe.RetryPolicy;
import net.jodah.failsafe.function.CheckedConsumer;
import org.jetbrains.annotations.NotNull;

@Singleton
public class DynamoPaymentRepository implements PaymentRepository {
  private final JsonDynamoMapper dynamoMapper;
  private final RetryPolicy retryPolicy;
  private final Clock clock;
  private final PaymentGatewayConfig paymentGatewayConfig;
  private final StatsDClient statsDClient;
  private final InMemoryObjectCache<Integer> lockingConflictExceptionCountCache;
  private static final String API_KEY_PAYMENT_USER_VALUE = ":apiKeyPaymentUserValue";

  @Inject
  public DynamoPaymentRepository(
      JsonDynamoMapper dynamoMapper,
      RetryPolicy retryPolicy,
      StatsDClient statsDClient,
      InMemoryObjectCache<Integer> lockingConflictExceptionCountCache,
      Clock clock,
      PaymentGatewayConfig paymentGatewayConfig) {
    this.dynamoMapper = dynamoMapper;
    this.retryPolicy = retryPolicy;
    this.statsDClient = statsDClient;
    this.lockingConflictExceptionCountCache = lockingConflictExceptionCountCache;
    this.clock = clock;
    this.paymentGatewayConfig = paymentGatewayConfig;
  }

  public Payment save(Payment payment) throws LockingConflictException {
    try {
      PaymentDAO paymentDAO = PaymentDAOMapper.paymentToPaymentDao(payment);
      PaymentDAO saved =
          Failsafe.with(retryPolicy)
              .withFallback(
                  (CheckedConsumer<Throwable>)
                      t -> {
                        if (t instanceof ConditionalCheckFailedException) {
                          throw (ConditionalCheckFailedException) t;
                        }
                        statsDClient.increment(
                            StatsConstants.DD_REPOSITORY_OPERATION_SAVE,
                            MetricsUtils.buildTags(
                                List.of(TAG_SERVICE, TAG_RESULT),
                                List.of(
                                    PaymentDAO.TABLE,
                                    StatsConstants.OperationResults.UNHANDLED_ERROR)));
                        Log.withMetadata(of(PAYMENT_ID, paymentDAO.getPaymentId()))
                            .error(t, "Unable to save payment (" + paymentDAO.getPaymentId() + ")");
                        throw new PaymentException(t);
                      })
              .get(() -> dynamoMapper.saveAndGet(paymentDAO));

      if (payment.getSessionId() != null) {
        save(getOrNewPaymentAndProProviderIdsFromPayment(payment, payment.getSessionId()));
      }

      return PaymentDAOMapper.paymentDaoToPayment(saved);
    } catch (ConditionalCheckFailedException exception) {
      String key = payment.getPaymentId() + "#" + payment.getVersion();
      int conditionalCheckExceptionCount = lockingConflictExceptionCountCache.get(key).orElse(0);

      if (conditionalCheckExceptionCount <= retryPolicy.getMaxRetries()) {
        conditionalCheckExceptionCount += 1;
        lockingConflictExceptionCountCache.put(key, conditionalCheckExceptionCount);
        throw new LockingConflictException(exception);
      }
      Log.withMetadata(of(PAYMENT_ID, payment.getPaymentId()))
          .error(
              exception,
              "Unable to save payment ("
                  + payment.getPaymentId()
                  + ") after validate the LockingConflictException");
      statsDClient.increment(
          StatsConstants.DD_REPOSITORY_OPERATION_SAVE,
          MetricsUtils.buildTags(
              List.of(TAG_API_KEY, TAG_SERVICE, TAG_RESULT),
              List.of(
                  payment.getApiKey(),
                  PaymentDAO.TABLE,
                  StatsConstants.OperationResults.UNHANDLED_ERROR)));

      throw new PaymentException(exception);
    }
  }

  @Override
  public Optional<Payment> getPayment(String paymentId) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(PAYMENT_ID, paymentId)).error(t, "Unable to load payment");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              PaymentDAO.TABLE, StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new PaymentException(t);
                })
        .get(() -> dynamoMapper.load(PaymentDAO.class, paymentId))
        .map(PaymentDAOMapper::paymentDaoToPayment);
  }

  @NotNull
  @Override
  @Deprecated
  public Payment getPaymentUnchecked(String paymentId) throws PaymentNotFoundException {
    return getPayment(paymentId)
        .orElseThrow(
            () ->
                new PaymentNotFoundException(
                    String.format("Payment with id " + paymentId + " not found", paymentId)));
  }

  @Override
  public List<PaymentIndexByExternalId> getPaymentsByExternalId(
      String apiKey, String userId, String externalId) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  // FIXME: load this log tag from LogMetadata Class
                  Log.withMetadata(of("externalId", externalId))
                      .error(
                          t,
                          "Unable to load payment from index {}",
                          PaymentIndexByExternalId.GLOBAL_SECONDARY_INDEX_NAME);
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              PaymentIndexByExternalId.GLOBAL_SECONDARY_INDEX_NAME,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new PaymentException(t);
                })
        .get(
            () -> {
              var dynamoFilter = new DynamoDBQueryHelper();
              dynamoFilter.addKeyCondition(PaymentIndexByExternalId.GSI_HASH_KEY, externalId);
              // for now, we are only searching by externalId and apiKey because we are not using
              // userId
              // in the process
              dynamoFilter.addKeyCondition(
                  PaymentIndexByExternalId.GSI_RANGE_KEY,
                  DynamoDBUtils.buildCompositeAttribute(apiKey, userId));
              return dynamoMapper.queryAll(
                  PaymentIndexByExternalId.class,
                  new DynamoDBQueryExpression<PaymentIndexByExternalId>()
                      .withIndexName(PaymentIndexByExternalId.GLOBAL_SECONDARY_INDEX_NAME)
                      .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
                      .withExpressionAttributeValues(dynamoFilter.getAttributeValues())
                      .withFilterExpression(dynamoFilter.getFilterExpressionChain())
                      .withConsistentRead(false));
            });
  }

  @Override
  public Optional<Payment> getPaymentByOrderId(String orderId) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(PAYMENT_ID, orderId)).error(t, "Unable to load payment");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              PaymentIndexByOrderId.GLOBAL_SECONDARY_INDEX_NAME,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new PaymentException(t);
                })
        .get(
            () -> {
              var dynamoFilter = new DynamoDBQueryHelper();
              dynamoFilter.addKeyCondition(ORDER_ID, orderId);

              var query =
                  new DynamoDBQueryExpression<PaymentDAO>()
                      .withIndexName(PaymentIndexByOrderId.GLOBAL_SECONDARY_INDEX_NAME)
                      .withConsistentRead(false)
                      .withScanIndexForward(false)
                      .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
                      .withFilterExpression(dynamoFilter.getFilterExpressionChain())
                      .withExpressionAttributeValues(dynamoFilter.getAttributeValues());

              var paymentInfoByOrderByIndex =
                  dynamoMapper.queryAll(PaymentIndexByOrderId.class, query);
              return paymentInfoByOrderByIndex.isEmpty()
                  ? Optional.empty()
                  : this.getPayment(paymentInfoByOrderByIndex.get(0).getPaymentId());
            });
  }

  public Optional<Payment> getPaymentBySessionId(String sessionId, String providerIdentifier) {
    Optional<PaymentSessionIdsDAO> paymentAndProviderIdsDAO =
        getPaymentSessionIds(sessionId, providerIdentifier);

    if (paymentAndProviderIdsDAO.isPresent()) {
      return getPayment(paymentAndProviderIdsDAO.get().getPaymentId());
    }
    return Optional.empty();
  }

  private List<PaymentIndexByUnclaimed> getUnclaimedPayments(String apiKey, String userId) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(API_KEY_ID, apiKey))
                      .error(t, "Unable to query to global secondary index to retrieve payments");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              PaymentIndexByUnclaimed.GLOBAL_SECONDARY_INDEX_NAME,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new PaymentIndexQueryException(t);
                })
        .get(
            () -> {
              DynamoDBQueryHelper dynamoFilter = new DynamoDBQueryHelper();
              dynamoFilter.addKeyCondition(PaymentIndexByUnclaimed.GSI_HASH_KEY, apiKey);
              if (userId != null) {
                dynamoFilter.addKeyCondition(PaymentIndexByUnclaimed.GSI_RANGE_KEY, userId);
              }

              // Delay added to avoid race condition of game claiming automatically just after
              // successful purchase
              var delay =
                  clock
                      .instant()
                      .minusSeconds(paymentGatewayConfig.unclaimedPaymentsDelaySeconds());
              dynamoFilter.addFilterCondition(
                  DynamoDBQueryHelper.Comparator.LESS_THAN, "updatedAt", delay.toString());

              return dynamoMapper.queryAll(
                  PaymentIndexByUnclaimed.class,
                  new DynamoDBQueryExpression<PaymentIndexByUnclaimed>()
                      .withIndexName(PaymentIndexByUnclaimed.GLOBAL_SECONDARY_INDEX_NAME)
                      .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
                      .withFilterExpression(dynamoFilter.getFilterExpressionChain())
                      .withExpressionAttributeValues(dynamoFilter.getAttributeValues())
                      .withConsistentRead(false)
                      .withScanIndexForward(false));
            });
  }

  @Override
  public List<PaymentIndexByUnclaimed> getUnclaimedPaymentsByUser(String apiKey, String userId) {
    return getUnclaimedPayments(apiKey, userId);
  }

  @Override
  public List<PaymentIndexByUnclaimed> getUnclaimedPaymentsByGame(String apiKey) {
    return getUnclaimedPayments(apiKey, null);
  }

  public List<PaymentIndexByUser> getPaymentsByUser(String apiKey, String userId) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(API_KEY_ID, apiKey, USER_ID, userId))
                      .error(t, "Unable to query to global secondary index to retrieve payments");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              PaymentIndexByUser.GLOBAL_SECONDARY_INDEX_NAME,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new PaymentIndexQueryException(t);
                })
        .get(
            () -> {
              Map<String, AttributeValue> expressionAttributes = new HashMap<>();
              final String gsiPK = DynamoDBUtils.buildCompositeAttribute(apiKey, userId);
              expressionAttributes.put(
                  API_KEY_PAYMENT_USER_VALUE, new AttributeValue().withS(gsiPK));

              return dynamoMapper.queryAll(
                  PaymentIndexByUser.class,
                  new DynamoDBQueryExpression<PaymentIndexByUser>()
                      .withIndexName(PaymentIndexByUser.GLOBAL_SECONDARY_INDEX_NAME)
                      .withKeyConditionExpression(
                          "apiKeyPaymentUserGSIPK = " + API_KEY_PAYMENT_USER_VALUE)
                      .withExpressionAttributeValues(expressionAttributes)
                      .withConsistentRead(false));
            });
  }

  @Override
  public BackfillPage<PaymentDAO> getUnclaimedPaymentsPaginated(PaginationToken token) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.error(t, "Unable to query next page for the unclaimed payments");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              PaymentDAO.TABLE, StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new PaymentException(t);
                })
        .get(
            () -> {
              var scan =
                  dynamoMapper.scan(
                      PaymentDAO.class,
                      new DynamoDBScanExpression()
                          .withExpressionAttributeNames(
                              Map.of("#s", "status", "#c", "claimed", "#uc", "unclaimedCompleted"))
                          .withExpressionAttributeValues(
                              Map.of(
                                  ":c",
                                  new AttributeValue().withBOOL(false),
                                  ":s",
                                  new AttributeValue().withS(PaymentStatus.COMPLETED.name()),
                                  ":uc",
                                  new AttributeValue().withNULL(true)))
                          .withFilterExpression(
                              "#c = :c AND #s = :s AND (attribute_not_exists(#uc) OR #uc = :uc)")
                          .withExclusiveStartKey(token.token()));
              return new BackfillPage<>(
                  new PaginationToken(scan.getLastEvaluatedKey()), scan.getResults());
            });
  }

  @Override
  public BackfillPage<PaymentIndexByUnclaimed> getUnclaimedPaymentsFromIndexPaginated(
      PaginationToken token) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.error(t, "Unable to query next page for the unclaimed payments");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              PaymentIndexByUnclaimed.GLOBAL_SECONDARY_INDEX_NAME,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new PaymentException(t);
                })
        .get(
            () -> {
              var scan =
                  dynamoMapper.scan(
                      PaymentIndexByUnclaimed.class,
                      new DynamoDBScanExpression()
                          .withIndexName(PaymentIndexByUnclaimed.GLOBAL_SECONDARY_INDEX_NAME)
                          .withExclusiveStartKey(token.token()));
              return new BackfillPage<>(
                  new PaginationToken(scan.getLastEvaluatedKey()), scan.getResults());
            });
  }

  private void save(PaymentSessionIdsDAO paymentSessionIdsDAO) {
    Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(SESSION_ID, paymentSessionIdsDAO.getSessionId()))
                      .error(t, "Unable to save PaymentAndProviderIds");
                  throw new PaymentHistoryException(t);
                })
        .run(() -> dynamoMapper.save(paymentSessionIdsDAO));
  }

  private Optional<PaymentSessionIdsDAO> getPaymentSessionIds(
      String sessionId, String providerIdentifier) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(SESSION_ID, sessionId))
                      .error(t, "Unable to load paymentSessionIds");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              PaymentSessionIdsDAO.TABLE,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new PaymentException(t);
                })
        .onSuccess(
            (Optional<PaymentSessionIdsDAO> optionalPaymentDAO) -> {
              if (optionalPaymentDAO.isEmpty()) {
                Log.withMetadata(of(SESSION_ID, sessionId)).debug("Payment not paymentSessionIds");
              }
            })
        .get(() -> dynamoMapper.load(PaymentSessionIdsDAO.class, sessionId, providerIdentifier));
  }

  private PaymentSessionIdsDAO getOrNewPaymentAndProProviderIdsFromPayment(
      Payment payment, String sessionId) {
    Optional<PaymentSessionIdsDAO> paymentSessionIdsDAO =
        getPaymentSessionIds(sessionId, payment.getProviderData().getProvider().name());

    return paymentSessionIdsDAO.orElseGet(
        () ->
            new PaymentSessionIdsDAO.Builder()
                .setPaymentId(payment.getPaymentId())
                .setProvider(payment.getProviderData().getProvider().name())
                .setSessionId(sessionId)
                .build());
  }
}
