package com.scopely.paymentgateway.repositories.daos;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import java.time.Instant;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = PaymentIndexByExternalId.Builder.class)
@DynamoDBTable(tableName = PaymentIndexByExternalId.TABLE)
public interface PaymentIndexByExternalId {
  String TABLE = PaymentDAO.TABLE;

  String GLOBAL_SECONDARY_INDEX_NAME = "GSI_Payment_External_Id";

  String HASH_KEY = PaymentDAO.PAYMENT_ID;

  String GSI_HASH_KEY = PaymentDAO.EXTERNAL_ID;

  String GSI_RANGE_KEY = PaymentDAO.API_KEY_USER;

  @DynamoDBHashKey(attributeName = HASH_KEY)
  String getPaymentId();

  @DynamoDBIndexHashKey(
      attributeName = GSI_HASH_KEY,
      globalSecondaryIndexName = GLOBAL_SECONDARY_INDEX_NAME)
  String getExternalId();

  @DynamoDBIndexRangeKey(
      attributeName = GSI_RANGE_KEY,
      globalSecondaryIndexName = GLOBAL_SECONDARY_INDEX_NAME)
  String getApiKeyPaymentUserGSIPK();

  Boolean getClaimed();

  PaymentStatus getStatus();

  Instant getCreatedAt();

  class Builder extends PaymentIndexByExternalId_Builder {

    public Builder() {}
  }
}
