package com.scopely.paymentgateway.repositories;

import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.repository.ChargebackRepositoryException;
import com.scopely.paymentgateway.model.reversal.Chargeback;
import java.util.List;
import java.util.Optional;

public interface ChargebackRepository {

  Chargeback save(Chargeback chargeback)
      throws LockingConflictException, ChargebackRepositoryException;

  Optional<Chargeback> getById(String chargebackId) throws ChargebackRepositoryException;

  List<Chargeback> getByUser(String apiKey, String userId) throws ChargebackRepositoryException;
}
