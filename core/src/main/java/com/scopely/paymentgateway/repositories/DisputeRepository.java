package com.scopely.paymentgateway.repositories;

import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.repository.DisputeRepositoryException;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

public interface DisputeRepository {

  Dispute save(Dispute dispute) throws LockingConflictException, DisputeRepositoryException;

  List<Dispute> getByPayment(String paymentId) throws DisputeRepositoryException;

  List<Dispute> getByUser(String apiKey, String userId) throws DisputeRepositoryException;

  List<Dispute> getByUserFromDate(String apiKey, String userId, Instant dateFrom)
      throws DisputeRepositoryException;

  Optional<Dispute> getByDisputeId(String disputeId) throws DisputeRepositoryException;
}
