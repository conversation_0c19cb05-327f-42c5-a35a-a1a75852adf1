package com.scopely.paymentgateway.repositories.daos;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import javax.annotation.Nullable;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = PaymentSessionIdsDAO.Builder.class)
@DynamoDBTable(tableName = PaymentSessionIdsDAO.TABLE)
public interface PaymentSessionIdsDAO {
  String TABLE = "PaymentGateway_PaymentSessionIds";

  String HASH_KEY = "sessionId";
  String RANGE_KEY = "provider";

  @DynamoDBHashKey(attributeName = HASH_KEY)
  String getSessionId();

  @DynamoDBRangeKey(attributeName = RANGE_KEY)
  String getProvider();

  @Nullable
  String getPaymentId();

  class Builder extends PaymentSessionIdsDAO_Builder {}
}
