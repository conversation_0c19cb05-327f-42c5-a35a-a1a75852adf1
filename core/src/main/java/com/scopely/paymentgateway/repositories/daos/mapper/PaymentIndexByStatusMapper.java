package com.scopely.paymentgateway.repositories.daos.mapper;

import com.scopely.paymentgateway.model.payment.PaymentStatus;
import com.scopely.paymentgateway.repositories.daos.PaymentDAO;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByUser;

@Deprecated
public class PaymentIndexByStatusMapper {

  public static PaymentIndexByUser paymentDaoToPaymentByStatus(PaymentDAO paymentDAO) {
    return new PaymentIndexByUser.Builder()
        .setPaymentId(paymentDAO.getPaymentId())
        .setUserId(paymentDAO.getUserId())
        .setStatus(PaymentStatus.valueOf(paymentDAO.getStatus()))
        .setApiKeyPaymentUserGSIPK(paymentDAO.getApiKeyPaymentUserGSIPK())
        .setStatusCreatedAtGSIPK(paymentDAO.getStatusCreatedAtGSIPK())
        .setCountry(paymentDAO.getCountry())
        .setCreatedAt(paymentDAO.getCreatedAt())
        .setClaimed(paymentDAO.getClaimed())
        .build();
  }
}
