package com.scopely.paymentgateway.repositories.daos;

import static com.scopely.satellites.Try.tryThis;
import static java.util.Objects.isNull;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBVersionAttribute;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.featuresmapping.PaymentFeature;
import com.scopely.paymentgateway.model.payment.ItemData;
import com.scopely.paymentgateway.model.payment.PaymentMethod;
import com.scopely.paymentgateway.model.payment.PaymentProviderData;
import com.scopely.paymentgateway.model.payment.PaymentProviderDataDeserializer;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import com.scopely.paymentgateway.model.payment.PlatformType;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.ProviderStatus;
import com.scopely.paymentgateway.utils.DynamoDBUtils;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import javax.annotation.Nullable;
import javax.money.MonetaryAmount;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = PaymentDAO.Builder.class)
@DynamoDBTable(tableName = PaymentDAO.TABLE)
public interface PaymentDAO {

  String TABLE = "PaymentGateway_Payment";
  String PAYMENT_ID = "paymentId";
  String SESSION_ID = "sessionId";
  String ORDER_ID = "orderId";
  String API_KEY_ID = "apiKey";
  String USER_ID = "userId";
  String EXTERNAL_ID = "externalId";
  String API_KEY_USER = "apiKeyPaymentUserGSIPK";
  String HASH_KEY = PAYMENT_ID;

  @DynamoDBHashKey(attributeName = HASH_KEY)
  String getPaymentId();

  String getStatus();

  String getUserId();

  @Nullable
  String getDeviceToken();

  String getApiKey();

  boolean isSandbox();

  @Nullable
  PriceData getPriceData();

  @Nullable
  PaymentMethod getPaymentMethodUsed();

  boolean isSavedPaymentMethod();

  Boolean getClaimed();

  @Nullable
  Instant getClaimedAt();

  @Nullable
  Instant getReversedAt();

  @Nullable
  Instant getReceiptSentAt();

  @Nullable
  String getReceiptId();

  String getCountry();

  Instant getCreatedAt();

  Instant getUpdatedAt();

  @Nullable
  String getOrderId();

  List<PaymentFeature> getFeatures();

  @Nullable
  @DynamoDBVersionAttribute(attributeName = "version")
  Integer getVersion();

  @Nullable
  String getTrackingId();

  @Nullable
  String getApiKeyPaymentUserGSIPK();

  @Nullable
  String getStatusCreatedAtGSIPK();

  @Nullable
  ProviderStatus getProviderStatus();

  @Nullable
  String getErrorMessage();

  @Nullable
  ItemData getItemData();

  boolean isVip();

  boolean isProviderFallback();

  @Nullable
  String getUnclaimedCompleted();

  @Nullable
  String getLocale();

  @Nullable
  PaymentProviderData getProviderData();

  @Nullable
  String getExternalId();

  PlatformType getPlatform();

  @Nullable
  String getPurchaseToken();

  @Nullable
  String getTitanContextProperties();

  // region legacy properties
  @Nullable
  @Deprecated
  MonetaryAmount getTotalPrice();

  @Nullable
  @Deprecated
  MonetaryAmount getPrice();

  @Nullable
  @Deprecated
  MonetaryAmount getTaxes();

  @Nullable
  @Deprecated
  MonetaryAmount getLocalTotalPrice();

  @Nullable
  @Deprecated
  MonetaryAmount getLocalPrice();

  @Nullable
  @Deprecated
  MonetaryAmount getLocalTaxes();

  @Nullable
  @Deprecated
  BigDecimal getTaxRate();

  // endregion

  class Builder extends PaymentDAO_Builder {

    public Builder() {
      setSavedPaymentMethod(false);
      setVip(false);
      setProviderFallback(false);
      setSandbox(false);
      setPlatform(PlatformType.WEB);
    }

    @Override
    public PaymentDAO build() {
      // Those fields are meant to be only used by dynamo, they are a composition of other
      // attributes
      this.setApiKeyPaymentUserGSIPK(
          DynamoDBUtils.buildCompositeAttribute(getApiKey(), getUserId()));
      this.setStatusCreatedAtGSIPK(
          DynamoDBUtils.buildCompositeAttribute(getStatus(), getCreatedAt().toString()));
      this.calculateUnclaimedGSIPK();

      // if we don't have the sandbox flag, we assume it's not sandbox
      setSandbox(tryThis(this::isSandbox).orElse(false));
      // While deciding to do a bd migration, establish backward compatibility with
      if (isNull(getPriceData())) {
        PaymentDAOMigrationHelper.migratePriceData(this);
      }

      return super.build();
    }

    private void calculateUnclaimedGSIPK() {
      this.setUnclaimedCompleted(
          this.getStatus().equals(PaymentStatus.COMPLETED.name())
                  && !this.getClaimed()
                  && this.getReversedAt() == null
              ? getApiKey()
              : null);
    }

    @Override
    @JsonDeserialize(using = PaymentProviderDataDeserializer.class)
    public Builder setProviderData(PaymentProviderData data) {
      return super.setProviderData(data);
    }
  }
}
