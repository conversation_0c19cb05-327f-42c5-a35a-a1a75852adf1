package com.scopely.paymentgateway.repositories.daos;

import static com.scopely.satellites.Try.tryThis;
import static java.util.Objects.isNull;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBVersionAttribute;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.deserializer.RefundProviderDataDeserializer;
import com.scopely.paymentgateway.model.payment.PaymentMethod;
import com.scopely.paymentgateway.model.refund.RefundProviderdata;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.model.refund.RefundStatus;
import com.scopely.paymentgateway.model.refund.RefundType;
import java.time.Instant;
import javax.annotation.Nullable;
import org.inferred.freebuilder.FreeBuilder;
import org.javamoney.moneta.Money;

@FreeBuilder
@JsonDeserialize(builder = RefundDAO.Builder.class)
@DynamoDBTable(tableName = RefundDAO.TABLE)
public interface RefundDAO {
  String TABLE = "PaymentGateway_Refund";
  String HASH_KEY = "paymentId";
  String RANGE_KEY = "refundId";
  String GSI_REFUND_ID = "GSI_Payment_RefundId";

  @DynamoDBHashKey(attributeName = HASH_KEY)
  String getPaymentId();

  @DynamoDBRangeKey(attributeName = RANGE_KEY)
  String getRefundId();

  String getApiKey();

  RefundReason getRefundReason();

  Instant getCreatedAt();

  @Nullable
  Instant getUpdatedAt();

  Money getRefundedAmount();

  Money getRefundedLocalAmount();

  @Nullable
  String getFailureReason();

  RefundStatus getStatus();

  @Nullable
  RefundProviderdata getProviderData();

  @Nullable
  RefundType getRefundType();

  @Nullable
  PaymentMethod getPaymentMethodUsed();

  @Nullable
  Instant getReceiptSentAt();

  @Nullable
  String getReceiptId();

  @Nullable
  @DynamoDBVersionAttribute(attributeName = "version")
  Integer getVersion();

  @Nullable
  String getSupportTicket();

  @Nullable
  String getComment();

  class Builder extends RefundDAO_Builder {

    @Override
    @JsonDeserialize(using = RefundProviderDataDeserializer.class)
    public Builder setProviderData(RefundProviderdata data) {
      return super.setProviderData(data);
    }

    @Override
    public RefundDAO build() {
      // For compatibility backwards with non-existing local amount we will add this conversion
      if (isNull(tryThis(this::getRefundedLocalAmount).orElse(null))) {
        this.setRefundedLocalAmount(this.getRefundedAmount());
      }
      return super.build();
    }
  }
}
