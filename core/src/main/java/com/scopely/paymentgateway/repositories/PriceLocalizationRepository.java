package com.scopely.paymentgateway.repositories;

import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.PriceLocalizationException;
import com.scopely.paymentgateway.model.localizedprice.PriceLocalization;
import java.util.Optional;

public interface PriceLocalizationRepository {
  Optional<PriceLocalization> getActivePriceLocalizationByApiKey(String apiKey)
      throws PriceLocalizationException;

  PriceLocalization saveNewPriceLocalizationVersion(PriceLocalization priceLocalization)
      throws LockingConflictException, PriceLocalizationException;
}
