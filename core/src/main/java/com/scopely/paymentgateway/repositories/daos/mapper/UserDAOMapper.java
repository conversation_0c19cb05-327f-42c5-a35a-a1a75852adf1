package com.scopely.paymentgateway.repositories.daos.mapper;

import com.scopely.paymentgateway.model.user.User;
import com.scopely.paymentgateway.repositories.daos.UserDAO;

public class UserDAOMapper {

  public static UserDAO userToUserDao(User user) {
    return new UserDAO.Builder()
        .setUserId(user.getUserId())
        .setBlocked(user.isBlocked())
        .setBlockedAt(user.getLastBlockedAt())
        .setBlockedBy(user.getBlockedBy())
        .setBlockedReason(user.getBlockedReason())
        .setApiKey(user.getApiKey())
        .setProvider(user.getProvider())
        .setUpdatedAt(user.getUpdatedAt())
        .setVersion(user.getVersion())
        .build();
  }

  public static User userDaoToUser(UserDAO userDAO) {
    return new User.Builder()
        .setUserId(userDAO.getUserId())
        .setBlocked(userDAO.isBlocked())
        .setLastBlockedAt(userDAO.getBlockedAt())
        .setBlockedBy(userDAO.getBlockedBy())
        .setBlockedReason(userDAO.getBlockedReason())
        .setApiKey(userDAO.getApiKey())
        .setProvider(userDAO.getProvider())
        .setUpdatedAt(userDAO.getUpdatedAt())
        .setVersion(userDAO.getVersion())
        .build();
  }
}
