package com.scopely.paymentgateway.repositories.daos.mapper;

import com.scopely.paymentgateway.model.reversal.Reversal;
import com.scopely.paymentgateway.repositories.daos.ReversalDAO;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class ReversalDAOMapper {

  @Inject
  public ReversalDAOMapper() {}

  public Reversal reversalDaoToReversal(ReversalDAO reversalDAO) {
    return new Reversal.Builder()
        .setApiKey(reversalDAO.getApiKey())
        .setUserId(reversalDAO.getUserId())
        .setPaymentId(reversalDAO.getPaymentId())
        .setReversalId(reversalDAO.getHeader())
        .setCreatedAt(reversalDAO.getCreatedAt())
        .setUpdatedAt(reversalDAO.getUpdatedAt())
        .setVersion(reversalDAO.getVersion())
        .build();
  }
}
