package com.scopely.paymentgateway.repositories.daos;

import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PriceDetail;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;
import javax.money.MonetaryAmount;

public final class PaymentDAOMigrationHelper {

  private PaymentDAOMigrationHelper() {}

  public static void migratePriceData(PaymentDAO.Builder builder) {
    var basePrice =
        buildPriceDetail(builder.getTotalPrice(), builder.getPrice(), builder.getTaxes());
    var localPrice =
        Objects.isNull(builder.getLocalPrice())
            ? basePrice
            : buildPriceDetail(
                builder.getLocalTotalPrice(), builder.getLocalPrice(), builder.getLocalTaxes());
    var priceData =
        new PriceData.Builder()
            .setLocalPrice(localPrice)
            .setBasePrice(basePrice)
            .setTaxRate(Optional.ofNullable(builder.getTaxRate()).orElse(BigDecimal.ZERO))
            .build();
    builder
        .setPriceData(priceData)
        .setLocalPrice(null)
        .setLocalTotalPrice(null)
        .setLocalTaxes(null)
        .setPrice(null)
        .setTotalPrice(null)
        .setTaxes(null)
        .setTaxRate(null);
  }

  private static PriceDetail buildPriceDetail(
      MonetaryAmount totalPrice, MonetaryAmount price, MonetaryAmount taxes) {
    return new PriceDetail.Builder()
        .setTotalAmount(new BigDecimal(totalPrice.getNumber().toString()))
        .setSubtotalAmount(new BigDecimal(price.getNumber().toString()))
        .setTaxAmount(new BigDecimal(taxes.getNumber().toString()))
        .setCurrency(price.getCurrency().getCurrencyCode())
        .build();
  }
}
