package com.scopely.paymentgateway.repositories;

import static com.google.common.collect.ImmutableMap.of;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SERVICE;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBQueryExpression;
import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.PaymentException;
import com.scopely.paymentgateway.exceptions.PaymentFilterNotValidException;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentFilterCriteria;
import com.scopely.paymentgateway.repositories.daos.PaymentDAO;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByOrderId;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByUser;
import com.scopely.paymentgateway.repositories.daos.mapper.PaymentDAOMapper;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.DynamoDBQueryHelper;
import com.scopely.paymentgateway.utils.DynamoDBUtils;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.inject.Inject;
import javax.inject.Singleton;
import net.jodah.failsafe.Failsafe;
import net.jodah.failsafe.RetryPolicy;
import net.jodah.failsafe.function.CheckedConsumer;

@Singleton
public class DynamoPaymentFilterRepository implements PaymentFilterRepository {
  private final JsonDynamoMapper dynamoMapper;
  private final RetryPolicy retryPolicy;
  private final StatsDClient statsDClient;
  private static final String PAYMENT_ID = "paymentId";
  private static final String API_KEY = "apiKey";
  public static final String SESSION_ID = "sessionId";
  public static final String USER_ID = "userId";
  public static final String ORDER_ID = "orderId";
  public static final String PROVIDER_KEY = "#providerData.#provider";

  @Inject
  public DynamoPaymentFilterRepository(
      JsonDynamoMapper dynamoMapper, RetryPolicy retryPolicy, StatsDClient statsDClient) {
    this.dynamoMapper = dynamoMapper;
    this.retryPolicy = retryPolicy;
    this.statsDClient = statsDClient;
  }

  @Override
  public List<Payment> getAll(PaymentFilterCriteria filter) {
    var result = new ArrayList<Payment>();

    // check if we have playgami id to use raw table
    if (filter.getPaymentId() != null) {
      result.addAll(getByPaymentId(filter));
      if (result.isEmpty() && (filter.getOrderId() != null)) {
        result.addAll(getByOrderId(filter));
      }
    } else if (filter.getOrderId() != null) { // check if we have orderId to use order id index
      result.addAll(getByOrderId(filter));
    } else if (filter.getUserId() != null) { // check if we have userId to use user id index
      result.addAll(getByUser(filter));
    } else { // if not, we will launch a payment filter not valid exception
      throw new PaymentFilterNotValidException(
          "Filter doesn't have enough filter parameters to be valid");
    }
    return result;
  }

  public List<Payment> getByPaymentId(PaymentFilterCriteria filter) {
    Objects.requireNonNull(filter.getPaymentId());
    return this.getByPaymentId(filter.getPaymentId(), filter);
  }

  public List<Payment> getByOrderId(PaymentFilterCriteria filter) {
    var results = new ArrayList<Payment>();
    getPaymentsByOrderId(filter)
        .forEach(
            indexByOrder -> results.addAll(getByPaymentId(indexByOrder.getPaymentId(), filter)));
    return results;
  }

  public List<Payment> getByUser(PaymentFilterCriteria filter) {
    var results = new ArrayList<Payment>();
    getPaymentsByUserId(filter)
        .forEach(indexByUser -> results.addAll(getByPaymentId(indexByUser.getPaymentId(), filter)));
    return results;
  }

  public List<Payment> getByPaymentId(String paymentId, PaymentFilterCriteria filter) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(PAYMENT_ID, paymentId)).error(t, "Unable to load payment");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              PaymentDAO.TABLE, StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new PaymentException(t);
                })
        .get(
            () -> {
              var queryHelper = new DynamoDBQueryHelper();
              queryHelper.addFilterCondition(API_KEY, filter.getApiKey());
              queryHelper.addFilterCondition(USER_ID, filter.getUserId());
              queryHelper.addFilterCondition(PROVIDER_KEY, filter.getProvider());
              queryHelper.addKeyCondition(PAYMENT_ID, paymentId);

              var query =
                  new DynamoDBQueryExpression<PaymentDAO>()
                      .withConsistentRead(false)
                      .withScanIndexForward(false)
                      .withKeyConditionExpression(queryHelper.getKeyConditionChain())
                      .withFilterExpression(queryHelper.getFilterExpressionChain())
                      .withExpressionAttributeValues(queryHelper.getAttributeValues())
                      .withExpressionAttributeNames(queryHelper.getAttributesNames());

              return dynamoMapper.queryAll(PaymentDAO.class, query);
            })
        .stream()
        .map(PaymentDAOMapper::paymentDaoToPayment)
        .toList();
  }

  public List<PaymentIndexByOrderId> getPaymentsByOrderId(PaymentFilterCriteria filter) {
    Objects.requireNonNull(filter.getOrderId());
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(ORDER_ID, filter.getOrderId()))
                      .error(t, "Unable to load payment");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              PaymentIndexByOrderId.GSI_HASH_KEY,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new PaymentException(t);
                })
        .onSuccess(
            (List<PaymentIndexByOrderId> payments) -> {
              if (payments.isEmpty()) {
                Log.withMetadata(of(ORDER_ID, filter.getOrderId()))
                    .debug("Payment not found by order id");
              }
            })
        .get(
            () -> {
              var queryHelper = new DynamoDBQueryHelper();
              queryHelper.addKeyCondition(PaymentIndexByOrderId.GSI_HASH_KEY, filter.getOrderId());
              var query =
                  new DynamoDBQueryExpression<PaymentDAO>()
                      .withIndexName(PaymentIndexByOrderId.GLOBAL_SECONDARY_INDEX_NAME)
                      .withConsistentRead(false)
                      .withScanIndexForward(false)
                      .withKeyConditionExpression(queryHelper.getKeyConditionChain())
                      .withFilterExpression(queryHelper.getFilterExpressionChain())
                      .withExpressionAttributeValues(queryHelper.getAttributeValues());

              return dynamoMapper.queryAll(PaymentIndexByOrderId.class, query);
            });
  }

  public List<PaymentIndexByUser> getPaymentsByUserId(PaymentFilterCriteria filter) {
    Objects.requireNonNull(filter.getUserId());
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  Log.withMetadata(of(USER_ID, filter.getUserId()))
                      .error(t, "Unable to load payments by user id");
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(
                              PaymentIndexByUser.GLOBAL_SECONDARY_INDEX_NAME,
                              StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new PaymentException(t);
                })
        .onSuccess(
            (List<PaymentIndexByUser> payments) -> {
              if (payments.isEmpty()) {
                Log.withMetadata(of(USER_ID, filter.getUserId()))
                    .debug("Payments not found for user id");
              }
            })
        .get(
            () -> {
              var queryHelper = new DynamoDBQueryHelper();
              queryHelper.addKeyCondition(
                  "apiKeyPaymentUserGSIPK",
                  DynamoDBUtils.buildCompositeAttribute(filter.getApiKey(), filter.getUserId()));

              var query =
                  new DynamoDBQueryExpression<PaymentDAO>()
                      .withIndexName(PaymentIndexByUser.GLOBAL_SECONDARY_INDEX_NAME)
                      .withConsistentRead(false)
                      .withScanIndexForward(false)
                      .withKeyConditionExpression(queryHelper.getKeyConditionChain())
                      .withFilterExpression(queryHelper.getFilterExpressionChain())
                      .withExpressionAttributeValues(queryHelper.getAttributeValues());

              return dynamoMapper.queryAll(PaymentIndexByUser.class, query);
            });
  }
}
