package com.scopely.paymentgateway.repositories.daos.mapper;

import com.scopely.paymentgateway.model.reversal.Chargeback;
import com.scopely.paymentgateway.repositories.daos.ChargebackDAO;
import com.scopely.paymentgateway.utils.DynamoDBUtils;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class ChargebackDAOMapper {
  private static final String[] HEADER_PREFIXES = new String[] {"REVERSAL", "CHARGEBACK"};

  @Inject
  public ChargebackDAOMapper() {}

  public ChargebackDAO chargebackToChargebackDao(Chargeback chargeback) {
    return new ChargebackDAO.Builder()
        .setApiKey(chargeback.getApiKey())
        .setUserId(chargeback.getUserId())
        .setPaymentId(chargeback.getPaymentId())
        .setHeader(getHeaderFromChargebackId(chargeback.getChargebackId()))
        .setChargebackReason(chargeback.getChargebackReason())
        .setCreatedAt(chargeback.getCreatedAt())
        .setUpdatedAt(chargeback.getUpdatedAt())
        .setAmount(chargeback.getAmount())
        .setLocalAmount(chargeback.getLocalAmount())
        .setVersion(chargeback.getVersion())
        .build();
  }

  public Chargeback chargebackDaoToChargeback(ChargebackDAO chargebackDAO) {
    return new Chargeback.Builder()
        .setApiKey(chargebackDAO.getApiKey())
        .setUserId(chargebackDAO.getUserId())
        .setPaymentId(chargebackDAO.getPaymentId())
        .setChargebackId(getChargebackIdFromHeader(chargebackDAO.getHeader()))
        .setChargebackReason(chargebackDAO.getChargebackReason())
        .setCreatedAt(chargebackDAO.getCreatedAt())
        .setUpdatedAt(chargebackDAO.getUpdatedAt())
        .setAmount(chargebackDAO.getAmount())
        .setLocalAmount(chargebackDAO.getLocalAmount())
        .setVersion(chargebackDAO.getVersion())
        .build();
  }

  public String getHeaderFromChargebackId(String chargebackId) {
    return DynamoDBUtils.buildCompositeAttribute(
        HEADER_PREFIXES[0], HEADER_PREFIXES[1], chargebackId);
  }

  public String getChargebackIdFromHeader(String header) {
    return header.split(DynamoDBUtils.DELIMITER)[HEADER_PREFIXES.length];
  }

  public String getHeaderPrefix() {
    return getHeaderFromChargebackId("");
  }
}
