package com.scopely.paymentgateway.repositories;

import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SERVICE;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBQueryExpression;
import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.repository.ReversalRepositoryException;
import com.scopely.paymentgateway.model.reversal.Reversal;
import com.scopely.paymentgateway.repositories.daos.ReversalDAO;
import com.scopely.paymentgateway.repositories.daos.mapper.ReversalDAOMapper;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.DynamoDBQueryHelper;
import com.scopely.paymentgateway.utils.DynamoDBUtils;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DynamoReversalRepository implements ReversalRepository {
  private final JsonDynamoMapper dynamoMapper;
  private final StatsDClient statsDClient;
  private final ReversalDAOMapper reversalDAOMapper;

  @Inject
  public DynamoReversalRepository(
      JsonDynamoMapper dynamoMapper,
      StatsDClient statsDClient,
      ReversalDAOMapper reversalDAOMapper) {
    this.dynamoMapper = dynamoMapper;
    this.statsDClient = statsDClient;
    this.reversalDAOMapper = reversalDAOMapper;
  }

  @Override
  public List<Reversal> getReversalsByPayment(String paymentId) throws ReversalRepositoryException {
    var dynamoFilter = new DynamoDBQueryHelper();
    dynamoFilter.addKeyCondition(ReversalDAO.HASH_KEY, paymentId);
    var query =
        new DynamoDBQueryExpression<ReversalDAO>()
            .withConsistentRead(false)
            .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
            .withFilterExpression(dynamoFilter.getFilterExpressionChain())
            .withExpressionAttributeValues(dynamoFilter.getAttributeValues());
    try {
      var reversalDaos = dynamoMapper.queryAll(ReversalDAO.class, query);
      return reversalDaos.stream().map(reversalDAOMapper::reversalDaoToReversal).toList();
    } catch (RuntimeException e) {
      statsDClient.increment(
          StatsConstants.DD_REPOSITORY_OPERATION_GET,
          MetricsUtils.buildTags(
              List.of(TAG_SERVICE, TAG_RESULT),
              List.of(ReversalDAO.TABLE, StatsConstants.OperationResults.UNHANDLED_ERROR)));
      throw new ReversalRepositoryException(e);
    }
  }

  @Override
  public List<Reversal> getReversalsByUser(String apiKey, String userId)
      throws ReversalRepositoryException {
    var dynamoFilter = new DynamoDBQueryHelper();
    dynamoFilter.addKeyCondition(
        ReversalDAO.API_KEY_USER, DynamoDBUtils.buildCompositeAttribute(apiKey, userId));
    var query =
        new DynamoDBQueryExpression<ReversalDAO>()
            .withConsistentRead(false)
            .withIndexName(ReversalDAO.APIKEY_USER_INDEX_NAME)
            .withKeyConditionExpression(dynamoFilter.getKeyConditionChain())
            .withExpressionAttributeValues(dynamoFilter.getAttributeValues())
            .withFilterExpression(dynamoFilter.getFilterExpressionChain());
    try {
      var reversalDaos = dynamoMapper.queryAll(ReversalDAO.class, query);
      return reversalDaos.stream().map(reversalDAOMapper::reversalDaoToReversal).toList();
    } catch (RuntimeException e) {
      statsDClient.increment(
          StatsConstants.DD_REPOSITORY_OPERATION_GET,
          MetricsUtils.buildTags(
              List.of(TAG_SERVICE, TAG_RESULT),
              List.of(
                  ReversalDAO.APIKEY_USER_INDEX_NAME,
                  StatsConstants.OperationResults.UNHANDLED_ERROR)));
      throw new ReversalRepositoryException(e);
    }
  }
}
