package com.scopely.paymentgateway.repositories;

import static com.scopely.paymentgateway.constants.StatsConstants.*;

import com.amazonaws.services.dynamodbv2.model.ConditionalCheckFailedException;
import com.scopely.mapper.JsonDynamoMapper;
import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.UserRepositoryException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.user.User;
import com.scopely.paymentgateway.repositories.daos.UserDAO;
import com.scopely.paymentgateway.repositories.daos.mapper.UserDAOMapper;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.DynamoDBUtils;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;
import net.jodah.failsafe.Failsafe;
import net.jodah.failsafe.RetryPolicy;
import net.jodah.failsafe.function.CheckedConsumer;

@Singleton
public class DynamoUserRepository implements UserRepository {

  private final JsonDynamoMapper dynamoMapper;
  private final RetryPolicy retryPolicy;
  private final StatsDClient statsDClient;

  @Inject
  public DynamoUserRepository(
      JsonDynamoMapper dynamoMapper, RetryPolicy retryPolicy, StatsDClient statsDClient) {
    this.dynamoMapper = dynamoMapper;
    this.retryPolicy = retryPolicy;
    this.statsDClient = statsDClient;
  }

  @Override
  public Optional<User> getById(String apiKey, String userId) {
    return Failsafe.with(retryPolicy)
        .withFallback(
            (CheckedConsumer<Throwable>)
                t -> {
                  new PaymentGatewayLogBuilder()
                      .addUserData(apiKey, userId)
                      .build()
                      .error(t, "Unable load user with userId: " + userId);
                  statsDClient.increment(
                      StatsConstants.DD_REPOSITORY_OPERATION_GET,
                      MetricsUtils.buildTags(
                          List.of(TAG_SERVICE, TAG_RESULT),
                          List.of(UserDAO.TABLE, StatsConstants.OperationResults.UNHANDLED_ERROR)));
                  throw new UserRepositoryException(t);
                })
        .onSuccess(
            (List<UserDAO> users) -> {
              new PaymentGatewayLogBuilder()
                  .addUserData(apiKey, userId)
                  .build()
                  .debug("Loaded user with userId: " + userId);
            })
        .get(
            () -> {
              var apiKeyUserId = DynamoDBUtils.buildCompositeAttribute(apiKey, userId);
              return dynamoMapper.load(UserDAO.class, apiKeyUserId);
            })
        .map(UserDAOMapper::userDaoToUser);
  }

  @Override
  public User save(User user) {
    try {
      UserDAO userDAO = UserDAOMapper.userToUserDao(user);
      UserDAO savedUserDAO =
          Failsafe.with(retryPolicy)
              .withFallback(
                  (CheckedConsumer<Throwable>)
                      t -> {
                        if (t instanceof ConditionalCheckFailedException) {
                          throw (ConditionalCheckFailedException) t;
                        }
                        new PaymentGatewayLogBuilder()
                            .addUserData(user.getApiKey(), user.getUserId())
                            .build()
                            .error(t, "Unable to save user (" + user.getUserId() + ")");

                        statsDClient.increment(
                            StatsConstants.DD_REPOSITORY_OPERATION_GET,
                            MetricsUtils.buildTags(
                                List.of(TAG_SERVICE, TAG_RESULT),
                                List.of(
                                    UserDAO.TABLE,
                                    StatsConstants.OperationResults.UNHANDLED_ERROR)));
                        throw new UserRepositoryException(t);
                      })
              .get(() -> dynamoMapper.saveAndGet(userDAO));
      return UserDAOMapper.userDaoToUser(savedUserDAO);
    } catch (Exception exception) {
      new PaymentGatewayLogBuilder()
          .addUserData(user.getApiKey(), user.getUserId())
          .build()
          .error(exception, "Unable to save user (" + user.getUserId() + ")");
      throw new UserRepositoryException(exception);
    }
  }
}
