package com.scopely.paymentgateway.repositories;

import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.provider.fees.ProviderFees;
import java.util.List;

public interface FeesByProviderRepository {
  List<ProviderFees> getFeesByApiKey(String apiKey);

  List<ProviderFees> getFeesByCountryAndCurrencyAndProvider(
      String apiKey, PaymentProviderIdentifier provider, String country, String currency);

  void save(ProviderFees fees);
}
