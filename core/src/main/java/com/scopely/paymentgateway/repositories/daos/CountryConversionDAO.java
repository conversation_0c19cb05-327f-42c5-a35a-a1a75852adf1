package com.scopely.paymentgateway.repositories.daos;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.math.BigDecimal;
import java.time.Instant;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = CountryConversionDAO.Builder.class)
@DynamoDBTable(tableName = CountryConversionDAO.TABLE)
public interface CountryConversionDAO {
  String TABLE = "PaymentGateway_CountryConversion";
  String HASH_KEY = "countryId";

  @DynamoDBHashKey(attributeName = HASH_KEY)
  String getCountryId();

  String getCurrencyId();

  BigDecimal getExchangeRate();

  boolean isTaxIncluded();

  BigDecimal getTaxRate();

  BigDecimal getConversionFactor();

  Instant getUpdatedAt();

  class Builder extends CountryConversionDAO_Builder {
    public Builder() {}
  }
}
