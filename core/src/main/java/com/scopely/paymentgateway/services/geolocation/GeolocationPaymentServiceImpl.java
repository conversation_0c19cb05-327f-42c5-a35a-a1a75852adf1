package com.scopely.paymentgateway.services.geolocation;

import static com.google.common.collect.ImmutableMap.of;
import static com.scopely.paymentgateway.constants.LocationConstants.*;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_COUNTRY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PROVIDER;

import com.scopely.geofencing.client.model.Location;
import com.scopely.geofencing.client.service.GeofencingService;
import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.CountryUnsupportedException;
import com.scopely.paymentgateway.model.location.CountryDetails;
import com.scopely.paymentgateway.model.location.CountryProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PaymentLocation;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.services.countryconversion.CountryInfoService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.logging.Log;
import com.scopely.proteus.util.StringUtils;
import com.timgroup.statsd.StatsDClient;
import java.util.Locale;
import javax.inject.Inject;
import javax.inject.Singleton;

// This provides thread-safe access to the GeoIP database.
@Singleton
public class GeolocationPaymentServiceImpl implements GeolocationPaymentService {

  public static final String DEFAULT_STATE = null;
  public static final String DEFAULT_CITY = null;
  public static final String DEFAULT_POSTAL_CODE = null;
  private static final String IP_ADDRESS = "IP_ADDRESS";

  private final StatsDClient statsDClient;
  private final GeofencingService geofencingService;
  private final CountryInfoService countryInfoService;

  @Inject
  public GeolocationPaymentServiceImpl(
      StatsDClient statsDClient,
      GeofencingService geofencingService,
      CountryInfoService countryInfoService) {
    this.statsDClient = statsDClient;
    this.geofencingService = geofencingService;
    this.countryInfoService = countryInfoService;
  }

  @Override
  public PaymentLocation getPaymentLocation(String ipAddress) {
    try {
      statsDClient.increment(StatsConstants.DD_GEOLOCATION_GET_BY_IP_HITS);
      Location locationByIpAddress = geofencingService.getLocationByIpAddress(ipAddress);
      CountryDetails countryDetails =
          countryInfoService.getDefaultCountryDetails(locationByIpAddress.getCountry());

      return new PaymentLocation.Builder()
          .setIpAddress(locationByIpAddress.getIpAddress())
          .setCountry(locationByIpAddress.getCountry())
          .setContinent(countryDetails.getRegion())
          .setCurrency(countryDetails.getCurrency())
          .setState(locationByIpAddress.getState())
          .setCity(locationByIpAddress.getCity())
          .setPostalCode(locationByIpAddress.getPostalCode())
          .setTaxIncluded(countryDetails.isTaxIncluded())
          .build();

    } catch (Exception e) {
      statsDClient.increment(StatsConstants.DD_GEOLOCATION_GET_BY_IP_ERRORS);
      Log.withMetadata(of(IP_ADDRESS, ipAddress))
          .debug(e, "There was an error getting the Geo location info");
      return createDefaultPaymentLocation();
    }
  }

  @Override
  public PaymentLocation getPaymentLocation(String countryId, String ipAddress)
      throws CountryUnsupportedException {
    return StringUtils.isEmptyWhenTrimmed(countryId)
        ? getPaymentLocation(ipAddress)
        : createPaymentLocation(countryId.toUpperCase(Locale.getDefault()), ipAddress);
  }

  @Override
  public PaymentLocation updatePaymentLocation(
      PaymentLocation paymentLocation, PaymentProviderIdentifier provider)
      throws CountryUnsupportedException {
    CountryDetails countryDetails =
        countryInfoService.getCountryDetails(paymentLocation.getCountry(), provider);

    validateCountrySupported(
        countryDetails,
        paymentLocation.getCountry(),
        CountryProviderIdentifier.fromPaymentProvider(provider));

    return PaymentLocation.Builder.from(paymentLocation)
        .setContinent(countryDetails.getRegion())
        .setCurrency(countryDetails.getCurrency())
        .build();
  }

  private PaymentLocation createPaymentLocation(String countryId, String ipAddress)
      throws CountryUnsupportedException {
    statsDClient.increment(StatsConstants.DD_GET_LOCATION_BY_COUNTRY_HITS);
    CountryDetails countryDetails = countryInfoService.getDefaultCountryDetails(countryId);

    validateCountrySupported(countryDetails, countryId, CountryProviderIdentifier.DEFAULT);

    return new PaymentLocation.Builder()
        .setIpAddress(ipAddress)
        .setCountry(countryId)
        .setContinent(countryDetails.getRegion())
        .setCurrency(countryDetails.getCurrency())
        .setTaxIncluded(countryDetails.isTaxIncluded())
        .build();
  }

  private void validateCountrySupported(
      CountryDetails countryDetails, String countryId, CountryProviderIdentifier provider)
      throws CountryUnsupportedException {

    // If the country is unsupported by the provider throw an error
    if (countryDetails.isUnsupported()) {
      statsDClient.increment(
          StatsConstants.DD_PAYMENT_LOCATION_NOT_SUPPORTED,
          buildDDTags(countryId, provider.toString()));
      Log.withMetadata(of("country", countryId)).debug("Country {} Not supported", countryId);
      throw new CountryUnsupportedException();
    }
  }

  private String[] buildDDTags(final String country, String provider) {
    return MetricsUtils.buildTags(
        new String[] {TAG_COUNTRY, TAG_PROVIDER}, new String[] {country, provider});
  }

  private PaymentLocation createDefaultPaymentLocation() {
    statsDClient.increment(StatsConstants.DD_DEFAULT_GEOLOCATION_VALUES);
    return new PaymentLocation.Builder()
        .setIpAddress(DEFAULT_IP)
        .setCountry(DEFAULT_COUNTRY)
        .setContinent(DEFAULT_CONTINENT)
        .setCurrency(DEFAULT_CURRENCY)
        .setState(DEFAULT_STATE)
        .setCity(DEFAULT_CITY)
        .setPostalCode(DEFAULT_POSTAL_CODE)
        .build();
  }
}
