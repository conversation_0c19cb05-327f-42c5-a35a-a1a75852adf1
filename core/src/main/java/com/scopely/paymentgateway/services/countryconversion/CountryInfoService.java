package com.scopely.paymentgateway.services.countryconversion;

import static com.google.common.collect.ImmutableMap.of;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import com.scopely.paymentgateway.config.clientconfig.CountryInfoFile;
import com.scopely.paymentgateway.exceptions.FileResourceException;
import com.scopely.paymentgateway.exceptions.InvalidCountryException;
import com.scopely.paymentgateway.model.location.CountryDetails;
import com.scopely.paymentgateway.model.location.CountryInfo;
import com.scopely.paymentgateway.model.location.CountryProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.utils.AsyncCacheLoader;
import com.scopely.proteus.logging.Log;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class CountryInfoService {

  private static final String PROVIDER = "PROVIDER";
  private final CountryInfoFile countryInfoFile;
  private final LoadingCache<CountryDetailsCacheKey, CountryDetails> countryDetailsCache;

  @Inject
  public CountryInfoService(CountryInfoFile countryInfoFile) {
    this.countryInfoFile = countryInfoFile;
    var cacheLoader = new AsyncCacheLoader<>(this::retrieveCountryDetails, "country-details");
    this.countryDetailsCache = CacheBuilder.newBuilder().build(cacheLoader);
  }

  public CountryDetails getCountryDetails(
      String countryCode, PaymentProviderIdentifier payProvider) {
    var provider = CountryProviderIdentifier.fromPaymentProvider(payProvider);
    try {
      return countryDetailsCache.get(new CountryDetailsCacheKey(countryCode, provider));
    } catch (Exception e) {
      if (e.getCause() instanceof InvalidCountryException) {
        throw (InvalidCountryException) e.getCause();
      }
      Log.withMetadata(of(PROVIDER, provider.toString()))
          .error(e, "There was an error getting the provider or the default Geolocation info");
      throw new FileResourceException(
          "There was an error getting the provider or the default Geolocation info", e.getCause());
    }
  }

  public CountryDetails getDefaultCountryDetails(String countryCode) {
    try {
      return countryDetailsCache.get(
          new CountryDetailsCacheKey(countryCode, CountryProviderIdentifier.DEFAULT));
    } catch (Exception e) {
      if (e.getCause() instanceof InvalidCountryException) {
        throw (InvalidCountryException) e.getCause();
      }
      Log.error(e, "There was an error getting the provider or the default Geolocation info");
      throw new FileResourceException(
          "There was an error getting the provider or the default Geolocation info", e.getCause());
    }
  }

  private CountryDetails retrieveCountryDetails(CountryDetailsCacheKey cacheKey) {
    try {
      var countryDetails = countryInfoFile.getCountryInfo(cacheKey.provider);
      var defaultCountryMap = conversionCountryMap(countryInfoFile.getCountryDefaultInfo());
      return generateCountryDetails(
          cacheKey.countryCode, cacheKey.provider, countryDetails, defaultCountryMap);
    } catch (InvalidCountryException e) {
      Log.debug(e, "Invalid CountryDetailsCacheKey: " + cacheKey.countryCode);
      throw e;
    } catch (Exception e) {
      Log.withMetadata(of(PROVIDER, cacheKey.provider.name()))
          .error(e, "There was an error getting country configuration. The default will be used.");
      throw new RuntimeException("Unable to load default country info", e);
    }
  }

  private Map<String, CountryDetails> conversionCountryMap(List<CountryInfo> countryInfoList) {
    return countryInfoList.stream()
        .collect(Collectors.toMap(CountryInfo::getKey, CountryInfo::getValue));
  }

  private CountryDetails generateCountryDetails(
      String countryCode,
      CountryProviderIdentifier provider,
      List<CountryInfo> providerDiffList,
      Map<String, CountryDetails> defaultCountryMap) {

    CountryDetails resultDetails = defaultCountryMap.get(countryCode);

    if (provider != CountryProviderIdentifier.DEFAULT) {
      Map<String, CountryDetails> providerDiffMap = conversionCountryMap(providerDiffList);

      if (providerDiffMap.containsKey(countryCode)) {
        resultDetails =
            new CountryDetails.Builder()
                .setCurrency(
                    Optional.ofNullable(providerDiffMap.get(countryCode).getCurrency())
                        .orElse(defaultCountryMap.get(countryCode).getCurrency()))
                .setRegion(
                    Optional.ofNullable(providerDiffMap.get(countryCode).getRegion())
                        .orElse(defaultCountryMap.get(countryCode).getRegion()))
                .setUnsupported(providerDiffMap.get(countryCode).isUnsupported())
                .build();
      }
    }
    if (resultDetails == null) {
      throw new InvalidCountryException("Country code `" + countryCode + "` is not supported");
    }
    return resultDetails;
  }

  /** //TODO: upgrade the CacheLoader library to use records instead of classes */
  private static class CountryDetailsCacheKey {
    final String countryCode;
    final CountryProviderIdentifier provider;

    CountryDetailsCacheKey(String countryCode, CountryProviderIdentifier provider) {
      this.countryCode = countryCode;
      this.provider = provider;
    }

    @Override
    public boolean equals(Object o) {
      if (this == o) return true;
      if (o == null || getClass() != o.getClass()) return false;

      CountryDetailsCacheKey that = (CountryDetailsCacheKey) o;
      return Objects.equals(countryCode, that.countryCode) && provider.equals(that.provider);
    }

    @Override
    public int hashCode() {
      int result = Objects.hashCode(countryCode);
      result = 31 * result + Objects.hashCode(provider);
      return result;
    }
  }
}
