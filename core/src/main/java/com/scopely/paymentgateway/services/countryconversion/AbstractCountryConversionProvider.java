package com.scopely.paymentgateway.services.countryconversion;

import com.scopely.paymentgateway.model.countryconversion.CountryConversion;
import java.util.Optional;

public abstract class AbstractCountryConversionProvider {
  protected AbstractCountryConversionProvider nextProvider;

  public void setNextProvider(AbstractCountryConversionProvider nextProvider) {
    this.nextProvider = nextProvider;
  }

  public Optional<CountryConversion> getCountryConversion(String countryId) {
    Optional<CountryConversion> result = this.provide(countryId);
    if (result.isPresent()) {
      return result;
    } else {
      return Optional.ofNullable(nextProvider).flatMap(p -> p.getCountryConversion(countryId));
    }
  }

  protected abstract Optional<CountryConversion> provide(String countryId);
}
