package com.scopely.paymentgateway.services.purchasetoken;

import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.exceptions.PurchaseTokenNotFoundException;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.purchasetoken.PaymentActiveStatus;
import com.scopely.paymentgateway.model.purchasetoken.PaymentInfoToken;
import com.scopely.paymentgateway.repositories.PurchaseTokenPaymentInfoRepository;
import java.security.SecureRandom;
import java.time.Clock;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class PurchaseTokenPaymentInfoService {

  private final PurchaseTokenPaymentInfoRepository purchaseTokenRepository;
  private final PaymentGatewayConfig config;
  private static final SecureRandom secureRandom = new SecureRandom();
  private final Clock clock;

  public static final String PAYMENT_ID = "PAYMENT_ID";

  @Inject
  public PurchaseTokenPaymentInfoService(
      PurchaseTokenPaymentInfoRepository purchaseTokenRepository,
      PaymentGatewayConfig config,
      Clock clock) {
    this.purchaseTokenRepository = purchaseTokenRepository;
    this.config = config;
    this.clock = clock;
  }

  public String createPurchaseTokenItem(Payment payment) {
    String token = generatePurchaseToken(config.purchaseTokenLength());
    PaymentInfoToken paymentInfoToken =
        new PaymentInfoToken.Builder()
            .setPurchaseToken(token)
            .setPaymentId(payment.getPaymentId())
            .setLastUpdateAt(
                clock.instant().plus(config.cancelationTimeSeconds(), ChronoUnit.SECONDS))
            .setExpireAt(
                clock
                    .instant()
                    .plus(config.expirationTimeMinutes(), ChronoUnit.MINUTES)
                    .getEpochSecond())
            .build();

    purchaseTokenRepository.savePaymentInfo(paymentInfoToken);

    return token;
  }

  public PaymentActiveStatus isPaymentInfoActive(String purchaseToken) {
    Optional<PaymentInfoToken> paymentDataItem =
        purchaseTokenRepository.getPaymentInfo(purchaseToken);
    if (paymentDataItem.isEmpty()) {
      return PaymentActiveStatus.NOT_FOUND;
    }
    if (isPaymentCanceled(paymentDataItem.get())) {
      return PaymentActiveStatus.CANCELED;
    }
    return PaymentActiveStatus.ACTIVE;
  }

  public PaymentInfoToken getPaymentInfoItemAndSetUsed(String purchaseToken)
      throws PurchaseTokenNotFoundException {
    PaymentInfoToken paymentInfoToken =
        purchaseTokenRepository
            .getPaymentInfo(purchaseToken)
            .orElseThrow(() -> new PurchaseTokenNotFoundException("Purchase token not found"));
    if (paymentInfoToken.isUsed()) {
      throw new PurchaseTokenNotFoundException("Purchase token is already used");
    }
    PaymentInfoToken newPaymentInfoToken =
        PaymentInfoToken.Builder.from(paymentInfoToken).setUsed(true).build();
    purchaseTokenRepository.savePaymentInfo(newPaymentInfoToken);

    return newPaymentInfoToken;
  }

  public void keepAlivePurchaseToken(String purchaseToken) throws PurchaseTokenNotFoundException {
    PaymentInfoToken paymentInfoToken =
        purchaseTokenRepository
            .getPaymentInfo(purchaseToken)
            .orElseThrow(() -> new PurchaseTokenNotFoundException("Purchase token not found"));

    PaymentInfoToken newPaymentInfoToken =
        PaymentInfoToken.Builder.from(paymentInfoToken)
            .setLastUpdateAt(
                clock.instant().plus(config.cancelationTimeSeconds(), ChronoUnit.SECONDS))
            .build();
    purchaseTokenRepository.savePaymentInfo(newPaymentInfoToken);
  }

  private static String generatePurchaseToken(int length) {
    byte[] randomBytes = new byte[length];
    secureRandom.nextBytes(randomBytes);
    return Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
  }

  private boolean isPaymentCanceled(PaymentInfoToken payment) {
    return payment.getLastUpdateAt().isBefore(clock.instant());
  }
}
