package com.scopely.paymentgateway.services.countryconversion;

import com.scopely.paymentgateway.model.countryconversion.CountryConversion;
import com.scopely.paymentgateway.model.countryconversion.DynamicPrice;
import java.math.BigDecimal;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class CountryConversionService {

  private final CountryConversionFromCache countryConversionFromCache;
  private final CountryConversionProviderFromDB countryConversionProviderFromDB;
  private final CountryConversionProviderFromExternalService
      countryConversionProviderFromExternalService;

  @Inject
  public CountryConversionService(
      CountryConversionFromCache countryConversionFromCache,
      CountryConversionProviderFromDB countryConversionProviderFromDB,
      CountryConversionProviderFromExternalService countryConversionProviderFromExternalService) {
    this.countryConversionFromCache = countryConversionFromCache;
    this.countryConversionProviderFromDB = countryConversionProviderFromDB;
    this.countryConversionProviderFromExternalService =
        countryConversionProviderFromExternalService;
  }

  public Optional<DynamicPrice> getDynamicPrice(
      String countryIdTo, String countryIdFrom, BigDecimal basePrice) {
    Optional<CountryConversion> countryConversion = getCountryConversion(countryIdTo);
    return countryConversion.map(
        cc ->
            new DynamicPrice.Builder()
                .setBasePrice(basePrice)
                .setBaseCountry(countryIdFrom)
                .setCountryConversion(cc)
                .setConvertedPrice(calculateFinalPrice(basePrice, cc.getConversionFactor()))
                .build());
  }

  private BigDecimal calculateFinalPrice(BigDecimal basePrice, BigDecimal conversionFactor) {
    return basePrice.multiply(conversionFactor);
  }

  private Optional<CountryConversion> getCountryConversion(String countryId) {
    // FIXME: this should be done once, not always and this is not the place to do this
    countryConversionFromCache.setNextProvider(countryConversionProviderFromDB);
    countryConversionProviderFromDB.setNextProvider(countryConversionProviderFromExternalService);
    return countryConversionFromCache.getCountryConversion(countryId);
  }
}
