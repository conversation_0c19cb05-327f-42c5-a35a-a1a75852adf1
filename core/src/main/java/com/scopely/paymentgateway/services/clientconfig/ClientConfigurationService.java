package com.scopely.paymentgateway.services.clientconfig;

import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;

public interface ClientConfigurationService {
  String NO_API_KEY = "";

  PlaygamiPaymentsClientConfig getConfiguration(String apiKey, boolean sandbox);

  PlaygamiPaymentsClientConfig getConfiguration(String apiKey);

  <T extends ClientConfiguration> T getConfiguration(
      ConfigurationProviderIdentifier configProviderIdentifier, String apiKey, boolean sandbox);

  <T extends ClientConfiguration> T getConfiguration(
      ConfigurationProviderIdentifier configProviderIdentifier, String apiKey);

  void refreshConfiguration(
      ConfigurationProviderIdentifier configProviderIdentifier, String key, boolean sandbox);
}
