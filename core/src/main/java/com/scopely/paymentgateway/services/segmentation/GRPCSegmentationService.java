package com.scopely.paymentgateway.services.segmentation;

import static com.scopely.paymentgateway.constants.StatsConstants.TAG_KEY_API_KEY;

import com.scopely.arche.client.grpc.SegmentationClient;
import com.scopely.paymentgateway.exceptions.segmentation.SegmentationDisabledException;
import com.scopely.paymentgateway.exceptions.segmentation.UnableToRetrieveSegments;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.proteus.monitoring.Counter;
import com.scopely.proteus.monitoring.MetricsRegistry;
import com.scopely.proteus.util.StringUtils;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class GRPCSegmentationService implements SegmentationService {
  public static final String USER_ID = "USER_ID";
  private static final String REQUEST_SUCCESS_METRIC = "segmentation.request.success";
  private static final String REQUEST_ERROR_METRIC = "segmentation.request.error";
  private final SegmentationClient segmentsService;
  private final UserPropertiesHelper userPropertiesHelper;
  private final ClientConfigurationService clientConfigurationService;
  private final Counter successCounter;
  private final Counter errorCounter;

  @Inject
  public GRPCSegmentationService(
      SegmentationClient segmentsService,
      UserPropertiesHelper userPropertiesHelper,
      ClientConfigurationService clientConfigurationService,
      MetricsRegistry metricsRegistry) {
    this.segmentsService = segmentsService;
    this.userPropertiesHelper = userPropertiesHelper;
    this.clientConfigurationService = clientConfigurationService;
    this.successCounter = metricsRegistry.counter(REQUEST_SUCCESS_METRIC);
    this.errorCounter = metricsRegistry.counter(REQUEST_ERROR_METRIC);
  }

  @Override
  public Boolean isUserInSegment(String appId, String userId, String segmentId)
      throws UnableToRetrieveSegments, SegmentationDisabledException {
    if (StringUtils.isEmptyWhenTrimmed(segmentId)) {
      return false;
    }
    var userProps = userPropertiesHelper.getUserProperties(appId);
    var userSegments = requestUserSegments(appId, userId, Set.of(segmentId), userProps);
    return !userSegments.isEmpty();
  }

  private List<String> requestUserSegments(
      String apiKey, String userId, Set<String> segments, Map<String, ?> userProperties)
      throws UnableToRetrieveSegments, SegmentationDisabledException {
    if (!clientConfigurationService.getConfiguration(apiKey, false).isSegmentationEnabled()) {
      throw new SegmentationDisabledException("Segmentation disabled for apikey: " + apiKey);
    }
    try {
      var result =
          this.segmentsService
              .getAllSegmentsUserIn(apiKey, segments, userId, userProperties, false)
              .get()
              .getSegmentIdList()
              .stream()
              .toList();
      successCounter.increment(TAG_KEY_API_KEY.of(apiKey));
      return result;
    } catch (Exception exception) {
      errorCounter.increment(TAG_KEY_API_KEY.of(apiKey));
      new PaymentGatewayLogBuilder()
          .addUserData(apiKey, userId)
          .build()
          .error(exception, "Unable to retrieve user's segments");
      throw new UnableToRetrieveSegments(exception);
    }
  }
}
