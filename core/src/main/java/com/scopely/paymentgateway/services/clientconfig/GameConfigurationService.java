package com.scopely.paymentgateway.services.clientconfig;

import com.scopely.paymentgateway.config.ParameterPath;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.gameconfig.ParametersConfig;
import java.util.*;
import java.util.stream.Collectors;
import javax.inject.Inject;

public class GameConfigurationService implements GameClientConfigurationService {

  private final ParameterStoreService parameterStoreService;
  private final ClientConfigurationCacheService clientConfigurationCacheService;

  @Inject
  public GameConfigurationService(
      ParameterStoreService parameterStoreService,
      ClientConfigurationCacheService clientConfigurationCacheService) {
    this.parameterStoreService = parameterStoreService;
    this.clientConfigurationCacheService = clientConfigurationCacheService;
  }

  @Override
  public Map<String, ClientConfiguration> getConfiguration(String apiKey, boolean sandbox) {
    return Arrays.stream(ConfigurationProviderIdentifier.values())
        .collect(
            Collectors.toMap(
                ConfigurationProviderIdentifier::toString,
                provider ->
                    clientConfigurationCacheService.getConfiguration(provider, apiKey, sandbox)));
  }

  @Override
  public Long addConfiguration(String apikey, ParametersConfig parameter) {
    return this.parameterStoreService.addParameterConfig(
        parameter.provider(), apikey, parameter.key(), parameter.value(), parameter.secured());
  }

  @Override
  public Boolean deleteConfiguration(
      String apiKey, ConfigurationProviderIdentifier providerIdentifier, String name) {
    return this.parameterStoreService.deleteParameterConfig(providerIdentifier, apiKey, name);
  }

  @Override
  public Long enableSandbox(String apikey) {
    return this.parameterStoreService.addParameterConfig(
        ConfigurationProviderIdentifier.PLAYGAMI_PAYMENTS,
        apikey,
        ParameterPath.SANDBOX.getDescription(),
        "true");
  }

  @Override
  public Boolean disableSandbox(String apikey) {
    return this.parameterStoreService.deleteParameterConfig(
        ConfigurationProviderIdentifier.PLAYGAMI_PAYMENTS,
        apikey,
        ParameterPath.SANDBOX.getDescription());
  }
}
