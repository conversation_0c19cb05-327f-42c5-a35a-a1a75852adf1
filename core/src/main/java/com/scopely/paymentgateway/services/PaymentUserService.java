package com.scopely.paymentgateway.services;

import static com.scopely.paymentgateway.constants.LocationConstants.DEFAULT_COUNTRY;

import com.scopely.paymentgateway.model.payment.PaymentStatus;
import com.scopely.paymentgateway.repositories.PaymentRepository;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByUser;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class PaymentUserService {

  private final PaymentRepository paymentRepository;

  @Inject
  public PaymentUserService(PaymentRepository paymentRepository) {
    this.paymentRepository = paymentRepository;
  }

  /**
   * TODO: maybe we should aim to retrieve only the first payments and not the whole list
   *
   * @param apiKey
   * @param userId
   * @return
   */
  public String getLastPaymentLocation(String apiKey, String userId) {
    var lastPayments = paymentRepository.getPaymentsByUser(apiKey, userId);
    lastPayments.sort((p1, p2) -> p2.getCreatedAt().compareTo(p1.getCreatedAt()));
    return lastPayments.stream()
        .filter(payment -> !payment.getStatus().equals(PaymentStatus.INITIATED))
        .map(PaymentIndexByUser::getCountry)
        .findFirst()
        .orElse(DEFAULT_COUNTRY);
  }
}
