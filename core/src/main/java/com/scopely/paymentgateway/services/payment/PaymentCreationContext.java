package com.scopely.paymentgateway.services.payment;

import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.logging.PaymentProcess;
import com.scopely.paymentgateway.model.checkout.ProviderItem;
import com.scopely.paymentgateway.model.createpayment.CreatePaymentRequestData;
import com.scopely.paymentgateway.model.payment.ItemData;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentLocation;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PlatformType;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PriceProvider;
import com.scopely.paymentgateway.model.payment.PricingMode;
import com.scopely.paymentgateway.model.payment.ProductType;
import com.scopely.paymentgateway.model.user.User;
import com.scopely.proteus.logging.Log;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;
import org.javamoney.moneta.Money;

public class PaymentCreationContext {
  private static final PriceProvider DEFAULT_PRICE_PROVIDER = PriceProvider.PLAYGAMI;
  private final String paymentId;
  private final CreatePaymentRequestData requestData;
  private final User payer;
  private final String uiLocale;
  private final String titanContextProperties;
  private PaymentLocation paymentLocation;
  private Money itemBasePrice;
  private Money itemLocalizedPrice;
  private PriceData totalPriceData;
  private final Log.MetadataLog logger;

  public PaymentCreationContext(
      String paymentId,
      CreatePaymentRequestData requestData,
      User payer,
      PaymentLocation paymentLocation,
      String uiLocale,
      String titanContextProperties) {
    this.paymentId = paymentId;
    this.requestData = requestData;
    this.payer = payer;
    this.paymentLocation = paymentLocation;
    this.uiLocale = uiLocale;
    this.itemBasePrice = requestData.getItem().getBasePrice();
    this.itemLocalizedPrice =
        Optional.ofNullable(requestData.getItem().getLocalizedPrice()).orElse(this.itemBasePrice);
    this.totalPriceData = calculateTotalPriceData();
    this.titanContextProperties = titanContextProperties;
    this.logger =
        new PaymentGatewayLogBuilder()
            .addProcess(PaymentProcess.CHECKOUT)
            .addFullPaymentData(requestData.getApiKey(), paymentId, payer.getUserId())
            .build();
  }

  public String getPaymentId() {
    return paymentId;
  }

  public String getApiKey() {
    return requestData.getApiKey();
  }

  public String getUserId() {
    return payer.getUserId();
  }

  public String getRequestLocale() {
    return requestData.getProperties().getLocale();
  }

  public String getUiLocale() {
    return uiLocale;
  }

  public Optional<PaymentProviderIdentifier> getAssignedProvider() {
    return Optional.ofNullable(payer.getProvider());
  }

  public PlatformType getPlatform() {
    return requestData.getPlatform();
  }

  public String getExternalId() {
    return requestData.getExternalId();
  }

  public boolean isSandbox() {
    return requestData.getProperties().isSandbox();
  }

  public PaymentLocation getPaymentLocation() {
    return paymentLocation;
  }

  public void setPaymentLocation(PaymentLocation paymentLocation) {
    this.paymentLocation = paymentLocation;
  }

  public com.scopely.paymentgateway.model.payment.User getRequestUserInfo() {
    return requestData.getProperties().getUser();
  }

  public boolean isVipUser() {
    return requestData.getProperties().getUser().isVip();
  }

  public PricingMode getPricingMode() {
    return requestData.getProperties().getPricingMode();
  }

  public int getQuantity() {
    return requestData.getItem().getQuantity();
  }

  public void setItemPrice(Money itemBasePrice, Money itemLocalizedPrice) {
    this.itemBasePrice = itemBasePrice;
    this.itemLocalizedPrice = itemLocalizedPrice;
    this.totalPriceData = calculateTotalPriceData();
  }

  public void setItemPrice(PriceData itemPriceData) {
    this.itemBasePrice = itemPriceData.getOriginalBasePrice();
    this.itemLocalizedPrice = itemPriceData.getOriginalLocalPrice();
    this.totalPriceData = calculateTotalPriceData(PriceData.Builder.from(itemPriceData));
  }

  public Money getTotalBasePrice() {
    Objects.requireNonNull(itemBasePrice, "Missing base price");
    return itemBasePrice.multiply(getQuantity());
  }

  private PriceData calculateTotalPriceData() {
    var builder = new PriceData.Builder().setTaxIncluded(getPaymentLocation().isTaxIncluded());
    return calculateTotalPriceData(builder);
  }

  private PriceData calculateTotalPriceData(PriceData.Builder builder) {
    if (itemBasePrice == null) {
      return null;
    }
    return builder
        .setPricingMode(getPricingMode())
        .setBasePrice(itemBasePrice.multiply(getQuantity()))
        .setLocalPrice(itemLocalizedPrice.multiply(getQuantity()))
        .build();
  }

  public boolean isMultiCurrency() {
    return getPricingMode() == PricingMode.EXPLICIT
        && Objects.isNull(requestData.getItem().getLocalizedPrice());
  }

  public PriceData getTotalPriceData() {
    return Objects.requireNonNull(totalPriceData);
  }

  public Optional<String> getProviderSku() {
    return requestData.getItem().getProviders().stream()
        .filter(provider -> provider.getProvider() == DEFAULT_PRICE_PROVIDER)
        .findFirst()
        .map(ProviderItem::getSku);
  }

  public ItemData getItemData() {
    var item = requestData.getItem();
    return new ItemData.Builder()
        .setInternalSku(item.getSku())
        .setName(item.getName())
        .setProductType(item.getProductType() != null ? item.getProductType() : ProductType.IAP)
        .setProviderItem(
            new ProviderItem.Builder()
                .setProvider(DEFAULT_PRICE_PROVIDER)
                .setSku(getProviderSku().orElse(null))
                .build())
        .setQuantity(getQuantity())
        .setUnitBasePrice(Objects.requireNonNull(itemBasePrice, "Missing base price"))
        .setUnitLocalPrice(Objects.requireNonNull(itemLocalizedPrice, "Missing localized price"))
        .build();
  }

  public Log.MetadataLog getLogger() {
    return logger;
  }

  public Payment.Builder getBaseNewPayment(Instant now) {
    return new Payment.Builder()
        .setPaymentId(getPaymentId())
        .setClaimed(false)
        .setCreatedAt(now)
        .setUpdatedAt(now)
        .setApiKey(getApiKey())
        .setUserId(getUserId())
        .setDeviceToken(requestData.getDeviceToken())
        .setTrackingId(requestData.getTrackingId())
        .setVip(isVipUser())
        .setExternalId(getExternalId())
        .setSandbox(isSandbox())
        .setPlatform(getPlatform());
  }

  public String getTitanContextProperties() {
    return titanContextProperties;
  }
}
