package com.scopely.paymentgateway.services.payment;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_CONTEXT_PROPERTIES_EXCEED_LIMIT;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_CONTEXT_PROPERTIES_NOT_SENT;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_CONTEXT_PROPERTIES_SENT;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_LOAD_EMAIL_INVALID;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;

import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.model.checkout.Item;
import com.scopely.paymentgateway.model.createpayment.CreatePaymentRequestData;
import com.scopely.paymentgateway.model.payment.User;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.util.StringUtils;
import com.scopely.proteus.util.validator.EmailValidator;
import com.timgroup.statsd.StatsDClient;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class CreatePaymentValidator {
  private final PaymentGatewayConfig paymentGatewayConfig;
  private final StatsDClient statsDClient;

  @Inject
  public CreatePaymentValidator(
      PaymentGatewayConfig paymentGatewayConfig, StatsDClient statsDClient) {
    this.paymentGatewayConfig = paymentGatewayConfig;
    this.statsDClient = statsDClient;
  }

  public void validateRequest(CreatePaymentRequestData createPaymentRequest) {
    validateUser(createPaymentRequest.getProperties().getUser(), createPaymentRequest.getApiKey());
    validateItem(createPaymentRequest.getItem());
  }

  public String validateContextProperties(String contextProperties, String apiKey) {
    if (StringUtils.isEmpty(contextProperties)) {
      statsDClient.incrementCounter(
          DD_PAYMENT_CONTEXT_PROPERTIES_NOT_SENT,
          MetricsUtils.buildTags(TAG_API_KEY, apiKey));
    } else if (contextProperties.length() > paymentGatewayConfig.contextPropertiesMaxLength()) {
      statsDClient.incrementCounter(
          DD_PAYMENT_CONTEXT_PROPERTIES_EXCEED_LIMIT,
          MetricsUtils.buildTags(TAG_API_KEY, apiKey));
      return null;
    } else {
      statsDClient.incrementCounter(
          DD_PAYMENT_CONTEXT_PROPERTIES_SENT,
          MetricsUtils.buildTags(TAG_API_KEY, apiKey));
    }
    return contextProperties;
  }

  private void validateUser(User user, String apiKey) {
    if (!EmailValidator.getInstance().isValid(user.getEmail())) {
      statsDClient.increment(
          DD_PAYMENT_LOAD_EMAIL_INVALID, MetricsUtils.buildTags(TAG_API_KEY, apiKey));
    }
  }

  private void validateItem(Item item) {
    if (item.getQuantity() < 1) {
      throw new IllegalArgumentException("Invalid item quantity: " + item.getQuantity());
    }
  }
}
