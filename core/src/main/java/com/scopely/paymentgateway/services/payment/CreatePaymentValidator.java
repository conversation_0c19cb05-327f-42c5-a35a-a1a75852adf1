package com.scopely.paymentgateway.services.payment;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_LOAD_EMAIL_INVALID;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;

import com.scopely.paymentgateway.model.checkout.Item;
import com.scopely.paymentgateway.model.createpayment.CreatePaymentRequestData;
import com.scopely.paymentgateway.model.payment.User;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.util.validator.EmailValidator;
import com.timgroup.statsd.StatsDClient;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class CreatePaymentValidator {
  private final StatsDClient statsDClient;

  @Inject
  public CreatePaymentValidator(StatsDClient statsDClient) {
    this.statsDClient = statsDClient;
  }

  public void validateRequest(CreatePaymentRequestData createPaymentRequest) {
    validateUser(createPaymentRequest.getProperties().getUser(), createPaymentRequest.getApiKey());
    validateItem(createPaymentRequest.getItem());
  }

  private void validateUser(User user, String apiKey) {
    if (!EmailValidator.getInstance().isValid(user.getEmail())) {
      statsDClient.increment(
          DD_PAYMENT_LOAD_EMAIL_INVALID, MetricsUtils.buildTags(TAG_API_KEY, apiKey));
    }
  }

  private void validateItem(Item item) {
    if (item.getQuantity() < 1) {
      throw new IllegalArgumentException("Invalid item quantity: " + item.getQuantity());
    }
  }
}
