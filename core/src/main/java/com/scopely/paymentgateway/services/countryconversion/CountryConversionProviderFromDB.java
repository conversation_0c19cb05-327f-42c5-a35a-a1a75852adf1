package com.scopely.paymentgateway.services.countryconversion;

import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.model.countryconversion.CountryConversion;
import com.scopely.paymentgateway.repositories.DynamoCountryConversionRepository;
import java.time.Instant;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class CountryConversionProviderFromDB extends AbstractCountryConversionProvider {
  private final DynamoCountryConversionRepository dynamoCountryConversionRepository;
  private final PaymentGatewayConfig paymentGatewayConfig;

  @Inject
  public CountryConversionProviderFromDB(
      DynamoCountryConversionRepository dynamoCountryConversionRepository,
      PaymentGatewayConfig paymentGatewayConfig) {
    this.dynamoCountryConversionRepository = dynamoCountryConversionRepository;
    this.paymentGatewayConfig = paymentGatewayConfig;
  }

  @Override
  protected Optional<CountryConversion> provide(String countryId) {
    Optional<CountryConversion> countryExchangeRateForCurrency =
        dynamoCountryConversionRepository.getCountryConversionForCurrency(countryId);
    if (countryExchangeRateForCurrency.isPresent()) {
      if (isExpired(countryExchangeRateForCurrency.get().getUpdatedAt())) {
        return Optional.empty();
      }
    }

    return countryExchangeRateForCurrency;
  }

  private boolean isExpired(Instant createdAt) {
    return createdAt.isBefore(
        Instant.now().minusSeconds(paymentGatewayConfig.countryConversionExpirationTimeSeconds()));
  }
}
