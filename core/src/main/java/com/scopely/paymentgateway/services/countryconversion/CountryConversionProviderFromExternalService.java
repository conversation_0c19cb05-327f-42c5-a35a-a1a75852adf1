package com.scopely.paymentgateway.services.countryconversion;

import com.scopely.paymentgateway.model.countryconversion.CountryConversion;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class CountryConversionProviderFromExternalService
    extends AbstractCountryConversionProvider {

  private final ExternalCountryConversionService externalCountryConversionService;
  private final CountryInfoService countryInfoService;

  @Inject
  public CountryConversionProviderFromExternalService(
      ExternalCountryConversionService externalCountryConversionService,
      CountryInfoService countryInfoService) {
    this.externalCountryConversionService = externalCountryConversionService;
    this.countryInfoService = countryInfoService;
  }

  @Override
  protected Optional<CountryConversion> provide(String countryId) {
    return externalCountryConversionService.retrieveCountryConversion(
        countryId, getCurrencyFromCountryInfo(countryId));
  }

  private String getCurrencyFromCountryInfo(String countryId) {
    return countryInfoService
        .getCountryDetails(countryId, PaymentProviderIdentifier.DIGITAL_RIVER)
        .getCurrency();
  }
}
