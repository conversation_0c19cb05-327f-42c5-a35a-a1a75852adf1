package com.scopely.paymentgateway.services.clientconfig;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import com.scopely.paymentgateway.config.ParameterStoreConfig;
import com.scopely.paymentgateway.exceptions.InvalidClientConfigurationException;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;
import com.scopely.paymentgateway.utils.AsyncCacheLoader;
import datadog.trace.api.Trace;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;

@SuppressWarnings({"PMD.AvoidCatchingThrowable", "unchecked"})
public class ClientConfigurationCacheService implements ClientConfigurationService {

  private final ParameterStoreService parameterStoreService;

  private final LoadingCache<KeyConfiguration, Optional<ClientConfiguration>>
      providerConfigLoadingCache;

  @Inject
  public ClientConfigurationCacheService(
      ParameterStoreConfig parameterStoreConfig, ParameterStoreService parameterStoreService) {
    this.parameterStoreService = parameterStoreService;
    this.providerConfigLoadingCache =
        CacheBuilder.newBuilder()
            .maximumSize(parameterStoreConfig.maximumSizeCache())
            .refreshAfterWrite(parameterStoreConfig.refreshCacheSeconds(), TimeUnit.SECONDS)
            .build(
                new AsyncCacheLoader<>(
                    this::getConfigurationFromParameterStore, "provider-config"));
  }

  @Override
  public PlaygamiPaymentsClientConfig getConfiguration(String apiKey) {
    return getConfiguration(ConfigurationProviderIdentifier.PLAYGAMI_PAYMENTS, apiKey, false);
  }

  @Override
  public PlaygamiPaymentsClientConfig getConfiguration(String apiKey, boolean sandbox) {
    return getConfiguration(ConfigurationProviderIdentifier.PLAYGAMI_PAYMENTS, apiKey, sandbox);
  }

  @Override
  @Trace
  public <T extends ClientConfiguration> T getConfiguration(
      ConfigurationProviderIdentifier configProviderIdentifier, String apiKey, boolean sandbox) {
    try {
      return (T)
          providerConfigLoadingCache
              .get(new KeyConfiguration(configProviderIdentifier, apiKey, sandbox))
              .orElseThrow(
                  () ->
                      new InvalidClientConfigurationException(
                          "Client configuration for the provider %s does not exist"
                              .formatted(configProviderIdentifier)));
    } catch (InvalidClientConfigurationException ex) {
      throw ex;
    } catch (Exception ex) {
      try {
        throw ex.getCause();
      } catch (InvalidClientConfigurationException e) {
        throw e;
      } catch (Throwable e) {
        throw new InvalidClientConfigurationException(
            "Error requesting configuration for " + configProviderIdentifier, ex);
      }
    }
  }

  @Override
  public <T extends ClientConfiguration> T getConfiguration(
      ConfigurationProviderIdentifier configProviderIdentifier, String apiKey) {
    return getConfiguration(configProviderIdentifier, apiKey, false);
  }

  @Override
  public void refreshConfiguration(
      ConfigurationProviderIdentifier configProviderIdentifier, String apiKey, boolean sandbox) {
    providerConfigLoadingCache.refresh(
        new KeyConfiguration(configProviderIdentifier, apiKey, sandbox));
  }

  private Optional<ClientConfiguration> getConfigurationFromParameterStore(KeyConfiguration key) {
    return parameterStoreService.getClientConfiguration(
        key.providerIdentifier(), key.apiKey(), key.sandbox());
  }
}
