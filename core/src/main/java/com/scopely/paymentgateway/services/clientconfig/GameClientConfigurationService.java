package com.scopely.paymentgateway.services.clientconfig;

import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.gameconfig.ParametersConfig;
import java.util.Map;

public interface GameClientConfigurationService {

  Map<String, ClientConfiguration> getConfiguration(String apiKey, boolean sandbox);

  Long addConfiguration(String apikey, ParametersConfig parameter);

  Long enableSandbox(String apikey);

  Boolean deleteConfiguration(
      String apiKey, ConfigurationProviderIdentifier providerIdentifier, String name);

  Boolean disableSandbox(String apikey);
}
