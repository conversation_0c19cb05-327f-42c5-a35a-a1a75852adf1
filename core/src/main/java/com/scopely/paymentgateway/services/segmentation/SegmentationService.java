package com.scopely.paymentgateway.services.segmentation;

import com.scopely.paymentgateway.exceptions.segmentation.SegmentationDisabledException;
import com.scopely.paymentgateway.exceptions.segmentation.UnableToRetrieveSegments;
import javax.inject.Singleton;

@Singleton
public interface SegmentationService {

  Boolean isUserInSegment(String apiKey, String userId, String segmentId)
      throws UnableToRetrieveSegments, SegmentationDisabledException;
}
