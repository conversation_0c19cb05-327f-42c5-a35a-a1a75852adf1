package com.scopely.paymentgateway.services.translations;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_LOCALE_REQUEST_DEFAULT;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_LOCALE_REQUEST_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_LOCALE_REQUEST_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_LOCALE_REQUEST_LANGUAGE;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;

import com.google.common.base.Strings;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.UncheckedExecutionException;
import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.config.clientconfig.EmailTranslationsFile;
import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.constants.LocationConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayRuntimeException;
import com.scopely.paymentgateway.exceptions.translation.TranslationNotFoundException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.AsyncCacheLoader;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import java.io.IOException;
import java.io.Serial;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class TranslationsService {
  private static final String EMAIL_TRANSLATION = "email";
  private final EmailTranslationsFile emailTranslationsFile;
  private final StatsDClient statsDClient;
  private final LoadingCache<String, Map<String, Map<String, String>>> emailTranslationsCache;
  private static final Map<String, String> DEFAULT_EN_FIELDS =
      Map.ofEntries(
          Map.entry("subjectSuccess", "Your receipt for %s No. %s"),
          Map.entry("subjectError", "There was a problem with your %s payment"),
          Map.entry("subjectRefund", "Your refund for %s No. %s"),
          Map.entry("invoiceLinkQuestion", "Can't see this email?"),
          Map.entry("invoiceLinkText", "Click here."),
          Map.entry("purchaseConfirmationTitle", "Purchase confirmation"),
          Map.entry("productGameSubtitle", "Product"),
          Map.entry("transactionNumberLabel", "Transaction number"),
          Map.entry("transactionDateLabel", "Transaction date"),
          Map.entry("merchantLabel", "Merchant"),
          Map.entry("userIdLabel", "User"),
          Map.entry("itemNameLabel", "Item"),
          Map.entry("subtotalLabel", "Subtotal"),
          Map.entry("salesTaxLabel", "Sales Tax"),
          Map.entry("totalLabel", "Total"),
          Map.entry("vatIncludedMessage", "VAT included"),
          Map.entry(
              "needHelpText",
              "Need help or a copy of your invoice? Contact us through the chat of our help center with your transaction number."),
          Map.entry("customerSupportLink", "Scopely Customer Support"),
          Map.entry("privacyPolicyLink", "Privacy Policy"),
          Map.entry("termsSaleLink", "Terms of Sale"),
          Map.entry("californiaPolicyLink", "California Privacy Policy"),
          Map.entry("cookiesLink", "Cookies"),
          Map.entry("cancelationRightLink", "Cancellation Right"),
          Map.entry("legalLink", "Legal Notice"),
          Map.entry("paymentErrorText", "Something went wrong with the payment"),
          Map.entry("reasonsErrorText", "Possible reasons why your purchase was declined"),
          Map.entry("reasonOneText", "• The payment amount exceeds the limit of your card."),
          Map.entry("reasonTwoText", "• Your card has insufficient funds."),
          Map.entry(
              "reasonThreeText",
              "• Your card has been blocked by your bank due to suspicious activity."),
          Map.entry("tryAgainButton", "Try purchasing again"),
          Map.entry("refundTitle", "Refund confirmation"),
          Map.entry(
              "refundInfo",
              "You will be receiving a refund via your original payment method in, usually, 1-3 business days. In rare cases, it can take up until 30 days."),
          Map.entry(
              "needHelpTextRefundError",
              "Do you need help? Contact us through the chat of our help center with your transaction number."),
          Map.entry(
              "merchantInfo",
              "is the authorized merchant of the products and services offered within this store."));

  @Inject
  public TranslationsService(
      EmailTranslationsFile emailTranslationsFile,
      PaymentGatewayConfig paymentGatewayConfig,
      StatsDClient statsDClient) {
    this.emailTranslationsFile = emailTranslationsFile;
    this.statsDClient = statsDClient;
    emailTranslationsCache =
        CacheBuilder.newBuilder()
            .refreshAfterWrite(
                paymentGatewayConfig.translationsExpirationTimeSeconds(), TimeUnit.SECONDS)
            .build(new AsyncCacheLoader<>(this::retrieveEmailTranslations, "email-translations"));
  }

  private Map<String, Map<String, String>> retrieveEmailTranslations(String ignoredKey) {
    try {
      return emailTranslationsFile.loadEmailTranslations();
    } catch (IOException e) {
      throw new InternalTranslationDataException("Couldn't retrieve translation data", e);
    }
  }

  private Map<String, Map<String, String>> getEmailData() {
    try {
      // FIXME: what should we do if we have emailTranslationsFile.loadEmailTranslations() is able
      // to load an empty file?
      return emailTranslationsCache.get(EMAIL_TRANSLATION);
    } catch (ExecutionException | UncheckedExecutionException exception) {
      Log.error(exception, "Unhandled exception when get email translations");
      return new HashMap<>(Map.of(LocationConstants.DEFAULT_LOCALE, DEFAULT_EN_FIELDS));
    }
  }

  public Map<String, String> getEmailTranslation(String locale) {
    var translationFields = new HashMap<>(getEmailData().get(LocationConstants.DEFAULT_LOCALE));

    if (Strings.isNullOrEmpty(locale) || !getEmailData().containsKey(locale)) {
      locale = LocationConstants.DEFAULT_LOCALE;
    }

    if (!locale.equals(LocationConstants.DEFAULT_LOCALE)) {
      getEmailData().get(locale).forEach(translationFields::replace);
    }
    return translationFields;
  }

  public String getValidateLocale(String locale, List<String> allowedLanguages, String apiKey) {
    var log = new PaymentGatewayLogBuilder().addApiKey(apiKey).build();

    var validLocale = LocationConstants.DEFAULT_LOCALE;
    statsDClient.increment(DD_LOCALE_REQUEST_HITS, MetricsUtils.buildTags(TAG_API_KEY, apiKey));
    try {
      if (Strings.isNullOrEmpty(locale)) {
        log.warn("Locale is empty. Default locale \"{}\" was used.", validLocale);
        locale = LocationConstants.DEFAULT_LOCALE;
      }

      if (isValidLocale(locale, allowedLanguages)) {
        validLocale = locale;
      } else {
        var localeTrimmed = locale.split("_")[0].toLowerCase(Locale.getDefault());
        if (isValidLocale(localeTrimmed, allowedLanguages)) {
          validLocale = localeTrimmed;
          statsDClient.increment(
              DD_LOCALE_REQUEST_LANGUAGE, MetricsUtils.buildTags(TAG_API_KEY, apiKey));
          log.warn(
              "Locale \"{}\" was not found but loaded successfully when trimmed to \"{}\". Valid locale: \"{}\"",
              locale,
              localeTrimmed,
              validLocale);
        } else {
          statsDClient.increment(
              DD_LOCALE_REQUEST_DEFAULT, MetricsUtils.buildTags(TAG_API_KEY, apiKey));
          log.warn(
              "Locale \"{}\" was not found. Default locale \"{}\" was used.", locale, validLocale);
        }
      }
    } catch (Exception ex) {
      statsDClient.increment(DD_LOCALE_REQUEST_ERROR, MetricsUtils.buildTags(TAG_API_KEY, apiKey));
      log.error(
          ex,
          "There was an error trying to validate the locale \"{}\". Default locale \"{}\" was used.",
          locale,
          validLocale);
    }
    return validLocale;
  }

  private boolean isValidLocale(String locale, List<String> allowedLanguages) {
    if (Strings.isNullOrEmpty(locale)) {
      return false;
    }
    return allowedLanguages.contains(locale);
  }

  public String getTranslation(String locale, String key) {
    var translations = this.getEmailTranslation(locale);
    if (translations.containsKey(key)) {
      return translations.get(key);
    } else {
      throw new TranslationNotFoundException(
          String.format(
              "The key %s is not found in the translation file for %s language.", key, locale));
    }
  }

  private static class InternalTranslationDataException extends PaymentGatewayRuntimeException {
    @Serial private static final long serialVersionUID = -1537640373434459053L;

    InternalTranslationDataException(String message, Exception cause) {
      super(message, cause);
    }

    @Override
    public ErrorConstants getErrorCode() {
      return ErrorConstants.INTERNAL_ERROR;
    }
  }
}
