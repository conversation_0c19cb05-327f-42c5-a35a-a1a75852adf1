package com.scopely.paymentgateway.services.geolocation;

import com.scopely.paymentgateway.exceptions.CountryUnsupportedException;
import com.scopely.paymentgateway.model.payment.PaymentLocation;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;

public interface GeolocationPaymentService {
  PaymentLocation getPaymentLocation(String ipAddress);

  PaymentLocation getPaymentLocation(String countryId, String ipAddress)
      throws CountryUnsupportedException;

  PaymentLocation updatePaymentLocation(
      PaymentLocation paymentLocation, PaymentProviderIdentifier provider)
      throws CountryUnsupportedException;
}
