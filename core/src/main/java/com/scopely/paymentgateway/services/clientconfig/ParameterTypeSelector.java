package com.scopely.paymentgateway.services.clientconfig;

import com.amazonaws.services.simplesystemsmanagement.model.ParameterType;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.providers.xsolla.config.XSollaClientConfig;
import com.scopely.paymentgateway.utils.validation.SecuredField;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class ParameterTypeSelector {

  @Inject
  public ParameterTypeSelector() {}

  public ParameterType execute(
      ConfigurationProviderIdentifier provider,
      String key,
      Object value,
      Optional<Boolean> forcedSecure) {
    try {
      Method method = getFieldMethod(provider, key, value);
      boolean isAnnotatedSecure = isSecureField(method);

      // Api request type is conflicting with config annotation type
      if (forcedSecure.filter(b -> !b.equals(isAnnotatedSecure)).isPresent()) {
        throw new IllegalArgumentException(
            String.format(
                "Parameter type mismatch: Field '%s' should %sbe SecureString.",
                key, isAnnotatedSecure ? "" : "not "));
      }

      // Secured field not specified in api or matches, determine type by config annotation
      return determineParameterType(value, isAnnotatedSecure);
    } catch (NoSuchMethodException e) {
      // Adding new field to parameter store that is not available in config, determine type by api
      return determineParameterType(value, forcedSecure.orElse(false));
    }
  }

  public ParameterType execute(ConfigurationProviderIdentifier provider, String key, Object value) {
    return execute(provider, key, value, Optional.empty());
  }

  private ParameterType determineParameterType(Object value, boolean isSecure) {
    if (isSecure) {
      return ParameterType.SecureString;
    }

    return switch (value) {
      case List<?> l -> ParameterType.StringList;
      case String s -> ParameterType.String;
      case Boolean b -> ParameterType.String;
      case Number n -> ParameterType.String;
      default -> throw new IllegalArgumentException("Invalid value.");
    };
  }

  private Method getFieldMethod(ConfigurationProviderIdentifier provider, String key, Object value)
      throws NoSuchMethodException {
    String getterName = toGetterName(value instanceof Boolean ? "is" : "get", key);
    return getProviderConfigClass(provider).getMethod(getterName);
  }

  private boolean isSecureField(Method method) {
    return method.isAnnotationPresent(SecuredField.class);
  }

  private String toGetterName(String prefix, String key) {
    return String.format("%s%s%s", prefix, key.substring(0, 1).toUpperCase(), key.substring(1));
  }

  private Class<?> getProviderConfigClass(ConfigurationProviderIdentifier provider) {
    return switch (provider) {
      case ConfigurationProviderIdentifier.DIGITAL_RIVER -> DigitalRiverClientConfig.class;
      case ConfigurationProviderIdentifier.XSOLLA -> XSollaClientConfig.class;
      case ConfigurationProviderIdentifier.PLAYGAMI_PAYMENTS -> PlaygamiPaymentsClientConfig.class;
    };
  }
}
