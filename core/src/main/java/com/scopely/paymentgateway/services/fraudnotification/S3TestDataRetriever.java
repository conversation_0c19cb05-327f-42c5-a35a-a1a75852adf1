package com.scopely.paymentgateway.services.fraudnotification;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionLikeType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.exceptions.FraudListException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.dto.looker.LookerDailyBlockedUsersDTO;
import com.scopely.paymentgateway.model.dto.looker.LookerWeeklyBlockedUsersDTO;
import java.util.ArrayList;
import javax.inject.Inject;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;

public class S3TestDataRetriever {
  private final S3Client amazonS3;
  private final ObjectMapper objectMapper;
  private final PaymentGatewayConfig paymentGatewayConfig;
  private static final String WEEKLY_BLOCKED_MOCK_FILE_NAME = "weeklyBlockedUsers-%s.json";
  private static final String DAILY_BLOCKED_MOCK_FILE_NAME = "dailyBlockedUsers.json";
  protected static final CollectionLikeType WEEKLY_BLOCKED_USER_LIST_TYPE =
      TypeFactory.defaultInstance()
          .constructCollectionType(ArrayList.class, LookerWeeklyBlockedUsersDTO.class);
  protected static final CollectionLikeType DAILY_BLOCKED_USER_LIST_TYPE =
      TypeFactory.defaultInstance()
          .constructCollectionType(ArrayList.class, LookerDailyBlockedUsersDTO.class);
  private static final String FOLDER_NAME = "fraudNotification";

  @Inject
  public S3TestDataRetriever(
      PaymentGatewayConfig paymentGatewayConfig, S3Client amazonS3, ObjectMapper objectMapper) {
    this.paymentGatewayConfig = paymentGatewayConfig;
    this.amazonS3 = amazonS3;
    this.objectMapper = objectMapper;
  }

  public <T> T loadWeeklyMockFraudData(String apiKey) throws FraudListException {
    return loadMockFraudDataFromS3(
        String.format(WEEKLY_BLOCKED_MOCK_FILE_NAME, apiKey), WEEKLY_BLOCKED_USER_LIST_TYPE);
  }

  public <T> T loadDailyMockFraudData() throws FraudListException {
    return loadMockFraudDataFromS3(DAILY_BLOCKED_MOCK_FILE_NAME, DAILY_BLOCKED_USER_LIST_TYPE);
  }

  private <T> T loadMockFraudDataFromS3(String fileName, CollectionLikeType collectionType)
      throws FraudListException {
    String key = String.format("%s/%s", FOLDER_NAME, fileName);
    String bucketName = paymentGatewayConfig.gatewayS3Bucket();
    GetObjectRequest getObjectRequest =
        GetObjectRequest.builder().bucket(bucketName).key(key).build();
    ResponseBytes<?> objectBytes = amazonS3.getObjectAsBytes(getObjectRequest);
    byte[] data = objectBytes.asByteArray();

    try {
      return objectMapper.readValue(data, collectionType);
    } catch (Exception e) {
      var exceptionMessage =
          String.format("Failed to retrieve mock blocked users data from S3 for key: %s", key);
      new PaymentGatewayLogBuilder().build().error(e, exceptionMessage);
      throw new FraudListException(exceptionMessage, e);
    }
  }
}
