package com.scopely.paymentgateway.services.fraudnotification;

import com.scopely.alertsender.client.exception.AlertApiException;
import com.scopely.alertsender.client.v1.AlertClientService;
import com.scopely.alertsender.contract.v1.SendAlertMessageDto;
import com.scopely.alertsender.contract.v1.SendSystemAlertMessageDto;
import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.exceptions.FraudListException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;
import com.scopely.paymentgateway.model.dto.looker.LookerDailyBlockedUsersDTO;
import com.scopely.paymentgateway.model.dto.looker.LookerWeeklyBlockedUsersDTO;
import com.scopely.paymentgateway.services.api.MetricsCatalogApi;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.utils.ApiCallsExecutor;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class FraudNotificationService {

  private final MetricsCatalogApi metricCatalogApi;
  private final AlertClientService alertClientService;
  private final ApiCallsExecutor apiCallsExecutor;
  private final PaymentGatewayConfig paymentGatewayConfig;
  private final ClientConfigurationService clientConfigurationService;
  private final S3TestDataRetriever s3TestDataRetriever;

  private static final String GAME_USER_ID = "Game User ID: ";
  private static final String VIP_LIST =
      "\n:crown: *Currently VIP users blocked in the past week:*\n";
  private static final String NON_VIP_LIST =
      "\n:no_entry_sign: *Currently non-VIP users blocked in the past week:*\n";
  private static final String BLOCKED_DATE = "*Blocked Date:* ";
  private static final String USER_ID = " — *User ID:* ";
  private static final String CUSTOMER_SUPPORT_MESSAGE_PREFIX =
      """
          :date: _Sent every Monday at 4 p.m. UTC+0_%n%n\
          :bar_chart: *Summary (Last 7 Days):*%n\
           • *Total users blocked:* %s%n\
           • *Currently VIP among them:* %s%n""";
  private static final String CUSTOMER_SUPPORT_MESSAGE_SUFFIX =
      "%n%n :link: Review or unblock in <%s|Transactions>";
  private static final String NO_USERS_BLOCKED_MESSAGE =
      ":date: _Sent every Monday at 4 p.m. UTC+0_\n:white_check_mark: No users were blocked for fraud in the past week.\n\n";
  private static final String PROVIDER_MESSAGE_PREFIX =
      "Hello! Please block the following users as they were flagged as fraud:\n";
  private static final String PRODUCT_CODE = "payments";
  private static final String EVENT_CODE_FOR_CS = "user_blocking_cs_notification";
  private static final String EVENT_CODE_FOR_PROVIDER = "user_blocking_provider_notification";
  private static final String FROM_DATE = "from_date";
  private static final String TO_DATE = "to_date";
  private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd");
  public static final String WEEKLY_BLOCKED_USERS_QUERY_IDENTIFIER = "weeklyBlockedUsers";
  public static final String DAILY_BLOCKED_USERS_QUERY_IDENTIFIER = "dailyBlockedNonVipUsers";

  @Inject
  public FraudNotificationService(
      MetricsCatalogApi metricCatalogApi,
      AlertClientService alertClientService,
      ApiCallsExecutor apiCallsExecutor,
      PaymentGatewayConfig paymentGatewayConfig,
      ClientConfigurationService clientConfigurationService,
      S3TestDataRetriever s3TestDataRetriever) {
    this.metricCatalogApi = metricCatalogApi;
    this.alertClientService = alertClientService;
    this.apiCallsExecutor = apiCallsExecutor;
    this.paymentGatewayConfig = paymentGatewayConfig;
    this.clientConfigurationService = clientConfigurationService;
    this.s3TestDataRetriever = s3TestDataRetriever;
  }

  public void sendAlertToCustomerSupport(String apiKey)
      throws AlertApiException, FraudListException {
    var results = retrieveWeeklyBlockedUsers(apiKey);

    try {
      alertClientService.send(apiKey, createRequestCustomerSupport(results, apiKey));
    } catch (AlertApiException e) {
      new PaymentGatewayLogBuilder()
          .addApiKey(apiKey)
          .build()
          .error(e, "There was an error sending fraud notification message to customer support.");
      throw e;
    }
  }

  public void sendAlertToProvider() throws AlertApiException, FraudListException {
    var blockedUsers = retrieveDailyBlockedUsers();

    var logger = new PaymentGatewayLogBuilder().build();
    if (!blockedUsers.isEmpty()) {
      try {
        alertClientService.sendSystem(createRequestProvider(blockedUsers));
      } catch (AlertApiException e) {
        logger.error(e, "There was an error sending fraud notification message to provider.");
        throw e;
      }
    } else {
      logger.info("No users were blocked for fraud in the past day. No daily notification sent.");
    }
  }

  private List<LookerWeeklyBlockedUsersDTO> retrieveWeeklyBlockedUsers(String apiKey)
      throws FraudListException {
    Map<String, String> dateRange = getLastWeekDateRange();
    List<LookerWeeklyBlockedUsersDTO> blockedUsers;

    try {
      blockedUsers =
          apiCallsExecutor.executeCall(
              metricCatalogApi.loadWeeklyBlockedUsers(
                  apiKey, dateRange.get(FROM_DATE), dateRange.get(TO_DATE)));
    } catch (RequestToProviderException e) {
      var exceptionMessage =
          String.format(
              "Failed to retrieve weekly blocked users from Metrics Catalog for query %s for api key %s",
              WEEKLY_BLOCKED_USERS_QUERY_IDENTIFIER, apiKey);
      new PaymentGatewayLogBuilder().addApiKey(apiKey).build().error(e, exceptionMessage);
      throw new FraudListException(exceptionMessage, e);
    }

    // If we are in local or preview, we will retrieve mock data
    if (paymentGatewayConfig.useMockFraudData()) {
      return s3TestDataRetriever.loadWeeklyMockFraudData(apiKey);
    }

    return blockedUsers;
  }

  private List<LookerDailyBlockedUsersDTO> retrieveDailyBlockedUsers() throws FraudListException {
    List<LookerDailyBlockedUsersDTO> blockedUsers;

    try {
      blockedUsers =
          apiCallsExecutor.executeCall(
              metricCatalogApi.loadDailyBlockedUsers(paymentGatewayConfig.metricsCatalogApiKey()));
    } catch (RequestToProviderException e) {
      var exceptionMessage =
          String.format(
              "Failed to retrieve daily blocked users from Metrics Catalog for query %s",
              DAILY_BLOCKED_USERS_QUERY_IDENTIFIER);
      new PaymentGatewayLogBuilder().build().error(e, exceptionMessage);
      throw new FraudListException(exceptionMessage, e);
    }

    // If we are in local or preview, we will retrieve mock data
    if (paymentGatewayConfig.useMockFraudData()) {
      return s3TestDataRetriever.loadDailyMockFraudData();
    }

    return blockedUsers;
  }

  private SendAlertMessageDto createRequestCustomerSupport(
      List<LookerWeeklyBlockedUsersDTO> results, String apiKey) {
    var logger = new PaymentGatewayLogBuilder().addApiKey(apiKey).build();
    var message =
        results.isEmpty()
            ? NO_USERS_BLOCKED_MESSAGE
            : formatWeeklyNotificationMessage(results, apiKey);
    logger.debug(
        "Sending fraud notification message to customer support for api key: {}. productCode: {} eventCode: {} message: {}",
        apiKey,
        PRODUCT_CODE,
        EVENT_CODE_FOR_PROVIDER,
        message);
    return new SendAlertMessageDto.Builder()
        .setProductCode(PRODUCT_CODE)
        .setEventCode(EVENT_CODE_FOR_CS)
        .setBody(message)
        .build();
  }

  private SendSystemAlertMessageDto createRequestProvider(
      List<LookerDailyBlockedUsersDTO> results) {
    var message = formatDailyNotificationMessage(results);
    PlaygamiPaymentsClientConfig clientConfig =
        clientConfigurationService.getConfiguration(
            ConfigurationProviderIdentifier.PLAYGAMI_PAYMENTS,
            ClientConfigurationService.NO_API_KEY);
    var destinationChannel = clientConfig.getFraudNotificationSystemDestination();
    var logger = new PaymentGatewayLogBuilder().build();
    logger.debug(
        "Sending fraud notification message to provider. productCode: {} eventCode: {} destinationChannel: {} message: {}",
        PRODUCT_CODE,
        EVENT_CODE_FOR_PROVIDER,
        destinationChannel,
        message);
    return new SendSystemAlertMessageDto.Builder()
        .setProductCode(PRODUCT_CODE)
        .setEventCode(EVENT_CODE_FOR_PROVIDER)
        .setBody(message)
        .setDestinationChannel(destinationChannel)
        .build();
  }

  private String formatWeeklyNotificationMessage(
      List<LookerWeeklyBlockedUsersDTO> result, String apiKey) {
    var link = String.format(paymentGatewayConfig.consoleUrlTemplate(), apiKey);
    var suffixWithLink = String.format(CUSTOMER_SUPPORT_MESSAGE_SUFFIX, link);

    var partitions =
        result.stream().collect(Collectors.partitioningBy(LookerWeeklyBlockedUsersDTO::isVip));
    var vipList = partitions.get(true);
    var nonVipList = partitions.get(false);
    int vipCount = vipList.size();

    var prefix = String.format(CUSTOMER_SUPPORT_MESSAGE_PREFIX, result.size(), vipCount);

    StringBuilder builder = new StringBuilder();
    builder.append(prefix);
    if (!vipList.isEmpty()) {
      builder.append(VIP_LIST);
      vipList.forEach(
          user ->
              builder
                  .append(BLOCKED_DATE)
                  .append(user.lastBlockTimestamp())
                  .append(USER_ID)
                  .append(user.userId())
                  .append("\n"));
    }

    if (!nonVipList.isEmpty()) {
      builder.append(NON_VIP_LIST);
      nonVipList.forEach(
          user ->
              builder
                  .append(BLOCKED_DATE)
                  .append(user.lastBlockTimestamp())
                  .append(USER_ID)
                  .append(user.userId())
                  .append("\n"));
    }
    builder.append(suffixWithLink);

    return builder.toString();
  }

  private String formatDailyNotificationMessage(List<LookerDailyBlockedUsersDTO> result) {
    StringBuilder builder = new StringBuilder();

    builder.append(PROVIDER_MESSAGE_PREFIX);
    result.stream()
        .collect(Collectors.groupingBy(LookerDailyBlockedUsersDTO::productName))
        .forEach(
            (product, dtos) -> {
              builder.append("\n*").append(product).append("*").append("\n");
              dtos.forEach(dto -> builder.append(GAME_USER_ID).append(dto.userId()).append("\n"));
            });
    builder.append("\nThank you!");
    return builder.toString();
  }

  private Map<String, String> getLastWeekDateRange() {
    LocalDate today = LocalDate.now();
    LocalDate thisMonday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
    LocalDate lastMonday = thisMonday.minusWeeks(1);

    Map<String, String> dateRange = new HashMap<>();
    dateRange.put(FROM_DATE, lastMonday.format(FORMATTER));
    dateRange.put(TO_DATE, thisMonday.format(FORMATTER));
    return dateRange;
  }
}
