package com.scopely.paymentgateway.services.clientconfig;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_CLIENT_CONFIGURATION_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PROVIDER;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SANDBOX;
import static com.scopely.paymentgateway.model.client.ClientConfigAttributes.API_KEY_ATTRIBUTE;
import static com.scopely.paymentgateway.model.client.ClientConfigAttributes.PAYMENT_PROVIDER_IDENTIFIER_ATTRIBUTE;
import static com.scopely.proteus.logging.Log.withMetadata;
import static org.inferred.freebuilder.shaded.com.google.common.collect.ImmutableMap.of;

import com.amazonaws.services.simplesystemsmanagement.model.ParameterType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.config.ParameterPath;
import com.scopely.paymentgateway.config.ParameterStoreConfig;
import com.scopely.paymentgateway.config.clientconfig.ParameterStoreClient;
import com.scopely.paymentgateway.exceptions.InvalidClientConfigurationException;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.client.config.ParameterStoreValue;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.caffeine.cache.InMemoryLoadingCache;
import com.scopely.proteus.core.validation.Validator;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class ParameterStoreService {

  public static final String PARAMETER_STORE_PATH = "PARAMETER_STORE_PATH";
  private static final String CACHE_KEY = "ALL_PARAMETERS";
  private final ParameterStoreClient parameterStoreClient;
  private final ParameterStoreConfig parameterStoreConfig;
  private final ParameterTypeSelector parameterTypeSelector;
  private final ObjectMapper objectMapper;
  private final StatsDClient statsDClient;
  private final InMemoryLoadingCache<Map<String, Object>> parameterStoreCache;

  @Inject
  public ParameterStoreService(
      StatsDClient statsDClient,
      ParameterStoreClient parameterStoreClient,
      ParameterStoreConfig parameterStoreConfig,
      ParameterTypeSelector parameterTypeSelector,
      ObjectMapper objectMapper) {

    this.statsDClient = statsDClient;
    this.parameterStoreClient = parameterStoreClient;
    this.parameterStoreConfig = parameterStoreConfig;
    this.parameterTypeSelector = parameterTypeSelector;
    this.objectMapper = objectMapper;
    // a single-entry cache that stores all the available client configs from parameter store
    this.parameterStoreCache =
        new InMemoryLoadingCache.Builder<Map<String, Object>>()
            .withTtl(parameterStoreConfig.refreshCacheSeconds(), TimeUnit.SECONDS)
            .withMaxSize(1)
            .build(this::getAllParameters);
  }

  public Map<String, Object> preloadCache() {
    return parameterStoreCache.get(CACHE_KEY);
  }

  public Set<String> getAllAvailableApiKeys() {
    return getConfigTypeKeys().stream()
        .filter(
            key ->
                !key.equals(ParameterPath.SANDBOX.getDescription())
                    && !key.equals(ParameterPath.GLOBAL.getDescription()))
        .collect(Collectors.toSet());
  }

  public Set<String> getConfigTypeKeys() {
    Map<String, Object> allParameters = parameterStoreCache.get(CACHE_KEY);
    return allParameters.keySet().stream()
        .map(key -> key.split("/", parameterStoreConfig.configPathApikeyLimit()))
        .filter(keyParts -> keyParts.length >= 2)
        .map(keyParts -> keyParts[1])
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());
  }

  public Map<String, Map<String, Object>> getAllConfigValues() {
    return getConfigTypeKeys().stream()
        .collect(Collectors.toMap(apiKey -> apiKey, this::getParamenterStoreValues));
  }

  public Long addParameterConfig(
      ConfigurationProviderIdentifier provider, String apikey, String name, Object value) {
    return addParameterConfig(provider, apikey, name, value, Optional.empty());
  }

  public Long addParameterConfig(
      ConfigurationProviderIdentifier provider,
      String apikey,
      String name,
      Object value,
      Optional<Boolean> secured) {
    ParameterType type = parameterTypeSelector.execute(provider, name, value, secured);
    String content = valueToString(value, type);

    // validate parameter belongs to its providers' client config & its content is valid
    Map<String, Object> configParams =
        getClientConfigMap(
            provider,
            apikey,
            apikey.equals(
                ParameterPath.SANDBOX
                    .getDescription())); // apikey could be "sandbox" or "global" or {game's apikey}
    configParams.put(name, value);
    ClientConfiguration clientConfiguration =
        objectMapper.convertValue(configParams, ClientConfiguration.class);
    Validator.validate(clientConfiguration);

    String path =
        parameterStoreConfig.clientConfigPath()
            + provider.getConfigPath()
            + ParameterStoreClient.PATH_SEPARATOR_CHAR
            + apikey
            + ParameterStoreClient.PATH_SEPARATOR_CHAR
            + name;
    return parameterStoreClient.putParameter(path, content, type);
  }

  public Boolean deleteParameterConfig(
      ConfigurationProviderIdentifier provider, String apiKey, String name) {

    // verify not deleting a mandatory parameter
    Map<String, Object> configParams =
        getClientConfigMap(provider, apiKey, apiKey.equals(ParameterPath.SANDBOX.getDescription()));
    configParams.remove(name);
    ClientConfiguration clientConfiguration =
        objectMapper.convertValue(configParams, ClientConfiguration.class);
    Validator.validate(clientConfiguration); // Not really needed but just in case

    String path =
        parameterStoreConfig.clientConfigPath()
            + provider.getConfigPath()
            + ParameterStoreClient.PATH_SEPARATOR_CHAR
            + apiKey
            + ParameterStoreClient.PATH_SEPARATOR_CHAR
            + name;
    return parameterStoreClient.deleteParameter(path);
  }

  public Optional<ClientConfiguration> getClientConfiguration(
      ConfigurationProviderIdentifier provider, String apiKey, Boolean sandbox) {

    String basePath = parameterStoreConfig.clientConfigPath();

    ClientConfiguration clientConfiguration;
    try {
      Map<String, Object> configParams = getClientConfigMap(provider, apiKey, sandbox);

      clientConfiguration = objectMapper.convertValue(configParams, ClientConfiguration.class);
      Validator.validate(clientConfiguration);
    } catch (Exception ex) {
      statsDClient.increment(
          DD_CLIENT_CONFIGURATION_ERROR,
          MetricsUtils.buildTags(
              List.of(TAG_API_KEY, TAG_PROVIDER, TAG_SANDBOX), List.of(apiKey, provider, sandbox)));
      withMetadata(of(PARAMETER_STORE_PATH, basePath))
          .error(
              ex,
              String.format(
                  "Unable to retrieve configuration for %s from AWS Parameter Store with path %s",
                  provider, basePath));
      throw new InvalidClientConfigurationException(
          "Invalid configuration for " + provider + ": " + ex.getMessage(), ex);
    }
    return Optional.ofNullable(clientConfiguration);
  }

  private Map<String, Object> getParamenterStoreValues(String configType) {
    return Arrays.stream(ConfigurationProviderIdentifier.values())
        .collect(
            Collectors.toMap(
                ConfigurationProviderIdentifier::toString,
                provider ->
                    getParameterStoreConfigMap(
                        provider, configType))); // get all parameters for each provider
  }

  private Map<String, ParameterStoreValue> getParameterStoreConfigMap(
      ConfigurationProviderIdentifier provider, String configType) {

    String basePath = parameterStoreConfig.clientConfigPath();
    String providerPath =
        basePath + provider.getConfigPath() + ParameterStoreClient.PATH_SEPARATOR_CHAR;
    String configPath = providerPath + configType + ParameterStoreClient.PATH_SEPARATOR_CHAR;

    return getConfigValues(configPath);
  }

  @Trace
  private Map<String, Object> getClientConfigMap(
      ConfigurationProviderIdentifier provider, String apiKey, Boolean sandbox) {

    String providerPath = provider.getConfigPath() + ParameterStoreClient.PATH_SEPARATOR_CHAR;
    String globalPath =
        providerPath
            + ParameterPath.GLOBAL.getDescription()
            + ParameterStoreClient.PATH_SEPARATOR_CHAR;
    String apiKeyPath = providerPath + apiKey + ParameterStoreClient.PATH_SEPARATOR_CHAR;
    String sandboxPath =
        providerPath
            + ParameterPath.SANDBOX.getDescription()
            + ParameterStoreClient.PATH_SEPARATOR_CHAR;

    Map<String, Object> allParameters = parameterStoreCache.get(CACHE_KEY);

    // Global
    Map<String, Object> providerConfig =
        new HashMap<>(removeUnnecessaryPrefix(allParameters, globalPath));

    // Specific over global
    if (!ClientConfigurationService.NO_API_KEY.equals(apiKey)) {
      providerConfig.putAll(removeUnnecessaryPrefix(allParameters, apiKeyPath));
    }

    // Sandbox over game specific config
    if (sandbox) {
      providerConfig.putAll(removeUnnecessaryPrefix(allParameters, sandboxPath));
    }

    providerConfig.put(PAYMENT_PROVIDER_IDENTIFIER_ATTRIBUTE, provider.toString());
    providerConfig.put(API_KEY_ATTRIBUTE, apiKey);

    return providerConfig;
  }

  private Map<String, Object> removeUnnecessaryPrefix(
      Map<String, Object> allParameters, String prefix) {

    return allParameters.entrySet().stream()
        .filter(entry -> entry.getKey().startsWith(prefix))
        .collect(
            Collectors.toMap(
                entry -> entry.getKey().substring(prefix.length()), Map.Entry::getValue));
  }

  private Map<String, ParameterStoreValue> getConfigValues(String path) {
    // recursively bulk fetching all parameters under {basePath} hierarchy);
    return parameterStoreClient.getParametersByPath(path, true);
  }

  private Map<String, Object> getAllParameters(String s) {
    String basePath = parameterStoreConfig.clientConfigPath();
    return getConfigValues(basePath).entrySet().stream()
        .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getValue()));
  }

  protected String valueToString(Object value, ParameterType type) {
    return switch (type) {
      case ParameterType.String, ParameterType.SecureString -> value.toString();
      case ParameterType.StringList -> {
        if (value instanceof List<?>) {
          yield String.join(",", objToList(value));
        }
        throw new IllegalArgumentException("Expected a List, but got: " + value.getClass());
      }
    };
  }

  private ArrayList<String> objToList(Object obj) {
    ArrayList<String> valueList = new ArrayList<>();
    ArrayList<?> array = (ArrayList<?>) obj;
    for (Object item : array) {
      valueList.add((String) item);
    }
    return valueList;
  }
}
