package com.scopely.paymentgateway.services.countryconversion;

import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PriceData;
import java.util.Map;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.javamoney.moneta.Money;

@Singleton
public class CountryConversionApplierProvider {
  private Map<PaymentProviderIdentifier, CountryConversionApplier> countryConversionApplierMap;

  @Inject
  public CountryConversionApplierProvider(
      Map<PaymentProviderIdentifier, CountryConversionApplier> countryConversionApplierMap) {
    this.countryConversionApplierMap = countryConversionApplierMap;
  }

  // FIXME: this is not a factory pattern, this is a service. If we are providing the conversion
  // applier, why are we returning the priceData instead?
  public PriceData calculateFromProvider(
      PaymentProviderIdentifier paymentProviderIdentifier, String countryId, Money originPrice) {
    // FIXME: what happens if no applier is found for this paymentProviderIdentifier?
    return this.countryConversionApplierMap
        .get(paymentProviderIdentifier)
        .calculateFromProvider(countryId, originPrice);
  }
}
