package com.scopely.paymentgateway.services.pricelocalization;

import static com.google.common.collect.ImmutableMap.of;
import static com.scopely.paymentgateway.constants.LocationConstants.DEFAULT_CURRENCY;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_COUNTRY_GET_LOCALIZATION_PRICES_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_COUNTRY_GET_LOCALIZATION_PRICES_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_COUNTRY_GET_LOCALIZATION_PRICES_INVALID_COUNTRY_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_COUNTRY;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.config.clientconfig.LocalizedPriceFile;
import com.scopely.paymentgateway.exceptions.InvalidCountryException;
import com.scopely.paymentgateway.exceptions.LocalizedPriceNotFoundException;
import com.scopely.paymentgateway.exceptions.localized_prices.LocalizationPriceFileNotFoundException;
import com.scopely.paymentgateway.exceptions.localized_prices.LocalizedPriceNotFoundInDBException;
import com.scopely.paymentgateway.exceptions.localized_prices.LocalizedPriceSaveVersionInDbException;
import com.scopely.paymentgateway.exceptions.localized_prices.LocalizedPriceUploadFileException;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;
import com.scopely.paymentgateway.model.dto.pricelocalization.CountryDataDTO;
import com.scopely.paymentgateway.model.dto.pricelocalization.CountryItemSkusDTO;
import com.scopely.paymentgateway.model.dto.pricelocalization.ItemSkuPriceDTO;
import com.scopely.paymentgateway.model.localizedprice.LocalizedPrice;
import com.scopely.paymentgateway.model.localizedprice.PriceLocalizationCsvParser;
import com.scopely.paymentgateway.model.localizedprice.PriceLocalizationData;
import com.scopely.paymentgateway.model.localizedprice.RegionalPrice;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PricingMode;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.countryconversion.CountryInfoService;
import com.scopely.paymentgateway.services.geolocation.GeolocationPaymentService;
import com.scopely.paymentgateway.services.payment.PaymentCreationContext;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.jetbrains.annotations.NotNull;

@Singleton
public class LocalizedPricesService {

  private final StatsDClient statsDClient;
  private final GeolocationPaymentService geolocationPaymentService;
  private final LocalizedPriceFile localizedPriceFile;
  private final CountryInfoService countryInfoService;
  private final ClientConfigurationService clientConfigurationService;

  @Inject
  public LocalizedPricesService(
      StatsDClient statsDClient,
      final GeolocationPaymentService geolocationPaymentService,
      LocalizedPriceFile localizedPriceFile,
      CountryInfoService countryInfoService,
      ClientConfigurationService clientConfigurationService) {
    this.statsDClient = statsDClient;
    this.geolocationPaymentService = geolocationPaymentService;
    this.localizedPriceFile = localizedPriceFile;
    this.countryInfoService = countryInfoService;
    this.clientConfigurationService = clientConfigurationService;
  }

  @Trace
  public PriceLocalizationData getAllLocalizedPrices(String apiKey)
      throws LocalizedPriceNotFoundException {
    try {
      if (localizedPriceFile.hasLocalizedPrices(apiKey)) {
        return localizedPriceFile.getLocalizedPrices(apiKey);
      } else {
        Log.withMetadata(of(TAG_API_KEY, apiKey))
            .info(
                String.format(
                    "Tried to retrieve localized prices for the apiKey %s, but none are configured.",
                    apiKey));
        throw new LocalizedPriceNotFoundException(
            String.format("The apiKey %s has no localized prices configured", apiKey));
      }
    } catch (LocalizedPriceNotFoundInDBException exception) {
      throw new LocalizedPriceNotFoundException(exception.getMessage(), exception);
    } catch (LocalizationPriceFileNotFoundException exception) {
      Log.withMetadata(of(TAG_API_KEY, apiKey))
          .error(String.format("Localized prices file for the apiKey %s is missing.", apiKey));
      throw new LocalizedPriceNotFoundException(exception.getMessage(), exception);
    }
  }

  @Trace
  public CountryItemSkusDTO getLocalizedPricesByCountry(
      String apiKey, String countryId, String userIp) throws LocalizedPriceNotFoundException {
    try {
      if (countryId == null || countryId.isEmpty()) {
        countryId = geolocationPaymentService.getPaymentLocation(userIp).getCountry();
      }
      if (countryId == null || !countryId.matches("[a-zA-Z]{2,4}")) {
        throw new InvalidCountryException("Invalid country code received: " + countryId);
      }

      countryId = countryId.toUpperCase(Locale.getDefault());

      List<LocalizedPrice> pricesListSku = localizedPriceFile.getLocalizedPrices(apiKey).prices();

      var defaultCurrency = getDefaultCurrency(apiKey);

      return getLocalizedPricesByCountry(pricesListSku, countryId, defaultCurrency);
    } catch (InvalidCountryException exception) {
      statsDClient.increment(
          DD_COUNTRY_GET_LOCALIZATION_PRICES_INVALID_COUNTRY_ERROR,
          MetricsUtils.buildTags(
              new String[] {TAG_API_KEY, TAG_COUNTRY}, new String[] {apiKey, countryId}));
      throw exception;
    } catch (LocalizedPriceNotFoundInDBException
        | LocalizationPriceFileNotFoundException exception) {
      throw new LocalizedPriceNotFoundException(exception.getMessage(), exception);
    }
  }

  @Trace
  public PriceData getLocalizedPriceDataBySku(PaymentCreationContext context)
      throws LocalizedPriceNotFoundException {
    var apiKey = context.getApiKey();
    var countryId = context.getPaymentLocation().getCountry();
    statsDClient.increment(
        DD_COUNTRY_GET_LOCALIZATION_PRICES_HITS,
        MetricsUtils.buildTags(
            new String[] {TAG_API_KEY, TAG_COUNTRY}, new String[] {apiKey, countryId}));

    var sku =
        context
            .getProviderSku()
            .filter(s -> !s.isBlank())
            .orElseThrow(
                () ->
                    new LocalizedPriceNotFoundException(
                        "No sku specified. In this pricingMode the provider sku must be specified"));

    var defaultCurrency = getDefaultCurrency(apiKey);

    List<LocalizedPrice> pricesListSku = localizedPriceFile.getLocalizedPrices(apiKey).prices();

    CountryItemSkusDTO countryItemSkusDTO =
        getLocalizedPricesByCountry(pricesListSku, countryId, defaultCurrency);

    var countryCurrency = countryItemSkusDTO.country().currency();

    Optional<BigDecimal> localPrice =
        countryItemSkusDTO.items().stream()
            .filter(skuPrice -> skuPrice.sku().equals(sku))
            .findFirst()
            .map(ItemSkuPriceDTO::price);

    Optional<BigDecimal> defaultPrice =
        pricesListSku.stream()
            .filter(localizedPrice -> localizedPrice.sku().equals(sku))
            .findFirst()
            .flatMap(price -> price.getPriceByCountryAndCurrency(countryId, defaultCurrency));

    if (localPrice.isEmpty() || defaultPrice.isEmpty()) {
      statsDClient.increment(
          DD_COUNTRY_GET_LOCALIZATION_PRICES_ERROR,
          MetricsUtils.buildTags(
              new String[] {TAG_API_KEY, TAG_COUNTRY}, new String[] {apiKey, countryId}));
      throw new LocalizedPriceNotFoundException("No price found for the provider SKU: " + sku);
    }

    return new PriceData.Builder()
        .setPricingMode(PricingMode.SKU)
        .setTaxIncluded(context.getPaymentLocation().isTaxIncluded())
        .setLocalPrice(localPrice.get(), countryCurrency)
        .setBasePrice(defaultPrice.get(), defaultCurrency)
        .build();
  }

  private String getDefaultCurrency(String apiKey) {
    // retrieve from PL the default currency
    PlaygamiPaymentsClientConfig clientConfig =
        this.clientConfigurationService.getConfiguration(apiKey);
    return Optional.ofNullable(clientConfig.getDefaultCurrency()).orElse(DEFAULT_CURRENCY);
  }

  @Trace
  private CountryItemSkusDTO getLocalizedPricesByCountry(
      List<LocalizedPrice> pricesListSku, String countryId, String defaultCurrency)
      throws LocalizedPriceNotFoundException {
    var countryDetails = countryInfoService.getDefaultCountryDetails(countryId);
    Boolean vatIncluded = countryDetails.isTaxIncluded();

    // retrieve all price localized items for the selected countryId using the regional prices
    return buildCountryItemSkuByCountryId(pricesListSku, countryId, vatIncluded)
        .or( // retrieve all price localized items for the country main currency
            () ->
                buildCountryItemSkuByCurrency(
                    pricesListSku, countryId, countryDetails.getCurrency(), vatIncluded))
        .or( // retrieve all price localized items for the default currency
            () ->
                buildCountryItemSkuByCurrency(
                    pricesListSku, countryId, defaultCurrency, vatIncluded))
        .orElseThrow(
            () ->
                new LocalizedPriceNotFoundException(
                    "Couldn't get the localized prices by the default currency. Some items don't have a price for "
                        + defaultCurrency));
  }

  @Trace
  /* Get the localized prices for the country with the given currency */
  private Optional<CountryItemSkusDTO> buildCountryItemSkuByCurrency(
      List<LocalizedPrice> localizedPricesList,
      String countryId,
      String currency,
      Boolean vatIncluded) {
    List<ItemSkuPriceDTO> localizedItemsList = new ArrayList<>();
    for (LocalizedPrice localizedPrice : localizedPricesList) {
      Optional<BigDecimal> price = localizedPrice.getPriceByCountryAndCurrency(countryId, currency);
      if (price.isEmpty()) {
        return Optional.empty();
      }
      localizedItemsList.add(new ItemSkuPriceDTO(localizedPrice.sku(), price.get()));
    }
    return Optional.of(
        new CountryItemSkusDTO(
            new CountryDataDTO(countryId, currency, vatIncluded), localizedItemsList));
  }

  /** Get the localized prices for the country using the regional prices */
  @Trace
  @NotNull
  private static Optional<CountryItemSkusDTO> buildCountryItemSkuByCountryId(
      List<LocalizedPrice> localizedPricesList, String countryId, Boolean vatIncluded) {
    List<ItemSkuPriceDTO> localizedItemsList = new ArrayList<>();
    String commonCurrency = null;
    for (LocalizedPrice localizedPrice : localizedPricesList) {
      Optional<RegionalPrice> regionalPrice = localizedPrice.getPriceByCountry(countryId);
      if (regionalPrice.isEmpty()
          || (commonCurrency != null && !commonCurrency.equals(regionalPrice.get().currency()))) {
        return Optional.empty();
      }
      localizedItemsList.add(
          new ItemSkuPriceDTO(localizedPrice.sku(), regionalPrice.get().amount()));

      commonCurrency = regionalPrice.get().currency();
    }
    return Optional.of(
        new CountryItemSkusDTO(
            new CountryDataDTO(countryId, commonCurrency, vatIncluded), localizedItemsList));
  }

  public List<LocalizedPrice> validateCsvItemsSkuPrices(InputStream priceConfigurationFile) {
    PriceLocalizationCsvParser priceLocalizationCsvParser = new PriceLocalizationCsvParser();
    return priceLocalizationCsvParser.parseCsv(priceConfigurationFile);
  }

  public List<LocalizedPrice> uploadCsvItemsSkuPrices(
      String apiKey, List<LocalizedPrice> localizedPrices, String createdBy, Instant createdAt) {
    String s3Key = localizedPriceFile.getLocalizedPriceFilePath(apiKey, createdAt);

    try {
      ObjectMapper objectMapper = new ObjectMapper();
      String jsonString = objectMapper.writeValueAsString(localizedPrices);

      localizedPriceFile.uploadLocalizedPriceFileInS3(apiKey, s3Key, jsonString);
      localizedPriceFile.saveLocalizedPriceVersionInDB(apiKey, s3Key, createdBy, createdAt);

      return localizedPrices;
    } catch (JsonProcessingException e) {
      throw new LocalizedPriceUploadFileException(e.getMessage());
    } catch (LocalizedPriceSaveVersionInDbException e) {
      localizedPriceFile.deleteLocalizedPriceFileInS3(apiKey, s3Key);
      throw new LocalizedPriceUploadFileException(e.getMessage());
    }
  }
}
