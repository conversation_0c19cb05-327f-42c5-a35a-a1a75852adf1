package com.scopely.paymentgateway.services.countryconversion;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.model.countryconversion.CountryConversion;
import com.scopely.paymentgateway.utils.AsyncCacheLoader;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class CountryConversionFromCache extends AbstractCountryConversionProvider {

  private final LoadingCache<String, Optional<CountryConversion>> countryConversionCache;

  @Inject
  public CountryConversionFromCache(PaymentGatewayConfig paymentGatewayConfig) {
    countryConversionCache =
        CacheBuilder.newBuilder()
            .refreshAfterWrite(
                paymentGatewayConfig.countryConversionExpirationTimeSeconds(), TimeUnit.SECONDS)
            .build(new AsyncCacheLoader<>(this::retrieveCountryConversions, "country-conversion"));
  }

  @Override
  protected Optional<CountryConversion> provide(String countryId) {
    try {
      return this.countryConversionCache.get(countryId);
    } catch (ExecutionException e) {
      return Optional.empty();
    }
  }

  private Optional<CountryConversion> retrieveCountryConversions(String countryId) {
    return nextProvider.getCountryConversion(countryId);
  }
}
