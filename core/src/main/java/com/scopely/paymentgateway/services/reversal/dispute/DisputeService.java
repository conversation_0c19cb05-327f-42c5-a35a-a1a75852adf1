package com.scopely.paymentgateway.services.reversal.dispute;

import com.scopely.paymentgateway.config.FraudPreventionConfig;
import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.dispute.DisputeNotFoundException;
import com.scopely.paymentgateway.exceptions.dispute.DisputeServiceException;
import com.scopely.paymentgateway.exceptions.repository.DisputeRepositoryException;
import com.scopely.paymentgateway.exceptions.segmentation.UnableToRetrieveSegments;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeEvent;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeStatus;
import com.scopely.paymentgateway.model.user.UserAutoblockOption;
import com.scopely.paymentgateway.providers.digitalriver.services.PaymentProcessAnalytics;
import com.scopely.paymentgateway.providers.digitalriver.webhook.processors.DisputeFactory;
import com.scopely.paymentgateway.repositories.DisputeRepository;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.user.UserService;
import com.scopely.proteus.sqs.publisher.SendMessageException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;
import net.jodah.failsafe.Failsafe;
import net.jodah.failsafe.FailsafeException;
import net.jodah.failsafe.RetryPolicy;
import org.javamoney.moneta.Money;

@Singleton
public class DisputeService {
  private final DisputeRepository disputeRepository;
  private final DisputeFactory disputeFactory;
  private final DisputePropagator disputePropagator;
  private final PaymentProcessAnalytics paymentProcessAnalytics;
  private final RetryPolicy retryPolicy;
  private final RetryPolicy retryPolicyForSave;
  private final UserService userService;
  private final ClientConfigurationService clientConfigurationService;
  private final FraudPreventionConfig fraudPreventionConfig;

  @Inject
  public DisputeService(
      DisputeRepository disputeRepository,
      DisputeFactory disputeFactory,
      DisputePropagator disputePropagator,
      PaymentProcessAnalytics paymentProcessAnalytics,
      RetryPolicy retryPolicy,
      UserService userService,
      ClientConfigurationService clientConfigurationService,
      FraudPreventionConfig fraudPreventionConfig) {
    this.disputeRepository = disputeRepository;
    this.disputeFactory = disputeFactory;
    this.disputePropagator = disputePropagator;
    this.paymentProcessAnalytics = paymentProcessAnalytics;
    this.retryPolicy = retryPolicy;
    // Don't do automatic retries on locking conflicts
    this.retryPolicyForSave = retryPolicy.copy().abortOn(LockingConflictException.class);
    this.userService = userService;
    this.clientConfigurationService = clientConfigurationService;
    this.fraudPreventionConfig = fraudPreventionConfig;
  }

  public Dispute save(Dispute dispute, Payment payment) throws DisputeServiceException {
    try {
      var savedDispute =
          Failsafe.with(retryPolicyForSave).get(() -> disputeRepository.save(dispute));
      sendDisputeEvent(savedDispute, payment);
      return savedDispute;
    } catch (FailsafeException e) {
      if (e.getCause() instanceof LockingConflictException) {
        return update(dispute, payment);
      } else {
        throw new DisputeServiceException("Unable to save dispute " + dispute.getDisputeId(), e);
      }
    } catch (DisputeRepositoryException e) {
      throw new DisputeServiceException("Unable to save dispute " + dispute.getDisputeId(), e);
    }
  }

  public Dispute update(Dispute dispute, Payment payment) throws DisputeServiceException {
    try {
      return Failsafe.with(retryPolicy).get(() -> retrieveAndSave(dispute, payment));
    } catch (FailsafeException | DisputeRepositoryException e) {
      throw new DisputeServiceException("Unable to update dispute " + dispute.getDisputeId(), e);
    }
  }

  private Dispute retrieveAndSave(Dispute dispute, Payment payment)
      throws DisputeNotFoundException, LockingConflictException {
    Dispute previous =
        disputeRepository
            .getByDisputeId(dispute.getDisputeId())
            .orElseThrow(
                () ->
                    new DisputeNotFoundException(
                        "Unable to update non-existent dispute " + dispute.getDisputeId()));
    if (previous.getUpdatedAt().isAfter(dispute.getUpdatedAt())) {
      return previous;
    }
    Dispute merged =
        Dispute.Builder.from(dispute)
            .setCreatedAt(previous.getCreatedAt())
            .setVersion(previous.getVersion())
            .build();
    var savedDispute = disputeRepository.save(merged);
    sendDisputeEvent(savedDispute, payment);

    return savedDispute;
  }

  public List<Dispute> getByPayment(String paymentId) throws DisputeServiceException {
    try {
      return Failsafe.with(retryPolicy).get(() -> disputeRepository.getByPayment(paymentId));
    } catch (DisputeRepositoryException e) {
      throw new DisputeServiceException("Unable to retrieve disputes for payment " + paymentId, e);
    }
  }

  public List<Dispute> getByUser(String apiKey, String userId) throws DisputeServiceException {
    try {
      return Failsafe.with(retryPolicy).get(() -> disputeRepository.getByUser(apiKey, userId));
    } catch (DisputeRepositoryException e) {
      throw new DisputeServiceException("Unable to retrieve disputes for user " + userId, e);
    }
  }

  public Optional<Dispute> getById(String disputeId) throws DisputeServiceException {
    try {
      return Failsafe.with(retryPolicy).get(() -> disputeRepository.getByDisputeId(disputeId));
    } catch (DisputeRepositoryException e) {
      throw new DisputeServiceException("Unable to retrieve dispute by id " + disputeId, e);
    }
  }

  public void checkAutoblockUser(String apiKey, String userId, String paymentId)
      throws UnableToRetrieveSegments {
    if (userService.isUserBlocked(apiKey, userId)) {
      return;
    }

    var clientConfig = clientConfigurationService.getConfiguration(apiKey);
    var searchDays = fraudPreventionConfig.autoblockSearchDays();
    Instant fromDate = Instant.now().minus(searchDays, ChronoUnit.DAYS);
    int maxDisputes =
        Optional.ofNullable(clientConfig.getAutoblockNumDisputes())
            .orElse(fraudPreventionConfig.autoblockNumDisputes());

    var numDisputedPayments = getNumberOfDisputedPayments(apiKey, userId, fromDate);
    if (numDisputedPayments >= maxDisputes) {
      boolean isUserVip = userService.isUserVip(apiKey, userId);
      var logger =
          new PaymentGatewayLogBuilder()
              .addUserData(apiKey, userId)
              .addMetadata(Map.of("vip", String.valueOf(isUserVip)))
              .build();
      logger.info(
          "Dispute threshold exceeded: {} disputed payments found in the last {} days",
          numDisputedPayments,
          searchDays);
      if (userNeedsToBeBlocked(clientConfig.getAutoblockUsersOption(), isUserVip)) {
        userService.autoblockUser(apiKey, userId, paymentId, isUserVip);
        logger.info("User has been blocked due to too many disputes");
      }
    }
  }

  private boolean userNeedsToBeBlocked(UserAutoblockOption autoblockUsersOption, boolean vipUser) {
    return switch (autoblockUsersOption) {
      case NO_BLOCK_USERS -> false;
      case BLOCK_NO_VIP_USERS -> !vipUser;
      case BLOCK_USERS -> true;
    };
  }

  protected int getNumberOfDisputedPayments(String apiKey, String userId, Instant dateFrom) {
    return (int)
        getDisputesByUserFromDate(apiKey, userId, dateFrom).stream()
            .map(Dispute::getPaymentId)
            .distinct()
            .count();
  }

  protected List<Dispute> getDisputesByUserFromDate(
      String apiKey, String userId, Instant dateFrom) {
    try {
      return Failsafe.with(retryPolicy)
          .get(() -> disputeRepository.getByUserFromDate(apiKey, userId, dateFrom));
    } catch (DisputeRepositoryException e) {
      new PaymentGatewayLogBuilder()
          .addUserData(apiKey, userId)
          .build()
          .error(e, "Unable to retrieve disputes for user.");
      return List.of();
    }
  }

  public void updateDisputesByChargeback(Money chargebackAmount, Payment payment)
      throws DisputeServiceException, SendMessageException {
    var disputeList = getByPayment(payment.getPaymentId());
    if (disputeList.isEmpty()) {
      // Create a Non-Contestable Dispute is there are not previous one
      var dispute =
          disputeFactory.buildDispute(payment, DisputeStatus.NON_CONTESTABLE, chargebackAmount);
      save(dispute, payment);
      sendDisputeToSqs(dispute);
    } else {
      // Update existing open disputes status to LOST
      for (Dispute dispute : disputeList) {
        if (dispute.getStatus().isOpenDispute()) {
          var updated = Dispute.Builder.from(dispute).setStatus(DisputeStatus.LOST).build();
          save(updated, payment);
          // No need to send the dispute to SQS queue
        }
      }
    }
  }

  private void sendDisputeToSqs(Dispute dispute) throws SendMessageException {
    disputePropagator.enqueueDisputeToSqs(
        new DisputeEvent(
            dispute.getDisputeId(),
            dispute.getApiKey(),
            dispute.getUserId(),
            dispute.getPaymentId()));
  }

  private void sendDisputeEvent(Dispute dispute, Payment payment) {
    boolean isUserVip = userService.isUserVipOrDefault(payment.getApiKey(), payment.getUserId());
    boolean isUserBlocked = userService.isUserBlocked(payment.getApiKey(), payment.getUserId());
    paymentProcessAnalytics.sendDisputeEvent(dispute, payment, isUserVip, isUserBlocked);
  }
}
