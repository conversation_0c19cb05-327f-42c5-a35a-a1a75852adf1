package com.scopely.paymentgateway.services;

import com.scopely.paymentgateway.exceptions.ExpiredPaymentException;
import com.scopely.paymentgateway.exceptions.InvalidPaymentException;
import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.RejectedPaymentException;
import com.scopely.paymentgateway.exceptions.dispute.ChargebackServiceException;
import com.scopely.paymentgateway.exceptions.dispute.DisputeServiceException;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.exceptions.repository.EntityLockingConflictException;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentAggregation;
import com.scopely.paymentgateway.model.payment.PaymentFilterRequest;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import com.scopely.paymentgateway.model.payment.TransactionDetails;
import com.scopely.paymentgateway.model.reversal.Chargeback;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import com.scopely.paymentgateway.repositories.PaymentFilterRepository;
import com.scopely.paymentgateway.repositories.PaymentRepository;
import com.scopely.paymentgateway.repositories.RefundRepository;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.payment.PaymentValidator;
import com.scopely.paymentgateway.services.reversal.ChargebackService;
import com.scopely.paymentgateway.services.reversal.dispute.DisputeService;
import java.time.Clock;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class PaymentService {

  private final PaymentRepository paymentRepository;
  private final ClientConfigurationService clientConfigurationService;
  private final PaymentValidator paymentValidator;
  private final PaymentFilterRepository paymentFilterRepo;
  private final RefundRepository refundRepository;
  private final ChargebackService chargebackService;
  private final DisputeService disputeService;
  private final Clock clock;

  @Inject
  public PaymentService(
      PaymentRepository paymentRepository,
      PaymentValidator paymentValidator,
      ClientConfigurationService clientConfigurationService,
      PaymentFilterRepository paymentFilterRepo,
      RefundRepository refundRepository,
      ChargebackService chargebackService,
      DisputeService disputeService,
      Clock clock) {
    this.clientConfigurationService = clientConfigurationService;
    this.paymentValidator = paymentValidator;
    this.paymentRepository = paymentRepository;
    this.paymentFilterRepo = paymentFilterRepo;
    this.refundRepository = refundRepository;
    this.chargebackService = chargebackService;
    this.disputeService = disputeService;
    this.clock = clock;
  }

  public Optional<Payment> optPaymentById(String paymentId) {
    Objects.requireNonNull(paymentId, "Payment id " + paymentId + " is invalid");
    return paymentRepository.getPayment(paymentId);
  }

  public Payment getPaymentById(String paymentId) throws PaymentNotFoundException {
    return optPaymentById(paymentId).orElseThrow(() -> new PaymentNotFoundException(paymentId));
  }

  public List<PaymentAggregation> getPaymentByFilter(PaymentFilterRequest filter) {
    // retrieve all payments using the filter
    return paymentFilterRepo.getAll(filter.buildCriteria()).stream()
        .map(
            payment -> {
              var response = new PaymentAggregation.Builder().setPayment(payment);
              // retrieve refunds if necessary
              if (filter.withReversals()
                  && (payment.getPaymentStatus() == PaymentStatus.COMPLETED
                      || payment.getOrderId() != null)) {
                response.setRefunds(refundRepository.getRefunds(payment.getPaymentId()));
              }
              return response.build();
            })
        .collect(Collectors.toList());
  }

  public List<TransactionDetails> getTransactionDetailsByFilter(PaymentFilterRequest filter)
      throws DisputeServiceException, ChargebackServiceException {
    var paymentAggregation = getPaymentByFilter(filter);

    if (paymentAggregation.isEmpty()) {
      return Collections.emptyList();
    }

    var apiKey = paymentAggregation.get(0).getPayment().getApiKey();
    var userId = paymentAggregation.get(0).getPayment().getUserId();
    Map<String, List<Dispute>> disputesMap = new HashMap<>();
    Map<String, List<Chargeback>> chargebacksMap = new HashMap<>();
    // Map disputes and chargebacks to their corresponding paymentId
    if (filter.withReversals()) {
      disputesMap.putAll(getDisputes(apiKey, userId));
      chargebacksMap.putAll(getChargebacks(apiKey, userId));
    }

    return paymentAggregation.stream()
        .map(
            payment -> {
              var paymentId = payment.getPayment().getPaymentId();
              var refunds = payment.getRefunds();
              var disputes = disputesMap.getOrDefault(paymentId, Collections.emptyList());
              var chargebacks = chargebacksMap.getOrDefault(paymentId, Collections.emptyList());
              return new TransactionDetails.Builder()
                  .setPayment(payment.getPayment())
                  .addAllRefunds(refunds)
                  .addAllDisputes(disputes)
                  .addAllChargebacks(chargebacks)
                  .build();
            })
        .toList();
  }

  private Map<String, List<Dispute>> getDisputes(String apiKey, String userId)
      throws DisputeServiceException {
    return disputeService.getByUser(apiKey, userId).stream()
        .collect(Collectors.groupingBy(Dispute::getPaymentId));
  }

  private Map<String, List<Chargeback>> getChargebacks(String apiKey, String userId)
      throws ChargebackServiceException {
    return chargebackService.getByUser(apiKey, userId).stream()
        .collect(Collectors.groupingBy(Chargeback::getPaymentId));
  }

  public Optional<Payment> getPaymentByOrderId(String orderId) {
    return paymentRepository.getPaymentByOrderId(orderId);
  }

  public Payment getPaymentByOrderIdUnchecked(String orderId) throws PaymentNotFoundException {
    return paymentRepository
        .getPaymentByOrderId(orderId)
        .orElseThrow(
            () -> PaymentNotFoundException.createPaymentNotFoundExceptionByOrderId(orderId));
  }

  public Payment getAndValidate(String paymentId, String validationCaller)
      throws InvalidPaymentException,
          PaymentNotFoundException,
          ExpiredPaymentException,
          RejectedPaymentException {
    var payment =
        paymentRepository
            .getPayment(paymentId)
            .orElseThrow(() -> new PaymentNotFoundException(paymentId));

    paymentValidator.validatePayment(payment, validationCaller);
    return payment;
  }

  public ClientConfiguration getClientConfiguration(Payment payment) {
    return clientConfigurationService.getConfiguration(
        ConfigurationProviderIdentifier.fromPaymentProvider(getProvider(payment)),
        payment.getApiKey(),
        payment.isSandbox());
  }

  public PlaygamiPaymentsClientConfig getPlaygamiPaymentsClientConfig(Payment payment) {
    return clientConfigurationService.getConfiguration(
        ConfigurationProviderIdentifier.PLAYGAMI_PAYMENTS, payment.getApiKey());
  }

  public PaymentProviderIdentifier getProvider(Payment payment) {
    return payment.getProviderData().getProvider();
  }

  public void getPaymentByExternalId(String apiKey, String userId, String externalId) {
    this.paymentRepository.getPaymentsByExternalId(apiKey, userId, externalId);
  }

  public Payment save(Payment payment) {
    return systemSave(Payment.Builder.from(payment).setUpdatedAt(clock.instant()).build());
  }

  public Payment systemSave(Payment payment) {
    try {
      return paymentRepository.save(payment);
    } catch (LockingConflictException exception) {
      var updatedPayment = paymentRepository.getPayment(payment.getPaymentId());
      if (updatedPayment.isPresent()) {
        return save(updatedPayment.get());
      }
      throw new EntityLockingConflictException(exception);
    }
  }
}
