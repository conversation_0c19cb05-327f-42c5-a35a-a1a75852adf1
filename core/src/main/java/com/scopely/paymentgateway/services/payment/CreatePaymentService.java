package com.scopely.paymentgateway.services.payment;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_CHECKOUT_CREATION_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_CHECKOUT_CREATION_RULE_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_CHECKOUT_ERROR_REVENUE;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_CHECKOUT_PROVIDER_FALLBACK;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_DOMAIN_CREATE_PAYMENT_SUCCESS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_CREATION_ID_ALREADY_EXISTS_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_DEVICE_TOKEN_NULL;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_TOTAL_CHECKOUTS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PROVIDER;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PROVIDER_FALLBACK;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_STATUS;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.PaymentProvidersNotAvailableException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.exceptions.payment.DuplicatedPaymentException;
import com.scopely.paymentgateway.exceptions.payment.ExternalIdConflictException;
import com.scopely.paymentgateway.exceptions.payment.UnableToSavePaymentException;
import com.scopely.paymentgateway.logging.MetadataKeys;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.logging.PaymentProcess;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;
import com.scopely.paymentgateway.model.createpayment.CreatePaymentRequestData;
import com.scopely.paymentgateway.model.dto.createpayment.CreatePaymentResponseDTO;
import com.scopely.paymentgateway.model.featuresmapping.PaymentFeature;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentProviderData;
import com.scopely.paymentgateway.model.payment.PaymentProviderDataHelper;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import com.scopely.paymentgateway.model.payment.Properties;
import com.scopely.paymentgateway.model.payment.User;
import com.scopely.paymentgateway.model.provider.ProviderCheckout;
import com.scopely.paymentgateway.providers.digitalriver.services.PaymentProcessAnalytics;
import com.scopely.paymentgateway.repositories.PaymentRepository;
import com.scopely.paymentgateway.services.abtest.ABTestService;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.countryconversion.CountryConversionApplierProvider;
import com.scopely.paymentgateway.services.customer.CustomerOperations;
import com.scopely.paymentgateway.services.features.FeaturesService;
import com.scopely.paymentgateway.services.geolocation.GeolocationPaymentService;
import com.scopely.paymentgateway.services.provider.PaymentProviderService;
import com.scopely.paymentgateway.services.purchasetoken.PurchaseTokenPaymentInfoService;
import com.scopely.paymentgateway.services.rules.PaymentProviderSelectorService;
import com.scopely.paymentgateway.services.rules.SelectPaymentProviderService;
import com.scopely.paymentgateway.services.user.UserService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.timgroup.statsd.StatsDClient;
import java.time.Clock;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class CreatePaymentService {
  private final Clock clock;
  private final PaymentRepository paymentRepository;
  private final PaymentProcessAnalytics paymentProcessAnalytics;
  private final StatsDClient statsDClient;
  private final ClientConfigurationService clientConfigurationService;
  private final SelectPaymentProviderService selectPaymentProviderService;
  private final GeolocationPaymentService geolocationPaymentService;
  private final CustomerOperations customerOperations;
  private final UserService userService;
  private final FeaturesService featuresService;
  private final ABTestService abtestService;
  private final CountryConversionApplierProvider countryConversionApplierProvider;
  private final ExternalIdValidator externalIdValidator;
  private final PaymentProviderSelectorService paymentProviderSelectorService;
  private final PurchaseTokenPaymentInfoService purchaseTokenPaymentInfoService;
  private final ObjectMapper objectMapper;

  @Inject
  public CreatePaymentService(
      Clock clock,
      PaymentRepository paymentRepository,
      PaymentProcessAnalytics paymentProcessAnalytics,
      StatsDClient statsDClient,
      ClientConfigurationService clientConfigurationService,
      SelectPaymentProviderService selectPaymentProviderService,
      GeolocationPaymentService geolocationPaymentService,
      CustomerOperations getCustomerId,
      PaymentProviderSelectorService paymentProviderSelectorService,
      CountryConversionApplierProvider countryConversionApplierProvider,
      UserService userService,
      FeaturesService featuresService,
      ABTestService abtestService,
      ExternalIdValidator externalIdValidator,
      PurchaseTokenPaymentInfoService purchaseTokenPaymentInfoService,
      ObjectMapper objectMapper) {
    this.clock = clock;
    this.paymentRepository = paymentRepository;
    this.paymentProcessAnalytics = paymentProcessAnalytics;
    this.statsDClient = statsDClient;
    this.clientConfigurationService = clientConfigurationService;
    this.selectPaymentProviderService = selectPaymentProviderService;
    this.geolocationPaymentService = geolocationPaymentService;
    this.customerOperations = getCustomerId;
    this.userService = userService;
    this.featuresService = featuresService;
    this.abtestService = abtestService;
    this.paymentProviderSelectorService = paymentProviderSelectorService;
    this.countryConversionApplierProvider = countryConversionApplierProvider;
    this.externalIdValidator = externalIdValidator;
    this.purchaseTokenPaymentInfoService = purchaseTokenPaymentInfoService;
    this.objectMapper = objectMapper;
  }

  public CreatePaymentResponseDTO createRejectedPayment(PaymentCreationContext context)
      throws DuplicatedPaymentException {

    Payment payment =
        context
            .getBaseNewPayment(clock.instant())
            .setCountry(context.getPaymentLocation().getCountry())
            .setItemData(context.getItemData())
            .setPriceData(context.getTotalPriceData())
            .setPaymentStatus(PaymentStatus.REJECTED)
            .setLocale(context.getUiLocale())
            .setContextProperties(context.getContextProperties())
            .build();

    // Save the new Payment
    var savedPayment = safeSave(payment);
    var provider = paymentProviderSelectorService.execute(context).getFirst();
    context.getLogger().info("Checkout saved for BLOCKED user using provider {}", provider);
    paymentProcessAnalytics.sendPaymentBlockedEvent(savedPayment, provider);
    return new CreatePaymentResponseDTO(savedPayment, provider);
  }

  // Should return what is going to go to the websdk token
  public CreatePaymentResponseDTO createPayment(PaymentCreationContext context)
      throws DuplicatedPaymentException, ExternalIdConflictException {

    // Check that externalId doesn't have a complete payment
    this.externalIdValidator.execute(
        context.getApiKey(), context.getUserId(), context.getExternalId());

    // Find all providers sorted for this user
    var providersList = paymentProviderSelectorService.execute(context);
    context.getLogger().info("Provider list: {}", providersList);
    ProviderCheckout providerCheckout = null;
    PaymentProviderIdentifier paymentProviderIdentifier = null;
    var checkoutDone = false;
    for (int i = 0; i < providersList.size() && !checkoutDone; i++) {
      try {
        paymentProviderIdentifier = providersList.get(i);
        context.getLogger().info("Trying to process checkout using {}", paymentProviderIdentifier);
        statsDClient.increment(
            DD_CHECKOUT_CREATION_RULE_HITS,
            MetricsUtils.buildTags(
                List.of(TAG_API_KEY, TAG_PROVIDER),
                List.of(context.getApiKey(), paymentProviderIdentifier)));
        // Finding provider service for the current provider
        PaymentProviderService paymentProviderService =
            selectPaymentProviderService.execute(paymentProviderIdentifier);

        // Getting client configuration based on defined provider
        ClientConfiguration clientConfiguration =
            clientConfigurationService.getConfiguration(
                ConfigurationProviderIdentifier.fromPaymentProvider(paymentProviderIdentifier),
                context.getApiKey(),
                context.isSandbox());

        // Update location with provider info
        context.setPaymentLocation(
            geolocationPaymentService.updatePaymentLocation(
                context.getPaymentLocation(), paymentProviderIdentifier));

        if (context.isMultiCurrency()) {
          // Multicurrency. The local price wasn't defined in checkout request.
          var itemPriceData =
              countryConversionApplierProvider.calculateFromProvider(
                  paymentProviderIdentifier,
                  context.getPaymentLocation().getCountry(),
                  context.getItemData().getUnitBasePrice());
          context.setItemPrice(itemPriceData);
          context.getLogger().info("Updated item price data by exchange: {}", itemPriceData);
        }

        // Getting customerId based on defined provider
        String customerId =
            customerOperations.updateAndGetOrCreateCustomer(context, paymentProviderIdentifier);
        // Hitting the provider for a new payment/checkout session to add info to the paymentBuilder
        providerCheckout =
            paymentProviderService.createProviderCheckout(context, clientConfiguration, customerId);
        context.getLogger().info("Checkout created using {}", paymentProviderIdentifier);
        checkoutDone = true;
      } catch (RequestToProviderException ex) {
        sendErrorMetricsForCheckout(context, ex, paymentProviderIdentifier, ex.getHttpStatus());
      } catch (Exception ex) {
        sendErrorMetricsForCheckout(context, ex, paymentProviderIdentifier, -1);
      }
    }

    if (!checkoutDone) {
      sendRevenueLossMetric(context, providersList);
      throw new PaymentProvidersNotAvailableException(
          "No provider available for this payment request");
    }

    boolean isFallback = isFallbackProvider(context, paymentProviderIdentifier);
    if (isFallback) {
      context
          .getLogger()
          .warn(
              "Provider fallback from {} to {}",
              context.getAssignedProvider().orElse(null),
              paymentProviderIdentifier);
      statsDClient.increment(
          DD_CHECKOUT_PROVIDER_FALLBACK,
          MetricsUtils.buildTags(
              List.of(TAG_API_KEY, TAG_PROVIDER, TAG_PROVIDER_FALLBACK),
              List.of(
                  context.getApiKey(), paymentProviderIdentifier, context.getAssignedProvider())));
    }

    PaymentProviderData providerData =
        PaymentProviderDataHelper.createPaymentProviderData(
            paymentProviderIdentifier, providerCheckout);

    // Check which features user is assigned and add in to payment response
    List<PaymentFeature> features =
        featuresService.getFeatures(
            context, providerCheckout.getCountry(), paymentProviderIdentifier);

    // Once all the info is gathered a new Payment is created
    Payment payment =
        context
            .getBaseNewPayment(clock.instant())
            .setCountry(providerCheckout.getCountry())
            .setItemData(providerCheckout.getItemData())
            .setPriceData(providerCheckout.getPriceData())
            .setPaymentStatus(PaymentStatus.INITIATED)
            .setSessionId(providerCheckout.getSessionId())
            .setLocale(providerCheckout.getLocale())
            .setEmail(providerCheckout.getEmail())
            .setProviderData(providerData)
            .setProviderFallback(isFallback)
            .setContextProperties(providerCheckout.getContextProperties())
            .addAllFeatures(features)
            .build();

    // Save the new Payment with the provider info
    var savedPayment = safeSave(payment);
    sendNewPaymentMetricsAndEvent(savedPayment);
    context.getLogger().info("Checkout Saved: {}", context.getPaymentId());

    // Save the payment in the purchase token service
    String purchaseToken = purchaseTokenPaymentInfoService.createPurchaseTokenItem(payment);

    return new CreatePaymentResponseDTO(savedPayment, purchaseToken);
  }

  private void sendRevenueLossMetric(
      PaymentCreationContext context, List<PaymentProviderIdentifier> providersList) {
    var revenueLoss = context.getTotalBasePrice().getNumberStripped();
    context
        .getLogger()
        .addMetadata(MetadataKeys.BASE_AMOUNT.toString(), revenueLoss.toPlainString())
        .error("None of the following providers was able to perform a checkout: {}", providersList);
    statsDClient.count(
        DD_CHECKOUT_ERROR_REVENUE,
        revenueLoss.doubleValue(),
        MetricsUtils.buildTags(TAG_API_KEY, context.getApiKey()));
  }

  private boolean isFallbackProvider(
      PaymentCreationContext context, PaymentProviderIdentifier paymentProviderIdentifier) {
    // If the user makes the payment for the first time, or the payment provider is equal
    // to the assigned payment provider, set isFallback to false
    return context
        .getAssignedProvider()
        .filter(assignedProvider -> assignedProvider != paymentProviderIdentifier)
        .isPresent();
  }

  private Payment safeSave(Payment payment) throws DuplicatedPaymentException {
    try {
      return paymentRepository.save(payment);
    } catch (LockingConflictException exception) {
      // Check if payment with generated paymentId already exists
      if (paymentRepository.getPayment(payment.getPaymentId()).isPresent()) {
        new PaymentGatewayLogBuilder()
            .addPayment(payment.getApiKey(), payment.getPaymentId())
            .addProcess(PaymentProcess.CREATE_PAYMENT)
            .build()
            .error("The payment with id {} already exists", payment.getPaymentId());

        statsDClient.increment(
            DD_PAYMENT_CREATION_ID_ALREADY_EXISTS_ERROR,
            MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));

        throw new DuplicatedPaymentException("Intent to create a payment with a duplicated ID");
      }
      throw new UnableToSavePaymentException("Unable to save payment");
    }
  }

  private void sendErrorMetricsForCheckout(
      PaymentCreationContext context,
      Exception ex,
      PaymentProviderIdentifier paymentProviderIdentifier,
      Integer statusCode) {
    context
        .getLogger()
        .warn(
            ex,
            "Unable to process checkout using {}. Error code: {}",
            paymentProviderIdentifier,
            statusCode);
    statsDClient.increment(
        DD_CHECKOUT_CREATION_ERROR,
        MetricsUtils.buildTags(
            List.of(TAG_API_KEY, TAG_PROVIDER, TAG_STATUS),
            List.of(context.getApiKey(), paymentProviderIdentifier, statusCode)));
  }

  private void sendNewPaymentMetricsAndEvent(Payment payment) {
    paymentProcessAnalytics.sendProviderSelectionEvent(payment);

    statsDClient.increment(
        DD_TOTAL_CHECKOUTS,
        MetricsUtils.buildTags(
            new String[] {TAG_API_KEY, TAG_PROVIDER},
            new String[] {payment.getApiKey(), payment.getProviderData().getProvider().name()}));

    if (payment.getDeviceToken() == null || payment.getDeviceToken().isEmpty()) {
      statsDClient.increment(
          DD_PAYMENT_DEVICE_TOKEN_NULL,
          MetricsUtils.buildTags(
              new String[] {TAG_API_KEY, TAG_PROVIDER},
              new String[] {payment.getApiKey(), payment.getProviderData().getProvider().name()}));

      new PaymentGatewayLogBuilder()
          .addPayment(payment.getApiKey(), payment.getPaymentId())
          .addProcess(PaymentProcess.CHECKOUT)
          .build()
          .warn("The deviceToken parameter sent is null");
    }

    statsDClient.increment(
        DD_DOMAIN_CREATE_PAYMENT_SUCCESS, MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
  }

  public CreatePaymentRequestData updatePaymentDataBySandbox(
      CreatePaymentRequestData createPaymentRequestData,
      PlaygamiPaymentsClientConfig clientConfig) {

    boolean sandbox =
        clientConfig.getSandbox() || createPaymentRequestData.getProperties().isSandbox();

    if (sandbox != createPaymentRequestData.getProperties().isSandbox()) {
      Properties newProperties =
          Properties.Builder.from(createPaymentRequestData.getProperties())
              .setSandbox(sandbox)
              .build();
      createPaymentRequestData =
          CreatePaymentRequestData.Builder.from(createPaymentRequestData)
              .setProperties(newProperties)
              .build();
    }

    return createPaymentRequestData;
  }

  protected CreatePaymentRequestData updatePaymentDataForVIP(
      CreatePaymentRequestData createPaymentRequestData,
      PlaygamiPaymentsClientConfig clientConfig) {
    boolean isUserVip = shouldFlagUserAsVip(createPaymentRequestData, clientConfig);
    if (isUserVip != createPaymentRequestData.getProperties().getUser().isVip()) {
      User newUser =
          User.Builder.from(createPaymentRequestData.getProperties().getUser())
              .setVip(isUserVip)
              .build();
      Properties newProperties =
          Properties.Builder.from(createPaymentRequestData.getProperties())
              .setUser(newUser)
              .build();
      createPaymentRequestData =
          CreatePaymentRequestData.Builder.from(createPaymentRequestData)
              .setProperties(newProperties)
              .build();
    }
    return createPaymentRequestData;
  }

  private boolean shouldFlagUserAsVip(
      CreatePaymentRequestData request, PlaygamiPaymentsClientConfig clientConfig) {
    User requestUser = request.getProperties().getUser();
    if (requestUser.isVip()) {
      return true;
    }
    if (!clientConfig.isFlagVIPUsersEnabled()) {
      return false;
    }
    return userService.isUserVipOrDefault(request.getApiKey(), requestUser.getUserId());
  }
}
