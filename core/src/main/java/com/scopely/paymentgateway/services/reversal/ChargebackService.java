package com.scopely.paymentgateway.services.reversal;

import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.dispute.ChargebackNotFoundException;
import com.scopely.paymentgateway.exceptions.dispute.ChargebackServiceException;
import com.scopely.paymentgateway.exceptions.repository.ChargebackRepositoryException;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.reversal.Chargeback;
import com.scopely.paymentgateway.repositories.ChargebackRepository;
import com.scopely.paymentgateway.services.payment.PaymentReversalService;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;
import net.jodah.failsafe.Failsafe;
import net.jodah.failsafe.FailsafeException;
import net.jodah.failsafe.RetryPolicy;

@Singleton
public class ChargebackService {
  private final ChargebackRepository chargebackRepository;
  private final PaymentReversalService paymentReversalService;
  private final RetryPolicy retryPolicy;
  private final RetryPolicy retryPolicyForSave;

  @Inject
  public ChargebackService(
      ChargebackRepository chargebackRepository,
      PaymentReversalService paymentReversalService,
      RetryPolicy retryPolicy) {
    this.chargebackRepository = chargebackRepository;
    this.paymentReversalService = paymentReversalService;
    this.retryPolicy = retryPolicy;
    // Don't do automatic retries on locking conflicts
    this.retryPolicyForSave = retryPolicy.copy().abortOn(LockingConflictException.class);
  }

  public Chargeback save(Chargeback chargeback, Payment payment) throws ChargebackServiceException {
    try {
      var savedChargeback =
          Failsafe.with(retryPolicyForSave).get(() -> chargebackRepository.save(chargeback));
      paymentReversalService.setReversedAtFromChargeback(payment);
      return savedChargeback;
    } catch (FailsafeException e) {
      if (e.getCause() instanceof LockingConflictException) {
        return update(chargeback);
      } else {
        throw new ChargebackServiceException("Unable to save chargeback " + chargeback, e);
      }
    } catch (ChargebackRepositoryException e) {
      throw new ChargebackServiceException("Unable to save chargeback " + chargeback, e);
    }
  }

  public Chargeback update(Chargeback chargeback) throws ChargebackServiceException {
    try {
      return Failsafe.with(retryPolicy).get(() -> retrieveAndSave(chargeback));
    } catch (FailsafeException | ChargebackRepositoryException e) {
      throw new ChargebackServiceException("Unable to update chargeback " + chargeback, e);
    }
  }

  public List<Chargeback> getByUser(String apiKey, String userId)
      throws ChargebackServiceException {
    try {
      return Failsafe.with(retryPolicy).get(() -> chargebackRepository.getByUser(apiKey, userId));
    } catch (FailsafeException | ChargebackRepositoryException e) {
      throw new ChargebackServiceException("Unable to retrieve chargeback for user" + userId, e);
    }
  }

  private Chargeback retrieveAndSave(Chargeback chargeback)
      throws ChargebackNotFoundException, LockingConflictException {
    Chargeback previous =
        chargebackRepository
            .getById(chargeback.getChargebackId())
            .orElseThrow(
                () ->
                    new ChargebackNotFoundException(
                        "Unable to update non-existent chargeback " + chargeback));
    if (previous.getUpdatedAt().isAfter(chargeback.getUpdatedAt())) {
      return previous;
    }
    Chargeback merged =
        Chargeback.Builder.from(chargeback)
            .setCreatedAt(previous.getCreatedAt())
            .setVersion(previous.getVersion())
            .build();
    return chargebackRepository.save(merged);
  }
}
