package com.scopely.paymentgateway.services.payment;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_DOMAIN_CREATE_PAYMENT_TRY;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_TITAN_CONTEXT_PROPERTIES_EXCEED_LIMIT;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_TITAN_CONTEXT_PROPERTIES_NOT_SENT;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_TITAN_CONTEXT_PROPERTIES_SENT;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;

import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.exceptions.CountryUnsupportedException;
import com.scopely.paymentgateway.exceptions.InvalidCreatePaymentRequestException;
import com.scopely.paymentgateway.exceptions.LocalizedPriceNotFoundException;
import com.scopely.paymentgateway.exceptions.payment.DuplicatedPaymentException;
import com.scopely.paymentgateway.exceptions.payment.ExternalIdConflictException;
import com.scopely.paymentgateway.exceptions.payment.UnableToSavePaymentException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.logging.PaymentProcess;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;
import com.scopely.paymentgateway.model.createpayment.CreatePaymentRequestData;
import com.scopely.paymentgateway.model.dto.createpayment.CreatePaymentResponseDTO;
import com.scopely.paymentgateway.model.payment.PaymentLocation;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PricingMode;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.geolocation.GeolocationPaymentService;
import com.scopely.paymentgateway.services.pricelocalization.LocalizedPricesService;
import com.scopely.paymentgateway.services.translations.TranslationsService;
import com.scopely.paymentgateway.services.user.UserService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.util.StringUtils;
import com.timgroup.statsd.StatsDClient;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.javamoney.moneta.Money;

@Singleton
public class CreateNewPayment {
  private final PaymentGatewayConfig paymentGatewayConfig;
  private final CreatePaymentService createPaymentService;
  private final StatsDClient statsDClient;
  private final ClientConfigurationService clientConfigurationService;
  private final GeolocationPaymentService geolocationPaymentService;
  private final UserService userService;
  private final LocalizedPricesService localizedPricesService;
  private final TranslationsService translationsService;
  private final PaymentTransactionIdGenerator paymentTransactionIdGenerator;
  private final CreatePaymentValidator createPaymentValidator;

  @Inject
  public CreateNewPayment(
      PaymentGatewayConfig paymentGatewayConfig,
      CreatePaymentService createPaymentService,
      StatsDClient statsDClient,
      ClientConfigurationService clientConfigurationService,
      GeolocationPaymentService geolocationPaymentService,
      UserService userService,
      LocalizedPricesService localizedPricesService,
      TranslationsService translationsService,
      PaymentTransactionIdGenerator paymentTransactionIdGenerator,
      CreatePaymentValidator createPaymentValidator) {
    this.paymentGatewayConfig = paymentGatewayConfig;
    this.createPaymentService = createPaymentService;
    this.statsDClient = statsDClient;
    this.clientConfigurationService = clientConfigurationService;
    this.geolocationPaymentService = geolocationPaymentService;
    this.userService = userService;
    this.localizedPricesService = localizedPricesService;
    this.translationsService = translationsService;
    this.paymentTransactionIdGenerator = paymentTransactionIdGenerator;
    this.createPaymentValidator = createPaymentValidator;
  }

  public CreatePaymentResponseDTO execute(
      CreatePaymentRequestData createPaymentRequestData, String titanContextProperties)
      throws CountryUnsupportedException,
          LocalizedPriceNotFoundException,
          ExternalIdConflictException {

    String apiKey = createPaymentRequestData.getApiKey();
    String userId = createPaymentRequestData.getProperties().getUser().getUserId();
    statsDClient.increment(
        DD_DOMAIN_CREATE_PAYMENT_TRY, MetricsUtils.buildTags(TAG_API_KEY, apiKey));

    var logger =
        new PaymentGatewayLogBuilder()
            .addProcess(PaymentProcess.CHECKOUT)
            .addUserData(apiKey, userId)
            .build();
    logger.info("Starting checkout process: {}", createPaymentRequestData);

    // Get Playgami Payments client config
    PlaygamiPaymentsClientConfig clientConfig = clientConfigurationService.getConfiguration(apiKey);

    // Finding location to determine providers
    PaymentLocation paymentLocation =
        geolocationPaymentService.getPaymentLocation(
            createPaymentRequestData.getProperties().getCountry(),
            createPaymentRequestData.getProperties().getUserIp());
    logger.info("Payment location: {}", paymentLocation);
    logger.info(
        "Selected price mode: {}", createPaymentRequestData.getProperties().getPricingMode());

    createPaymentRequestData = updateRequestInfo(createPaymentRequestData, clientConfig);
    return generateValidPayment(
        createPaymentRequestData, clientConfig, paymentLocation, titanContextProperties);
  }

  private CreatePaymentResponseDTO generateValidPayment(
      CreatePaymentRequestData createPaymentRequestData,
      PlaygamiPaymentsClientConfig clientConfig,
      PaymentLocation paymentLocation,
      String titanContextProperties)
      throws ExternalIdConflictException, LocalizedPriceNotFoundException {

    // Validate request
    createPaymentValidator.validateRequest(createPaymentRequestData);

    String apiKey = createPaymentRequestData.getApiKey();
    String userId = createPaymentRequestData.getProperties().getUser().getUserId();

    titanContextProperties = normalizeContextProperties(titanContextProperties, apiKey);

    // Get user info to check if is in blocked list
    var currentPayer = userService.getUserPayerInfo(apiKey, userId);
    String requestLocale = createPaymentRequestData.getProperties().getLocale();
    String uiLocale =
        translationsService.getValidateLocale(
            requestLocale,
            clientConfig.getAllowedLanguages(),
            createPaymentRequestData.getApiKey());

    for (int retryCount = 0;
        retryCount < paymentGatewayConfig.createPaymentRetries();
        retryCount++) {

      // Creating a new paymentId
      String paymentId = paymentTransactionIdGenerator.generateId();
      var context =
          new PaymentCreationContext(
              paymentId,
              createPaymentRequestData,
              currentPayer,
              paymentLocation,
              uiLocale,
              titanContextProperties);
      context.getLogger().info("Payment ID Generated: {}", paymentId);

      // Update unit price if pricingMode is SKU
      if (context.getPricingMode() == PricingMode.SKU) {
        PriceData priceData = localizedPricesService.getLocalizedPriceDataBySku(context);
        context.setItemPrice(priceData.getOriginalBasePrice(), priceData.getOriginalLocalPrice());
      }
      validateBasePrice(context.getItemData().getUnitBasePrice());
      context.getLogger().info("Item data: {}", context.getItemData());

      try {
        return currentPayer.isBlocked()
            ? createPaymentService.createRejectedPayment(context)
            : createPaymentService.createPayment(context);
      } catch (DuplicatedPaymentException exception) {
        context.getLogger().warn("Duplicated payment detected. Will retry");
      }
    }
    throw new UnableToSavePaymentException("Reached max attempts to create Checkout");
  }

  private void validateBasePrice(Money basePrice) {
    if (Optional.ofNullable(basePrice).map(Money::isNegativeOrZero).orElse(true)) {
      throw new InvalidCreatePaymentRequestException(
          String.format("Item price cannot be 0 or negative (%s given)", basePrice));
    }
  }

  protected CreatePaymentRequestData updateRequestInfo(
      CreatePaymentRequestData createPaymentRequestData,
      PlaygamiPaymentsClientConfig clientConfig) {

    String apiKey = createPaymentRequestData.getApiKey();
    String userId = createPaymentRequestData.getProperties().getUser().getUserId();
    var logger =
        new PaymentGatewayLogBuilder()
            .addProcess(PaymentProcess.CHECKOUT)
            .addUserData(apiKey, userId)
            .build();

    // Check if game is sandbox and update CreatePaymentRequestData with the value
    createPaymentRequestData =
        createPaymentService.updatePaymentDataBySandbox(createPaymentRequestData, clientConfig);
    logger.info(
        "Sandbox flag for the payment is: {}",
        createPaymentRequestData.getProperties().isSandbox());

    // Check if the user is VIP and update CreatePaymentRequestData with the value
    createPaymentRequestData =
        createPaymentService.updatePaymentDataForVIP(createPaymentRequestData, clientConfig);
    logger.info(
        "VIP flag for the payer is: {}",
        createPaymentRequestData.getProperties().getUser().isVip());

    return createPaymentRequestData;
  }

  private String normalizeContextProperties(String contextProperties, String apiKey) {
    var logger = new PaymentGatewayLogBuilder().build();
    logger.debug("Normalizing contextProperties: '{}' for apiKey: {}", contextProperties, apiKey);
    if (StringUtils.isEmpty(contextProperties)) {
      logger.debug("Context properties is empty, sending NOT_SENT metric");
      statsDClient.increment(
          DD_PAYMENT_TITAN_CONTEXT_PROPERTIES_NOT_SENT,
          MetricsUtils.buildTags(TAG_API_KEY, apiKey));
      return null;
    } else if (contextProperties.length()
        > paymentGatewayConfig.titanContextPropertiesMaxLength()) {
      statsDClient.increment(
          DD_PAYMENT_TITAN_CONTEXT_PROPERTIES_EXCEED_LIMIT,
          MetricsUtils.buildTags(TAG_API_KEY, apiKey));
      logger.info("Context properties exceed limit. Length sent: {}", contextProperties.length());
      return null;
    } else {
      statsDClient.increment(
          DD_PAYMENT_TITAN_CONTEXT_PROPERTIES_SENT, MetricsUtils.buildTags(TAG_API_KEY, apiKey));
      return contextProperties;
    }
  }
}
