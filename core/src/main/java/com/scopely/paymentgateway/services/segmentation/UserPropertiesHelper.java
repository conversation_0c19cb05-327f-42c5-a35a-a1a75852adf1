package com.scopely.paymentgateway.services.segmentation;

import java.util.Map;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class UserPropertiesHelper {
  static final String APP_BUNDLE_KEY = "app.bundle";
  private static final Map<String, String> APP_BUNDLE_VALUES_BY_APIKEY =
      Map.of(
          "cde7f281-566c-496d-b025-a3c60cc3073c", "YahtzeeNux",
          "a645ce61-6cce-4fed-9dba-0a20f53b9886", "DiceNux");

  @Inject
  public UserPropertiesHelper() {}

  public Map<String, String> getUserProperties(String apiKey) {
    String appBundle = APP_BUNDLE_VALUES_BY_APIKEY.get(apiKey);
    return appBundle == null ? Map.of() : Map.of(APP_BUNDLE_KEY, appBundle);
  }
}
