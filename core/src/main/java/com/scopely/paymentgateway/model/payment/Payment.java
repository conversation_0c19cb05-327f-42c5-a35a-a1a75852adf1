package com.scopely.paymentgateway.model.payment;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.featuresmapping.PaymentFeature;
import com.scopely.paymentgateway.model.fulfillment.FulfillmentItem;
import java.time.Instant;
import java.util.List;
import javax.annotation.Nullable;
import org.inferred.freebuilder.FreeBuilder;

@JsonDeserialize(builder = Payment.Builder.class)
@FreeBuilder
public interface Payment extends ReceiptResource {
  String getPaymentId();

  PaymentStatus getPaymentStatus();

  String getUserId();

  @Nullable
  String getDeviceToken();

  String getApiKey();

  boolean isSandbox();

  PriceData getPriceData();

  @Nullable
  PaymentMethod getPaymentMethodUsed();

  Boolean getClaimed();

  @Nullable
  Instant getClaimedAt();

  @Nullable
  Instant getReversedAt();

  String getCountry();

  Instant getCreatedAt();

  Instant getUpdatedAt();

  @Nullable
  Integer getVersion();

  @Nullable
  String getTrackingId();

  @Nullable
  String getOrderId();

  @Nullable
  List<FulfillmentItem> getFulfillmentItems();

  @Nullable
  ProviderStatus getProviderStatus();

  @Nullable
  String getErrorMessage();

  ItemData getItemData();

  @Nullable
  String getSessionId();

  @Nullable
  String getLocale();

  @Nullable
  String getEmail();

  PlatformType getPlatform();

  @Nullable
  String getPurchaseToken();

  boolean isSavedPaymentMethod();

  boolean isVip();

  boolean isProviderFallback();

  @Nullable
  String getExternalId();

  @Nullable
  PaymentProviderData getProviderData();

  List<PaymentFeature> getFeatures();

  @Nullable
  String getTitanContextProperties();

  class Builder extends Payment_Builder {

    public Builder() {
      setSavedPaymentMethod(false);
      setVip(false);
      setProviderFallback(false);
      setSandbox(false);
      setPlatform(PlatformType.WEB);
      setItemData(new ItemData.Builder().build());
    }

    @Override
    public Builder setProviderStatus(ProviderStatus providerStatus) {
      if (providerStatus != null && super.getPaymentStatus() != PaymentStatus.FAILED) {
        setPaymentStatus(providerStatus.getPaymentStatus());
      }
      return super.setProviderStatus(providerStatus);
    }

    @Override
    @JsonDeserialize(using = PaymentProviderDataDeserializer.class)
    public Payment.Builder setProviderData(PaymentProviderData data) {
      return super.setProviderData(data);
    }
  }
}
