package com.scopely.paymentgateway.model.provider;

import com.scopely.paymentgateway.model.payment.ItemData;
import com.scopely.paymentgateway.model.payment.PriceData;
import java.util.Map;
import javax.annotation.Nullable;
import org.inferred.freebuilder.FreeBuilder;

// This class is the domain representation of a provider new payment/checkout response.
@FreeBuilder
public interface ProviderCheckout {

  @Nullable
  String getSessionId();

  @Nullable
  String getToken();

  String getCheckoutId();

  PriceData getPriceData();

  String getCountry();

  ItemData getItemData();

  @Nullable
  String getBrowserIP();

  @Nullable
  String getLocale();

  @Nullable
  String getEmail();

  @Nullable
  Map<String, Object> getMetadata();

  @Nullable
  String getCompleteUserName();

  class Builder extends ProviderCheckout_Builder {

    public Builder() {}
  }
}
