package com.scopely.paymentgateway.model.dto.createpayment;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

@Schema(name = "CreatePaymentResponse")
public record CreatePaymentResponseDTO(
    String paymentId,
    @JsonInclude(JsonInclude.Include.NON_NULL) String externalId,
    String userId,
    String provider,
    Date createdAt,
    Date updatedAt,
    String purchaseToken) {
  private static final String UNKNOWN_PROVIDER = "UNKNOWN";

  public CreatePaymentResponseDTO(Payment payment) {
    this(payment, payment.getProviderData().getProvider());
  }

  public CreatePaymentResponseDTO(Payment payment, PaymentProviderIdentifier provider) {
    this(
        payment.getPaymentId(),
        payment.getExternalId(),
        payment.getUserId(),
        provider != null ? provider.name() : UNKNOWN_PROVIDER,
        payment.getCreatedAt() != null ? Date.from(payment.getCreatedAt()) : null,
        payment.getUpdatedAt() != null ? Date.from(payment.getUpdatedAt()) : null,
        null);
  }

  public CreatePaymentResponseDTO(Payment payment, String purchaseToken) {
    this(
        payment.getPaymentId(),
        payment.getExternalId(),
        payment.getUserId(),
        payment.getProviderData().getProvider().name(),
        payment.getCreatedAt() != null ? Date.from(payment.getCreatedAt()) : null,
        payment.getUpdatedAt() != null ? Date.from(payment.getUpdatedAt()) : null,
        purchaseToken);
  }
}
