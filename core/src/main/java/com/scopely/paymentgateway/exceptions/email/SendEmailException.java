package com.scopely.paymentgateway.exceptions.email;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class SendEmailException extends EmailServiceException {

  @Serial private static final long serialVersionUID = 2926245128217921511L;
  private static final String ERROR_MESSAGE = "Email sending failed.";
  private static final String ERROR_MESSAGE_WITH_PROVIDER = "Email sending failed with %s.";

  public SendEmailException() {
    super(ERROR_MESSAGE);
  }

  public SendEmailException(String emailProvider) {
    super(String.format(ERROR_MESSAGE_WITH_PROVIDER, emailProvider));
  }

  public SendEmailException(Exception e) {
    super(e);
  }

  public SendEmailException(String message, Exception ex) {
    super(message, ex);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.ERROR_SENDING_EMAIL;
  }
}
