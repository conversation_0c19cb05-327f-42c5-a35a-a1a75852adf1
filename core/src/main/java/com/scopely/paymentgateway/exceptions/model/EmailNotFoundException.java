package com.scopely.paymentgateway.exceptions.model;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class EmailNotFoundException extends EntityNotFoundException {

  @Serial private static final long serialVersionUID = 5593135397906965014L;

  public static EmailNotFoundException createEmailNotFoundExceptionByPaymentId(String paymentId) {
    return new EmailNotFoundException(
        String.format("Email with payment id %s is missing", paymentId));
  }

  public static EmailNotFoundException createEmailNotFoundExceptionByPaymentId(
      String paymentId, Exception ex) {
    return new EmailNotFoundException(
        String.format("Email with payment id %s is missing", paymentId), ex);
  }

  public EmailNotFoundException(String message, Exception ex) {
    super(message, ex);
  }

  public EmailNotFoundException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.EMAIL_NOT_FOUND;
  }
}
