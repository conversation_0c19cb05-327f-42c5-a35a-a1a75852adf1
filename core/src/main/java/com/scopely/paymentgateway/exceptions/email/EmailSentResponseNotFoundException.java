package com.scopely.paymentgateway.exceptions.email;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class EmailSentResponseNotFoundException extends EmailServiceException {

  @Serial private static final long serialVersionUID = -255116962602653708L;
  private static final String ERROR_MESSAGE = "email_sent_response_not_found";

  public EmailSentResponseNotFoundException() {
    super(ERROR_MESSAGE);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.EMAIL_SENT_RESPONSE_NOT_FOUND;
  }
}
