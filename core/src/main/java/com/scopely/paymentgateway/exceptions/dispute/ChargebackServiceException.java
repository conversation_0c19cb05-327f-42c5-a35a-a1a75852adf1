package com.scopely.paymentgateway.exceptions.dispute;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayException;
import java.io.Serial;

public class ChargebackServiceException extends PaymentGatewayException {
  @Serial private static final long serialVersionUID = -2132297813026191685L;

  public ChargebackServiceException(String message) {
    super(message);
  }

  public ChargebackServiceException(String message, Exception ex) {
    super(message, ex);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.CHARGEBACK_SERVICE_ERROR;
  }
}
