package com.scopely.paymentgateway.exceptions.repository;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayRuntimeException;
import java.io.Serial;

public class ReversalRepositoryException extends PaymentGatewayRuntimeException {
  @Serial private static final long serialVersionUID = -6000128680379185149L;

  public ReversalRepositoryException(Throwable cause) {
    super("Caused by: " + cause.toString());
    this.initCause(cause);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.UNABLE_TO_PERFORM_REVERSAL_OPERATION;
  }
}
