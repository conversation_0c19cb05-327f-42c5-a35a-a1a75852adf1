package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class FeatureMappingException extends PaymentGatewayRuntimeException {

  @Serial static final long serialVersionUID = -8277472146516010760L;

  public FeatureMappingException(String message) {
    super(message);
  }

  public FeatureMappingException(String message, Exception e) {
    super(message, e);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.UNABLE_TO_GET_PAYMENT_SYSTEM_CONFIGS;
  }
}
