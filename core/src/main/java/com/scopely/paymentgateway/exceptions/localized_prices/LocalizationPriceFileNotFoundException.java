package com.scopely.paymentgateway.exceptions.localized_prices;

import static com.scopely.paymentgateway.constants.ErrorConstants.LOCALIZED_PRICE_FILE_NOT_FOUND;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayRuntimeException;
import java.io.Serial;

public class LocalizationPriceFileNotFoundException extends PaymentGatewayRuntimeException {
  @Serial private static final long serialVersionUID = 5838401536167223562L;

  public LocalizationPriceFileNotFoundException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return LOCALIZED_PRICE_FILE_NOT_FOUND;
  }
}
