package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class EmailProvidersNotAvailableException extends PaymentGatewayRuntimeException {
  @Serial static final long serialVersionUID = 1703291211418772241L;

  public EmailProvidersNotAvailableException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.EMAIL_PROVIDERS_NOT_AVAILABLE;
  }
}
