package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;
import java.util.Map;

/**
 * This exception is thrown when there is an error in the payment gateway service. It should be the
 * main exception to be inherited by all the other exceptions in the service.
 */
public abstract class PaymentGatewayException extends Exception {

  @Serial static final long serialVersionUID = -3978664762837520673L;

  protected PaymentGatewayException(String message) {
    super(message);
  }

  protected PaymentGatewayException(Exception e) {
    super(e);
  }

  protected PaymentGatewayException(String message, Exception ex) {
    super(message, ex);
  }

  public abstract ErrorConstants getErrorCode();

  public Map<String, Object> getResponseProperties() {
    return Map.of();
  }
}
