package com.scopely.paymentgateway.exceptions.segmentation;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayException;
import java.io.Serial;

public class UnableToRetrieveSegments extends PaymentGatewayException {

  @Serial private static final long serialVersionUID = -8499846306422127468L;

  public UnableToRetrieveSegments(String message) {
    super(message);
  }

  public UnableToRetrieveSegments(Exception e) {
    super(e);
  }

  public UnableToRetrieveSegments(String message, Exception ex) {
    super(message, ex);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.UNABLE_TO_RETRIEVE_SEGMENTS;
  }
}
