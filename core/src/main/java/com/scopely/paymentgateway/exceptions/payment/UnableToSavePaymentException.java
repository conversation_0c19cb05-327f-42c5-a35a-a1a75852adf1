package com.scopely.paymentgateway.exceptions.payment;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayRuntimeException;
import java.io.Serial;

public class UnableToSavePaymentException extends PaymentGatewayRuntimeException {

  @Serial private static final long serialVersionUID = -8499041106422127468L;

  public UnableToSavePaymentException(String message) {
    super(message);
  }

  public UnableToSavePaymentException(String message, Exception ex) {
    super(message, ex);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.UNABLE_TO_SAVE_PAYMENT;
  }
}
