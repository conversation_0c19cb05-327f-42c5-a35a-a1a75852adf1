package com.scopely.paymentgateway.exceptions.dispute;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class ChargebackNotFoundException extends ChargebackServiceException {
  @Serial private static final long serialVersionUID = -4129246436214890768L;

  public ChargebackNotFoundException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.CHARGEBACK_NOT_FOUND;
  }
}
