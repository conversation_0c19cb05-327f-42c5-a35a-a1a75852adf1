package com.scopely.paymentgateway.exceptions.model;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class PaymentNotFoundException extends EntityNotFoundException {

  @Serial private static final long serialVersionUID = 4429483273603713716L;

  public PaymentNotFoundException(String message) {
    super(message);
  }

  public static PaymentNotFoundException createPaymentNotFoundExceptionByOrderId(String orderId) {
    return new PaymentNotFoundException(
        String.format("Payment with order id %s does not exist", orderId));
  }

  public static PaymentNotFoundException createPaymentNotFoundExceptionByPaymentId(
      String paymentId) {
    return new PaymentNotFoundException(
        String.format("Payment with id %s does not exist", paymentId));
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.PAYMENT_NOT_FOUND;
  }
}
