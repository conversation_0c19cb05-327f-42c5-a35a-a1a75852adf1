package com.scopely.paymentgateway.exceptions.localized_prices;

import static com.scopely.paymentgateway.constants.ErrorConstants.PRICE_LOCALIZATION_CSV_PARSER_ERROR;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayRuntimeException;
import com.scopely.paymentgateway.model.localizedprice.PriceLocalizationVerificationError;
import java.io.Serial;
import java.util.Collection;
import java.util.stream.Collectors;

public class PriceLocalizationCsvParserException extends PaymentGatewayRuntimeException {
  @Serial private static final long serialVersionUID = 5176040977031978929L;
  public static final String ERROR_SEPARATOR = "\n";

  public PriceLocalizationCsvParserException(
      Collection<PriceLocalizationVerificationError> errors) {
    super(
        errors.stream()
            .map(PriceLocalizationVerificationError::toString)
            .distinct() // This removes duplicate error strings
            .collect(Collectors.joining(ERROR_SEPARATOR)));
  }

  @Override
  public ErrorConstants getErrorCode() {
    return PRICE_LOCALIZATION_CSV_PARSER_ERROR;
  }
}
