package com.scopely.paymentgateway.exceptions.repository;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayRuntimeException;
import java.io.Serial;

public class DisputeRepositoryException extends PaymentGatewayRuntimeException {
  @Serial private static final long serialVersionUID = -212171463718841806L;

  public DisputeRepositoryException(Throwable cause) {
    super("Caused by: " + cause.toString());
    this.initCause(cause);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.UNABLE_TO_PERFORM_DISPUTE_OPERATION;
  }
}
