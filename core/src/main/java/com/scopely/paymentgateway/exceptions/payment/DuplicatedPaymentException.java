package com.scopely.paymentgateway.exceptions.payment;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayException;
import java.io.Serial;

public class DuplicatedPaymentException extends PaymentGatewayException {

  @Serial private static final long serialVersionUID = -6828708324192889263L;

  public DuplicatedPaymentException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.DUPLICATED_CHECKOUT;
  }
}
