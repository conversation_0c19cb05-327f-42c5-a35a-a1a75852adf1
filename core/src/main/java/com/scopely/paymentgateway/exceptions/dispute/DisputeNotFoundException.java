package com.scopely.paymentgateway.exceptions.dispute;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class DisputeNotFoundException extends DisputeServiceException {
  @Serial private static final long serialVersionUID = 6240995828472731072L;

  public DisputeNotFoundException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.DISPUTE_NOT_FOUND;
  }
}
