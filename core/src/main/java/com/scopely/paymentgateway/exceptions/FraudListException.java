package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class FraudListException extends PaymentGatewayException {
  @Serial private static final long serialVersionUID = -4611174312237540764L;

  public FraudListException(String message) {
    super(message);
  }

  public FraudListException(String message, Exception e) {
    super(message, e);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.UNABLE_TO_GET_FRAUD_LIST;
  }
}
