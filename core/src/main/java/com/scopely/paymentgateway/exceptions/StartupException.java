package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class StartupException extends PaymentGatewayRuntimeException {

  @Serial private static final long serialVersionUID = -7684143082001750758L;
  private static final String ERROR_MESSAGE = "Startup probe failed.";

  public StartupException() {
    super(ERROR_MESSAGE);
  }

  public StartupException(Exception e) {
    super(e);
  }

  public StartupException(String message, Exception e) {
    super(message, e);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.STARTUP_ERROR;
  }
}
