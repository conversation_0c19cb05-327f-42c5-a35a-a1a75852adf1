package com.scopely.paymentgateway.exceptions.translation;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayRuntimeException;
import java.io.Serial;

public class TranslationNotFoundException extends PaymentGatewayRuntimeException {
  @Serial static final long serialVersionUID = 8736978504798993664L;

  public TranslationNotFoundException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.TRANSLATION_KEY_NOT_FOUND;
  }
}
