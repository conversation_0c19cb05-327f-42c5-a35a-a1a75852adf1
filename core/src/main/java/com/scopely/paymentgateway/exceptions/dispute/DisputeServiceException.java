package com.scopely.paymentgateway.exceptions.dispute;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayException;
import java.io.Serial;

public class DisputeServiceException extends PaymentGatewayException {
  @Serial private static final long serialVersionUID = -9178508247498949943L;

  public DisputeServiceException(String message) {
    super(message);
  }

  public DisputeServiceException(String message, Exception ex) {
    super(message, ex);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.DISPUTE_SERVICE_ERROR;
  }
}
