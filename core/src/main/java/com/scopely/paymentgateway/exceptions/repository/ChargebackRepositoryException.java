package com.scopely.paymentgateway.exceptions.repository;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayRuntimeException;
import java.io.Serial;

public class ChargebackRepositoryException extends PaymentGatewayRuntimeException {
  @Serial private static final long serialVersionUID = 3372810102634968049L;

  public ChargebackRepositoryException(Throwable cause) {
    super("Caused by: " + cause.toString());
    this.initCause(cause);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.UNABLE_TO_PERFORM_CHARGEBACK_OPERATION;
  }
}
