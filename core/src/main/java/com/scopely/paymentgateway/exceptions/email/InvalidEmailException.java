package com.scopely.paymentgateway.exceptions.email;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class InvalidEmailException extends EmailServiceException {

  @Serial private static final long serialVersionUID = 8016831460831291906L;

  public InvalidEmailException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.INVALID_EMAIL_SYNTAX;
  }
}
