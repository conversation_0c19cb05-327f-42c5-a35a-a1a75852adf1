package com.scopely.paymentgateway.exceptions.localized_prices;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayRuntimeException;
import java.io.Serial;

public class LocalizedPriceNotFoundInDBException extends PaymentGatewayRuntimeException {
  @Serial private static final long serialVersionUID = 2758258336479664777L;

  public LocalizedPriceNotFoundInDBException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.LOCALIZED_PRICE_RECORD_NOT_FOUND;
  }
}
