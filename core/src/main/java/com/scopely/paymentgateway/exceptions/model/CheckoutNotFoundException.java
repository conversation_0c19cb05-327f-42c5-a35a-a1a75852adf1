package com.scopely.paymentgateway.exceptions.model;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class CheckoutNotFoundException extends EntityNotFoundException {

  @Serial private static final long serialVersionUID = -1335076996436244856L;

  public CheckoutNotFoundException(String checkoutId) {
    super(ErrorConstants.CHECKOUT_NOT_FOUND.toString());
  }

  public CheckoutNotFoundException(String checkoutId, Exception ex) {
    super(String.format("Checkout with id %s is missing", checkoutId), ex);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.CHECKOUT_NOT_FOUND;
  }
}
