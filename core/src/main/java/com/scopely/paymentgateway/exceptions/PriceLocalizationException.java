package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.model.EntityNotFoundException;
import java.io.Serial;

public class PriceLocalizationException extends EntityNotFoundException {
  @Serial private static final long serialVersionUID = 3022977632219840253L;

  public PriceLocalizationException(Throwable cause) {
    super("Caused by: " + cause.toString());
    this.initCause(cause);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.LOCALIZED_PRICE;
  }
}
