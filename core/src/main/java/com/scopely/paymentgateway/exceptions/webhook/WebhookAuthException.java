package com.scopely.paymentgateway.exceptions.webhook;

import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import java.io.Serial;

public class WebhookAuthException extends Exception {

  @Serial private static final long serialVersionUID = 5418294617950090531L;

  public WebhookAuthException(PaymentProviderIdentifier paymentProviderIdentifier) {
    this("Unauthorized access for provider: " + paymentProviderIdentifier);
  }

  public WebhookAuthException(
      PaymentProviderIdentifier paymentProviderIdentifier, Exception exception) {
    this("Unauthorized access for provider: " + paymentProviderIdentifier);
    this.initCause(exception);
  }

  public WebhookAuthException(final Throwable cause) {
    super(cause);
  }

  public WebhookAuthException(final String message) {
    super(message);
  }

  public WebhookAuthException(final String message, final Throwable cause) {
    super(message, cause);
  }
}
