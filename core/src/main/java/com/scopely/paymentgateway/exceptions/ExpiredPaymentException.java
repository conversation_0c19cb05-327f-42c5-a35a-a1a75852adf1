package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;
import java.util.Map;

public class ExpiredPaymentException extends PaymentGatewayException {

  @Serial private static final long serialVersionUID = 4553965741954247963L;

  public ExpiredPaymentException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.EXPIRED_PAYMENT;
  }

  @Override
  public Map<String, Object> getResponseProperties() {
    return Map.of("cause", getErrorCode().name().toLowerCase());
  }
}
