package com.scopely.paymentgateway.exceptions.model;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class UserNotFoundException extends EntityNotFoundException {

  @Serial private static final long serialVersionUID = 9212079806077019359L;

  public UserNotFoundException(String userId) {
    super(String.format("User with id %s does not exist", userId));
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.USER_NOT_FOUND;
  }
}
