package com.scopely.paymentgateway.exceptions.payment;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayException;
import java.io.Serial;

public class ExternalIdConflictException extends PaymentGatewayException {

  @Serial private static final long serialVersionUID = -4830968186997092788L;

  public ExternalIdConflictException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.EXTERNAL_ID_CONFLICT;
  }
}
