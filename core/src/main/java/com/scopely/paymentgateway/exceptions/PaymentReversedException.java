package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class PaymentReversedException extends PaymentGatewayException {
  @Serial private static final long serialVersionUID = -1993980189778827437L;

  public PaymentReversedException(String detail, String paymentId) {
    this("%s: payment has been reversed. Payment ID: %s".formatted(detail, paymentId));
  }

  public PaymentReversedException(final String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.PAYMENT_REVERSED;
  }
}
