package com.scopely.paymentgateway.exceptions.localized_prices;

import static com.scopely.paymentgateway.constants.ErrorConstants.LOCALIZED_PRICE_UPLOAD_EXCEPTION;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayRuntimeException;
import java.io.Serial;

public class LocalizedPriceUploadFileException extends PaymentGatewayRuntimeException {
  @Serial private static final long serialVersionUID = 5030748628764127853L;

  public LocalizedPriceUploadFileException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return LOCALIZED_PRICE_UPLOAD_EXCEPTION;
  }
}
