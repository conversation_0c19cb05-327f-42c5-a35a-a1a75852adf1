package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import java.io.Serial;

public class RejectedPaymentException extends InvalidPaymentException {

  @Serial private static final long serialVersionUID = 8121445604030524379L;

  public RejectedPaymentException(String message) {
    super(message, PaymentStatus.REJECTED);
  }

  public RejectedPaymentException(String message, String locale) {
    super(message, PaymentStatus.REJECTED, locale);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.USER_BLOCKED;
  }
}
