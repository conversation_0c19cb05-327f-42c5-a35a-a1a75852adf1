package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class PurchaseTokenInvalidApiKeyException extends PaymentGatewayException {

  @Serial private static final long serialVersionUID = -5002671895129703350L;

  public PurchaseTokenInvalidApiKeyException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.INVALID_APIKEY;
  }
}
