package com.scopely.paymentgateway.exceptions.email;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class RejectedEmailException extends EmailServiceException {

  @Serial private static final long serialVersionUID = 2927423688217921511L;
  private static final String ERROR_MESSAGE = "Email was rejected by at least one email provider.";

  public RejectedEmailException() {
    super(ERROR_MESSAGE);
  }

  public RejectedEmailException(Exception e) {
    super(e);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.REJECTED_EMAIL;
  }
}
