package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import java.io.Serial;

public class OrderAlreadyCreatedException extends RuntimeException {

  @Serial static final long serialVersionUID = -1696872391273073839L;

  public OrderAlreadyCreatedException(PaymentProviderIdentifier paymentProviderIdentifier) {
    this("Invalid request from provider " + paymentProviderIdentifier);
  }

  public OrderAlreadyCreatedException(
      PaymentProviderIdentifier paymentProviderIdentifier, Exception exception) {
    this("Invalid request from provider " + paymentProviderIdentifier);
    this.initCause(exception);
  }

  public OrderAlreadyCreatedException(final Throwable cause) {
    super(cause);
  }

  public OrderAlreadyCreatedException(final String message) {
    super(message);
  }

  public OrderAlreadyCreatedException(final String message, final Throwable cause) {
    super(message, cause);
  }
}
