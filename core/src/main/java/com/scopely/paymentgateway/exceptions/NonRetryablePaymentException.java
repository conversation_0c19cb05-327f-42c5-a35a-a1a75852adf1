package com.scopely.paymentgateway.exceptions;

import java.io.Serial;
import java.util.Optional;

public class NonRetryablePaymentException extends Exception {

  @Serial private static final long serialVersionUID = -3612782199417988938L;

  private final String errorMsg;

  public NonRetryablePaymentException(String message) {
    super(message);
    this.errorMsg = message;
  }

  public NonRetryablePaymentException(
      final String message, final RequestToProviderException exception) {
    super(message, exception);
    this.errorMsg = exception.getErrorCode();
  }

  public NonRetryablePaymentException(String errorCode, Exception exception) {
    super(exception.getMessage(), exception);
    this.errorMsg = errorCode;
  }

  public String getErrorMsg() {
    return errorMsg;
  }

  @Override
  public String getMessage() {
    return Optional.ofNullable(errorMsg).orElse(super.getMessage());
  }
}
