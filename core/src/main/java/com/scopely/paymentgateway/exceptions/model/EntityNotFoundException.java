package com.scopely.paymentgateway.exceptions.model;

import com.scopely.paymentgateway.exceptions.PaymentGatewayException;
import java.io.Serial;

public abstract class EntityNotFoundException extends PaymentGatewayException {
  @Serial private static final long serialVersionUID = 2924261219064430928L;

  protected EntityNotFoundException(String message) {
    super(message);
  }

  protected EntityNotFoundException(Exception e) {
    super(e);
  }

  protected EntityNotFoundException(String message, Exception ex) {
    super(message, ex);
  }
}
