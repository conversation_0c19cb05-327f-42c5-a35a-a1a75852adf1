package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.model.EntityNotFoundException;
import java.io.Serial;

public class FeeException extends EntityNotFoundException {

  @Serial private static final long serialVersionUID = 3022977632219840253L;

  public FeeException(String cause) {
    super(cause);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.FEE_NOT_FOUND;
  }
}
