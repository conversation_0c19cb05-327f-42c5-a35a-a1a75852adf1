package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class SourceNotFoundException extends PaymentGatewayRuntimeException {
  @Serial private static final long serialVersionUID = -4035229973777436830L;

  public SourceNotFoundException(String message) {
    super(message);
  }

  public SourceNotFoundException(String message, Exception ex) {
    super(message, ex);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.SOURCE_NOT_FOUND;
  }
}
