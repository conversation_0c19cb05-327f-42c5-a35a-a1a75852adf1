package com.scopely.paymentgateway.exceptions.webhook;

import static com.scopely.paymentgateway.exceptions.webhook.WebhookException.Error.UNKNOWN;

import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import java.io.Serial;

public class WebhookException extends Exception {

  @Serial private static final long serialVersionUID = 2646377414089050571L;
  private final Error error;
  private static final String DEFAULT_ERR_MESSAGE = "Invalid request from provider %s";
  private static final String CUSTOM_ERR_MESSAGE = "Invalid request from provider %s. Reason: %s";

  public WebhookException(final String message) {
    super(message);
    this.error = UNKNOWN;
  }

  public WebhookException(final String message, final Throwable cause) {
    super(message, cause);
    this.error = UNKNOWN;
  }

  public WebhookException(PaymentProviderIdentifier paymentProviderIdentifier) {
    this(String.format(DEFAULT_ERR_MESSAGE, paymentProviderIdentifier));
  }

  public WebhookException(Error error, PaymentProviderIdentifier paymentProviderIdentifier) {
    super(String.format(CUSTOM_ERR_MESSAGE, paymentProviderIdentifier, error));
    this.error = error;
  }

  public WebhookException(
      Error error, PaymentProviderIdentifier paymentProviderIdentifier, Exception exception) {
    super(String.format(CUSTOM_ERR_MESSAGE, paymentProviderIdentifier, error), exception);
    this.error = error;
  }

  public WebhookException(
      PaymentProviderIdentifier paymentProviderIdentifier, Exception exception) {
    this(String.format(DEFAULT_ERR_MESSAGE, paymentProviderIdentifier), exception);
  }

  public Error getError() {
    return error;
  }

  public enum Error {
    EVENT_TO_BE_FORWARDED,
    EVENT_NOT_FOUND,
    DUPLICATED_EXTERNAL_ID,
    UNEXPECTED_ERROR,
    PARSING_EVENT_EXCEPTION,
    PAYMENT_NOT_FOUND,
    UNKNOWN
  }
}
