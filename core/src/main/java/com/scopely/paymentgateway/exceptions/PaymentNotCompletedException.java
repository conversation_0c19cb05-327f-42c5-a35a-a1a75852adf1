package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import java.io.Serial;

public class PaymentNotCompletedException extends PaymentGatewayException {

  @Serial private static final long serialVersionUID = 6549667194840310920L;

  public PaymentNotCompletedException(
      PaymentProviderIdentifier paymentProviderIdentifier, String paymentId) {
    this(
        "Payment is not completed and cannot be claimed. Payment ID: "
            + paymentId
            + " from "
            + paymentProviderIdentifier);
  }

  public PaymentNotCompletedException(final String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.PAYMENT_NOT_COMPLETED;
  }
}
