package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class InvalidUserAttributeException extends PaymentGatewayException {

  @Serial private static final long serialVersionUID = 261228054470553859L;

  public InvalidUserAttributeException(String message) {
    super(message);
  }

  protected InvalidUserAttributeException(Exception e) {
    super(e);
  }

  public InvalidUserAttributeException(String message, Exception ex) {
    super(message, ex);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.INVALID_USER_ATTRIBUTE;
  }
}
