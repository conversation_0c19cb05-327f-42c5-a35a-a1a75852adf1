package com.scopely.paymentgateway.exceptions.repository;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayRuntimeException;
import java.io.Serial;

public class EntityLockingConflictException extends PaymentGatewayRuntimeException {
  @Serial private static final long serialVersionUID = -2696024995288287220L;

  public EntityLockingConflictException(String message) {
    super(message);
  }

  public EntityLockingConflictException(Exception e) {
    super(e);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.OPTIMISTIC_LOCKING_DEADLOCK;
  }
}
