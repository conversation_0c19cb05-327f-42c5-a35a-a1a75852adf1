package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class PurchaseTokenNotFoundException extends PaymentGatewayException {
  @Serial private static final long serialVersionUID = -4035229973777436830L;

  public PurchaseTokenNotFoundException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.PURCHASE_TOKEN_NOT_FOUND;
  }
}
