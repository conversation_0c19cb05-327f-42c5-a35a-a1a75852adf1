package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

/**
 * This exception is thrown when there is an error in the payment gateway service. It should be the
 * main exception to be inherited by all the other runtime exceptions in the service.
 */
public abstract class PaymentGatewayRuntimeException extends RuntimeException {

  @Serial private static final long serialVersionUID = 8554378194125566830L;

  protected PaymentGatewayRuntimeException(String message) {
    super(message);
  }

  protected PaymentGatewayRuntimeException(Exception ex) {
    super(ex);
  }

  protected PaymentGatewayRuntimeException(String message, Exception ex) {
    super(message, ex);
  }

  public abstract ErrorConstants getErrorCode();
}
