package com.scopely.paymentgateway.exceptions.email;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class UnableToSendEmailException extends EmailServiceException {

  @Serial private static final long serialVersionUID = -5578470709268724655L;
  private static final String ERROR_MESSAGE = "unable_to_send_email";

  public UnableToSendEmailException() {
    super(ERROR_MESSAGE);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.ERROR_SENDING_EMAIL;
  }
}
