package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import java.io.Serial;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class InvalidPaymentException extends PaymentGatewayException {

  @Serial private static final long serialVersionUID = 3249188878335944453L;
  private String paymentStatus;
  private String locale;

  public InvalidPaymentException(String message) {
    super(message);
  }

  public InvalidPaymentException(String message, PaymentStatus paymentStatus) {
    super(message);
    this.paymentStatus = paymentStatus.name().toLowerCase();
  }

  public InvalidPaymentException(String message, PaymentStatus paymentStatus, String locale) {
    super(message);
    this.paymentStatus = paymentStatus.name().toLowerCase();
    this.locale = locale;
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.INVALID_PAYMENT;
  }

  @Override
  public Map<String, Object> getResponseProperties() {
    Map<String, Object> propertiesMap = new HashMap<>();
    Optional.ofNullable(paymentStatus)
        .ifPresent(paymentStatus -> propertiesMap.put("status", paymentStatus));
    Optional.ofNullable(locale).ifPresent(locale -> propertiesMap.put("locale", locale));

    return propertiesMap;
  }
}
