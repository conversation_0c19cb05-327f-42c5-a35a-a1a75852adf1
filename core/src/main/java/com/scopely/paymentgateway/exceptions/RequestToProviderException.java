package com.scopely.paymentgateway.exceptions;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.proteus.util.JacksonMapper;
import com.scopely.satellites.Try;
import java.io.Serial;
import java.util.Optional;
import retrofit2.Response;

@SuppressWarnings({"PMD.ConstructorCallsOverridableMethod"})
public class RequestToProviderException extends Exception {

  private static final Integer DEFAULT_SERVER_ERROR_CODE = 500;

  @Serial static final long serialVersionUID = 8475347872769567922L;
  private static ObjectMapper MAPPER = JacksonMapper.MAPPER;
  private final Integer httpStatus;

  private final String errorCode;

  public RequestToProviderException(final Integer httpStatus, final String errorMsg) {
    super(errorMsg);
    this.errorCode = this.getMessage();
    this.httpStatus = Optional.ofNullable(httpStatus).orElse(DEFAULT_SERVER_ERROR_CODE);
  }

  public RequestToProviderException(final String errorCode, final Integer httpStatus) {
    super(errorCode);
    this.errorCode = errorCode;
    this.httpStatus = Optional.ofNullable(httpStatus).orElse(DEFAULT_SERVER_ERROR_CODE);
  }

  public RequestToProviderException(
      final Integer httpStatus, String responseMessage, Exception ex) {
    super(responseMessage, ex);
    this.errorCode = this.getMessage();
    this.httpStatus = Optional.ofNullable(httpStatus).orElse(DEFAULT_SERVER_ERROR_CODE);
  }

  public RequestToProviderException(
      final Integer httpStatus, String responseCode, String responseMessage, Exception ex) {
    super(responseMessage, ex);
    this.errorCode = responseCode;
    this.httpStatus = Optional.ofNullable(httpStatus).orElse(DEFAULT_SERVER_ERROR_CODE);
  }

  private RequestToProviderException(int httpStatus, String responseMessage) {
    super(responseMessage);
    this.errorCode = this.getMessage();
    this.httpStatus = Optional.ofNullable(httpStatus).orElse(DEFAULT_SERVER_ERROR_CODE);
  }

  public RequestToProviderException(Response<?> response) {
    this(
        Optional.ofNullable(response).map(Response::code).orElse(DEFAULT_SERVER_ERROR_CODE),
        extractErrorMessage(response));
  }

  public Integer getHttpStatus() {
    return httpStatus;
  }

  public static String extractErrorMessage(Response<?> response) {
    return Optional.ofNullable(response)
        .map(Response::body)
        .map(Object::toString)
        .orElse(
            Optional.ofNullable(response)
                .map(Response::errorBody)
                .map(b -> Try.tryThis(b::string).orElse(null))
                .orElse("Failed HTTP Request without a valid message."));
  }

  public String getErrorCode() {
    return this.errorCode;
  }
}
