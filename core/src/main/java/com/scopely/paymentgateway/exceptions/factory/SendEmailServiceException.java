package com.scopely.paymentgateway.exceptions.factory;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayRuntimeException;
import java.io.Serial;

public class SendEmailServiceException extends PaymentGatewayRuntimeException {

  @Serial private static final long serialVersionUID = -2368752709430168566L;

  public SendEmailServiceException(String message, String... parameters) {
    super(String.format(message, (Object[]) parameters));
  }

  public SendEmailServiceException(Exception ex) {
    super(ex);
  }

  public SendEmailServiceException(Exception ex, String message, String... parameters) {
    super(String.format(message, (Object[]) parameters), ex);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.SEND_EMAIL_SERVICE_NOT_FOUND;
  }
}
