package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import java.io.Serial;

public class PaymentNotUpdatableException extends RuntimeException {

  @Serial private static final long serialVersionUID = -2624729327820137620L;

  public PaymentNotUpdatableException(
      PaymentProviderIdentifier paymentProviderIdentifier, String paymentId) {
    this(
        "Payment not updatable for paymentId "
            + paymentId
            + " and provider "
            + paymentProviderIdentifier);
  }

  public PaymentNotUpdatableException(String message) {
    super(message);
  }
}
