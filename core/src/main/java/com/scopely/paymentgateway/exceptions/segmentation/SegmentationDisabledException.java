package com.scopely.paymentgateway.exceptions.segmentation;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayException;
import java.io.Serial;

public class SegmentationDisabledException extends PaymentGatewayException {
  @Serial private static final long serialVersionUID = 7144579425993088438L;

  public SegmentationDisabledException(String message) {
    super(message);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.SEGMENTATION_DISABLED;
  }
}
