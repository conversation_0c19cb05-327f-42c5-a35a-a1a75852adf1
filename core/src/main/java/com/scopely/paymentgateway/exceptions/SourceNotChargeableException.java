package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class SourceNotChargeableException extends PaymentGatewayRuntimeException {
  @Serial private static final long serialVersionUID = -4035229973777436830L;

  public SourceNotChargeableException(String message) {
    super(message);
  }

  public SourceNotChargeableException(String message, Exception ex) {
    super(message, ex);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.INVALID_STATE_SOURCE;
  }
}
