package com.scopely.paymentgateway.exceptions.factory;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayRuntimeException;
import java.io.Serial;

public class RetrieveEmailFromProviderServiceException extends PaymentGatewayRuntimeException {

  @Serial private static final long serialVersionUID = 7067180635151931057L;

  public RetrieveEmailFromProviderServiceException(String message, String... parameters) {
    super(String.format(message, (Object[]) parameters));
  }

  public RetrieveEmailFromProviderServiceException(Exception ex) {
    super(ex);
  }

  public RetrieveEmailFromProviderServiceException(
      Exception ex, String message, String... parameters) {
    super(String.format(message, (Object[]) parameters), ex);
  }

  public RetrieveEmailFromProviderServiceException() {
    super("Service not found for selected provider");
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.RETRIEVE_EMAIL_SERVICE_NOT_FOUND;
  }
}
