package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import java.io.Serial;

public class PaymentProviderIntentionNotUpdatableException extends RuntimeException {

  @Serial static final long serialVersionUID = 7236409333838035452L;

  public PaymentProviderIntentionNotUpdatableException(
      PaymentProviderIdentifier paymentProviderIdentifier, String paymentId) {
    this(
        "Payment intention update not allowed to provider "
            + paymentProviderIdentifier
            + " with payment "
            + paymentId);
  }

  public PaymentProviderIntentionNotUpdatableException(String message) {
    super(message);
  }
}
