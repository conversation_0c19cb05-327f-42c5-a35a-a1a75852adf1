package com.scopely.paymentgateway.exceptions;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class PurchaseTokenRepositoryException extends PaymentGatewayRuntimeException {

  @Serial private static final long serialVersionUID = -597445807419711030L;

  public PurchaseTokenRepositoryException(Throwable cause) {
    super("Caused by: " + cause.toString());
    this.initCause(cause);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.UNABLE_TO_PERFORM_USER_OPERATION;
  }
}
