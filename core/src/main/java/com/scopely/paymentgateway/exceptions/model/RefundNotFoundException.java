package com.scopely.paymentgateway.exceptions.model;

import com.scopely.paymentgateway.constants.ErrorConstants;
import java.io.Serial;

public class RefundNotFoundException extends EntityNotFoundException {

  @Serial private static final long serialVersionUID = 141475795659670707L;

  public RefundNotFoundException(String refundId) {
    super(String.format("Refund with id %s does not exist", refundId));
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.REFUND_NOT_FOUND;
  }
}
