package com.scopely.paymentgateway.constants;

import com.scopely.proteus.monitoring.Tag;

public class StatsConstants {

  private StatsConstants() {}

  // region HITS & ERRORS
  public static final String DD_RESTEASY_CLIENT_HITS = "satellite.request.hits";
  public static final String DD_RESTEASY_CLIENT_ERRORS = "satellite.request.errors";
  // endregion

  // User metrics
  public static final String DD_ADMIN_USER_PROVIDER_UPDATE_HITS =
      "payments.user.provider.update.admin.hits";
  public static final String DD_ADMIN_USER_PROVIDER_UPDATE_ERRORS =
      "payments.user.provider.update.admin.errors";

  // region BACKFILL
  public static final String DD_BACKFILL_HITS = "payments.backfill.hits";
  public static final String DD_BACKFILL_ITEMS = "payments.backfill.items";

  public static final String DD_BACKFILL_ITEMS_ERRORS = "payments.backfill.items.errors";
  public static final String DD_BACKFILL_ERRORS = "payments.satellite.errors";
  public static final String DD_BACKFILL_STOPPED = "payments.satellite.stopped";

  public static final String DD_BACKFILL_UNCLAIMED_HITS = "payments.backfill.unclaimed.hits";
  public static final String DD_BACKFILL_UNCLAIMED_ERRORS_ITEMS =
      "payments.backfill.unclaimed.errors.items";

  public static final String DD_BACKFILL_REVERSED_AT_ITEMS = "payments.backfill.reversed.items";
  public static final String DD_BACKFILL_REVERSED_AT_ERRORS_ITEMS =
      "payments.backfill.reversed.errors.items";
  // endregion

  // region FEES
  public static final String DD_FEES_NOT_FOUND = "fees.not.found";
  public static final String DD_FEES_TASK_LOAD_HITS = "fees.loading.hits";
  public static final String DD_FEES_TASK_LOAD_ERROR = "fees.loading.error";
  // endregion

  // region TRANSLATIONS
  // Use locale requested
  public static final String DD_LOCALE_LOAD_DATA_HITS = "locale.data.load.hits";
  public static final String DD_LOCALE_LOAD_DATA_ERROR = "locale.data.load.error";
  public static final String DD_LOCALE_REQUEST_HITS = "locale.validation.hits";
  public static final String DD_LOCALE_REQUEST_LANGUAGE = "locale.validation.language";
  public static final String DD_LOCALE_REQUEST_DEFAULT = "locale.validation.default";
  public static final String DD_LOCALE_REQUEST_ERROR = "locale.validation.error";
  // endregion

  // region REPOSITORY OPERATIONS
  public static final String DD_REPOSITORY_OPERATION_SAVE = "repository.operation.save";
  public static final String DD_REPOSITORY_OPERATION_GET = "repository.operation.get";
  // endregion

  // region PAYMENTS Total income:
  public static final String DD_TOTAL_INCOME = "payment.total.income";
  public static final String DD_TOTAL_PAYMENTS = "payment.total";
  public static final String DD_TOTAL_REFUND = "refund.total";
  public static final String DD_TOTAL_CHECKOUTS = "payment.checkout.total";
  public static final String DD_PAYMENT_LOAD_EMAIL_INVALID = "payment.load.email.invalid";
  // endregion

  // region PAYMENTS UnclaimedByGame:
  public static final String DD_UNCLAIMED_PAYMENTS_BY_GAME = "unclaimed.payments.by.game";
  // endregion

  // region SEGMENTATION
  public static final String DD_CHECKOUT_VIP_UPDATE = "checkout.segmentation.update.errors";
  // endregion

  // region PRICE LOCALIZATION
  public static final String DD_COUNTRY_GET_LOCALIZATION_PRICES_HITS =
      "country_get_localization_prices.get.hits";
  public static final String DD_COUNTRY_GET_LOCALIZATION_PRICES_ERROR =
      "country_get_localization_prices.get.error";
  public static final String DD_COUNTRY_LOAD_LOCALIZATION_PRICES_HITS =
      "country_get_localization_prices.load.hits";
  public static final String DD_COUNTRY_LOAD_LOCALIZATION_PRICES_ERROR =
      "country_get_localization_prices.load.error";
  public static final String DD_PRICE_PROVIDER_FEES_HITS = "provider.fees.load.hits";
  public static final String DD_PRICE_PROVIDER_FEES_ERROR = "provider.fees.load.error";
  public static final String DD_SAVE_LOCALIZATION_PRICES_HITS = "localization_prices.save.hits";
  public static final String DD_SAVE_LOCALIZATION_PRICES_ERROR = "localization_prices.save.error";
  public static final String DD_SAVE_LOCALIZATION_PRICES_ERROR_ACTIVE =
      "localization_prices.save.error.active";
  public static final String DD_SAVE_LOCALIZATION_PRICES_ERROR_HISTORIC =
      "localization_prices.save.error.historic";
  public static final String DD_COUNTRY_GET_LOCALIZATION_PRICES_INVALID_COUNTRY_ERROR =
      "country_get_localization_prices.get.invalid_country.error";

  // endregion

  // region COUNTRY EXCHANGE RATE
  public static final String DD_PRICE_EXCHANGE_RATES_HITS = "country.exchange.rates.load.hits";
  public static final String DD_PRICE_EXCHANGE_RATES_ERROR = "country.exchange.rates.load.error";
  // endregion

  // region PAYMENT LOCATION
  public static final String DD_GEOLOCATION_GET_BY_IP_HITS = "geolocation.get_by.ip.hits";
  public static final String DD_DEFAULT_GEOLOCATION_VALUES = "geolocation.use.default.values";
  public static final String DD_GEOLOCATION_GET_BY_IP_ERRORS = "geolocation.get_by.ip.errors";
  public static final String DD_GET_LOCATION_BY_COUNTRY_HITS =
      "geolocation.get_by.country_code.hits";
  public static final String DD_PAYMENT_LOCATION_NOT_SUPPORTED =
      "payment.geolocation.country.not_supported";
  // endregion

  // region CONFIG VALIDATION
  public static final String DD_CLIENT_CONFIGURATION_ERROR = "client.configuration.error";
  // endregion

  // region AB TESTING
  public static final String DD_GET_PAYMENT_FEATURES_ERROR = "get_payment_features_abtest.error";
  public static final String DD_FETCH_USER_EXPERIMENTS_HITS = "fetch_user_experiments.hits";
  public static final String DD_FETCH_USER_EXPERIMENTS_ERROR = "fetch_user_experiments.error";
  public static final String DD_PARSE_USER_EXPERIMENT_ERROR = "parse_user_experiments.error";
  public static final String DD_LOAD_FEATURES_FILE_HITS = "get_payment_features.load.hits";
  public static final String DD_LOAD_FEATURES_FILE_ERROR = "get_payment_features.load.error";
  // endregion

  // region PAYMENT VALIDATION
  public static final String DD_PAYMENT_INVALID_ALREADY_PROCESSED =
      "payment.invalid.already.processed";
  public static final String DD_PAYMENT_INVALID_EXPIRED = "payment.invalid.expired";
  public static final String DD_PAYMENT_INVALID_REJECTED = "payment.invalid.rejected";
  public static final String DD_PAYMENT_EXTERNAL_ID_DUPLICATED = "payment.externalId.duplicated";
  public static final String DD_PAYMENT_MISMATCH_AMOUNT = "payment.mismatch.amount";
  public static final String DD_PAYMENT_CREATION_ID_ALREADY_EXISTS_ERROR =
      "payment.creation.id.already_exists.error";
  public static final String DD_PAYMENT_CONTEXT_PROPERTIES_SENT = "payment.context.properties.sent";
  public static final String DD_PAYMENT_CONTEXT_PROPERTIES_NOT_SENT =
      "payment.context.properties.not.sent";
  public static final String DD_PAYMENT_CONTEXT_PROPERTIES_EXCEED_LIMIT =
      "payment.context.properties.exceeds.limit";
  public static final String DD_PAYMENT_DEVICE_TOKEN_NULL = "payment.parameter.device-token.null";
  // endregion

  // region queues
  public static final String DD_QUEUE_PARSER_HITS = "queue.forwarder.parser.hits";
  public static final String DD_QUEUE_PARSER_ERROR = "queue.forwarder.parser.error";
  public static final String DD_QUEUE_FORWARDER_HITS = "queue.forwarder.hits";
  public static final String DD_QUEUE_FORWARDER_EMPTY = "queue.forwarder.consumers.empty";
  public static final String DD_QUEUE_PROCESSOR_HITS = "queue.forwarder.processor.hits";
  public static final String DD_QUEUE_PROCESSOR_ERROR = "queue.forwarder.processor.error";
  public static final String DD_QUEUE_FORWARDER_ERROR = "queue.forwarder.error";
  // endregion

  // region WEBHOOKS
  public static final String DD_WEBHOOK_AUTO_FORWARD_EVENT = "payment.webhook.auto.forward.event";
  public static final String DD_WEBHOOK_REDIRECT_HITS = "payment.webhook.redirect.hit";
  public static final String DD_WEBHOOK_REDIRECT_URL_ERROR = "payment.webhook.redirect.url.error";
  public static final String DD_WEBHOOK_REDIRECT_ERROR = "payment.webhook.redirect.error";
  public static final String DD_WEBHOOK_PROCESSOR_HITS = "payment.webhook.processor.hit";
  public static final String DD_WEBHOOK_UNAUTHORIZED = "payment.webhook.unauthorized";
  public static final String DD_WEBHOOK_PROCESSOR_ERROR = "payment.webhook.processor.error";
  public static final String DD_TAXES_TASK_LOAD_HITS = "taxes.loading.hits";
  public static final String DD_TAXES_TASK_LOAD_ERROR = "taxes.loading.error";
  public static final String DD_WEBHOOK_MISMATCH_TAXES_CALCULATION_ERROR =
      "payment.mismatch.taxes.calculation.error";
  public static final String DD_WEBHOOK_XSOLLA_OTHER_PAYMENT_METHOD =
      "payment.webhook.xsolla.payment_method.other";
  public static final String DD_WEBHOOK_XSOLLA_DISPUTE_SUCCESS =
      "payment.webhook.xsolla.dispute.success";
  public static final String DD_WEBHOOK_XSOLLA_DISPUTE_ERROR =
      "payment.webhook.xsolla.dispute.error";
  public static final String DD_WEBHOOK_XSOLLA_DISPUTE_NO_PAYMENT_ID =
      "payment.webhook.xsolla.dispute.nopaymentid";
  public static final String DD_WEBHOOK_XSOLLA_CHARGEBACK_SUCCESS =
      "payment.webhook.xsolla.chargeback.success";
  public static final String DD_WEBHOOK_XSOLLA_CHARGEBACK_ERROR =
      "payment.webhook.xsolla.chargeback.error";
  // endregion

  // region EMAIL
  public static final String DD_PAYMENT_SENDING_EMAIL_HITS = "email.send.hits";
  public static final String DD_PAYMENT_SENDING_EMAIL_ERROR = "email.send.error";
  public static final String FACTORY_RETRIEVE_SERVICE_ERROR = "factory.get.service.error";
  public static final String DD_EMAIL_PROVIDER_HITS = "email.provider.hits";
  public static final String DD_EMAIL_PROVIDER_ERROR = "email.provider.error";
  public static final String DD_EMAIL_PROVIDER_RESPONSE_FAILURE = "email.provider.response.failure";
  // endregion

  // region fraud notification
  public static final String DD_FRAUD_NOTIFICATION_TO_PROVIDER_HITS =
      "fraud.notification.to.provider.hits";
  public static final String DD_FRAUD_NOTIFICATION_TO_PROVIDER_ERROR =
      "fraud.notification.to.provider.error";
  public static final String DD_FRAUD_NOTIFICATION_TO_CUSTOMER_SUPPORT_HITS =
      "fraud.notification.to.support.hits";
  public static final String DD_FRAUD_NOTIFICATION_TO_CUSTOMER_SUPPORT_ERROR =
      "fraud.notification.to.support.error";
  //

  // region AUTOREFUND
  public static final String DD_AUTOREFUND_QUEUE_HITS = "autorefund.queue.hits";
  public static final String DD_AUTOREFUND_QUEUE_ERRORS = "autorefund.queue.error";
  public static final String DD_AUTOREFUND_PAYMENT_INVALID = "autorefund.payment.invalid";
  // endregion

  // region TITAN
  public static final String DD_TITAN_SOFT_FAILURE = "titan.failure.soft";
  public static final String DD_TITAN_HARD_FAILURE = "titan.failure.hard";
  public static final String DD_TITAN_SHUTDOWN_QUEUE = "titan.queue.shutdown";
  public static final String DD_TITAN_SUCCESS = "titan.success";

  public static final String DD_PAYMENT_TITAN_EVENT_MISSING_TRACKING_ID =
      "payment.titan.event.missing.tracking_id";
  public static final String DD_PAYMENT_AB_TESTING_FAILURE = "payment.titan.ab.testing.failure";
  // endregion

  // region DR WEBHOOK
  public static final String DD_DR_ORDER_ACCEPTED_WEBHOOK_ERROR =
      "payment.digitalriver.webhook.order.accepted.error";
  public static final String DD_DR_ORDER_ACCEPTED_WEBHOOK_FULFILLED_ERROR =
      "payment.digitalriver.webhook.order.accepted.error.fulfilled";
  public static final String DD_DR_ORDER_ACCEPTED_WEBHOOK_SUCCESS =
      "payment.digitalriver.webhook.order.accepted.success";
  public static final String DD_DR_ORDER_FAIL_WEBHOOK_SUCCESS =
      "payment.digitalriver.webhook.order.fail.success";
  public static final String DD_DR_ORDER_COMPLETE_WEBHOOK_ERROR =
      "payment.digitalriver.webhook.order.complete.error";
  public static final String DD_DR_ORDER_COMPLETE_WEBHOOK_SUCCESS =
      "payment.digitalriver.webhook.order.complete.success";
  public static final String DD_DR_DISPUTE_OPEN_WEBHOOK_SUCCESS =
      "payment.digitalriver.webhook.dispute.open.success";
  public static final String DD_DR_DISPUTE_OPEN_WEBHOOK_ERROR =
      "payment.digitalriver.webhook.dispute.open.error";
  public static final String DD_DR_DISPUTE_RESOLVED_WEBHOOK_SUCCESS =
      "payment.digitalriver.webhook.dispute.resolved.success";
  public static final String DD_DR_DISPUTE_RESOLVED_WEBHOOK_ERROR =
      "payment.digitalriver.webhook.dispute.resolved.error";
  public static final String DD_DR_CHARGEBACK_WEBHOOK_SUCCESS =
      "payment.digitalriver.webhook.chargeback.success";
  public static final String DD_DR_CHARGEBACK_WEBHOOK_ERROR =
      "payment.digitalriver.webhook.chargeback.error";
  public static final String DD_DR_CHARGEBACK_WEBHOOK_QUEUEING_ERROR =
      "payment.digitalriver.webhook.chargeback.queueing.error";
  // endregion

  // region B-LOGIC
  public static final String DD_DOMAIN_CREATE_PAYMENT_TRY = "payment.create.try";
  public static final String DD_DOMAIN_CREATE_PAYMENT_BLOCKED = "payment.create.blocked";
  public static final String DD_DOMAIN_CREATE_PAYMENT_DUPLICATED_EXTERNAL_ID =
      "payment.create.duplicated.external.id";
  public static final String DD_DOMAIN_CREATE_PAYMENT_SUCCESS = "payment.create.success";
  public static final String DD_CHECKOUT_CREATION_RULE_HITS = "payment.checkout.creation.hits";
  public static final String DD_CHECKOUT_CREATION_ERROR = "payment.checkout.creation.error";
  public static final String DD_CHECKOUT_ERROR_REVENUE = "payment.checkout.error.revenue";
  public static final String DD_CHECKOUT_PROVIDER_FALLBACK = "payment.checkout.provider.fallback";
  public static final String DD_DOMAIN_MARK_PAYMENT_COMPLETED = "payment.status.completed";
  public static final String DD_DOMAIN_MARK_PAYMENT_UPDATED = "payment.status.updated";
  public static final String DD_DOMAIN_CLAIM_PAYMENT_NOT_COMPLETED = "payment.claim.not.completed";
  public static final String DD_DOMAIN_CLAIM_PAYMENT_ALREADY_CLAIMED =
      "payment.claim.already.claimed";
  public static final String DD_TOTAL_CLAIMS_SUCCESS = "payment.claim.success";

  public static final String DD_API_CALLS_EXECUTOR = "payment.external.api.request";
  public static final String DD_API_CALLS_EXECUTOR_HISTOGRAM =
      "payment.external.api.request.histogram";
  // endregion

  // region TAGS
  public static final String TAG_API_KEY = "apiKey";
  public static final String TAG_EMAIL_PROVIDER = "emailProvider";
  public static final String TAG_RESOURCE_NAME = "resource_name";
  public static final String TAG_METHOD = "method";
  public static final String TAG_HTTP_CODE = "httpCode";
  public static final String TAG_ERROR_CODE = "errorCode";
  public static final String TAG_API = "api";
  public static final String TAG_REJECTED = "rejectedReason";
  public static final String TAG_STATUS = "status";
  public static final String TAG_RESULT = "result";
  public static final String TAG_CALLER = "caller";
  public static final String TAG_API_EVENT_NAME = "eventName";
  public static final String TAG_WEBHOOK_EVENT_NAME = "webhookEventName";
  public static final String TAG_PROVIDER = "provider";
  public static final String TAG_SANDBOX = "sandbox";

  public static final String TAG_PROVIDER_FALLBACK = "providerFallback";
  public static final String TAG_PAYMENT_METHOD = "paymentMethod";
  public static final String TAG_PRICING_MODE = "pricingMode";
  public static final String TAG_COUNTRY = "country";
  public static final String TAG_CURRENCY = "currency";

  public static final String TAG_STEP_ID = "stepId";
  public static final String TAG_DELIMITER = ":";

  public static final String TAG_TYPE = "type";
  // TODO change this name
  public static final String TAG_SERVICE = "service";
  // endregion

  // region proteus-next Tag.Key's
  public static final Tag.Key TAG_KEY_API_KEY = Tag.key(TAG_API_KEY);
  // endregion

  // region API
  public static final String SEGMENTATION_API = "segmentation";
  public static final String MAILCHIMP_API = "mailchimp";
  public static final String ABTEST_API = "mailchimp";
  // endregion

  // region TRACES
  public static final String XSOLLA_WEBHOOK = "XsollaWebhooks";
  public static final String DR_WEBHOOK = "DRWebhooks";

  // endregion

  public enum OperationResults {
    UNHANDLED_ERROR
  }
}
