package com.scopely.paymentgateway.constants;

import java.util.Arrays;

public enum Currencies {
  USD,
  CAD,
  AUD,
  NZD,
  JPY,
  EUR,
  GBP,
  SEK,
  DKK,
  NOK,
  PLN,
  CHF,
  OTHER;

  public static Currencies fromCode(String code) {
    return Arrays.stream(Currencies.values())
        .filter(currency -> currency.name().equals(code))
        .findAny()
        .orElse(Currencies.OTHER);
  }
}
