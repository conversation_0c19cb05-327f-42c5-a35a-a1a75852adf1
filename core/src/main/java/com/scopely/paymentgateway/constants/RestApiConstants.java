package com.scopely.paymentgateway.constants;

// FIXME: split me into different files
public abstract class RestApiConstants {

  public static final String PATH_GENERIC_ACCESS = "playgami-payments";
  public static final String PATH_API_VERSION = "v1";
  public static final String PATH_APPS = "apps";

  public static final String PARAM_API_KEY = "apiKey";
  public static final String PARAM_COUNTRY = "country";
  public static final String PARAM_SCHEDULED_TASK_ID = "scheduledTaskId";
  public static final String PARAM_PAYMENT_ID = "paymentId";
  public static final String PARAM_REFUND_ID = "refundId";
  public static final String PARAM_ORDER_ID = "orderId";
  public static final String PARAM_USER_ID = "userId";
  public static final String PARAM_USER_ATTRIBUTE_NAME = "userAttributeName";
  public static final String PARAM_PROVIDER = "provider";
  public static final String PARAM_WITH_REVERSALS = "withReversals";
  public static final String HEADER_PAYMENT_ID = "X-Payment-Id";
  public static final String ROUTE_PAYMENT_WITH_PARAM = "/payment/{" + PARAM_PAYMENT_ID + "}";
  public static final String ROUTE_ROOT = PATH_API_VERSION + "/" + PATH_APPS;
  public static final String ROUTE_BASE = ROUTE_ROOT + "/{" + PARAM_API_KEY + "}";

  public static final String ROUTE_ROOT_PUBLIC = PATH_GENERIC_ACCESS + "/" + ROUTE_ROOT;
  public static final String ROUTE_BASE_PUBLIC = PATH_GENERIC_ACCESS + "/" + ROUTE_BASE;

  public static final String PROVIDER = "/provider";
  public static final String ROUTE_CHECKOUT = "/checkout";
  public static final String ROUTE_PAYMENT = "/payment";
  public static final String STATUS = "/status";
  public static final String ACTIVE = "/active";

  public static final String ROUTE_ITEM_SKUS = "/items";
  public static final String ROUTE_ALL_ITEMS_SKUS = ROUTE_ITEM_SKUS + "/all";
  public static final String ROUTE_VALIDATE_CSV_ITEMS_SKUS = ROUTE_ITEM_SKUS + "/validate";
  public static final String ROUTE_UPLOAD_CSV_LOCALIZED_PRICE = ROUTE_ITEM_SKUS + "/upload";
  public static final String ROUTE_UPDATE_CHECKOUT = ROUTE_PAYMENT_WITH_PARAM + "/checkout";
  public static final String ROUTE_UPDATE_CHECKOUT_WITH_HEADER = ROUTE_PAYMENT + "/checkout";
  public static final String ROUTE_PAYMENT_STATUS = ROUTE_PAYMENT_WITH_PARAM + STATUS;
  public static final String ROUTE_PAYMENT_STATUS_WITH_HEADER = ROUTE_PAYMENT + STATUS;
  public static final String ROUTE_PAYMENT_CLAIM = ROUTE_PAYMENT_WITH_PARAM + "/claim";
  public static final String ROUTE_PAYMENT_CLAIM_VALIDATE =
      ROUTE_PAYMENT_WITH_PARAM + "/claim/validate";

  public static final String USERS_ROUTE = "/users";
  public static final String ROUTE_USER_DATA = "/{" + PARAM_USER_ID + "}" + "/info";
  public static final String ROUTE_USER_DATA_ATTRIBUTE =
      ROUTE_USER_DATA + "/{" + PARAM_USER_ATTRIBUTE_NAME + "}";
  public static final String ROUTE_BLOCK_USERS = "/block";
  public static final String ROUTE_UNBLOCK_USERS = "/unblock";

  public static final String ROUTE_CHANGE_USERS_PROVIDER =
      USERS_ROUTE + PROVIDER + "/{" + PARAM_PROVIDER + "}";

  public static final String ROUTE_USER = ROUTE_BASE + "/users/{" + PARAM_USER_ID + "}";
  public static final String ROUTE_UNCLAIMED_PAYMENTS = "/payments/unclaimed";
  public static final String ROUTE_PAYMENT_BY_ID = ROUTE_PAYMENT_WITH_PARAM;
  public static final String ROUTE_PAYMENTS_BY_FILTER = "/payments/filter";
  public static final String ROUTE_REFUND_PAYMENT = ROUTE_PAYMENT_WITH_PARAM + "/refund";

  public static final String ROUTE_REFUND_BY_REFUND_ID = "/refund/{" + PARAM_REFUND_ID + "}";
  public static final String ROUTE_PAYMENTS_SUMMARY_BY_CSV = "/payments/csv";
  public static final String ROUTE_REFUNDS_BY_PAYMENT_ID = ROUTE_PAYMENT_WITH_PARAM + "/refunds";

  public static final String RESOURCE_PRICE_MANAGEMENT_CLAIM = "price-management";

  public static final String RESOURCE_PAYMENT_GATEWAY = "payments";
  public static final String ROUTE_PAYMENTS_BY_USER =
      "/{" + PARAM_USER_ID + "}/" + RESOURCE_PAYMENT_GATEWAY;

  public static final String WEBHOOK = "/webhook";
  public static final String SOURCE = "/source";
  public static final String SOURCE_DETACHMENT = SOURCE + "/detachment";
  public static final String DIGITAL_RIVER = "/digital_river";
  public static final String XSOLLA = "/xsolla";

  public static final String ROUTE_DR =
      PATH_API_VERSION + "/" + PATH_APPS + PROVIDER + DIGITAL_RIVER;
  public static final String ROUTE_XS_BASE = PATH_API_VERSION + "/" + PATH_APPS + PROVIDER + XSOLLA;
  public static final String ROUTE_XS_WEBHOOK = ROUTE_XS_BASE + "/{" + PARAM_API_KEY + "}";
  public static final String ROUTE_PAYMENT_BY_ORDER_ID = "/payment/order/{" + PARAM_ORDER_ID + "}";

  private static final String ADMIN_ROUTE = "/admin";
  public static final String ROUTE_BASE_ADMIN = ROUTE_BASE + ADMIN_ROUTE;
  public static final String ROUTE_USER_ADMIN = ROUTE_BASE + ADMIN_ROUTE + USERS_ROUTE;

  public static final String ROUTE_PAYMENT_CONFIG = "/config/payment-config";
  public static final String ROUTE_PAYMENT_CONFIG_MAPPING = "/ab-test-data";
  public static final String ROUTE_UPDATE_COUNTRY_EXCHANGES = PATH_API_VERSION + "/taxes";
  public static final String LOAD_ROUTE = "/load";
  public static final String ROUTE_TAXES_BY_COUNTRY = "{" + PARAM_COUNTRY + "}";

  // region PurchaseTokenEndpoint

  public static final String ROUTE_PAYMENT_DATA = "/paymentdata";
  public static final String ROUTE_PING_ALIVE = "/ping";
  public static final String PARAM_PURCHASE_TOKEN = "token";

  public static final String ROUTE_PAYMENT_ACTIVE_WITH_PURCHASE_TOKEN = ROUTE_PAYMENT + ACTIVE;

  // endregion

  public static final String RESOURCE_PROVIDER_BY_USER = "/provider";
  public static final String ROUTE_LOAD_PLAYER_PREFERENCES =
      USERS_ROUTE + RESOURCE_PROVIDER_BY_USER + "/{" + PARAM_PROVIDER + "}";
  public static final String ROUTE_RUN_SCHEDULED_TASK =
      "/tasks/scheduled/{" + PARAM_SCHEDULED_TASK_ID + "}";

  // region GameConfigEndpoint
  public static final String PARAM_NAME_PARAMETER = "name";
  public static final String ROUTE_CONFIG = "/config";
  public static final String ROUTE_GLOBAL = "/global";
  public static final String ROUTE_SANDBOX = "/sandbox";
  public static final String ROUTE_RAW_CONFIG = "/raw";
  public static final String ROUTE_GET_CONFIG =
      PATH_API_VERSION + "/" + PATH_APPS + ADMIN_ROUTE + ROUTE_CONFIG;
  public static final String ROUTE_GAME_CONFIG = "/{" + PARAM_API_KEY + "}";
  public static final String ROUTE_SANDBOX_CONFIG = "/{" + PARAM_API_KEY + "}" + ROUTE_SANDBOX;
  public static final String ROUTE_GLOBAL_SANDBOX_CONFIG = ROUTE_GLOBAL + ROUTE_SANDBOX;
  public static final String ROUTE_NEW_GAME_CONFIG = "/{" + PARAM_API_KEY + "}";
  public static final String ROUTE_DEL_GAME_CONFIG =
      "/{" + PARAM_API_KEY + "}" + "/{" + PARAM_PROVIDER + "}" + "/{" + PARAM_NAME_PARAMETER + "}";
  public static final String ROUTE_DEL_GLOBAL_CONFIG =
      ROUTE_GLOBAL + "/{" + PARAM_PROVIDER + "}" + "/{" + PARAM_NAME_PARAMETER + "}";

  public static final String ROUTE_DEL_SANDBOX_CONFIG =
      ROUTE_SANDBOX + "/{" + PARAM_PROVIDER + "}" + "/{" + PARAM_NAME_PARAMETER + "}";

  public static final String ROUTE_TOGGLE_SANDBOX = "/{" + PARAM_API_KEY + "}" + ROUTE_SANDBOX;
  // endregion

}
