package com.scopely.paymentgateway.constants;

import java.util.Locale;

public enum ErrorConstants {
  INVALID_EMAIL_SYNTAX,
  ERROR_SENDING_EMAIL,
  PAYMENT_NOT_FOUND,
  PAYMENT_NOT_COMPLETED,
  <PERSON>YMENT_REVERSED,
  REFUND_NOT_FOUND,
  CHECKOUT_NOT_FOUND,
  FEE_NOT_FOUND,
  EMAIL_TEMPLATE_PROCESSOR_NOT_FOUND,
  EMAIL_NOT_FOUND,
  EMAIL_SENT_RESPONSE_NOT_FOUND,
  LOCALIZED_PRICE,
  LOCALIZED_PRICE_FILE_NOT_FOUND,
  LOCALIZED_PRICE_RECORD_NOT_FOUND,
  PRICE_LOCALIZATION_CSV_PARSER_ERROR,
  LOCALIZED_PRICE_UPLOAD_EXCEPTION,
  REJECTED_EMAIL,
  INVALID_EMAIL_TEMPLATE,
  UNEXPECTED_ERROR,
  OPTIMISTIC_LOCKING_DEADLOCK,
  USER_NOT_FOUND,
  EXPIRED_PAYMENT,
  INVALID_PAYMENT,
  USER_BLOCKED,
  INVALID_USER_ATTRIBUTE,
  INVALID_BLOCK_REASON,
  UNABLE_TO_PERFORM_USER_OPERATION,
  EMAIL_PROVIDERS_NOT_AVAILABLE,
  TRANSLATION_KEY_NOT_FOUND,
  INVALID_STATE_SOURCE,
  SOURCE_NOT_FOUND,

  UNABLE_TO_PERFORM_REVERSAL_OPERATION,
  UNABLE_TO_PERFORM_DISPUTE_OPERATION,
  UNABLE_TO_PERFORM_CHARGEBACK_OPERATION,
  DUPLICATED_CHECKOUT,
  EXTERNAL_ID_CONFLICT,
  UNABLE_TO_SAVE_PAYMENT,
  DISPUTE_SERVICE_ERROR,
  DISPUTE_NOT_FOUND,
  CHARGEBACK_SERVICE_ERROR,
  CHARGEBACK_NOT_FOUND,
  PURCHASE_TOKEN_NOT_FOUND,
  INVALID_APIKEY,
  CUSTOMER_ID_NOT_CREATED,
  CUSTOMER_ID_NOT_UPDATED,
  UNABLE_TO_GET_PAYMENT_SYSTEM_CONFIGS,
  UNABLE_TO_GET_ABTEST_EXPERIMENTS,
  UNABLE_TO_GET_FRAUD_LIST,

  // Service not found errors
  RETRIEVE_EMAIL_SERVICE_NOT_FOUND,
  SEND_EMAIL_SERVICE_NOT_FOUND,
  REQUEST_TO_PROVIDER_ERROR,
  EVENT_TYPE_NOT_VALID,
  UNEXPECTED_WORKER_ERROR,
  UNPARSEABLE_EMAIL_EVENT,
  UNPARSEABLE_SQS_EVENT,
  UNPARSEABLE_USER_INFO,
  UNABLE_TO_RETRIEVE_SEGMENTS,
  SEGMENTATION_DISABLED,
  INTERNAL_ERROR,

  // Service startup error
  STARTUP_ERROR;

  @Override
  public String toString() {
    return this.name().toLowerCase(Locale.getDefault());
  }
}
