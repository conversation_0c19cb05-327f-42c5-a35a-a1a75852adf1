package com.scopely.paymentgateway.analytics.events;

// FIXME: change the name to TitanEventConstants
public class EventAttributesConstants {

  public static final String TRACKING_ID = "tracking_id";
  public static final String STEP_ID = "step_id";
  public static final String STEP_NAME = "step_name";
  public static final String STEP_CATEGORY = "step_category";
  public static final String STEP_SOURCE = "step_source";
  public static final String PRODUCT_NAME = "product_name";
  public static final String PROCESS_NAME = "process_name";
  public static final String PROCESS_ORDER = "process_order";
  public static final String PROCESS_ATTRIBUTES = "process_attributes";
  public static final String INTERACTION_TYPE = "interaction_type";
  public static final String INTERACTION_OBJECT = "interaction_object";
  public static final String INTERACTION_OBJECT_CATEGORY = "interaction_object_category";
  public static final String REFUND_ID = "refund_id";
  public static final String TRANSACTION_ID = "transaction_id";
  public static final String PROVIDER_REFUND_ID = "provider_refund_id";
  public static final String REFUND_TYPE = "refund_type";
  public static final String PAYMENT_PROVIDER = "payment_provider";
  public static final String PAYMENT_METHOD = "payment_method";
  public static final String STATUS = "status";
  public static final String STATUS_MESSAGE = "status_message";
  public static final String LOCAL_CURRENCY_TYPE = "local_currency_type";
  public static final String GROSS_AMOUNT_LOCAL = "gross_amount_local";
  public static final String GROSS_AMOUNT_US_PROVIDER = "gross_amount_us_provider";
  public static final String ERROR_MESSAGE = "error_message";
  public static final String PROVIDER_TRANSACTION_ID = "provider_transaction_id";
  public static final String SAVED_PAYMENT_METHOD = "saved_payment_method";
  public static final String STORE_SKU = "store_sku";
  public static final String GAME_SKU = "game_sku";
  public static final String NET_AMOUNT_LOCAL = "net_amount_local";
  public static final String TAXES_AMOUNT_LOCAL = "taxes_amount_local";
  public static final String NET_AMOUNT_US_PROVIDER = "net_amount_us_provider";
  public static final String TAXES_AMOUNT_US_PROVIDER = "taxes_amount_us_provider";
  public static final String PAYMENT_PRICING_MODE = "payment_pricing_mode";
  public static final String DISPUTE_ID = "dispute_id";
  public static final String PROVIDER_DISPUTE_ID = "provider_dispute_id";
  public static final String REASON = "reason";
  public static final String IS_VIP = "is_vip";
  public static final String IS_FALLBACK = "is_fallback";
  public static final String PROVIDER = "provider";
  public static final String IS_BLOCKED = "is_blocked";
  public static final String ACTION_TYPE = "action_type";
  public static final String USER_ID = "user_id";
  public static final String UNKNOWN = "UNKNOWN";
  public static final String UNIT_QUANTITY = "unit_quantity";
  public static final String DISPUTE_TYPE = "dispute_type";
  public static final String PAYMENT_INTERFACE = "payment_interface";
}
