package com.scopely.paymentgateway.analytics.events;

import static com.scopely.paymentgateway.analytics.events.EventAttributesConstants.*;
import static java.util.Objects.isNull;

import com.scopely.titan.model.ClientEventDto;
import com.scopely.titan.model.EventPriority;
import com.scopely.titan.model.EventType;
import com.scopely.titan.model.PrimitiveDictionary;
import java.math.BigDecimal;
import java.time.Clock;
import java.time.Instant;

public class PaymentTransactionEvent {

  private final Clock clock;
  private final String transactionId;
  private final String providerTransactionId;
  private final String trackingId;
  private final String paymentProvider;
  private final String paymentMethod;
  private final Boolean savedPaymentMethod;
  private final String status;
  private final String statusMessage;
  private final String storeSku;
  private final String gameSku;
  private final String localCurrencyType;
  private final BigDecimal grossAmountLocal;
  private final BigDecimal netAmountLocal;
  private final BigDecimal taxesAmountLocal;
  private final BigDecimal grossAmountUsProvider;
  private final BigDecimal netAmountUsProvider;
  private final BigDecimal taxesAmountUsProvider;
  private final String princingMode;
  private final int unitQuantity;

  public PaymentTransactionEvent(
      Clock clock,
      String transactionId,
      String providerTransactionId,
      String trackingId,
      String paymentProvider,
      String paymentMethod,
      Boolean savedPaymentMethod,
      String status,
      String statusMessage,
      String storeSku,
      String gameSku,
      String localCurrencyType,
      BigDecimal grossAmountLocal,
      BigDecimal netAmountLocal,
      BigDecimal taxesAmountLocal,
      BigDecimal grossAmountUsProvider,
      BigDecimal netAmountUsProvider,
      BigDecimal taxesAmountUsProvider,
      String pricingMode,
      int unitQuantity) {
    this.clock = clock;
    this.transactionId = transactionId;
    this.providerTransactionId = providerTransactionId;
    this.trackingId = trackingId;
    this.paymentProvider = paymentProvider;
    this.paymentMethod = paymentMethod;
    this.savedPaymentMethod = savedPaymentMethod;
    this.status = status;
    this.statusMessage = statusMessage;
    this.storeSku = storeSku;
    this.gameSku = gameSku;
    this.localCurrencyType = localCurrencyType;
    this.grossAmountLocal = grossAmountLocal;
    this.netAmountLocal = netAmountLocal;
    this.taxesAmountLocal = taxesAmountLocal;
    this.grossAmountUsProvider = grossAmountUsProvider;
    this.netAmountUsProvider = netAmountUsProvider;
    this.taxesAmountUsProvider = taxesAmountUsProvider;
    this.princingMode = pricingMode;
    this.unitQuantity = unitQuantity;
  }

  public ClientEventDto toEvent() {
    PrimitiveDictionary properties = new PrimitiveDictionary();

    putIfValid(properties, TRANSACTION_ID, this.transactionId);
    putIfValid(properties, PROVIDER_TRANSACTION_ID, this.providerTransactionId);
    putIfValid(properties, TRACKING_ID, this.trackingId);
    putIfValid(properties, PAYMENT_PROVIDER, this.paymentProvider);
    putIfValid(properties, PAYMENT_METHOD, this.paymentMethod);
    putIfValid(properties, SAVED_PAYMENT_METHOD, this.savedPaymentMethod);
    putIfValid(properties, STATUS, this.status);
    putIfValid(properties, STATUS_MESSAGE, this.statusMessage);
    putIfValid(properties, STORE_SKU, this.storeSku);
    putIfValid(properties, GAME_SKU, this.gameSku);
    putIfValid(properties, GAME_SKU, this.gameSku);
    putIfValid(properties, LOCAL_CURRENCY_TYPE, this.localCurrencyType);
    putIfValid(properties, GROSS_AMOUNT_LOCAL, this.grossAmountLocal);
    putIfValid(properties, NET_AMOUNT_LOCAL, this.netAmountLocal);
    putIfValid(properties, TAXES_AMOUNT_LOCAL, this.taxesAmountLocal);
    putIfValid(properties, GROSS_AMOUNT_US_PROVIDER, this.grossAmountUsProvider);
    putIfValid(properties, NET_AMOUNT_US_PROVIDER, this.netAmountUsProvider);
    putIfValid(properties, TAXES_AMOUNT_US_PROVIDER, this.taxesAmountUsProvider);
    putIfValid(properties, PAYMENT_PRICING_MODE, this.princingMode);
    putIfValid(properties, UNIT_QUANTITY, this.unitQuantity);

    ClientEventDto clientEventDto = new ClientEventDto();
    clientEventDto.setTime(Instant.now(this.clock).toEpochMilli());
    clientEventDto.setName(EventName.PLAYGAMI_PAYMENT_TRANSACTION.getName());
    clientEventDto.setEventType(EventType.GAME);
    clientEventDto.setPriority(EventPriority.MEDIUM);
    clientEventDto.setProperties(properties);

    return clientEventDto;
  }

  private void putIfValid(PrimitiveDictionary properties, String key, Object value) {
    if (!isNull(value)) {
      properties.put(key, value);
    }
  }
}
