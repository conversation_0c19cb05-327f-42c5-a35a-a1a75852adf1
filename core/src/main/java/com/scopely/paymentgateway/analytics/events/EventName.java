package com.scopely.paymentgateway.analytics.events;

public enum EventName {
  PLAYGAMI_PAYMENT_TRANSACTION("playgami.payment.transaction"),
  PLAYGAMI_PAYMENT_REFUND("playgami.payment.refund"),
  PLAYGAMI_TRACKING("playgami.tracking"),
  PLAYGAMI_PAYMENT_DISPUTE("playgami.payment_dispute"),
  PLAYGAMI_PAYMENT_USERBLOCK("playgami.payment_userblock");

  private final String name;

  EventName(final String name) {
    this.name = name;
  }

  public String getName() {
    return name;
  }
}
