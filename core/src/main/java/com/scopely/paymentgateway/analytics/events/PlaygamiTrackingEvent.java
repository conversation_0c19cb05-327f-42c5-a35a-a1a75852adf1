package com.scopely.paymentgateway.analytics.events;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.proteus.logging.Log;
import com.scopely.proteus.util.JacksonMapper;
import com.scopely.titan.model.ClientEventDto;
import com.scopely.titan.model.EventPriority;
import com.scopely.titan.model.EventType;
import com.scopely.titan.model.PrimitiveDictionary;
import java.time.Clock;
import java.util.Map;
import java.util.stream.Collectors;

public class PlaygamiTrackingEvent {

  private final Clock clock;

  private final String trackingId;

  private final StepIdEnum stepId;

  private final StepNameEnum stepName;

  private final StepCategoryEnum stepCategory;

  private final StepSourceEnum stepSource;

  private final ProductNameEnum productName;

  private final ProcessNameEnum processName;

  private final ProcessOrderEnum processOrder;

  private final Map<String, String> processAttributes;

  private final String transactionId;

  private final InteractionTypeEnum interactionType;

  private final InteractionObjectEnum interactionObject;

  private final InteractionObjectCategoryEnum interactionObjectCategory;

  private static final ObjectMapper MAPPER = JacksonMapper.MAPPER.copy();

  public PlaygamiTrackingEvent(
      Clock clock,
      String trackingId,
      StepIdEnum stepId,
      StepNameEnum stepName,
      StepCategoryEnum stepCategory,
      StepSourceEnum stepSource,
      ProductNameEnum productName,
      ProcessNameEnum processName,
      ProcessOrderEnum processOrder,
      Map<String, String> processAttributes,
      String transactionId,
      InteractionTypeEnum interactionType,
      InteractionObjectEnum interactionObject,
      InteractionObjectCategoryEnum interactionObjectCategory) {
    this.clock = clock;
    this.trackingId = trackingId;
    this.stepId = stepId;
    this.stepName = stepName;
    this.stepCategory = stepCategory;
    this.stepSource = stepSource;
    this.productName = productName;
    this.processName = processName;
    this.processOrder = processOrder;
    this.processAttributes = processAttributes;
    this.transactionId = transactionId;
    this.interactionType = interactionType;
    this.interactionObject = interactionObject;
    this.interactionObjectCategory = interactionObjectCategory;
  }

  public enum StepIdEnum {
    INIT_CHECKOUT_1("init_checkout_1"),
    PROVIDER_SELECTION_1("provider_selection_1"),
    PROCESS_PAYMENT_1("process_payment_1"),
    PROCESS_PAYMENT_2("process_payment_2"),
    BLOCKED_USER_CHECKOUT_1("blocked_user_checkout_1");

    private final String value;

    public String getValue() {
      return value;
    }

    StepIdEnum(String value) {
      this.value = value;
    }
  }

  public enum StepNameEnum {
    PROVIDER_SELECTION("provider_selection"),
    PAYMENT_PROCESS_STARTED("payment_process_started"),
    PAYMENT_PROCESS_UPDATED("payment_process_updated"),
    PAYMENT_PROCESS_FINISHED("payment_process_finished"),
    PAYMENT_PROCESS_CLAIMED("payment_process_claimed"),
    BLOCKED_USER_CHECKOUT("blocked_user_checkout");

    private final String value;

    public String getValue() {
      return value;
    }

    StepNameEnum(String value) {
      this.value = value;
    }
  }

  public enum StepCategoryEnum {
    PRE_CHECKOUT("pre_checkout"),
    CHECKOUT("checkout");

    private final String value;

    public String getValue() {
      return value;
    }

    StepCategoryEnum(String value) {
      this.value = value;
    }
  }

  public enum StepSourceEnum {
    CLIENT("client"),
    PLAYGAMI_SERVER("playgami_server");

    private final String value;

    public String getValue() {
      return value;
    }

    StepSourceEnum(String value) {
      this.value = value;
    }
  }

  public enum ProductNameEnum {
    PAYMENTS("payments");

    private final String value;

    public String getValue() {
      return value;
    }

    ProductNameEnum(String value) {
      this.value = value;
    }
  }

  public enum ProcessNameEnum {
    PAYMENT("payment");

    private final String value;

    public String getValue() {
      return value;
    }

    ProcessNameEnum(String value) {
      this.value = value;
    }
  }

  public enum ProcessOrderEnum {
    PROCESS_ORDER_1("1"),
    PROCESS_ORDER_2("2"),
    PROCESS_ORDER_3("3"),
    PROCESS_ORDER_4("4"),
    PROCESS_ORDER_5("5");

    private final String value;

    public String getValue() {
      return value;
    }

    ProcessOrderEnum(String value) {
      this.value = value;
    }
  }

  public enum InteractionTypeEnum {
    CLICK("click"),

    PROCESS_EXECUTION("process_execution");

    private final String value;

    public String getValue() {
      return value;
    }

    InteractionTypeEnum(String value) {
      this.value = value;
    }
  }

  public enum InteractionObjectEnum {
    PROVIDER_SELECTION("provider_selection"),
    PAYMENT_PROCESS_STARTED("payment_process_started"),
    PAYMENT_PROCESS_UPDATED("payment_process_updated"),
    PAYMENT_PROCESS_FINISHED("payment_process_finished"),
    PAYMENT_PROCESS_CLAIMED("payment_process_claimed"),
    BLOCKED_USER_CHECKOUT("blocked_user_checkout");

    private final String value;

    public String getValue() {
      return value;
    }

    InteractionObjectEnum(String value) {
      this.value = value;
    }
  }

  public enum InteractionObjectCategoryEnum {
    PROCESS("process");

    private final String value;

    public String getValue() {
      return value;
    }

    InteractionObjectCategoryEnum(String value) {
      this.value = value;
    }
  }

  public ClientEventDto toEvent() {
    PrimitiveDictionary properties = new PrimitiveDictionary();

    putIfValid(properties, EventAttributesConstants.TRACKING_ID, this.trackingId);
    putIfValid(properties, EventAttributesConstants.STEP_ID, this.stepId.getValue());
    putIfValid(properties, EventAttributesConstants.STEP_NAME, this.stepName.getValue());
    putIfValid(properties, EventAttributesConstants.STEP_CATEGORY, this.stepCategory.getValue());
    putIfValid(properties, EventAttributesConstants.STEP_SOURCE, this.stepSource.getValue());
    putIfValid(properties, EventAttributesConstants.PRODUCT_NAME, this.productName.getValue());
    putIfValid(properties, EventAttributesConstants.PROCESS_NAME, this.processName.getValue());
    putIfValid(properties, EventAttributesConstants.PROCESS_ORDER, this.processOrder.getValue());
    putIfValid(properties, EventAttributesConstants.TRANSACTION_ID, this.transactionId);
    putIfValid(
        properties, EventAttributesConstants.INTERACTION_TYPE, this.interactionType.getValue());
    putIfValid(
        properties, EventAttributesConstants.INTERACTION_OBJECT, this.interactionObject.getValue());
    putIfValid(
        properties,
        EventAttributesConstants.INTERACTION_OBJECT_CATEGORY,
        this.interactionObjectCategory.getValue());
    addProcessAttributes(properties);

    ClientEventDto clientEventDto = new ClientEventDto();
    clientEventDto.setTime(clock.millis());
    clientEventDto.setName(EventName.PLAYGAMI_TRACKING.getName());
    clientEventDto.setEventType(EventType.GAME);
    clientEventDto.setPriority(EventPriority.MEDIUM);
    clientEventDto.setProperties(properties);

    return clientEventDto;
  }

  private void addProcessAttributes(PrimitiveDictionary properties) {
    if (nonNull(this.processAttributes) && !this.processAttributes.isEmpty()) {
      try {
        putIfValid(
            properties,
            EventAttributesConstants.PROCESS_ATTRIBUTES,
            MAPPER.writeValueAsString(this.processAttributes));
      } catch (JsonProcessingException e) {
        Log.debug(e, "Unable to parse process attributes {}", this.processAttributes.toString());
        String processAttributesAsJson =
            "{"
                + this.processAttributes.entrySet().stream()
                    .map(
                        attribute ->
                            "\"" + attribute.getKey() + "\":\"" + attribute.getValue() + "\"")
                    .collect(Collectors.joining(", "))
                + "}";
        putIfValid(
            properties, EventAttributesConstants.PROCESS_ATTRIBUTES, processAttributesAsJson);
      }
    }
  }

  private void putIfValid(PrimitiveDictionary properties, String key, Object value) {
    if (!isNull(value)) {
      properties.put(key, value);
    }
  }
}
