package com.scopely.paymentgateway.analytics.events;

import com.scopely.titan.EventFactory;
import com.scopely.titan.model.BuildType;
import java.util.concurrent.ConcurrentHashMap;
import javax.inject.Inject;

class TitanEventFactory {

  private final ConcurrentHashMap<String, EventFactory> eventFactoryMap;

  @Inject
  TitanEventFactory() {
    this.eventFactoryMap = new ConcurrentHashMap<>();
  }

  EventFactory getInstance(String apiKey) {
    return eventFactoryMap.computeIfAbsent(
        apiKey, newApiKey -> new EventFactory(BuildType.PRODUCTION, newApiKey));
  }
}
