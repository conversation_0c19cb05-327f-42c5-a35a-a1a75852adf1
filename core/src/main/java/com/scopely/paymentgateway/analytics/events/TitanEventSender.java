package com.scopely.paymentgateway.analytics.events;

import static com.scopely.paymentgateway.constants.StatsConstants.*;
import static com.scopely.proteus.logging.Log.*;

import com.scopely.paymentgateway.config.TitanConfig;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.titan.TitanClient;
import com.scopely.titan.model.EventStreamEntry;
import com.timgroup.statsd.StatsDClient;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import net.jodah.failsafe.Failsafe;
import net.jodah.failsafe.RetryPolicy;

public class TitanEventSender implements Runnable {

  private final String apiKey;
  private final String name;
  private final TitanClient titanClient;
  private final TitanConfig config;
  private final EventStreamEntry eventStreamEntry;
  private final StatsDClient statsDClient;

  @Inject
  public TitanEventSender(
      String apiKey,
      String name,
      TitanConfig config,
      TitanClient titanClient,
      EventStreamEntry eventStreamEntry,
      StatsDClient statsDClient) {
    this.apiKey = apiKey;
    this.name = name;
    this.titanClient = titanClient;
    this.config = config;
    this.eventStreamEntry = eventStreamEntry;
    this.statsDClient = statsDClient;
  }

  @Override
  public void run() {
    try {
      Failsafe.with(getRetryPolicy())
          .onFailedAttempt(
              (throwable) -> {
                statsDClient.increment(DD_TITAN_SOFT_FAILURE, buildDDTags(apiKey, name));
                warn(throwable, "Attempt failed to send titan event. A retry will be scheduled");
              })
          .onSuccess(e -> statsDClient.increment(DD_TITAN_SUCCESS, buildDDTags(apiKey, name)))
          .run(() -> titanClient.sendEvent(eventStreamEntry));
    } catch (Exception e) {
      error(e, "Titan event could not be sent. Entry was {}", eventStreamEntry);
      statsDClient.increment(DD_TITAN_HARD_FAILURE, buildDDTags(apiKey, name));
    }
  }

  private RetryPolicy getRetryPolicy() {
    return new RetryPolicy()
        .withMaxRetries(config.titanMaxRetries())
        .withBackoff(
            config.titanEventsBackoffMillis(),
            config.titanEventsBackoffMaxDelayMillis(),
            TimeUnit.MILLISECONDS);
  }

  private String[] buildDDTags(final String apiKey, String name) {
    return MetricsUtils.buildTags(
        new String[] {TAG_API_KEY, TAG_API_EVENT_NAME}, new String[] {apiKey, name});
  }
}
