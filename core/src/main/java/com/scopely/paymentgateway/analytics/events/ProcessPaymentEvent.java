package com.scopely.paymentgateway.analytics.events;

import static java.util.Objects.isNull;

import com.scopely.titan.model.ClientEventDto;
import com.scopely.titan.model.EventPriority;
import com.scopely.titan.model.EventType;
import com.scopely.titan.model.PrimitiveDictionary;
import java.time.Clock;
import java.time.Instant;

public class ProcessPaymentEvent {

  private final Clock clock;

  private final String eventName;

  private final String trackingId;

  private final String paymentProvider;

  private final String errorMessage;

  public ProcessPaymentEvent(
      Clock clock,
      String eventName,
      String trackingId,
      String paymentProvider,
      String errorMessage) {
    this.clock = clock;
    this.eventName = eventName;
    this.trackingId = trackingId;
    this.paymentProvider = paymentProvider;
    this.errorMessage = errorMessage;
  }

  public String getTrackingId() {
    return trackingId;
  }

  public String getErrorMessage() {
    return errorMessage;
  }

  public ClientEventDto toEvent() {
    PrimitiveDictionary properties = new PrimitiveDictionary();
    putIfValid(properties, EventAttributesConstants.TRACKING_ID, this.trackingId);
    putIfValid(properties, EventAttributesConstants.PAYMENT_PROVIDER, this.paymentProvider);
    putIfValid(properties, EventAttributesConstants.ERROR_MESSAGE, this.errorMessage);

    ClientEventDto clientEventDto = new ClientEventDto();
    clientEventDto.setTime(Instant.now(this.clock).toEpochMilli());
    clientEventDto.setName(this.eventName);
    clientEventDto.setEventType(EventType.GAME);
    clientEventDto.setPriority(EventPriority.MEDIUM);
    clientEventDto.setProperties(properties);

    return clientEventDto;
  }

  private void putIfValid(PrimitiveDictionary properties, String key, Object value) {
    if (!isNull(value)) {
      properties.put(key, value);
    }
  }
}
