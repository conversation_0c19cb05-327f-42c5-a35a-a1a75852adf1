package com.scopely.paymentgateway.analytics.events;

import com.scopely.paymentgateway.model.payment.PaymentMethod;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeStatus;
import com.scopely.titan.model.ClientEventDto;
import com.scopely.titan.model.EventPriority;
import com.scopely.titan.model.EventType;
import com.scopely.titan.model.PrimitiveDictionary;
import java.math.BigDecimal;
import java.time.Clock;
import java.util.Objects;
import java.util.Optional;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
public interface DisputeEvent {

  String getDisputeId();

  DisputeType getDisputeType();

  String getTransactionId();

  Optional<String> getTrackingId();

  PaymentProviderIdentifier getPaymentProvider();

  Optional<PaymentMethod> getPaymentMethod();

  String getReason();

  DisputeStatus getStatus();

  String getLocalCurrency();

  BigDecimal getGrossAmountLocal();

  BigDecimal getGrossAmountUsProvider();

  BigDecimal getNetAmountLocal();

  BigDecimal getNetAmountUsProvider();

  BigDecimal getTaxesAmountLocal();

  BigDecimal getTaxesAmountUsProvider();

  boolean isVip();

  boolean isBlocked();

  default ClientEventDto toTitanEvent(Clock clock) {
    PrimitiveDictionary properties = new PrimitiveDictionary();
    putIfValid(properties, EventAttributesConstants.DISPUTE_ID, getDisputeId());
    putIfValid(
        properties, EventAttributesConstants.DISPUTE_TYPE, getDisputeType().getDescription());
    putIfValid(properties, EventAttributesConstants.TRANSACTION_ID, getTransactionId());
    putIfValid(properties, EventAttributesConstants.PROVIDER_DISPUTE_ID, getDisputeId());
    getTrackingId()
        .ifPresent(trackingId -> properties.put(EventAttributesConstants.TRACKING_ID, trackingId));
    putIfValid(properties, EventAttributesConstants.PAYMENT_PROVIDER, getPaymentProvider());
    getPaymentMethod()
        .map(PaymentMethod::getDescription)
        .ifPresent(method -> properties.put(EventAttributesConstants.PAYMENT_METHOD, method));
    putIfValid(properties, EventAttributesConstants.REASON, getReason());
    putIfValid(properties, EventAttributesConstants.STATUS, getStatus().getDescription());

    // Amounts
    putIfValid(properties, EventAttributesConstants.LOCAL_CURRENCY_TYPE, getLocalCurrency());
    putIfValid(properties, EventAttributesConstants.GROSS_AMOUNT_LOCAL, getGrossAmountLocal());
    putIfValid(
        properties, EventAttributesConstants.GROSS_AMOUNT_US_PROVIDER, getGrossAmountUsProvider());
    putIfValid(properties, EventAttributesConstants.NET_AMOUNT_LOCAL, getNetAmountLocal());
    putIfValid(
        properties, EventAttributesConstants.NET_AMOUNT_US_PROVIDER, getNetAmountUsProvider());
    putIfValid(properties, EventAttributesConstants.TAXES_AMOUNT_LOCAL, getTaxesAmountLocal());
    putIfValid(
        properties, EventAttributesConstants.TAXES_AMOUNT_US_PROVIDER, getTaxesAmountUsProvider());

    putIfValid(properties, EventAttributesConstants.IS_VIP, isVip());
    putIfValid(properties, EventAttributesConstants.IS_BLOCKED, isBlocked());

    ClientEventDto clientEventDto = new ClientEventDto();
    clientEventDto.setTime(clock.millis());
    clientEventDto.setName(EventName.PLAYGAMI_PAYMENT_DISPUTE.getName());
    clientEventDto.setEventType(EventType.GAME);
    clientEventDto.setPriority(EventPriority.MEDIUM);
    clientEventDto.setProperties(properties);

    return clientEventDto;
  }

  private static void putIfValid(PrimitiveDictionary properties, String key, Object value) {
    if (Objects.nonNull(value)) {
      properties.put(key, value);
    }
  }

  class Builder extends DisputeEvent_Builder {}
}
