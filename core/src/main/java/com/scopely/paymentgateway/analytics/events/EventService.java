package com.scopely.paymentgateway.analytics.events;

import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import com.scopely.paymentgateway.model.user.User;
import com.scopely.paymentgateway.model.user.UserBlockActionType;
import java.util.Map;

public interface EventService {

  void sendProviderSelectionEvent(Payment payment);

  void sendProcessPaymentStartedEvent(Payment payment);

  void sendPaymentUpdateEvent(Payment payment, Map<String, String> processAttributes);

  void sendFinishedPaymentEvent(Payment payment);

  void sendPaymentTransactionEvent(Payment payment);

  void sendRefundEvent(Refund refund, Payment payment);

  void sendClaimedPaymentEvent(Payment payment);

  void sendDisputeEvent(Dispute dispute, Payment payment, boolean isUserVip, boolean isUserBlocked);

  void sendUserBlockEvent(
      User user, String paymentId, UserBlockActionType userBlockActionType, boolean isUserVip);

  void sendPaymentBlockedEvent(Payment payment);
}
