package com.scopely.paymentgateway.analytics.events;

import static java.util.Objects.isNull;

import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.refund.RefundType;
import com.scopely.titan.model.ClientEventDto;
import com.scopely.titan.model.EventPriority;
import com.scopely.titan.model.EventType;
import com.scopely.titan.model.PrimitiveDictionary;
import java.math.BigDecimal;
import java.time.Clock;

public class RefundEvent {
  private final Clock clock;
  private final String eventName;
  private final String trackingId;
  private final String refundId;
  private final String transactionId;
  private final String providerRefundId;
  private final RefundType refundType;
  private final PaymentProviderIdentifier paymentProvider;
  private final String paymentMethod;
  private final String status;
  private final String statusMessage;
  private final String localCurrencyType;
  private final BigDecimal grossAmountLocal;
  private final BigDecimal getGrossAmountUsProvider;
  private final BigDecimal netAmountLocal;
  private final BigDecimal netAmountUsProvider;
  private final BigDecimal taxesAmountLocal;
  private final BigDecimal taxesAmountUsProvider;

  public RefundEvent(
      Clock clock,
      String trackingId,
      String refundId,
      String transactionId,
      String providerRefundId,
      RefundType refundType,
      PaymentProviderIdentifier paymentProvider,
      String paymentMethod,
      String status,
      String statusMessage,
      String localCurrencyType,
      BigDecimal grossAmountLocal,
      BigDecimal getGrossAmountUsProvider,
      BigDecimal netAmountLocal,
      BigDecimal netAmountUsProvider,
      BigDecimal taxesAmountLocal,
      BigDecimal taxesAmountUsProvider) {
    this.clock = clock;
    this.eventName = EventName.PLAYGAMI_PAYMENT_REFUND.getName();
    this.trackingId = trackingId;
    this.refundId = refundId;
    this.transactionId = transactionId;
    this.providerRefundId = providerRefundId;
    this.refundType = refundType;
    this.paymentProvider = paymentProvider;
    this.paymentMethod = paymentMethod;
    this.status = status;
    this.statusMessage = statusMessage;
    this.localCurrencyType = localCurrencyType;
    this.grossAmountLocal = grossAmountLocal;
    this.getGrossAmountUsProvider = getGrossAmountUsProvider;
    this.netAmountLocal = netAmountLocal;
    this.netAmountUsProvider = netAmountUsProvider;
    this.taxesAmountLocal = taxesAmountLocal;
    this.taxesAmountUsProvider = taxesAmountUsProvider;
  }

  public String getTrackingId() {
    return trackingId;
  }

  public String getRefundId() {
    return refundId;
  }

  public String getStatus() {
    return status;
  }

  public ClientEventDto toEvent() {
    PrimitiveDictionary properties = new PrimitiveDictionary();

    putIfValid(properties, EventAttributesConstants.TRACKING_ID, trackingId);
    putIfValid(properties, EventAttributesConstants.REFUND_ID, refundId);
    putIfValid(properties, EventAttributesConstants.TRANSACTION_ID, transactionId);
    putIfValid(properties, EventAttributesConstants.PROVIDER_REFUND_ID, providerRefundId);
    putIfValid(properties, EventAttributesConstants.REFUND_TYPE, refundType);
    putIfValid(properties, EventAttributesConstants.PAYMENT_PROVIDER, paymentProvider);
    putIfValid(properties, EventAttributesConstants.PAYMENT_METHOD, paymentMethod);
    putIfValid(properties, EventAttributesConstants.STATUS, status);
    putIfValid(properties, EventAttributesConstants.STATUS_MESSAGE, statusMessage);
    putIfValid(properties, EventAttributesConstants.LOCAL_CURRENCY_TYPE, localCurrencyType);
    putIfValid(properties, EventAttributesConstants.GROSS_AMOUNT_LOCAL, grossAmountLocal);
    putIfValid(
        properties, EventAttributesConstants.GROSS_AMOUNT_US_PROVIDER, getGrossAmountUsProvider);
    putIfValid(properties, EventAttributesConstants.NET_AMOUNT_LOCAL, netAmountLocal);
    putIfValid(properties, EventAttributesConstants.NET_AMOUNT_US_PROVIDER, netAmountUsProvider);
    putIfValid(properties, EventAttributesConstants.TAXES_AMOUNT_LOCAL, taxesAmountLocal);
    putIfValid(
        properties, EventAttributesConstants.TAXES_AMOUNT_US_PROVIDER, taxesAmountUsProvider);

    ClientEventDto clientEventDto = new ClientEventDto();
    clientEventDto.setTime(clock.millis());
    clientEventDto.setName(this.eventName);
    clientEventDto.setEventType(EventType.GAME);
    clientEventDto.setPriority(EventPriority.MEDIUM);
    clientEventDto.setProperties(properties);

    return clientEventDto;
  }

  private void putIfValid(PrimitiveDictionary properties, String key, Object value) {
    if (!isNull(value)) {
      properties.put(key, value);
    }
  }
}
