package com.scopely.paymentgateway.analytics.events;

import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.user.UserBlockActionType;
import com.scopely.titan.model.ClientEventDto;
import com.scopely.titan.model.EventPriority;
import com.scopely.titan.model.EventType;
import com.scopely.titan.model.PrimitiveDictionary;
import java.time.Clock;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Nullable;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
public interface UserBlockEvent {

  String getUserId();

  Optional<String> getTransactionId();

  Optional<String> getTrackingId();

  Optional<String> getReason();

  boolean isBlocked();

  @Nullable
  PaymentProviderIdentifier getPaymentProvider();

  boolean isVip();

  UserBlockActionType getActionType();

  default ClientEventDto toTitanEvent(Clock clock) {

    PrimitiveDictionary properties = new PrimitiveDictionary();
    putIfValid(properties, EventAttributesConstants.USER_ID, getUserId());
    getTransactionId().ifPresent(tx -> properties.put(EventAttributesConstants.TRANSACTION_ID, tx));
    getTrackingId()
        .ifPresent(trackingId -> properties.put(EventAttributesConstants.TRACKING_ID, trackingId));
    getReason().ifPresent(reason -> properties.put(EventAttributesConstants.REASON, reason));
    putIfValid(properties, EventAttributesConstants.IS_BLOCKED, isBlocked());
    putIfValid(
        properties,
        EventAttributesConstants.PAYMENT_PROVIDER,
        getPaymentProvider() == null ? EventAttributesConstants.UNKNOWN : getPaymentProvider());
    putIfValid(properties, EventAttributesConstants.IS_VIP, isVip());
    putIfValid(properties, EventAttributesConstants.ACTION_TYPE, getActionType().getDescription());

    ClientEventDto clientEventDto = new ClientEventDto();
    clientEventDto.setTime(clock.millis());
    clientEventDto.setName(EventName.PLAYGAMI_PAYMENT_USERBLOCK.getName());
    clientEventDto.setEventType(EventType.GAME);
    clientEventDto.setPriority(EventPriority.MEDIUM);
    clientEventDto.setProperties(properties);

    return clientEventDto;
  }

  private static void putIfValid(PrimitiveDictionary properties, String key, Object value) {
    if (Objects.nonNull(value)) {
      properties.put(key, value);
    }
  }

  class Builder extends UserBlockEvent_Builder {}
}
