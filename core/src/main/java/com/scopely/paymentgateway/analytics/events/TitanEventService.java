package com.scopely.paymentgateway.analytics.events;

import static com.scopely.paymentgateway.analytics.events.EventAttributesConstants.GAME_SKU;
import static com.scopely.paymentgateway.analytics.events.EventAttributesConstants.IS_FALLBACK;
import static com.scopely.paymentgateway.analytics.events.EventAttributesConstants.IS_VIP;
import static com.scopely.paymentgateway.analytics.events.EventAttributesConstants.PAYMENT_INTERFACE;
import static com.scopely.paymentgateway.analytics.events.EventAttributesConstants.PAYMENT_METHOD;
import static com.scopely.paymentgateway.analytics.events.EventAttributesConstants.PROVIDER;
import static com.scopely.paymentgateway.analytics.events.EventAttributesConstants.STATUS;
import static com.scopely.paymentgateway.analytics.events.EventAttributesConstants.UNIT_QUANTITY;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_TITAN_EVENT_MISSING_TRACKING_ID;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_TITAN_SHUTDOWN_QUEUE;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_STEP_ID;
import static com.scopely.proteus.logging.Log.debug;
import static com.scopely.proteus.logging.Log.error;
import static com.scopely.proteus.logging.Log.info;
import static com.scopely.proteus.logging.Log.warn;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.scopely.paymentgateway.config.TitanConfig;
import com.scopely.paymentgateway.model.checkout.ProviderItem;
import com.scopely.paymentgateway.model.dto.BigDecimalHelper;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentFeaturesKey;
import com.scopely.paymentgateway.model.payment.PaymentMethod;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import com.scopely.paymentgateway.model.user.User;
import com.scopely.paymentgateway.model.user.UserBlockActionType;
import com.scopely.paymentgateway.model.user.UserBlockedReason;
import com.scopely.paymentgateway.services.features.FeaturesService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.PaymentAmountCalculator;
import com.scopely.proteus.util.StringUtils;
import com.scopely.titan.ContextPropertiesProvider;
import com.scopely.titan.EventFactory;
import com.scopely.titan.TitanClient;
import com.scopely.titan.model.BuildType;
import com.scopely.titan.model.ClientEventDto;
import com.scopely.titan.model.EventStreamEntry;
import com.scopely.titan.model.GameContext;
import com.timgroup.statsd.StatsDClient;
import java.math.BigDecimal;
import java.time.Clock;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class TitanEventService implements EventService {
  private static final String DEVICE_TOKEN_AUX = "00000000-0000-0000-0000-000000000000";

  final ThreadPoolExecutor asyncTitanSenderExecutor;
  private final Map<String, EventFactory> eventFactoryMap;

  private final TitanConfig titanConfig;
  private final TitanClient titanClient;
  private final StatsDClient statsDClient;
  private final LinkedBlockingQueue<Runnable> asyncTasksQueue;
  private final Clock clock;

  @Inject
  public TitanEventService(
      TitanConfig titanConfig, TitanClient titanClient, StatsDClient statsDClient, Clock clock) {
    this.titanConfig = titanConfig;
    this.titanClient = titanClient;
    this.statsDClient = statsDClient;
    this.clock = clock;

    asyncTasksQueue = new LinkedBlockingQueue<>(titanConfig.titanEventsMaxQueueSize());
    this.eventFactoryMap = new ConcurrentHashMap<>();

    this.asyncTitanSenderExecutor =
        new ThreadPoolExecutor(
            1,
            titanConfig.titanEventsThreadPoolSize(),
            10L,
            TimeUnit.SECONDS,
            asyncTasksQueue,
            new ThreadFactoryBuilder().setNameFormat("titan-event-sender-%d").build(),
            new LoggingRejectedExecutionHandler());
  }

  private static class LoggingRejectedExecutionHandler implements RejectedExecutionHandler {

    @Override
    public void rejectedExecution(Runnable runnable, ThreadPoolExecutor executor) {
      if (!(runnable instanceof TitanEventSender)) {
        return;
      }
      if (executor.isTerminating() || executor.isTerminated()) {
        warn("Titan event could not be sent because titan sender is shutting down.");
      } else {
        error(
            "Titan event could not be sent since async submission process cannot process more requests");
      }
    }
  }

  public void shutdownTitanEventSender(int secondsToWait) {
    try {
      info("Shutting down titan event async sender");
      asyncTitanSenderExecutor.shutdown();
      if (asyncTitanSenderExecutor.awaitTermination(secondsToWait, TimeUnit.SECONDS)) {
        info("Async sender shutdown gracefully");
      } else {
        int pendingTasks = asyncTasksQueue.size();
        warn(
            "Async titan sender was shutdown before all tasks could be completed. "
                + "Approximate number of entries not sent: {}",
            pendingTasks + asyncTitanSenderExecutor.getActiveCount());
        statsDClient.count(DD_TITAN_SHUTDOWN_QUEUE, pendingTasks);
      }
    } catch (InterruptedException e) {
      warn("InterruptedException while waiting async titan sender to shutdown gracefully", e);
      Thread.currentThread().interrupt();
    } finally {
      asyncTitanSenderExecutor.shutdownNow();
    }
  }

  @Override
  public void sendProviderSelectionEvent(Payment payment) {
    String trackingId = payment.getTrackingId();
    String apiKey = payment.getApiKey();
    if (StringUtils.isEmpty(trackingId)) {
      sendNoTrackingIdInstrumentation(apiKey, EventName.PLAYGAMI_TRACKING);
      return;
    }

    PlaygamiTrackingEvent event =
        new PlaygamiTrackingEvent(
            clock,
            trackingId,
            PlaygamiTrackingEvent.StepIdEnum.PROVIDER_SELECTION_1,
            PlaygamiTrackingEvent.StepNameEnum.PROVIDER_SELECTION,
            PlaygamiTrackingEvent.StepCategoryEnum.PRE_CHECKOUT,
            PlaygamiTrackingEvent.StepSourceEnum.PLAYGAMI_SERVER,
            PlaygamiTrackingEvent.ProductNameEnum.PAYMENTS,
            PlaygamiTrackingEvent.ProcessNameEnum.PAYMENT,
            PlaygamiTrackingEvent.ProcessOrderEnum.PROCESS_ORDER_1,
            Map.of(
                PROVIDER,
                payment.getProviderData().getProvider().name(),
                IS_FALLBACK,
                String.valueOf(payment.isProviderFallback()),
                PAYMENT_INTERFACE,
                FeaturesService.getFeatureValue(payment, PaymentFeaturesKey.WIDGET_UI_VERSION)),
            payment.getPaymentId(),
            PlaygamiTrackingEvent.InteractionTypeEnum.PROCESS_EXECUTION,
            PlaygamiTrackingEvent.InteractionObjectEnum.PROVIDER_SELECTION,
            PlaygamiTrackingEvent.InteractionObjectCategoryEnum.PROCESS);

    sendEventWithContext(
        event.toEvent(),
        apiKey,
        payment.getUserId(),
        payment.getDeviceToken(),
        payment.getTitanContextProperties());
  }

  @Override
  public void sendPaymentUpdateEvent(Payment payment, Map<String, String> processAttributes) {

    String trackingId = payment.getTrackingId();
    String apiKey = payment.getApiKey();
    if (StringUtils.isEmpty(trackingId)) {
      sendNoTrackingIdInstrumentation(apiKey, EventName.PLAYGAMI_TRACKING);
      return;
    }

    PlaygamiTrackingEvent event =
        new PlaygamiTrackingEvent(
            clock,
            trackingId,
            PlaygamiTrackingEvent.StepIdEnum.PROCESS_PAYMENT_1,
            PlaygamiTrackingEvent.StepNameEnum.PAYMENT_PROCESS_UPDATED,
            PlaygamiTrackingEvent.StepCategoryEnum.CHECKOUT,
            PlaygamiTrackingEvent.StepSourceEnum.PLAYGAMI_SERVER,
            PlaygamiTrackingEvent.ProductNameEnum.PAYMENTS,
            PlaygamiTrackingEvent.ProcessNameEnum.PAYMENT,
            PlaygamiTrackingEvent.ProcessOrderEnum.PROCESS_ORDER_2,
            processAttributes,
            payment.getPaymentId(),
            PlaygamiTrackingEvent.InteractionTypeEnum.PROCESS_EXECUTION,
            PlaygamiTrackingEvent.InteractionObjectEnum.PAYMENT_PROCESS_UPDATED,
            PlaygamiTrackingEvent.InteractionObjectCategoryEnum.PROCESS);

    sendEventWithContext(
        event.toEvent(),
        apiKey,
        payment.getUserId(),
        payment.getDeviceToken(),
        payment.getTitanContextProperties());
  }

  @Override
  public void sendProcessPaymentStartedEvent(Payment payment) {
    String trackingId = payment.getTrackingId();
    String apiKey = payment.getApiKey();
    String paymentMethodUsed =
        Optional.ofNullable(payment.getPaymentMethodUsed())
            .map(PaymentMethod::getDescription)
            .orElse(PaymentMethod.UNKNOWN.getDescription());

    if (StringUtils.isEmpty(trackingId)) {
      sendNoTrackingIdInstrumentation(apiKey, EventName.PLAYGAMI_TRACKING);
      return;
    }

    PlaygamiTrackingEvent event =
        new PlaygamiTrackingEvent(
            clock,
            trackingId,
            PlaygamiTrackingEvent.StepIdEnum.PROCESS_PAYMENT_1,
            PlaygamiTrackingEvent.StepNameEnum.PAYMENT_PROCESS_STARTED,
            PlaygamiTrackingEvent.StepCategoryEnum.CHECKOUT,
            PlaygamiTrackingEvent.StepSourceEnum.PLAYGAMI_SERVER,
            PlaygamiTrackingEvent.ProductNameEnum.PAYMENTS,
            PlaygamiTrackingEvent.ProcessNameEnum.PAYMENT,
            PlaygamiTrackingEvent.ProcessOrderEnum.PROCESS_ORDER_3,
            Map.of(
                PAYMENT_METHOD,
                paymentMethodUsed,
                UNIT_QUANTITY,
                String.valueOf(payment.getItemData().getQuantity())),
            payment.getPaymentId(),
            PlaygamiTrackingEvent.InteractionTypeEnum.PROCESS_EXECUTION,
            PlaygamiTrackingEvent.InteractionObjectEnum.PAYMENT_PROCESS_STARTED,
            PlaygamiTrackingEvent.InteractionObjectCategoryEnum.PROCESS);

    sendEventWithContext(
        event.toEvent(),
        apiKey,
        payment.getUserId(),
        payment.getDeviceToken(),
        payment.getTitanContextProperties());
  }

  @Override
  public void sendFinishedPaymentEvent(Payment payment) {
    String trackingId = payment.getTrackingId();
    String apiKey = payment.getApiKey();
    String paymentMethodUsed =
        Optional.ofNullable(payment.getPaymentMethodUsed())
            .map(PaymentMethod::getDescription)
            .orElse(null);
    if (StringUtils.isEmpty(trackingId)) {
      sendNoTrackingIdInstrumentation(apiKey, EventName.PLAYGAMI_TRACKING);
      return;
    }

    PlaygamiTrackingEvent event =
        new PlaygamiTrackingEvent(
            clock,
            trackingId,
            PlaygamiTrackingEvent.StepIdEnum.PROCESS_PAYMENT_1,
            PlaygamiTrackingEvent.StepNameEnum.PAYMENT_PROCESS_FINISHED,
            PlaygamiTrackingEvent.StepCategoryEnum.CHECKOUT,
            PlaygamiTrackingEvent.StepSourceEnum.PLAYGAMI_SERVER,
            PlaygamiTrackingEvent.ProductNameEnum.PAYMENTS,
            PlaygamiTrackingEvent.ProcessNameEnum.PAYMENT,
            PlaygamiTrackingEvent.ProcessOrderEnum.PROCESS_ORDER_4,
            Map.of(
                PAYMENT_METHOD,
                paymentMethodUsed,
                STATUS,
                payment.getPaymentStatus().getDescription()),
            payment.getPaymentId(),
            PlaygamiTrackingEvent.InteractionTypeEnum.PROCESS_EXECUTION,
            PlaygamiTrackingEvent.InteractionObjectEnum.PAYMENT_PROCESS_FINISHED,
            PlaygamiTrackingEvent.InteractionObjectCategoryEnum.PROCESS);

    sendEventWithContext(
        event.toEvent(),
        apiKey,
        payment.getUserId(),
        payment.getDeviceToken(),
        payment.getTitanContextProperties());
  }

  @Override
  public void sendClaimedPaymentEvent(Payment payment) {
    String trackingId = payment.getTrackingId();
    String apiKey = payment.getApiKey();
    if (StringUtils.isEmpty(trackingId)) {
      sendNoTrackingIdInstrumentation(apiKey, EventName.PLAYGAMI_TRACKING);
      return;
    }

    PlaygamiTrackingEvent event =
        new PlaygamiTrackingEvent(
            clock,
            trackingId,
            PlaygamiTrackingEvent.StepIdEnum.PROCESS_PAYMENT_2,
            PlaygamiTrackingEvent.StepNameEnum.PAYMENT_PROCESS_CLAIMED,
            PlaygamiTrackingEvent.StepCategoryEnum.CHECKOUT,
            PlaygamiTrackingEvent.StepSourceEnum.PLAYGAMI_SERVER,
            PlaygamiTrackingEvent.ProductNameEnum.PAYMENTS,
            PlaygamiTrackingEvent.ProcessNameEnum.PAYMENT,
            PlaygamiTrackingEvent.ProcessOrderEnum.PROCESS_ORDER_5,
            Map.of(
                GAME_SKU,
                Optional.ofNullable(payment.getItemData().getInternalSku()).orElse("unknown")),
            payment.getPaymentId(),
            PlaygamiTrackingEvent.InteractionTypeEnum.PROCESS_EXECUTION,
            PlaygamiTrackingEvent.InteractionObjectEnum.PAYMENT_PROCESS_CLAIMED,
            PlaygamiTrackingEvent.InteractionObjectCategoryEnum.PROCESS);

    sendEventWithContext(
        event.toEvent(),
        apiKey,
        payment.getUserId(),
        payment.getDeviceToken(),
        payment.getTitanContextProperties());
  }

  @Override
  public void sendPaymentTransactionEvent(Payment payment) {
    if (StringUtils.isEmpty(payment.getTrackingId())) {
      sendNoTrackingIdInstrumentation(payment.getApiKey(), EventName.PLAYGAMI_PAYMENT_TRANSACTION);
      return;
    }

    var event =
        new PaymentTransactionEvent(
            clock,
            payment.getPaymentId(),
            payment.getOrderId(),
            payment.getTrackingId(),
            payment.getProviderData().getProvider().getDescription(),
            Optional.ofNullable(payment.getPaymentMethodUsed())
                .map(PaymentMethod::getDescription)
                .orElse(null),
            payment.isSavedPaymentMethod(),
            payment.getPaymentStatus().getDescription(),
            PaymentStatus.FAILED.equals(payment.getPaymentStatus())
                ? payment.getErrorMessage()
                : null,
            Optional.ofNullable(payment.getItemData().getProviderItem())
                .map(ProviderItem::getSku)
                .orElse(null),
            payment.getItemData().getInternalSku(),
            payment.getPriceData().getLocalPrice().getCurrency(),
            payment.getPriceData().getLocalPrice().getTotalAmount(),
            payment.getPriceData().getLocalPrice().getSubtotalAmount(),
            payment.getPriceData().getLocalPrice().getTaxAmount(),
            payment.getPriceData().getBasePrice().getTotalAmount(),
            payment.getPriceData().getBasePrice().getSubtotalAmount(),
            payment.getPriceData().getBasePrice().getTaxAmount(),
            payment.getPriceData().getPricingMode().getDescription(),
            payment.getItemData().getQuantity());

    sendEventWithContext(
        event.toEvent(),
        payment.getApiKey(),
        payment.getUserId(),
        payment.getDeviceToken(),
        payment.getTitanContextProperties());
  }

  @Override
  public void sendRefundEvent(Refund refund, Payment payment) {
    BigDecimal localTaxAmount =
        PaymentAmountCalculator.calculateTaxAmount(
            refund.getRefundedLocalAmount().getNumberStripped(), payment);
    BigDecimal baseTaxAmount = PaymentAmountCalculator.calculateBaseAmount(localTaxAmount, payment);
    BigDecimal localSubtotalAmount =
        refund.getRefundedLocalAmount().getNumberStripped().subtract(localTaxAmount);
    BigDecimal baseSubtotalAmount =
        refund.getRefundedAmount().getNumberStripped().subtract(baseTaxAmount);

    RefundEvent event =
        new RefundEvent(
            clock,
            payment.getTrackingId(),
            refund.getRefundId(),
            refund.getPaymentId(),
            refund.getRefundId(),
            refund.getRefundType(),
            Objects.requireNonNull(refund.getProviderData()).getProvider(),
            Objects.requireNonNull(refund.getPaymentMethodUsed()).getDescription(),
            refund.getStatus().getDescription(),
            refund.getFailureReason(),
            refund.getRefundedLocalAmount().getCurrency().getCurrencyCode(),
            refund.getRefundedLocalAmount().getNumberStripped(),
            refund.getRefundedAmount().getNumberStripped(),
            localSubtotalAmount,
            baseSubtotalAmount,
            localTaxAmount,
            baseTaxAmount);

    sendEventWithContext(
        event.toEvent(),
        payment.getApiKey(),
        payment.getUserId(),
        payment.getDeviceToken(),
        payment.getTitanContextProperties());
  }

  @Override
  public void sendDisputeEvent(
      Dispute dispute, Payment payment, boolean isUserVip, boolean isUserBlocked) {
    BigDecimal disputeLocalTotalAmount = BigDecimalHelper.asPlainDecimal(dispute.getLocalAmount());
    BigDecimal disputeBaseTotalAmount = BigDecimalHelper.asPlainDecimal(dispute.getAmount());
    BigDecimal localTaxAmount =
        PaymentAmountCalculator.calculateTaxAmount(disputeLocalTotalAmount, payment);
    BigDecimal baseTaxAmount = PaymentAmountCalculator.calculateBaseAmount(localTaxAmount, payment);
    BigDecimal disputeLocalSubtotalAmount =
        BigDecimalHelper.asPlainDecimal(disputeLocalTotalAmount.subtract(localTaxAmount));
    BigDecimal disputeBaseSubtotalAmount =
        BigDecimalHelper.asPlainDecimal(disputeBaseTotalAmount.subtract(baseTaxAmount));
    BigDecimal paymentLocalTotalAmount =
        BigDecimalHelper.asPlainDecimal(payment.getPriceData().getLocalPrice().getTotalAmount());
    DisputeType disputeType =
        paymentLocalTotalAmount.equals(disputeLocalTotalAmount)
            ? DisputeType.TOTAL
            : DisputeType.PARTIAL;

    var event =
        new DisputeEvent.Builder()
            .setDisputeId(dispute.getDisputeId())
            .setDisputeType(disputeType)
            .setTransactionId(dispute.getPaymentId())
            .setNullableTrackingId(payment.getTrackingId())
            .setPaymentProvider(payment.getProviderData().getProvider())
            .setNullablePaymentMethod(payment.getPaymentMethodUsed())
            .setReason(dispute.getDisputeReason())
            .setStatus(dispute.getStatus())
            .setLocalCurrency(dispute.getLocalAmount().getCurrency().getCurrencyCode())
            .setGrossAmountLocal(disputeLocalTotalAmount)
            .setGrossAmountUsProvider(disputeBaseTotalAmount)
            .setNetAmountLocal(disputeLocalSubtotalAmount)
            .setNetAmountUsProvider(disputeBaseSubtotalAmount)
            .setTaxesAmountLocal(localTaxAmount)
            .setTaxesAmountUsProvider(baseTaxAmount)
            .setVip(isUserVip)
            .setBlocked(isUserBlocked)
            .build();
    var titanEvent = event.toTitanEvent(clock);
    sendEventWithContext(
        titanEvent,
        payment.getApiKey(),
        payment.getUserId(),
        payment.getDeviceToken(),
        payment.getTitanContextProperties());
  }

  @Override
  public void sendPaymentBlockedEvent(Payment payment) {
    String trackingId = payment.getTrackingId();
    String apiKey = payment.getApiKey();
    String userId = payment.getUserId();
    if (StringUtils.isEmpty(trackingId)) {
      sendNoTrackingIdInstrumentation(apiKey, EventName.PLAYGAMI_TRACKING);
      return;
    }

    PlaygamiTrackingEvent event =
        new PlaygamiTrackingEvent(
            clock,
            trackingId,
            PlaygamiTrackingEvent.StepIdEnum.BLOCKED_USER_CHECKOUT_1,
            PlaygamiTrackingEvent.StepNameEnum.BLOCKED_USER_CHECKOUT,
            PlaygamiTrackingEvent.StepCategoryEnum.PRE_CHECKOUT,
            PlaygamiTrackingEvent.StepSourceEnum.PLAYGAMI_SERVER,
            PlaygamiTrackingEvent.ProductNameEnum.PAYMENTS,
            PlaygamiTrackingEvent.ProcessNameEnum.PAYMENT,
            PlaygamiTrackingEvent.ProcessOrderEnum.PROCESS_ORDER_1,
            Map.of(
                GAME_SKU,
                Optional.ofNullable(payment.getItemData().getInternalSku()).orElse("unknown"),
                IS_VIP,
                String.valueOf(payment.isVip())),
            payment.getPaymentId(),
            PlaygamiTrackingEvent.InteractionTypeEnum.PROCESS_EXECUTION,
            PlaygamiTrackingEvent.InteractionObjectEnum.BLOCKED_USER_CHECKOUT,
            PlaygamiTrackingEvent.InteractionObjectCategoryEnum.PROCESS);

    sendEventWithContext(
        event.toEvent(),
        apiKey,
        userId,
        payment.getDeviceToken(),
        payment.getTitanContextProperties());
  }

  @Override
  public void sendUserBlockEvent(
      User user, String paymentId, UserBlockActionType userBlockActionType, boolean isUserVip) {

    var event =
        new UserBlockEvent.Builder()
            .setUserId(user.getUserId())
            .setNullableTransactionId(paymentId)
            .setReason(
                Optional.ofNullable(user.getBlockedReason())
                    .map(UserBlockedReason::getBlockedReasonValue))
            .setBlocked(user.isBlocked())
            .setPaymentProvider(user.getProvider())
            .setVip(isUserVip)
            .setActionType(userBlockActionType)
            .build();

    var titanEvent = event.toTitanEvent(clock);
    sendEventWithoutContext(titanEvent, user.getApiKey(), user.getUserId(), null);
  }

  private void sendEventWithContext(
      ClientEventDto event,
      String apiKey,
      String userId,
      String deviceToken,
      String contextProperties) {
    sendEvent(
        event, apiKey, userId, deviceToken, ContextPropertiesProvider.ofValue(contextProperties));
  }

  private void sendEventWithoutContext(
      ClientEventDto event, String apiKey, String userId, String deviceToken) {
    sendEvent(event, apiKey, userId, deviceToken, ContextPropertiesProvider.unavailable());
  }

  private void sendEvent(
      ClientEventDto event,
      String apiKey,
      String userId,
      String deviceToken,
      ContextPropertiesProvider contextPropertiesProvider) {
    asyncTitanSenderExecutor.execute(
        new TitanEventSender(
            apiKey,
            event.getName(),
            titanConfig,
            titanClient,
            createEventStreamEntry(
                apiKey, getDeviceTokenValue(deviceToken), userId, event, contextPropertiesProvider),
            statsDClient));
  }

  private EventStreamEntry createEventStreamEntry(
      String apiKey,
      String deviceToken,
      String userId,
      ClientEventDto clientEventDto,
      ContextPropertiesProvider contextPropertiesProvider) {
    return getEventFactoryByApiKey(apiKey)
        .builder(Collections.singletonList(clientEventDto), GameContext.serverContext(userId))
        .withDeviceToken(deviceToken)
        .withContextPropertiesProvider(contextPropertiesProvider)
        .build();
  }

  private EventFactory getEventFactoryByApiKey(String apiKey) {
    return eventFactoryMap.computeIfAbsent(
        apiKey, newApiKey -> new EventFactory(BuildType.PRODUCTION, newApiKey));
  }

  private void sendNoTrackingIdInstrumentation(String apiKey, EventName eventName) {
    debug(
        "Tracking id was null. No track event will be sent for event "
            + eventName
            + " and api key "
            + apiKey);
    statsDClient.increment(
        DD_PAYMENT_TITAN_EVENT_MISSING_TRACKING_ID,
        MetricsUtils.buildTags(
            new String[] {TAG_API_KEY, TAG_STEP_ID}, new Object[] {apiKey, eventName.getName()}));
  }

  private String getDeviceTokenValue(String deviceToken) {
    return deviceToken == null || deviceToken.isEmpty() ? DEVICE_TOKEN_AUX : deviceToken;
  }
}
