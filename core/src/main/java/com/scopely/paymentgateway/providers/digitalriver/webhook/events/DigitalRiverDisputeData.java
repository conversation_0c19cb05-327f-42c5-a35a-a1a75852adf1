package com.scopely.paymentgateway.providers.digitalriver.webhook.events;

import com.scopely.paymentgateway.providers.digitalriver.model.order.FraudStateTransitions;
import com.scopely.paymentgateway.providers.digitalriver.model.order.StateTransitions;
import javax.annotation.Nullable;
import org.inferred.freebuilder.FreeBuilder;
import org.javamoney.moneta.Money;

@FreeBuilder
public interface DigitalRiverDisputeData extends DigitalRiverRequestData {

  String getRequestId();

  String getOrderId();

  String getState();

  StateTransitions getStateTransitions();

  @Nullable
  String getFraudState();

  @Nullable
  FraudStateTransitions getFraudStateTransitions();

  Money getOrderTotalAmount();

  Money getAvailableToRefundAmount();

  class Builder extends DigitalRiverDisputeData_Builder {}
}
