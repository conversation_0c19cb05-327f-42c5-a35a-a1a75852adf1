package com.scopely.paymentgateway.providers.digitalriver.model.checkout;

import static com.scopely.paymentgateway.constants.LocationConstants.DEFAULT_COUNTRY;
import static java.util.Objects.nonNull;

import com.scopely.paymentgateway.model.payment.BillingAddress;
import com.scopely.paymentgateway.model.payment.ItemData;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.providers.digitalriver.model.Metadata;
import com.scopely.paymentgateway.providers.digitalriver.model.order.Address;
import com.scopely.paymentgateway.providers.digitalriver.model.order.BillTo;
import com.scopely.paymentgateway.services.payment.PaymentCreationContext;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

public record CheckoutRequest(
    String customerId,
    String currency,
    String sourceId,
    Boolean taxInclusive,
    String browserIp,
    PurchaseLocation purchaseLocation,
    BillTo billTo,
    List<DigitalRiverItem> items,
    String email,
    String applicationId,
    String upstreamId,
    Metadata metadata) {

  private static final String NO_SOURCE_ID = "";

  // This method is used for updates since Dynamic Prices
  // Update DRCheckout
  public CheckoutRequest(
      com.scopely.paymentgateway.model.payment.Payment payment,
      String browserIP,
      String customerId,
      String sourceId,
      BillingAddress billingAddress,
      String groupSku,
      String localCurrencyCode,
      boolean taxIncluded,
      String email,
      String drApiKey) {
    this(
        customerId,
        localCurrencyCode,
        sourceId,
        taxIncluded,
        browserIP,
        nonNull(billingAddress)
            ? new PurchaseLocation(
                billingAddress.getCountry(),
                billingAddress.getState(),
                billingAddress.getPostalCode())
            : new PurchaseLocation(null, null, null),
        nonNull(billingAddress)
            ? new BillTo(
                new Address(
                    billingAddress.getAddressLine1(),
                    billingAddress.getCity(),
                    billingAddress.getPostalCode(),
                    billingAddress.getState(),
                    billingAddress.getCountry()),
                null,
                null,
                null,
                null)
            : new BillTo(new Address(null, null, null, null, null), null, null, null, null),
        List.of(
            new DigitalRiverItem(
                payment.getItemData().getName(),
                payment.getItemData().getProductType(),
                DEFAULT_COUNTRY,
                Objects.requireNonNull(payment.getItemData().getUnitLocalPrice())
                    .getNumberStripped(),
                groupSku,
                payment.isVip(),
                drApiKey,
                payment.getItemData().getQuantity())),
        email,
        payment.getApiKey(),
        payment.getPaymentId(),
        new Metadata(payment.getUserId()));
  }

  // Create DRCheckout
  public static CheckoutRequest fromRequestData(
      PaymentCreationContext context,
      String groupSku,
      String email,
      String postalCode,
      String customerId,
      String drApiKey) {

    PriceData priceData = context.getTotalPriceData();
    ItemData itemData = context.getItemData();
    BigDecimal unitPrice = Objects.requireNonNull(itemData.getUnitLocalPrice()).getNumberStripped();

    return new CheckoutRequest(
        customerId,
        priceData.getOriginalLocalPrice().getCurrency().getCurrencyCode(),
        NO_SOURCE_ID, // This is important if we are dealing with saved creditcards, this makes the
        // checkout to be created without the default customer source.
        priceData.isTaxIncluded(),
        context.getPaymentLocation().getIpAddress(),
        new PurchaseLocation(
            context.getPaymentLocation().getCountry(),
            context.getPaymentLocation().getState(),
            context.getPaymentLocation().getPostalCode()),
        new BillTo(
            new Address(null, null, postalCode, null, context.getPaymentLocation().getCountry()),
            null,
            null,
            null,
            null),
        List.of(
            new DigitalRiverItem(
                itemData.getName(),
                itemData.getProductType(),
                DEFAULT_COUNTRY,
                unitPrice,
                groupSku,
                context.isVipUser(),
                drApiKey,
                itemData.getQuantity())),
        email,
        context.getApiKey(),
        context.getPaymentId(),
        new Metadata(context.getUserId()));
  }
}
