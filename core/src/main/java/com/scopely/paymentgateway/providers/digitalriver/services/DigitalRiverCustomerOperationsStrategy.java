package com.scopely.paymentgateway.providers.digitalriver.services;

import static com.google.common.collect.ImmutableMap.of;

import com.scopely.paymentgateway.exceptions.CustomerIdException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.customer.Customer;
import com.scopely.paymentgateway.model.customer.CustomerId;
import com.scopely.paymentgateway.model.payment.User;
import com.scopely.paymentgateway.repositories.CustomerIdRepository;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.customer.CustomerOperationsStrategy;
import com.scopely.paymentgateway.services.customer.ExternalCustomerIdService;
import com.scopely.paymentgateway.services.customer.GenerateApiKeyCustomerId;
import com.scopely.paymentgateway.services.payment.PaymentCreationContext;
import com.scopely.proteus.logging.Log;
import com.scopely.proteus.util.validator.EmailValidator;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DigitalRiverCustomerOperationsStrategy implements CustomerOperationsStrategy {

  private static final String CUSTOMER_ID = "CUSTOMER_ID";

  private final CustomerIdRepository customerIdRepository;
  private final DigitalRiverPaymentProviderService digitalRiverPaymentProviderService;
  private final ClientConfigurationService clientConfigurationService;
  private final GenerateApiKeyCustomerId generateApiKeyCustomerId;
  private final ExternalCustomerIdService externalCustomerIdService;

  @Inject
  public DigitalRiverCustomerOperationsStrategy(
      CustomerIdRepository customerIdRepository,
      DigitalRiverPaymentProviderService digitalRiverPaymentProviderService,
      ClientConfigurationService clientConfigurationService,
      GenerateApiKeyCustomerId generateApiKeyCustomerId,
      ExternalCustomerIdService externalCustomerIdService) {
    this.customerIdRepository = customerIdRepository;
    this.digitalRiverPaymentProviderService = digitalRiverPaymentProviderService;
    this.clientConfigurationService = clientConfigurationService;
    this.generateApiKeyCustomerId = generateApiKeyCustomerId;
    this.externalCustomerIdService = externalCustomerIdService;
  }

  @Override
  public Optional<Customer> getCustomer(String userId, String apiKey, boolean sandbox) {

    ClientConfiguration clientConfiguration =
        clientConfigurationService.getConfiguration(
            ConfigurationProviderIdentifier.DIGITAL_RIVER, apiKey, sandbox);

    String customerId =
        this.getExternalCustomerId(userId, apiKey)
            .orElseThrow(() -> new CustomerIdException("Customer ID not found"));
    return digitalRiverPaymentProviderService
        .getCustomer(customerId, clientConfiguration)
        .map(customer -> Customer.Builder.from(customer).setId(userId).build())
        .or(
            () ->
                Optional.of(
                    new Customer.Builder().setExternalId(customerId).setId(userId).build()));
  }

  @Override
  public String updateAndGetOrCreateCustomer(PaymentCreationContext context) {
    String internalCustomerId =
        generateApiKeyCustomerId.execute(context.getUserId(), context.getApiKey());

    Optional<String> externalCustomerIdOpt =
        getExternalCustomerId(context.getUserId(), context.getApiKey());

    if (externalCustomerIdOpt.isPresent()) {
      String externalCustomerId = externalCustomerIdOpt.get();
      updateCustomer(externalCustomerId, context);
      return externalCustomerId;
    } else {
      return generateAndSaveNewExternalId(internalCustomerId, context);
    }
  }

  @Override
  public Optional<String> getExternalCustomerId(String userId, String apiKey) {
    String internalCustomerId = generateApiKeyCustomerId.execute(userId, apiKey);
    Optional<CustomerId> customerId = customerIdRepository.getCustomerId(internalCustomerId);
    return customerId.map(CustomerId::getExternalId);
  }

  private void updateCustomer(String externalCustomerId, PaymentCreationContext context) {
    User user = context.getRequestUserInfo();
    if (null == user.getEmail()) {
      return;
    }
    ClientConfiguration clientConfiguration =
        clientConfigurationService.getConfiguration(
            ConfigurationProviderIdentifier.DIGITAL_RIVER,
            context.getApiKey(),
            context.isSandbox());
    try {
      digitalRiverPaymentProviderService.updateCustomer(
          externalCustomerId, context.getApiKey(), validateUserData(user), clientConfiguration);
    } catch (RequestToProviderException e) {
      Log.withMetadata(of(CUSTOMER_ID, externalCustomerId)).error(e, "CustomerID not updated");
      throw new CustomerIdException("Error updating customer on DR", e);
    }
  }

  private String generateAndSaveNewExternalId(
      String internalCustomerId, PaymentCreationContext context) {

    String externalId = externalCustomerIdService.generateNewExternalId();

    ClientConfiguration clientConfiguration =
        clientConfigurationService.getConfiguration(
            ConfigurationProviderIdentifier.DIGITAL_RIVER,
            context.getApiKey(),
            context.isSandbox());

    try {
      digitalRiverPaymentProviderService.createCustomer(
          externalId,
          context.getApiKey(),
          validateUserData(context.getRequestUserInfo()),
          clientConfiguration);
    } catch (RequestToProviderException e) {
      Log.withMetadata(of(CUSTOMER_ID, internalCustomerId)).error(e, "CustomerID not saved");
      throw new CustomerIdException("CustomerId already existed on DR", e);
    }

    externalCustomerIdService.saveCustomerId(internalCustomerId, externalId);

    return externalId;
  }

  private User validateUserData(User user) {
    if (!EmailValidator.getInstance().isValid(user.getEmail())) {
      return User.Builder.from(user).setEmail("").build();
    }
    return user;
  }
}
