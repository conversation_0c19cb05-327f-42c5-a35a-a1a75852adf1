package com.scopely.paymentgateway.providers.xsolla.model.payment;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.payment.PaymentProviderData;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import java.util.Map;
import javax.annotation.Nullable;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = XsollaPaymentProviderData.Builder.class)
@JsonIgnoreProperties
public interface XsollaPaymentProviderData extends PaymentProviderData {

  String getToken();

  @Nullable
  Integer getPaymentMethod();

  default Map<String, String> getPublicProviderData() {
    return Map.of("token", this.getToken());
  }

  class Builder extends XsollaPaymentProviderData_Builder {
    public Builder() {
      setProvider(PaymentProviderIdentifier.XSOLLA);
    }
  }
}
