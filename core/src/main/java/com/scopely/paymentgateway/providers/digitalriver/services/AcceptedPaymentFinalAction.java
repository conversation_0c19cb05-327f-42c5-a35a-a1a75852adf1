package com.scopely.paymentgateway.providers.digitalriver.services;

import com.scopely.paymentgateway.exceptions.NonRetryablePaymentException;
import com.scopely.paymentgateway.model.mailchimp.EmailType;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.services.email.ReceiptMailProcessor;
import com.scopely.paymentgateway.services.payment.MarkPaymentAsCompleted;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class AcceptedPaymentFinalAction {

  private final FulfillmentHandler fulfillmentHandler;
  private final ReceiptMailProcessor receiptMailProcessor;
  private final PaymentProcessAnalytics paymentProcessAnalytics;
  private final MarkPaymentAsCompleted markPaymentAsCompleted;

  @Inject
  public AcceptedPaymentFinalAction(
      FulfillmentHandler fulfillmentHandler,
      ReceiptMailProcessor receiptMailProcessor,
      PaymentProcessAnalytics paymentProcessAnalytics,
      MarkPaymentAsCompleted markPaymentAsCompleted) {
    this.fulfillmentHandler = fulfillmentHandler;
    this.receiptMailProcessor = receiptMailProcessor;
    this.paymentProcessAnalytics = paymentProcessAnalytics;
    this.markPaymentAsCompleted = markPaymentAsCompleted;
  }

  public void execute(
      Payment paymentWithFulfillItems, DigitalRiverClientConfig digitalRiverClientConfig)
      throws NonRetryablePaymentException {
    /* It's important to send to the FulfillmentHandler the payment object that
    comes from the Order creation, because this object has
    the items to be fulfilled and we don't have those items stored in our DB */

    Payment fulfilledPayment =
        fulfillmentHandler.execute(paymentWithFulfillItems, digitalRiverClientConfig);

    Payment completedPayment = markPaymentAsCompleted.execute(fulfilledPayment);

    receiptMailProcessor.processPaymentReceipt(completedPayment, EmailType.SUCCESS);

    paymentProcessAnalytics.sendPaymentFinishedEvents(completedPayment);
  }
}
