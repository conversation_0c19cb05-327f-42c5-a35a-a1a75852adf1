package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.DR_WEBHOOK;

import com.scopely.paymentgateway.exceptions.PaymentAlreadyRequestedToRefundException;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.logging.PaymentProcess;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.model.refund.RefundStatus;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.DigitalRiverRefundProviderData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverRefundPendingRequestData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.body.DigitalRiverRefundPendingRequestBody;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.services.refund.RefundService;
import datadog.trace.api.Trace;
import java.util.List;
import java.util.Objects;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class RefundPendingWebhookProcessor
    implements DigitalRiverWebhookProcessor<
        DigitalRiverRefundPendingRequestData, DigitalRiverRefundPendingRequestBody> {

  private final PaymentService paymentService;
  private final RefundService refundService;

  @Inject
  public RefundPendingWebhookProcessor(PaymentService paymentService, RefundService refundService) {
    this.paymentService = paymentService;
    this.refundService = refundService;
  }

  @Override
  @Trace(operationName = DR_WEBHOOK, resourceName = "refund.pending")
  public void execute(DigitalRiverRefundPendingRequestData request)
      throws PaymentNotFoundException {
    try {
      var payment = getPaymentByRefundRequest(request);
      // retrieve the refund
      var refunds = refundService.getRefundsByPaymentId(payment.getPaymentId());
      var refund =
          refunds.stream()
              .filter(_refund -> _refund.getRefundId().equals(request.getRefundId()))
              .findFirst()
              .orElseGet(() -> tryToGenerateTheRefund(request, payment));

      // process only the refund if the status is pending or created in order to not override a
      // final status
      var previousStatus = Objects.requireNonNull(refund.getProviderData()).getStatus();
      if (request.getState().greaterThan(previousStatus)) {
        // update this refund with the latest provider info
        refundService.saveRefund(updateRefund(refund, request), payment);
      }
      // delete the refunds that have status created and are returns
      refunds.stream()
          .filter(_refund -> _refund.getStatus().equals(RefundStatus.CREATED))
          .forEach(refundService::deleteReturn);
    } catch (PaymentAlreadyRequestedToRefundException paymentException) {
      new PaymentGatewayLogBuilder()
          .addRefund(request.getRefundId())
          .addProcess(PaymentProcess.REFUND_WEBHOOK)
          .build()
          .debug(
              paymentException, "Refund is already begin processed, no need to generate it again");
    }
  }

  private Refund updateRefund(Refund refund, DigitalRiverRefundPendingRequestData request) {
    var metadata =
        DigitalRiverRefundProviderData.Builder.from(
                (DigitalRiverRefundProviderData) refund.getProviderData())
            .setStatus(request.getState())
            .build();
    return Refund.Builder.from(refund)
        // we only update the refunded local amount because DR doesn't provide the base amount
        .setRefundedLocalAmount(request.getRefundedLocalAmount())
        .setProviderData(metadata)
        .setStatus(request.getState().getRefundStatus())
        .build();
  }

  private Refund tryToGenerateTheRefund(
      DigitalRiverRefundPendingRequestData request, Payment payment) {
    return refundService.saveRefund(request.toRefund(payment), payment);
  }

  private Payment getPaymentByRefundRequest(DigitalRiverRefundPendingRequestData request)
      throws PaymentNotFoundException {
    return paymentService.getPaymentByOrderIdUnchecked(request.getOrderId());
  }

  @Override
  public List<DigitalRiverWebhookEvent> getAssociatedEvents() {
    return List.of(DigitalRiverWebhookEvent.REFUND_PENDING);
  }

  @Override
  public Class<DigitalRiverRefundPendingRequestBody> getModel() {
    return DigitalRiverRefundPendingRequestBody.class;
  }
}
