package com.scopely.paymentgateway.providers.xsolla.webhook.constants;

import static com.google.common.collect.ImmutableMap.of;

import com.fasterxml.jackson.annotation.JsonValue;
import com.scopely.proteus.logging.Log;

public enum XsollaEventName {
  PAYMENT("payment"),
  USER_VALIDATION("user_validation"),
  REFUND("refund"),
  PARTIAL_REFUND("partial_refund"),
  DISPUTE("dispute"),
  UNKNOWN("unknown");

  private final String eventName;

  XsollaEventName(String eventName) {
    this.eventName = eventName;
  }

  @JsonValue
  public String getEventName() {
    return eventName;
  }

  public static XsollaEventName fromValue(final String value) {
    for (XsollaEventName event : XsollaEventName.values()) {
      if (event.getEventName().equals(value)) {
        return event;
      }
    }
    Log.withMetadata(of(XsollaEventName.class.getSimpleName(), value))
        .warn("Event with name {} is not registered, we will use unknown instead", value);
    // default value to avoid unchecked events
    return UNKNOWN;
  }
}
