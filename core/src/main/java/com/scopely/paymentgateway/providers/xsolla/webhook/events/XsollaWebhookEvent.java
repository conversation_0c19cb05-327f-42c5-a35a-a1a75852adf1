package com.scopely.paymentgateway.providers.xsolla.webhook.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = XsollaWebhookEvent.Builder.class)
public interface XsollaWebhookEvent {

  @JsonProperty("notification_type")
  String getEventName();

  class Builder extends XsollaWebhookEvent_Builder {
    public Builder() {}
  }
}
