package com.scopely.paymentgateway.providers.xsolla.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.client.ClientConfigAttributes;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.utils.validation.SecuredField;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import java.beans.Transient;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;
import org.hibernate.validator.constraints.URL;
import org.inferred.freebuilder.FreeBuilder;

@JsonDeserialize(builder = XSollaClientConfig.Builder.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonTypeName(ClientConfigAttributes.XSOLLA_VALUE)
@FreeBuilder
public interface XSollaClientConfig extends ClientConfiguration {

  @Positive(message = "Project id must be a positive number")
  Integer getProjectId();

  @SecuredField
  @NotBlank(message = "Auth apiKey cannot be blank")
  String getAuthApiKey();

  @SecuredField
  @NotBlank(message = "Webhook secret key cannot be blank")
  String getWebhookSecretKey();

  @Positive(message = "Merchant id must be a positive number")
  Integer getMerchantId();

  @Nullable
  @URL(protocol = "https", message = "Invalid URL format")
  String getWebhookFallbackURI();

  @Size(min = 1, message = "Must have at least one allowed language")
  List<String> getAllowedLanguages();

  @Nullable
  @Size(min = 1, message = "Must have at least one event to forward")
  List<String> getEventsToForward();

  boolean isDisableSavedMethods();

  boolean isUseCrossAsCloseButtonIcon();

  @JsonIgnore
  @Transient
  @Override
  default Map<String, Object> getPublicConfig() {
    return Map.of();
  }

  class Builder extends XSollaClientConfig_Builder {

    public Builder() {
      setDisableSavedMethods(false);
      setUseCrossAsCloseButtonIcon(false);
    }
  }
}
