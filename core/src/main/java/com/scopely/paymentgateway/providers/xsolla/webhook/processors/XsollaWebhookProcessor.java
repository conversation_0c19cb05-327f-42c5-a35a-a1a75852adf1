package com.scopely.paymentgateway.providers.xsolla.webhook.processors;

import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.providers.xsolla.webhook.constants.XsollaEventName;
import com.scopely.paymentgateway.providers.xsolla.webhook.events.XsollaWebhookEvent;
import java.util.List;

public interface XsollaWebhookProcessor<T extends XsollaWebhookEvent> {
  void execute(T xsollaWebhookRequest) throws WebhookException, PaymentNotFoundException;

  List<XsollaEventName> getAssociatedEvents();

  Class<T> getModel();
}
