package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.body.DigitalRiverWebhookRequestBody;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
@SuppressWarnings({"unchecked", "rawtypes"})
public class DigitalRiverWebhookProcessorProvider {

  private final Map<String, DigitalRiverWebhookProcessor> processors = new HashMap<>();

  @Inject
  public DigitalRiverWebhookProcessorProvider(Set<DigitalRiverWebhookProcessor<?, ?>> processors) {
    processors.stream().forEach(this::register);
  }

  public void addProcessor(String identifier, DigitalRiverWebhookProcessor processor) {
    this.processors.put(identifier, processor);
  }

  private <T extends DigitalRiverWebhookRequestBody> void register(
      DigitalRiverWebhookProcessor processor) {
    processor
        .getAssociatedEvents()
        .forEach(
            event -> {
              this.addProcessor(((DigitalRiverWebhookEvent) event).getEventName(), processor);
            });
  }

  public Optional<DigitalRiverWebhookProcessor> getProcessor(DigitalRiverWebhookEvent identifier) {
    return Optional.ofNullable(this.processors.get(identifier.getEventName()));
  }
}
