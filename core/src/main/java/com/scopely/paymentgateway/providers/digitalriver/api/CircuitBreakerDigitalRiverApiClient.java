package com.scopely.paymentgateway.providers.digitalriver.api;

import com.scopely.circuitbreaker.CircuitBreakerConfig;
import com.scopely.circuitbreaker.FunnelCircuitBreakerFactory;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.AttachSourceToCustomerResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.CheckoutRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.CheckoutResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.CreateCustomerRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.CreateCustomerResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.GetCustomerResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.SourceResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.UpdateCheckoutRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.UpdateCustomerRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.fulfillment.FulfillmentRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.fulfillment.FulfillmentResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.order.OrderCreationRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.order.OrderResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.CreateRefundRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.RefundCreationResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.ReturnCreationRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.ReturnResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.ReturnUpdate;
import com.scopely.paymentgateway.providers.digitalriver.model.taxes.DigitalRiverCountryConversionResponse;
import java.util.function.BiFunction;
import java.util.function.Function;

public class CircuitBreakerDigitalRiverApiClient implements DigitalRiverApi {
  private final DigitalRiverApi internalClient;
  private final BiFunction<String, CheckoutRequest, CheckoutResponse>
      createCheckoutDecoratedFunction;
  private final Function<UpdateCheckoutArguments, CheckoutResponse> updateCheckoutDecoratedFunction;
  private final BiFunction<String, String, GetCustomerResponse> getCustomerDecoratedFunction;
  private final BiFunction<String, String, SourceResponse> getSourceDecoratedFunction;
  private final BiFunction<String, OrderCreationRequest, OrderResponse>
      createOrderDecoratedFunction;
  private final BiFunction<String, FulfillmentRequest, FulfillmentResponse>
      fulfillOrderDecoratedFunction;

  public CircuitBreakerDigitalRiverApiClient(
      DigitalRiverApi internalClient,
      CircuitBreakerConfig circuitBreakerConfig,
      FunnelCircuitBreakerFactory circuitBreakerFactory) {
    this.internalClient = internalClient;
    this.createCheckoutDecoratedFunction =
        circuitBreakerFactory
            .forEntryStep("dr-createCheckout", circuitBreakerConfig)
            .decorateBiFunction(this.internalClient::createCheckout);
    this.updateCheckoutDecoratedFunction =
        circuitBreakerFactory
            .forNonEntryStep("dr-updateCheckout", circuitBreakerConfig)
            .decorateFunction(this::updateCheckoutInternal);
    this.getCustomerDecoratedFunction =
        circuitBreakerFactory
            .forNonEntryStep("dr-getCustomer", circuitBreakerConfig)
            .decorateBiFunction(this.internalClient::getCustomer);
    this.getSourceDecoratedFunction =
        circuitBreakerFactory
            .forNonEntryStep("dr-getSource", circuitBreakerConfig)
            .decorateBiFunction(this.internalClient::getSource);
    this.createOrderDecoratedFunction =
        circuitBreakerFactory
            .forNonEntryStep("dr-createOrder", circuitBreakerConfig)
            .decorateBiFunction(this.internalClient::createOrder);
    this.fulfillOrderDecoratedFunction =
        circuitBreakerFactory
            .forNonEntryStep("dr-fulfillOrder", circuitBreakerConfig)
            .decorateBiFunction(this.internalClient::fulfillOrder);
  }

  @Override
  public CheckoutResponse createCheckout(String auth, CheckoutRequest checkoutRequest) {
    return createCheckoutDecoratedFunction.apply(auth, checkoutRequest);
  }

  @Override
  public CheckoutResponse updateCheckout(
      String auth, String checkoutId, UpdateCheckoutRequest associationRequest) {
    return updateCheckoutDecoratedFunction.apply(
        new UpdateCheckoutArguments(auth, checkoutId, associationRequest));
  }

  @Override
  public GetCustomerResponse getCustomer(String auth, String customerId) {
    return getCustomerDecoratedFunction.apply(auth, customerId);
  }

  @Override
  public SourceResponse getSource(String auth, String sourceId) {
    return getSourceDecoratedFunction.apply(auth, sourceId);
  }

  @Override
  public OrderResponse createOrder(String auth, OrderCreationRequest associationRequest) {
    return createOrderDecoratedFunction.apply(auth, associationRequest);
  }

  @Override
  public FulfillmentResponse fulfillOrder(String auth, FulfillmentRequest fulfillmentRequest) {
    return fulfillOrderDecoratedFunction.apply(auth, fulfillmentRequest);
  }

  @Override
  public CreateCustomerResponse createCustomer(
      String auth, CreateCustomerRequest createCustomerRequest) {
    return internalClient.createCustomer(auth, createCustomerRequest);
  }

  @Override
  public CreateCustomerResponse updateCustomer(
      String auth, String customerId, UpdateCustomerRequest updateCustomerRequest) {
    return internalClient.updateCustomer(auth, customerId, updateCustomerRequest);
  }

  @Override
  public AttachSourceToCustomerResponse attachSourceToCustomer(
      String auth, String customerId, String sourceId) {
    return internalClient.attachSourceToCustomer(auth, customerId, sourceId);
  }

  @Override
  public Void detachSourceFromCustomer(String auth, String customerId, String sourceId) {
    return internalClient.detachSourceFromCustomer(auth, customerId, sourceId);
  }

  @Override
  public RefundCreationResponse requestRefund(
      String auth, CreateRefundRequest createRefundRequest) {
    return internalClient.requestRefund(auth, createRefundRequest);
  }

  @Override
  public ReturnResponse createReturn(String auth, ReturnCreationRequest returnCreationRequest) {
    return internalClient.createReturn(auth, returnCreationRequest);
  }

  @Override
  public ReturnResponse updateReturn(String auth, String id, ReturnUpdate returnUpdate) {
    return internalClient.updateReturn(auth, id, returnUpdate);
  }

  @Override
  public CheckoutResponse getCheckout(String auth, String checkoutId) {
    return internalClient.getCheckout(auth, checkoutId);
  }

  @Override
  public CheckoutResponse deleteCheckout(String auth, String checkoutId) {
    return internalClient.deleteCheckout(auth, checkoutId);
  }

  @Override
  public Void detachSource(String auth, String checkoutId, String sourceId) {
    return internalClient.detachSource(auth, checkoutId, sourceId);
  }

  @Override
  public OrderResponse getOrder(String auth, String orderId) {
    return internalClient.getOrder(auth, orderId);
  }

  @Override
  public DigitalRiverCountryConversionResponse retrieveTaxesForSpecificCountryAndCurrency(
      String auth, String country, String currency) {
    return internalClient.retrieveTaxesForSpecificCountryAndCurrency(auth, country, currency);
  }

  private CheckoutResponse updateCheckoutInternal(UpdateCheckoutArguments arguments) {
    return internalClient.updateCheckout(
        arguments.auth(), arguments.checkoutId(), arguments.associationRequest());
  }

  private record UpdateCheckoutArguments(
      String auth, String checkoutId, UpdateCheckoutRequest associationRequest) {}
}
