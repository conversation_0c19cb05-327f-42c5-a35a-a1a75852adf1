package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_DR_ORDER_COMPLETE_WEBHOOK_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_DR_ORDER_COMPLETE_WEBHOOK_SUCCESS;
import static com.scopely.paymentgateway.constants.StatsConstants.DR_WEBHOOK;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;

import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.payment.ProviderStatus;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverOrderRequestData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.body.DigitalRiverWebhookOrderRequestBody;
import com.scopely.paymentgateway.services.payment.FindPaymentBySessionId;
import com.scopely.paymentgateway.services.payment.UpdateProviderStatus;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class OrderCompletedWebhookProcessor
    implements DigitalRiverWebhookProcessor<
        DigitalRiverOrderRequestData, DigitalRiverWebhookOrderRequestBody> {
  private final FindPaymentBySessionId findPaymentBySessionId;
  private final UpdateProviderStatus updateProviderStatus;
  private final StatsDClient statsDClient;

  @Inject
  public OrderCompletedWebhookProcessor(
      FindPaymentBySessionId findPaymentBySessionId,
      UpdateProviderStatus updateProviderStatus,
      StatsDClient statsDClient) {
    this.findPaymentBySessionId = findPaymentBySessionId;
    this.updateProviderStatus = updateProviderStatus;
    this.statsDClient = statsDClient;
  }

  @Override
  @Trace(operationName = DR_WEBHOOK, resourceName = "order.complete")
  public void execute(DigitalRiverOrderRequestData request) {
    Optional<Payment> optPayment =
        findPaymentBySessionId.execute(
            request.getSessionId(), PaymentProviderIdentifier.DIGITAL_RIVER);
    optPayment.ifPresent(
        payment -> {
          if (isNotCompletedYet(payment)) {
            try {
              updateProviderStatus.execute(payment, ProviderStatus.COMPLETE);
              statsDClient.increment(
                  DD_DR_ORDER_COMPLETE_WEBHOOK_SUCCESS,
                  MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
            } catch (Exception exception) {
              Log.error(exception, "Error updating ");
              statsDClient.increment(
                  DD_DR_ORDER_COMPLETE_WEBHOOK_ERROR,
                  MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
              throw exception;
            }
          }
        });
  }

  private boolean isNotCompletedYet(Payment payment) {
    return payment.getProviderStatus() != ProviderStatus.COMPLETE;
  }

  @Override
  public List<DigitalRiverWebhookEvent> getAssociatedEvents() {
    return List.of(DigitalRiverWebhookEvent.ORDER_COMPLETE);
  }

  @Override
  public Class<DigitalRiverWebhookOrderRequestBody> getModel() {
    return DigitalRiverWebhookOrderRequestBody.class;
  }
}
