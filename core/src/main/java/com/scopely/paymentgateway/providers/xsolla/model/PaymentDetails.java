package com.scopely.paymentgateway.providers.xsolla.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;

public record PaymentDetails(
    CurrencyAndAmount payment,
    @JsonProperty("xsolla_balance_sum") CurrencyAndAmount xsollaBalanceSum,
    CurrencyAndAmount payout,
    @JsonProperty("xsolla_fee") CurrencyAndAmount xsollaFee,
    @JsonProperty("payment_method_fee") CurrencyAndAmount paymentMethodFee,
    CurrencyAmountAndPercent vat,
    @JsonProperty("sales_tax") CurrencyAmountAndPercent salesTax,
    @JsonProperty("direct_wht") CurrencyAmountAndPercent directWht,
    @JsonProperty("payout_currency_rate") String payoutCurrencyRate,
    @JsonProperty("repatriation_commission") CurrencyAndAmount repatriationCommission,
    @JsonProperty("payment_method_sum") CurrencyAndAmount paymentMethodSum) {

  public BigDecimal getCurrencyRate() {
    return new BigDecimal(this.payoutCurrencyRate);
  }
}
