package com.scopely.paymentgateway.providers.digitalriver.webhook.events.body;

import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.providers.digitalriver.model.order.BillTo;
import com.scopely.paymentgateway.providers.digitalriver.model.order.FraudStateTransitions;
import com.scopely.paymentgateway.providers.digitalriver.model.order.Item;
import com.scopely.paymentgateway.providers.digitalriver.model.order.Payment;
import com.scopely.paymentgateway.providers.digitalriver.model.order.SellingEntity;
import com.scopely.paymentgateway.providers.digitalriver.model.order.Session;
import com.scopely.paymentgateway.providers.digitalriver.model.order.ShipFrom;
import com.scopely.paymentgateway.providers.digitalriver.model.order.ShipTo;
import com.scopely.paymentgateway.providers.digitalriver.model.order.StateTransitions;
import com.scopely.paymentgateway.providers.digitalriver.model.tokenization.CreditCard;
import com.scopely.paymentgateway.providers.digitalriver.model.tokenization.Owner;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverOrderRequestData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverRequestData;
import java.util.ArrayList;
import java.util.Optional;

public record DigitalRiverWebhookOrderRequestBody(
    String id,
    String createdTime,
    String customerId,
    String currency,
    String email,
    ShipTo shipTo,
    ShipFrom shipFrom,
    BillTo billTo,
    PaymentBigDecimal totalAmount,
    PaymentBigDecimal subtotal,
    PaymentBigDecimal totalFees,
    PaymentBigDecimal totalTax,
    PaymentBigDecimal totalImporterTax,
    PaymentBigDecimal totalDuty,
    PaymentBigDecimal totalDiscount,
    PaymentBigDecimal totalShipping,
    ArrayList<Item> items,
    Object metadata,
    String upstreamId,
    String updatedTime,
    String browserIp,
    String locale,
    String customerType,
    SellingEntity sellingEntity,
    Boolean liveMode,
    Payment payment,
    String state,
    StateTransitions stateTransitions,
    String fraudState,
    FraudStateTransitions fraudStateTransitions,
    Boolean requestToBeForgotten,
    PaymentBigDecimal capturedAmount,
    PaymentBigDecimal cancelledAmount,
    PaymentBigDecimal availableToRefundAmount,
    String checkoutId,
    PaymentBigDecimal amount,
    Boolean reusable,
    Owner owner,
    String paymentSessionId,
    String sessionId,
    String clientSecret,
    CreditCard creditCard)
    implements DigitalRiverWebhookRequestBody {

  public DigitalRiverRequestData toRequestData() {
    String innerSessionId =
        Optional.ofNullable(this.payment).map(Payment::session).map(Session::id).orElse(null);

    return new DigitalRiverOrderRequestData.Builder()
        .setSessionId(Optional.ofNullable(this.paymentSessionId()).orElse(innerSessionId))
        .setSourceId(this.id())
        .setState(this.state())
        .build();
  }
}
