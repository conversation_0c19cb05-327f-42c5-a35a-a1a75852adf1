package com.scopely.paymentgateway.providers.digitalriver.webhook.events;

import java.util.Objects;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
public interface DigitalRiverSourceCreation {

  String getPaymentId();

  String getSourceId();

  String getPaymentMethod();

  Boolean getFutureUse();

  boolean isSavedPaymentMethod();

  class Builder extends DigitalRiverSourceCreation_Builder {
    public Builder() {
      setSavedPaymentMethod(false);
    }

    @Override
    public DigitalRiverSourceCreation.Builder setFutureUse(Boolean setFutureUse) {
      return super.setFutureUse(Objects.requireNonNullElse(setFutureUse, false));
    }
  }
}
