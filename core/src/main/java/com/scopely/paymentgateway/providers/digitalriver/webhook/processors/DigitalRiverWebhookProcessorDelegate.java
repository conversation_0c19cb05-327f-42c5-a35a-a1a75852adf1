package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_PROCESSOR_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DR_WEBHOOK;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_EVENT_NAME;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PROVIDER;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.exceptions.model.RefundNotFoundException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookRequest;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import java.util.List;
import java.util.Map;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DigitalRiverWebhookProcessorDelegate {

  private final ObjectMapper mapper;
  private final DigitalRiverWebhookProcessorProvider digitalRiverWebhookProcessorProvider;
  private final StatsDClient statsDClient;

  @Inject
  public DigitalRiverWebhookProcessorDelegate(
      DigitalRiverWebhookProcessorProvider digitalRiverWebhookProcessorProvider,
      StatsDClient statsDClient,
      ObjectMapper mapper) {
    this.digitalRiverWebhookProcessorProvider = digitalRiverWebhookProcessorProvider;
    this.mapper = mapper;
    this.statsDClient = statsDClient;
  }

  @SuppressWarnings("unchecked")
  @Trace(operationName = DR_WEBHOOK, resourceName = "event_forwarder")
  public void execute(DigitalRiverWebhookEvent identifier, DigitalRiverWebhookRequest data)
      throws WebhookException, RefundNotFoundException, PaymentNotFoundException {
    var optProcessor = digitalRiverWebhookProcessorProvider.getProcessor(identifier);
    if (optProcessor.isPresent()) {
      var processor = optProcessor.get();
      processor.execute(processor.toRequestData(data, mapper));
    } else {
      Log.withMetadata(
              Map.of(
                  TAG_PROVIDER,
                  PaymentProviderIdentifier.DIGITAL_RIVER.getDescription(),
                  TAG_API_EVENT_NAME,
                  data.getType().getEventName()))
          .warn("Event with name {} is not registered", data.getType());
      statsDClient.increment(
          DD_WEBHOOK_PROCESSOR_ERROR,
          MetricsUtils.buildTags(
              List.of(TAG_PROVIDER, TAG_API_EVENT_NAME, TAG_RESULT),
              List.of(
                  PaymentProviderIdentifier.DIGITAL_RIVER.getDescription(),
                  data.getType().getEventName(),
                  WebhookException.Error.EVENT_NOT_FOUND)));
      throw new WebhookException(
          WebhookException.Error.EVENT_NOT_FOUND, PaymentProviderIdentifier.DIGITAL_RIVER);
    }
  }
}
