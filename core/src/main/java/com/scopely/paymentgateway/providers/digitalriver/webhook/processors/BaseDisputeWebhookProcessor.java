package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.exceptions.dispute.DisputeServiceException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeEvent;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeStatus;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverDisputeData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookRequest;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.body.DigitalRiverWebhookOrderRequestBody;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.services.reversal.dispute.DisputePropagator;
import com.scopely.paymentgateway.services.reversal.dispute.DisputeService;
import com.scopely.proteus.logging.Log;
import com.scopely.proteus.sqs.publisher.SendMessageException;
import com.timgroup.statsd.StatsDClient;
import java.util.List;

public abstract class BaseDisputeWebhookProcessor
    implements DigitalRiverWebhookProcessor<
        DigitalRiverDisputeData, DigitalRiverWebhookOrderRequestBody> {
  private final PaymentService paymentService;
  private final DisputeService disputeService;
  private final DisputePropagator disputePropagator;
  private final DisputeFactory disputeFactory;
  private final StatsDClient statsDClient;
  private final List<DigitalRiverWebhookEvent> associatedEvents;

  public BaseDisputeWebhookProcessor(
      PaymentService paymentService,
      DisputeService disputeService,
      DisputePropagator disputePropagator,
      DisputeFactory disputeFactory,
      StatsDClient statsDClient,
      List<DigitalRiverWebhookEvent> associatedEvents) {
    this.paymentService = paymentService;
    this.disputeService = disputeService;
    this.disputePropagator = disputePropagator;
    this.disputeFactory = disputeFactory;
    this.statsDClient = statsDClient;
    this.associatedEvents = associatedEvents;
  }

  @Override
  public DigitalRiverDisputeData toRequestData(
      DigitalRiverWebhookRequest request, ObjectMapper mapper) {
    var body = mapper.convertValue(request.getEventData(), getModel());
    return new DigitalRiverDisputeData.Builder()
        .setRequestId(request.getId())
        .setOrderId(body.id())
        .setState(body.state())
        .setStateTransitions(body.stateTransitions())
        .setFraudState(body.fraudState())
        .setFraudStateTransitions(body.fraudStateTransitions())
        .setOrderTotalAmount(body.totalAmount().getMoneyValue(body.currency()))
        .setAvailableToRefundAmount(body.availableToRefundAmount().getMoneyValue(body.currency()))
        .build();
  }

  @Override
  public List<DigitalRiverWebhookEvent> getAssociatedEvents() {
    return associatedEvents;
  }

  @Override
  public Class<DigitalRiverWebhookOrderRequestBody> getModel() {
    return DigitalRiverWebhookOrderRequestBody.class;
  }

  protected PaymentService getPaymentService() {
    return paymentService;
  }

  protected DisputeService getDisputeService() {
    return disputeService;
  }

  protected StatsDClient getStatsDClient() {
    return statsDClient;
  }

  protected Log.MetadataLog getLogger(Payment payment) {
    return new PaymentGatewayLogBuilder()
        .addPayment(payment.getApiKey(), payment.getPaymentId())
        .build();
  }

  protected Dispute buildDispute(
      DigitalRiverDisputeData request, Payment payment, DisputeStatus status) {
    return disputeFactory.buildDispute(payment, status, request.getAvailableToRefundAmount());
  }

  protected void saveDispute(Dispute dispute, Payment payment, String statsConstant)
      throws WebhookException {
    try {
      disputeService.save(dispute, payment);
    } catch (DisputeServiceException e) {
      getStatsDClient().increment(statsConstant);
      getLogger(payment).error("Couldn't save dispute: {}", dispute);
      var message =
          "Couldn't save dispute %s for payment %s"
              .formatted(dispute.getDisputeId(), payment.getPaymentId());
      throw new WebhookException(message, e);
    }
  }

  protected void sendDisputeToSqs(Dispute dispute, String paymentId, String statsConstant)
      throws WebhookException {
    try {
      disputePropagator.enqueueDisputeToSqs(
          new DisputeEvent(
              dispute.getDisputeId(),
              dispute.getApiKey(),
              dispute.getUserId(),
              dispute.getPaymentId()));
    } catch (SendMessageException e) {
      getStatsDClient().increment(statsConstant);
      var message =
          "Couldn't enqueue dispute %s for payment %s".formatted(dispute.getDisputeId(), paymentId);
      throw new WebhookException(message, e);
    }
  }
}
