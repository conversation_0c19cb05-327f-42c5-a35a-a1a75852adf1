package com.scopely.paymentgateway.providers.digitalriver.model.checkout;

import com.scopely.paymentgateway.model.customer.Customer;
import com.scopely.paymentgateway.model.payment.SavedCreditCard;
import java.util.List;
import java.util.Map;

@SuppressWarnings({"PMD.UseLocaleWithCaseConversions", "PMD.NullAssignment"})
public record GetCustomerResponse(
    String id,
    String createdTime,
    String defaultSourceId,
    String email,
    Boolean enabled,
    boolean requestToBeForgotten,
    String locale,
    String type,
    Map<String, Object> metadata,
    boolean liveMode,
    List<SavedSource> sources) {

  private static final String FIRST_NAME = "firstName";
  private static final String LAST_NAME = "lastName";

  public Customer toCustomer() {

    List<SavedCreditCard> savedCreditCards =
        this.sources() != null
            ? this.sources().stream()
                .map(
                    savedSource ->
                        new SavedCreditCard.Builder()
                            .setLastFourDigits(savedSource.creditCard().lastFourDigits())
                            .setExpirationYear(savedSource.creditCard().expirationYear())
                            .setExpirationMonth(savedSource.creditCard().expirationMonth())
                            .setCardNetwork(savedSource.creditCard().brand().toLowerCase())
                            .setEmail(savedSource.owner().email())
                            .setId(savedSource.id())
                            .setPostalCode(savedSource.getCountryAddress().postalCode())
                            .setClientSecret(savedSource.clientSecret())
                            .setCreatedTime(savedSource.createdTime())
                            .setCountry(savedSource.getCountryAddress().country())
                            .build())
                .toList()
            : List.of();

    return new Customer.Builder()
        .setExternalId(this.id())
        .addAllSavedCards(savedCreditCards)
        .setEmail(this.email())
        .setFirstName(
            this.metadata != null && this.metadata.containsKey(FIRST_NAME)
                ? this.metadata.get(FIRST_NAME).toString()
                : null)
        .setLastName(
            this.metadata != null && this.metadata.containsKey(LAST_NAME)
                ? this.metadata.get(LAST_NAME).toString()
                : null)
        .build();
  }
}
