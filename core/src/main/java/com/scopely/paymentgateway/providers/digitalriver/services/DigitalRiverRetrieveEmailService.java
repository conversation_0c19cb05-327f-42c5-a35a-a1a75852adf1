package com.scopely.paymentgateway.providers.digitalriver.services;

import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.exceptions.email.InvalidEmailException;
import com.scopely.paymentgateway.exceptions.model.EmailNotFoundException;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.provider.ProviderCheckout;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.providers.digitalriver.model.payment.DigitalRiverPaymentProviderData;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.email.RetrieveEmailFromProviderService;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.apache.commons.lang.StringUtils;

@Singleton
public class DigitalRiverRetrieveEmailService implements RetrieveEmailFromProviderService {
  private final DigitalRiverPaymentProviderService digitalRiverPaymentProviderService;

  private final ClientConfigurationService clientConfigurationService;

  @Inject
  public DigitalRiverRetrieveEmailService(
      DigitalRiverPaymentProviderService digitalRiverPaymentProviderService,
      ClientConfigurationService clientConfigurationService) {
    this.digitalRiverPaymentProviderService = digitalRiverPaymentProviderService;
    this.clientConfigurationService = clientConfigurationService;
  }

  /**
   * Intent to retrieve the email from this user in the service provider
   *
   * @param payment
   * @return
   * @throws InvalidEmailException when the email recovered from the provider is not valid
   * @throws EmailNotFoundException when the checkout is not found in the provider database
   */
  @Override
  public String retrieveEmail(Payment payment)
      throws InvalidEmailException, EmailNotFoundException {
    DigitalRiverPaymentProviderData providerData =
        (DigitalRiverPaymentProviderData) payment.getProviderData();
    try {
      ProviderCheckout checkout =
          digitalRiverPaymentProviderService.getCheckout(
              providerData.getCheckoutId(),
              getClientConfiguration(payment.getApiKey(), payment.isSandbox()));
      if (StringUtils.isNotBlank(checkout.getEmail())) {
        return checkout.getEmail();
      }
    } catch (RequestToProviderException e) {
      throw EmailNotFoundException.createEmailNotFoundExceptionByPaymentId(
          payment.getPaymentId(), e);
    }

    throw new InvalidEmailException(
        String.format("Invalid email in checkout %s billTo object", providerData.getCheckoutId()));
  }

  private DigitalRiverClientConfig getClientConfiguration(String apiKey, Boolean sandbox) {
    return clientConfigurationService.getConfiguration(
        ConfigurationProviderIdentifier.DIGITAL_RIVER, apiKey, sandbox);
  }
}
