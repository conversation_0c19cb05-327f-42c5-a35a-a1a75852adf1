package com.scopely.paymentgateway.providers.xsolla.model.refund;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.refund.RefundProviderdata;
import javax.annotation.Nullable;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = XsollaRefundProviderData.Builder.class)
@JsonIgnoreProperties
public interface XsollaRefundProviderData extends RefundProviderdata {

  XsollaProviderRefundStatus getStatus();

  PaymentProviderIdentifier getProvider();

  Integer getOrderId();

  @Nullable
  String getAuthor();

  class Builder extends XsollaRefundProviderData_Builder {
    public Builder() {
      setProvider(PaymentProviderIdentifier.XSOLLA);
    }
  }
}
