package com.scopely.paymentgateway.providers.digitalriver.services;

import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.NonRetryablePaymentException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.exceptions.repository.EntityLockingConflictException;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentOrder;
import com.scopely.paymentgateway.model.payment.ProviderStatus;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.repositories.PaymentRepository;
import java.time.Clock;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class FulfillmentHandler {

  private final DigitalRiverPaymentProviderService paymentProviderDigitalRiverService;
  private final PaymentRepository paymentRepository;
  private final Clock clock;

  @Inject
  public FulfillmentHandler(
      DigitalRiverPaymentProviderService paymentProviderDigitalRiverService,
      PaymentRepository paymentRepository,
      Clock clock) {
    this.paymentProviderDigitalRiverService = paymentProviderDigitalRiverService;
    this.paymentRepository = paymentRepository;
    this.clock = clock;
  }

  public Payment execute(Payment payment, DigitalRiverClientConfig digitalRiverClientConfig)
      throws NonRetryablePaymentException {

    try {
      PaymentOrder paymentOrder =
          paymentProviderDigitalRiverService.fulfillOrder(payment, digitalRiverClientConfig);

      return safeSave(payment, paymentOrder.getProviderStatus());
    } catch (RequestToProviderException exception) {
      throw new NonRetryablePaymentException(
          String.format("Unable to fulfill items for payment id %s", payment.getPaymentId()),
          exception);
    }
  }

  private Payment safeSave(Payment payment, ProviderStatus providerStatus) {
    Payment fulfilledPayment =
        Payment.Builder.from(payment)
            .setProviderStatus(providerStatus)
            .setUpdatedAt(clock.instant())
            .build();

    try {
      return paymentRepository.save(fulfilledPayment);
    } catch (LockingConflictException exception) {
      var updatedPayment = paymentRepository.getPayment(payment.getPaymentId());
      if (updatedPayment.isPresent()) {
        return safeSave(updatedPayment.get(), providerStatus);
      }
      throw new EntityLockingConflictException(exception);
    }
  }
}
