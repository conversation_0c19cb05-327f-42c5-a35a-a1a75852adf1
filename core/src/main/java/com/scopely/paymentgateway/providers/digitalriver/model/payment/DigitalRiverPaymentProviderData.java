package com.scopely.paymentgateway.providers.digitalriver.model.payment;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.payment.PaymentProviderData;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nullable;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = DigitalRiverPaymentProviderData.Builder.class)
@JsonIgnoreProperties
public interface DigitalRiverPaymentProviderData extends PaymentProviderData {

  String getCheckoutId();

  @Nullable
  String getSessionId();

  @Nullable
  String getSourceId();

  default Map<String, String> getPublicProviderData() {
    return Optional.ofNullable(getSessionId())
        .map(sessionId -> Map.of("sessionId", sessionId))
        .orElse(Map.of());
  }

  class Builder extends DigitalRiverPaymentProviderData_Builder {
    public Builder() {
      setProvider(PaymentProviderIdentifier.DIGITAL_RIVER);
    }
  }
}
