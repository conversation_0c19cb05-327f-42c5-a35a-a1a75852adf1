package com.scopely.paymentgateway.providers.xsolla.webhook.events;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.providers.xsolla.model.dispute.Dispute;
import com.scopely.paymentgateway.providers.xsolla.model.dispute.DisputeTransaction;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = XsollaDisputeWebhookEvent.Builder.class)
public interface XsollaDisputeWebhookEvent extends XsollaWebhookEvent {

  String getAction();

  DisputeTransaction getTransaction();

  Dispute getDispute();

  class Builder extends XsollaDisputeWebhookEvent_Builder {}
}
