package com.scopely.paymentgateway.providers.digitalriver.services;

import static com.scopely.paymentgateway.utils.ApiClientExceptionHandler.unwrapWebApplicationException;
import static java.util.Objects.isNull;

import com.google.common.base.Strings;
import com.scopely.paymentgateway.exceptions.RefundPaymentRequestException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.logging.PaymentProcess;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.customer.Customer;
import com.scopely.paymentgateway.model.mailchimp.EmailType;
import com.scopely.paymentgateway.model.payment.*;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.provider.ProviderCheckout;
import com.scopely.paymentgateway.model.provider.digitalriver.error.DRErrorResponse;
import com.scopely.paymentgateway.model.provider.digitalriver.error.ErrorReason;
import com.scopely.paymentgateway.model.refund.ProviderRefundInformation;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.providers.digitalriver.api.DigitalRiverApi;
import com.scopely.paymentgateway.providers.digitalriver.api.DigitalRiverApiError;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.*;
import com.scopely.paymentgateway.providers.digitalriver.model.fulfillment.FulfillmentItem;
import com.scopely.paymentgateway.providers.digitalriver.model.fulfillment.FulfillmentRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.order.OrderCreationRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.order.OrderResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.payment.DigitalRiverPaymentProviderData;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.*;
import com.scopely.paymentgateway.providers.digitalriver.model.taxes.DigitalRiverCountryConversionResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.tokenization.Address;
import com.scopely.paymentgateway.providers.digitalriver.model.tokenization.Owner;
import com.scopely.paymentgateway.providers.digitalriver.services.countryconversion.PostalCodeManageService;
import com.scopely.paymentgateway.services.email.ReceiptMailProcessor;
import com.scopely.paymentgateway.services.payment.PaymentCreationContext;
import com.scopely.paymentgateway.services.payment.UpdateProviderStatus;
import com.scopely.proteus.util.JacksonMapper;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.ws.rs.core.Response;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
@SuppressFBWarnings("RV_RETURN_VALUE_IGNORED_NO_SIDE_EFFECT")
public class DigitalRiverPaymentProviderServiceImpl implements DigitalRiverPaymentProviderService {

  private static final String BEARER = "Bearer ";
  private static final String DEFAULT_EMAIL = "<EMAIL>";
  private static final String INDIVIDUAL = "individual";
  private static final String FIRST_NAME_METADATA_KEY = "firstName";
  private static final String LAST_NAME_METADATA_KEY = "lastName";
  private static final String API_KEY_METADATA_KEY = "apiKey";
  private static final String USER_ID_METADATA_KEY = "userId";

  private final DigitalRiverApi api;
  private final UpdateProviderStatus updateProviderStatus;
  private final PostalCodeManageService postalCodeManageService;
  private final ReceiptMailProcessor receiptMailProcessor;

  @Inject
  public DigitalRiverPaymentProviderServiceImpl(
      final DigitalRiverApi api,
      UpdateProviderStatus updateProviderStatus,
      PostalCodeManageService postalCodeManageService,
      ReceiptMailProcessor receiptMailProcessor) {
    this.api = api;
    this.updateProviderStatus = updateProviderStatus;
    this.postalCodeManageService = postalCodeManageService;
    this.receiptMailProcessor = receiptMailProcessor;
  }

  @Override
  public PaymentProviderIdentifier paymentProviderIdentifier() {
    return PaymentProviderIdentifier.DIGITAL_RIVER;
  }

  @Override
  public void createCustomer(
      String customerId, String apiKey, User userData, ClientConfiguration clientConfiguration)
      throws RequestToProviderException {
    var logger =
        new PaymentGatewayLogBuilder()
            .addUserId(userData.getUserId())
            .addApiKey(apiKey)
            .addProvider(PaymentProviderIdentifier.DIGITAL_RIVER)
            .addProcess(PaymentProcess.CREATE_CUSTOMER)
            .build();
    try {
      DigitalRiverClientConfig digitalRiverClientConfig =
          (DigitalRiverClientConfig) clientConfiguration;
      var metadata = buildMetadata(apiKey, userData);
      api.createCustomer(
          getAuth(digitalRiverClientConfig),
          new CreateCustomerRequest(customerId, userData.getEmail(), metadata, INDIVIDUAL, true));
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Error on DR creating new customer or customer already exists");
      throw exception;
    }
  }

  @Override
  public void updateCustomer(
      String customerId, String apiKey, User userData, ClientConfiguration clientConfiguration)
      throws RequestToProviderException {
    var logger = new PaymentGatewayLogBuilder().addUserId(userData.getUserId()).build();
    try {
      DigitalRiverClientConfig digitalRiverClientConfig =
          (DigitalRiverClientConfig) clientConfiguration;
      api.updateCustomer(
          getAuth(digitalRiverClientConfig),
          customerId,
          new UpdateCustomerRequest(userData.getEmail(), buildMetadata(apiKey, userData)));
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      if (Response.Status.NOT_FOUND.getStatusCode() == exception.getHttpStatus()) {
        createCustomer(customerId, apiKey, userData, clientConfiguration);
      } else {
        logger.error(exception, "Error on DR updating customer");
        throw exception;
      }
    }
  }

  private Map<String, Object> buildMetadata(String apiKey, User userData) {
    Map<String, Object> metadata = new HashMap<>();
    metadata.put(API_KEY_METADATA_KEY, apiKey);
    metadata.put(USER_ID_METADATA_KEY, userData.getUserId());
    putIfNotEmpty(metadata, FIRST_NAME_METADATA_KEY, userData.getFirstName());
    putIfNotEmpty(metadata, LAST_NAME_METADATA_KEY, userData.getLastName());
    return metadata;
  }

  private void putIfNotEmpty(Map<String, Object> metadata, String key, String value) {
    if (!Strings.isNullOrEmpty(value)) {
      metadata.put(key, value);
    }
  }

  @Override
  public Optional<Customer> getCustomer(
      String customerId, ClientConfiguration clientConfiguration) {
    var logger = new PaymentGatewayLogBuilder().addApiKey(clientConfiguration.getApiKey()).build();
    try {
      DigitalRiverClientConfig digitalRiverClientConfig =
          (DigitalRiverClientConfig) clientConfiguration;
      GetCustomerResponse customerResponse =
          api.getCustomer(getAuth(digitalRiverClientConfig), customerId);
      return Optional.ofNullable(customerResponse.toCustomer());
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Could not retrieve customer from Digital River");
      return Optional.empty();
    }
  }

  @Override
  public void attachSourceToCustomer(
      String customerId, String sourceId, ClientConfiguration clientConfiguration)
      throws RequestToProviderException {
    var logger =
        new PaymentGatewayLogBuilder()
            .addUserData(clientConfiguration.getApiKey(), customerId)
            .build();
    try {
      DigitalRiverClientConfig digitalRiverClientConfig =
          (DigitalRiverClientConfig) clientConfiguration;
      api.attachSourceToCustomer(getAuth(digitalRiverClientConfig), customerId, sourceId);
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Error on DR attaching customer with source");
      throw exception;
    }
  }

  @Override
  public void detachSourceFromCustomer(
      String customerId, String sourceId, ClientConfiguration clientConfiguration)
      throws RequestToProviderException {
    var logger = new PaymentGatewayLogBuilder().addApiKey(clientConfiguration.getApiKey()).build();
    try {
      DigitalRiverClientConfig digitalRiverClientConfig =
          (DigitalRiverClientConfig) clientConfiguration;
      api.detachSourceFromCustomer(getAuth(digitalRiverClientConfig), customerId, sourceId);
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Error on DR detaching source from customer: {}", customerId);
      throw exception;
    }
  }

  @Override
  public void detachSourceFromCheckout(
      String checkoutId, String sourceId, ClientConfiguration clientConfiguration)
      throws RequestToProviderException {
    var logger = new PaymentGatewayLogBuilder().addApiKey(clientConfiguration.getApiKey()).build();
    try {
      DigitalRiverClientConfig digitalRiverClientConfig =
          (DigitalRiverClientConfig) clientConfiguration;
      api.detachSource(getAuth(digitalRiverClientConfig), checkoutId, sourceId);
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Error on DR detaching source from checkout");
      throw exception;
    }
  }

  @Override
  public ProviderRefundInformation createFullRefundRequest(
      Payment paymentToBeRefund,
      RefundReason refundReason,
      ClientConfiguration clientConfiguration) {
    var providerResponse =
        createRefund(
            new CreateRefundRequest.Builder()
                .setOrderId(paymentToBeRefund.getOrderId())
                .setCurrency(paymentToBeRefund.getPriceData().getLocalPrice().getCurrency())
                .setReason(refundReason.getDescription())
                .setPercent(100)
                .build(),
            (DigitalRiverClientConfig) clientConfiguration);

    // map response from DR to refund object
    return providerResponse.toRefundProviderInformation(paymentToBeRefund);
  }

  @Override
  public void processRefundReceipt(Refund refund, EmailType type) {
    receiptMailProcessor.processRefundReceipt(refund, type);
  }

  private RefundCreationResponse createRefund(
      CreateRefundRequest request, DigitalRiverClientConfig clientConfiguration) {
    var logger = new PaymentGatewayLogBuilder().addApiKey(clientConfiguration.getApiKey()).build();
    try {
      // retrieve payment
      return api.requestRefund(getAuth(clientConfiguration), request);
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      var errorMessage = getProviderErrorMessage(exception);
      logger.error("unexpected error connecting to DR services");
      throw new RefundPaymentRequestException(errorMessage);
    }
  }

  @Override
  public ProviderCheckout getCheckout(
      String checkoutId, DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException {
    var logger =
        new PaymentGatewayLogBuilder().addApiKey(digitalRiverClientConfig.getApiKey()).build();
    try {
      CheckoutResponse getResponse = api.getCheckout(getAuth(digitalRiverClientConfig), checkoutId);

      ProviderCheckout checkout = getResponse.toProviderCheckout();

      logger.debug("Digital River checkout retrieved successfully: {}", checkout.getCheckoutId());

      return checkout;
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Error getting Digital River checkout");
      throw exception;
    }
  }

  @Override
  public ProviderCheckout createProviderCheckout(
      PaymentCreationContext context, ClientConfiguration clientConfiguration, String customerId)
      throws RequestToProviderException {
    try {
      DigitalRiverClientConfig digitalRiverClientConfig =
          (DigitalRiverClientConfig) clientConfiguration;

      CheckoutRequest checkoutRequest =
          CheckoutRequest.fromRequestData(
              context,
              // Sku hardcoded by agreement with Digital River
              digitalRiverClientConfig.getSkuGroupId(),
              DEFAULT_EMAIL,
              postalCodeManageService.getInitialPostalCode(
                  context.getPaymentLocation().getCountry(),
                  PaymentProviderIdentifier.DIGITAL_RIVER,
                  clientConfiguration.getApiKey()),
              customerId,
              clientConfiguration.getApiKey());

      CheckoutResponse creationResponse =
          api.createCheckout(getAuth(digitalRiverClientConfig), checkoutRequest);
      ProviderCheckout providerCheckout =
          creationResponse.toProviderCheckout(
              context.getItemData(), context.getTotalPriceData(), context.getUiLocale());
      context
          .getLogger()
          .debug(
              "Digital River checkout was created successfully: {}",
              providerCheckout.getCheckoutId());

      return providerCheckout;
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      context.getLogger().error(exception, "Error creating Digital River checkout");
      throw exception;
    }
  }

  @Override
  public ProviderCheckout updateProviderCheckout(
      String apiKey,
      Payment payment,
      ClientConfiguration clientConfiguration,
      String customerId,
      String email,
      String sourceId,
      BillingAddress billingAddress,
      PriceData priceDataProvider)
      throws RequestToProviderException {

    DigitalRiverClientConfig digitalRiverClientConfig =
        (DigitalRiverClientConfig) clientConfiguration;
    var logger =
        new PaymentGatewayLogBuilder()
            .addPayment(clientConfiguration.getApiKey(), payment.getPaymentId())
            .build();

    try {

      // Compare countries for Multi-Currency mode. The currency didn't update for sku mode
      if (!payment.getCountry().equals(billingAddress.getCountry())
          && (!payment
                  .getPriceData()
                  .getLocalPrice()
                  .getCurrency()
                  .equals(priceDataProvider.getLocalPrice().getCurrency())
              || payment.getPriceData().isTaxIncluded() != priceDataProvider.isTaxIncluded())) {

        return renewCheckout(
            payment,
            clientConfiguration,
            customerId,
            email,
            sourceId,
            billingAddress,
            priceDataProvider,
            digitalRiverClientConfig);
      }

      return updateCheckout(payment, email, sourceId, billingAddress, digitalRiverClientConfig);

    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Error creating Digital River checkout");
      throw exception;
    }
  }

  private RequestToProviderException parseProviderError(RequestToProviderException e) {
    return getProviderErrorResponse(e.getMessage())
        .map(DRErrorResponse::errors)
        .filter(errorList -> !errorList.isEmpty())
        .map(List::getFirst)
        .map(
            errorReason ->
                new RequestToProviderException(
                    e.getHttpStatus(),
                    convertResponseCodeToInternalCode(errorReason.code()),
                    errorReason.message(),
                    (Exception) e.getCause()))
        .orElse(e);
  }

  private String convertResponseCodeToInternalCode(String responseCode) {
    return DigitalRiverApiError.findByCode(responseCode)
        .map(DigitalRiverApiError::getInternalCode)
        .orElse(responseCode);
  }

  private static Optional<DRErrorResponse> getProviderErrorResponse(String message) {
    try {
      return Optional.ofNullable(JacksonMapper.MAPPER.readValue(message, DRErrorResponse.class));
    } catch (Exception e) {
      return Optional.empty();
    }
  }

  private String getProviderErrorMessage(RequestToProviderException e) {
    return getProviderErrorResponse(e.getMessage())
        .map(DRErrorResponse::errors)
        .filter(errorList -> !errorList.isEmpty())
        .map(List::getFirst)
        .map(ErrorReason::message)
        .orElse("Unexpected error happened sending the refund request to digital river");
  }

  private ProviderCheckout renewCheckout(
      Payment payment,
      ClientConfiguration clientConfiguration,
      String customerId,
      String email,
      String sourceId,
      BillingAddress billingAddress,
      PriceData priceData,
      DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException {

    ProviderCheckout providerCheckout;
    var logger =
        new PaymentGatewayLogBuilder()
            .addPayment(payment.getApiKey(), payment.getPaymentId())
            .build();
    if (!isNull(billingAddress)) {
      billingAddress =
          BillingAddress.Builder.from(billingAddress)
              .setPostalCode(
                  postalCodeManageService.managePostalCode(
                      billingAddress.getPostalCode(),
                      billingAddress.getCountry(),
                      payment.getProviderData().getProvider(),
                      payment.getApiKey()))
              .build();
    }

    String browserIP =
        getCheckout(
                ((DigitalRiverPaymentProviderData) payment.getProviderData()).getCheckoutId(),
                digitalRiverClientConfig)
            .getBrowserIP();

    CheckoutRequest checkoutRequest =
        new CheckoutRequest(
            payment,
            browserIP,
            customerId,
            sourceId,
            billingAddress,
            // Sku hardcoded by agreement with Digital River
            digitalRiverClientConfig.getSkuGroupId(),
            priceData.getOriginalLocalPrice().getCurrency().getCurrencyCode(),
            priceData.isTaxIncluded(),
            email,
            clientConfiguration.getApiKey());

    CheckoutResponse creationResponse =
        api.createCheckout(getAuth(digitalRiverClientConfig), checkoutRequest);
    providerCheckout =
        creationResponse.toProviderCheckout(payment.getItemData(), priceData, payment.getLocale());
    logger.debug(
        "New Digital River checkout was created successfully: {}",
        providerCheckout.getCheckoutId());

    deleteCheckout(
        ((DigitalRiverPaymentProviderData) payment.getProviderData()).getCheckoutId(),
        digitalRiverClientConfig);

    return providerCheckout;
  }

  public ProviderCheckout updateCheckout(
      Payment payment,
      String email,
      String sourceId,
      BillingAddress billingAddress,
      DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException {
    var logger =
        new PaymentGatewayLogBuilder()
            .addPayment(payment.getApiKey(), payment.getPaymentId())
            .addUserId(payment.getUserId())
            .addProcess(PaymentProcess.CHECKOUT)
            .build();
    DigitalRiverPaymentProviderData providerData =
        (DigitalRiverPaymentProviderData) payment.getProviderData();

    try {
      // Only change postalCode or Country but taxIncluded and currency are the same.
      CheckoutResponse updateResponse =
          api.updateCheckout(
              getAuth(digitalRiverClientConfig),
              providerData.getCheckoutId(),
              new UpdateCheckoutRequest(billingAddress, email, sourceId));

      ProviderCheckout providerCheckout = updateResponse.toProviderCheckout(payment);

      logger.debug(
          "Digital River checkout was updated successfully: {}", providerCheckout.getCheckoutId());

      return providerCheckout;
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Error updating Digital River checkout");
      throw exception;
    }
  }

  private void deleteCheckout(String checkoutId, DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException {
    var logger =
        new PaymentGatewayLogBuilder().addApiKey(digitalRiverClientConfig.getApiKey()).build();
    try {
      api.deleteCheckout(getAuth(digitalRiverClientConfig), checkoutId);

      logger.debug("Digital River checkout deprecated deleted successfully: {}", checkoutId);
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Error deleting Digital River deprecated checkout");
      throw exception;
    }
  }

  @Override
  public PaymentOrder createPaymentOrder(
      Payment payment, DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException {
    var logger =
        new PaymentGatewayLogBuilder()
            .addPayment(payment.getApiKey(), payment.getPaymentId())
            .build();

    try {
      OrderResponse orderCreationResponse =
          api.createOrder(
              getAuth(digitalRiverClientConfig), OrderCreationRequest.fromPayment(payment));
      logger.debug("Digital River order was created successfully");
      return orderCreationResponse.toPaymentOrder(payment);
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      if (exception.getHttpStatus() == Response.Status.CONFLICT.getStatusCode()) {
        logger.info(
            "Conflict during order creation in Digital River. errorCode: {}, errorMessage: {}",
            exception.getErrorCode(),
            exception.getMessage());
      } else {
        logger.error(
            exception,
            "Error creating Digital River order. errorCode: {}, errorMessage: {}",
            exception.getErrorCode(),
            exception.getMessage());
      }
      throw exception;
    }
  }

  @Override
  public PaymentOrder fulfillOrder(
      Payment payment, DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException {
    var logger =
        new PaymentGatewayLogBuilder()
            .addPayment(payment.getApiKey(), payment.getPaymentId())
            .build();

    try {
      api.fulfillOrder(
          getAuth(digitalRiverClientConfig),
          new FulfillmentRequest(
              payment.getOrderId(),
              Stream.ofNullable(payment.getFulfillmentItems())
                  .flatMap(Collection::stream)
                  .map(i -> new FulfillmentItem(i.getItemId(), i.getQuantity()))
                  .toList()));
      logger.debug("Digital River order was fulfilled successfully: {}", payment.getOrderId());

      return new PaymentOrder.Builder()
          .setOrderId(payment.getOrderId())
          .setPaymentId(payment.getPaymentId())
          .setProviderStatus(ProviderStatus.FULFILLED)
          .addAllFulfillmentItems(payment.getFulfillmentItems())
          .setPriceData(payment.getPriceData())
          .build();
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Error fulfilling Digital River order");
      throw exception;
    }
  }

  @Override
  public OrderResponse getOrder(String orderId, DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException {
    var logger =
        new PaymentGatewayLogBuilder().addApiKey(digitalRiverClientConfig.getApiKey()).build();
    try {
      OrderResponse order = api.getOrder(getAuth(digitalRiverClientConfig), orderId);
      logger.debug("Digital River order retrieved successfully: {}", orderId);

      return order;
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Error getting Digital River order");
      throw exception;
    }
  }

  @Override
  public DigitalRiverCountryConversionResponse getCountryCurrencyTaxes(
      String country, String currency, DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException {
    var logger =
        new PaymentGatewayLogBuilder().addApiKey(digitalRiverClientConfig.getApiKey()).build();
    try {
      DigitalRiverCountryConversionResponse countryCurrencyTaxesResponse =
          api.retrieveTaxesForSpecificCountryAndCurrency(
              getAuth(digitalRiverClientConfig), country, currency);
      logger.debug(
          "Digital River country taxes retrieved successfully: {}", country + "-" + currency);

      return countryCurrencyTaxesResponse;
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(
          exception, "Error getting Digital River country taxes: {}", country + "-" + currency);
      throw exception;
    }
  }

  @Override
  public boolean isValidToPerformProviderRefund(
      Payment payment, ClientConfiguration clientConfiguration) throws RequestToProviderException {

    if (ProviderStatus.COMPLETE.equals(payment.getProviderStatus())) {
      return true;
    }

    // If the order is not completed yet, we validate the latest order status
    ProviderStatus orderStatus = getOrderStatus(payment, clientConfiguration);
    if (!orderStatus.equals(payment.getProviderStatus())) {
      updateProviderStatus.execute(payment, orderStatus);
    }

    return ProviderStatus.COMPLETE.equals(orderStatus);
  }

  @Override
  public SourceInformation getSource(
      String sourceId, DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException {
    var logger =
        new PaymentGatewayLogBuilder().addApiKey(digitalRiverClientConfig.getApiKey()).build();
    try {
      SourceResponse sourceResponse = api.getSource(getAuth(digitalRiverClientConfig), sourceId);
      logger.debug("Digital River source retrieved successfully: {}", sourceId);

      Owner owner = sourceResponse.owner();
      Address address = Optional.ofNullable(owner).map(Owner::address).orElse(null);
      return new SourceInformation.Builder()
          .setEmail(Optional.ofNullable(owner).map(Owner::email).orElse(null))
          .setId(sourceResponse.id())
          .setBillingAddress(
              Optional.ofNullable(address)
                  .map(
                      adr ->
                          new BillingAddress.Builder()
                              .setPostalCode(
                                  postalCodeManageService.managePostalCode(
                                      adr.postalCode(),
                                      adr.country(),
                                      PaymentProviderIdentifier.DIGITAL_RIVER,
                                      digitalRiverClientConfig.getApiKey()))
                              .setCountry(adr.country())
                              .setAddressLine1(adr.line1())
                              .setAddressLine2(adr.line2())
                              .setCity(adr.city())
                              .setCountry(adr.country())
                              .build())
                  .orElse(null))
          .setState(SourceState.fromDescription(sourceResponse.state()))
          .build();
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Error getting Digital River source");
      throw exception;
    }
  }

  private String getAuth(DigitalRiverClientConfig digitalRiverClientConfig) {
    return BEARER + digitalRiverClientConfig.getSecretKey();
  }

  private ProviderStatus getOrderStatus(Payment payment, ClientConfiguration clientConfiguration)
      throws RequestToProviderException {
    var logger =
        new PaymentGatewayLogBuilder()
            .addPayment(clientConfiguration.getApiKey(), payment.getPaymentId())
            .build();
    try {
      DigitalRiverClientConfig digitalRiverClientConfig =
          (DigitalRiverClientConfig) clientConfiguration;

      OrderResponse orderResponse = getOrder(payment.getOrderId(), digitalRiverClientConfig);

      return ProviderStatus.fromDescription(orderResponse.state());
    } catch (RequestToProviderException exception) {
      logger.error(exception, "Error getting Digital River order: {}", payment.getOrderId());
      throw exception;
    }
  }

  @Override
  public String createReturnFullOrder(
      String orderId, RefundReason refundReason, ClientConfiguration clientConfiguration)
      throws RequestToProviderException {
    var logger = new PaymentGatewayLogBuilder().addApiKey(clientConfiguration.getApiKey()).build();
    try {
      var returnItems =
          this.getOrder(orderId, (DigitalRiverClientConfig) clientConfiguration).items().stream()
              .map(item -> new RefundItem(item.id(), item.quantity()))
              .toList();

      ReturnResponse returnOrder =
          api.createReturn(
              getAuth((DigitalRiverClientConfig) clientConfiguration),
              new ReturnCreationRequest(orderId, returnItems, refundReason));
      logger.debug("Digital River return created successfully: {}", orderId);

      return returnOrder.id();
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Error creating Digital River return");
      throw exception;
    }
  }

  @Override
  public ReturnResponse updateReturn(String orderId, ClientConfiguration clientConfiguration)
      throws RequestToProviderException {
    var logger = new PaymentGatewayLogBuilder().addApiKey(clientConfiguration.getApiKey()).build();
    try {
      ReturnResponse returnOrder =
          api.updateReturn(
              getAuth((DigitalRiverClientConfig) clientConfiguration),
              orderId,
              new ReturnUpdate("accepted"));

      logger.debug("Digital River return updated successfully: {}", orderId);
      return returnOrder;
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      exception = parseProviderError(exception);
      logger.error(exception, "Error updating Digital River return");
      throw exception;
    }
  }
}
