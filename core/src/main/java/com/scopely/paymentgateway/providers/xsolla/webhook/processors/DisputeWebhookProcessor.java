package com.scopely.paymentgateway.providers.xsolla.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_XSOLLA_DISPUTE_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_XSOLLA_DISPUTE_NO_PAYMENT_ID;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_XSOLLA_DISPUTE_SUCCESS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.XSOLLA_WEBHOOK;

import com.scopely.paymentgateway.exceptions.dispute.DisputeServiceException;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeEvent;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeStatus;
import com.scopely.paymentgateway.providers.xsolla.webhook.constants.XsollaEventName;
import com.scopely.paymentgateway.providers.xsolla.webhook.events.XsollaDisputeWebhookEvent;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.services.reversal.dispute.DisputePropagator;
import com.scopely.paymentgateway.services.reversal.dispute.DisputeService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.PaymentAmountCalculator;
import com.scopely.proteus.logging.Log;
import com.scopely.proteus.sqs.publisher.SendMessageException;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import java.time.Clock;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.apache.commons.lang3.StringUtils;

@Singleton
public class DisputeWebhookProcessor implements XsollaWebhookProcessor<XsollaDisputeWebhookEvent> {
  private static final String DISPUTE_ID_FORMAT = "%d_%d";
  private final PaymentService paymentService;
  private final DisputeService disputeService;
  private final DisputePropagator disputePropagator;
  private final StatsDClient statsDClient;
  private final Clock clock;

  @Inject
  public DisputeWebhookProcessor(
      PaymentService paymentService,
      DisputeService disputeService,
      DisputePropagator disputePropagator,
      StatsDClient statsDclient,
      Clock clock) {
    this.paymentService = paymentService;
    this.disputeService = disputeService;
    this.disputePropagator = disputePropagator;
    this.statsDClient = statsDclient;
    this.clock = clock;
  }

  @Override
  @Trace(operationName = XSOLLA_WEBHOOK, resourceName = "dispute")
  public void execute(XsollaDisputeWebhookEvent xsollaWebhookRequest)
      throws WebhookException, PaymentNotFoundException {
    var paymentId = xsollaWebhookRequest.getTransaction().externalId();
    if (StringUtils.isEmpty(paymentId)) {
      new PaymentGatewayLogBuilder()
          .build()
          .warn("Discarding dispute event with empty payment id: {}", xsollaWebhookRequest);
      statsDClient.increment(DD_WEBHOOK_XSOLLA_DISPUTE_NO_PAYMENT_ID);
      throw PaymentNotFoundException.createPaymentNotFoundExceptionByPaymentId(paymentId);
    }
    var payment =
        paymentService
            .optPaymentById(paymentId)
            .orElseThrow(
                () -> {
                  statsDClient.increment(DD_WEBHOOK_XSOLLA_DISPUTE_ERROR);
                  return PaymentNotFoundException.createPaymentNotFoundExceptionByPaymentId(
                      paymentId);
                });
    Dispute dispute = buildDispute(xsollaWebhookRequest, payment);
    var logger =
        new PaymentGatewayLogBuilder()
            .addPayment(payment.getApiKey(), payment.getPaymentId())
            .build();
    saveDispute(xsollaWebhookRequest, dispute, payment, logger, paymentId);
    sendDisputeToSqs(dispute, paymentId);
    statsDClient.increment(
        DD_WEBHOOK_XSOLLA_DISPUTE_SUCCESS,
        MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
    logger.debug("Dispute event received: {}", xsollaWebhookRequest);
  }

  private void saveDispute(
      XsollaDisputeWebhookEvent xsollaWebhookRequest,
      Dispute dispute,
      Payment payment,
      Log.MetadataLog logger,
      String paymentId)
      throws WebhookException {
    try {
      disputeService.save(dispute, payment);
    } catch (DisputeServiceException e) {
      statsDClient.increment(DD_WEBHOOK_XSOLLA_DISPUTE_ERROR);
      logger.error(e, "Couldn't save dispute: {}", xsollaWebhookRequest);
      throw new WebhookException(
          "Couldn't save dispute %s for payment %s".formatted(dispute.getDisputeId(), paymentId),
          e);
    }
  }

  private void sendDisputeToSqs(Dispute dispute, String paymentId) throws WebhookException {
    try {
      disputePropagator.enqueueDisputeToSqs(
          new DisputeEvent(
              dispute.getDisputeId(),
              dispute.getApiKey(),
              dispute.getUserId(),
              dispute.getPaymentId()));
    } catch (SendMessageException e) {
      statsDClient.increment(DD_WEBHOOK_XSOLLA_DISPUTE_ERROR);
      throw new WebhookException(
          "Couldn't enqueue dispute %s for payment %s".formatted(dispute.getDisputeId(), paymentId),
          e);
    }
  }

  private Dispute buildDispute(XsollaDisputeWebhookEvent request, Payment payment) {
    var disputeId =
        DISPUTE_ID_FORMAT.formatted(
            request.getTransaction().id(), request.getDispute().incomingDate().toEpochMilli());
    var status =
        switch (request.getDispute().status()) {
          case NEW -> DisputeStatus.NEW;
          case ACCEPTED -> DisputeStatus.NON_CONTESTABLE;
          case NO_ACTIONS_REQUIRED -> DisputeStatus.IN_PROGRESS;
          case WON -> DisputeStatus.WON;
          case LOST -> DisputeStatus.LOST;
        };
    var localAmount = request.getTransaction().total().getMoneyValue();
    return new Dispute.Builder()
        .setApiKey(payment.getApiKey())
        .setUserId(payment.getUserId())
        .setPaymentId(payment.getPaymentId())
        .setDisputeId(disputeId)
        .setDisputeReason(request.getDispute().reason())
        .setCreatedAt(request.getDispute().incomingDate())
        .setUpdatedAt(clock.instant())
        .setLocalAmount(localAmount)
        .setAmount(PaymentAmountCalculator.calculateBaseAmount(localAmount, payment))
        .setStatus(status)
        .build();
  }

  @Override
  public List<XsollaEventName> getAssociatedEvents() {
    return List.of(XsollaEventName.DISPUTE);
  }

  @Override
  public Class<XsollaDisputeWebhookEvent> getModel() {
    return XsollaDisputeWebhookEvent.class;
  }
}
