package com.scopely.paymentgateway.providers.digitalriver.webhook.events.body;

import static com.scopely.satellites.Try.tryThis;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverRefundFailedRequestData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverRequestData;
import java.time.Instant;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = DigitalRiverRefundFailedRequestBody.Builder.class)
public abstract class DigitalRiverRefundFailedRequestBody
    implements DigitalRiverRefundBody, DigitalRiverWebhookRequestBody {

  @Override
  public DigitalRiverRequestData toRequestData() {
    return new DigitalRiverRefundFailedRequestData.Builder()
        .setLocalAmount(getLocalAmount().getMoneyValue(getCurrency()))
        .setState(getState())
        .setOrderId(getOrderId())
        .setReason(getReason())
        .setCreatedAt(Instant.parse(getCreatedTime()))
        .setRefundedLocalAmount(getRefundedLocalAmount().getMoneyValue(getCurrency()))
        .setRefundId(getId())
        .build();
  }

  public static class Builder extends DigitalRiverRefundFailedRequestBody_Builder {
    public Builder() {
      super.setReason(RefundReason.OTHER);
    }

    @Override
    public Builder setLocalAmount(PaymentBigDecimal amount) {
      return super.setLocalAmount(amount);
    }

    @Override
    public Builder setRefundedLocalAmount(PaymentBigDecimal refundedAmount) {
      return super.setRefundedLocalAmount(refundedAmount);
    }

    @Override
    public DigitalRiverRefundFailedRequestBody build() {
      if (!tryThis(this::getLocalAmount).isSuccess()) {
        // retrieve the local amount from the items
        super.setLocalAmount(
            DigitalRiverRefundBody.calculateLocalAmountFromPurchasedItems(super.getItems()));
      }
      return super.build();
    }
  }
}
