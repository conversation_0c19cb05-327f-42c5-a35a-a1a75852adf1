package com.scopely.paymentgateway.providers.digitalriver.model.transaction;

import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import java.math.BigDecimal;

public record PayoutAmounts(
    String currency,
    BigDecimal exchangeRate,
    PaymentBigDecimal amount,
    PaymentBigDecimal tax,
    PaymentBigDecimal shipping,
    PaymentBigDecimal regulatoryFees,
    PaymentBigDecimal landedCost,
    PaymentBigDecimal productPrice,
    PaymentBigDecimal digitalRiverShare,
    PaymentBigDecimal distributorShare,
    PaymentBigDecimal transactionFees,
    PaymentBigDecimal shippingDiscount,
    PaymentBigDecimal regulatoryFeeDiscount,
    PaymentBigDecimal remitShipping,
    PaymentBigDecimal remitLandedCost,
    PaymentBigDecimal payoutAmount,
    PaymentBigDecimal storeCredit) {}
