package com.scopely.paymentgateway.providers.digitalriver.model.order;

import com.scopely.paymentgateway.providers.digitalriver.model.Metadata;
import com.scopely.paymentgateway.providers.digitalriver.model.payment.DigitalRiverPaymentProviderData;

public record OrderCreationRequest(String checkoutId, String upstreamId, Metadata metadata) {

  public static OrderCreationRequest fromPayment(
      com.scopely.paymentgateway.model.payment.Payment payment) {
    return new OrderCreationRequest(
        ((DigitalRiverPaymentProviderData) payment.getProviderData()).getCheckoutId(),
        payment.getPaymentId(),
        new Metadata(payment.getUserId()));
  }
}
