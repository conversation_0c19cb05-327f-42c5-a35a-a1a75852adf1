package com.scopely.paymentgateway.providers.xsolla.services;

import com.scopely.paymentgateway.model.customer.Customer;
import com.scopely.paymentgateway.services.customer.CustomerOperationsStrategy;
import com.scopely.paymentgateway.services.payment.PaymentCreationContext;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class XsollaCustomerOperationsStrategy implements CustomerOperationsStrategy {

  @Inject
  public XsollaCustomerOperationsStrategy() {}

  @Override
  public String updateAndGetOrCreateCustomer(PaymentCreationContext context) {
    return context.getUserId();
  }

  @Override
  public Optional<Customer> getCustomer(String userId, String apiKey, boolean sandbox) {
    return Optional.empty();
  }

  @Override
  public Optional<String> getExternalCustomerId(String userId, String apiKey) {
    return Optional.empty();
  }
}
