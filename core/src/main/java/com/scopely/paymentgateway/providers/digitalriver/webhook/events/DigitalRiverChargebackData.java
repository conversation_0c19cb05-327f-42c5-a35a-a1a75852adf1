package com.scopely.paymentgateway.providers.digitalriver.webhook.events;

import com.scopely.paymentgateway.providers.digitalriver.model.transaction.SalesTransactionType;
import org.inferred.freebuilder.FreeBuilder;
import org.javamoney.moneta.Money;

@FreeBuilder
public interface DigitalRiverChargebackData extends DigitalRiverRequestData {

  String getRequestId();

  String getChargebackId();

  String getOrderId();

  Money getChargebackAmount();

  SalesTransactionType getChargebackType();

  class Builder extends DigitalRiverChargebackData_Builder {}
}
