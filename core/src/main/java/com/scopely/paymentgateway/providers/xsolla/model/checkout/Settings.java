package com.scopely.paymentgateway.providers.xsolla.model.checkout;

import com.fasterxml.jackson.annotation.JsonProperty;

public record Settings(
    boolean sandbox,
    @JsonProperty("external_id") String externalId,
    @JsonProperty("ui") UISettings ui,
    @JsonProperty("redirect_policy") RedirectPolicy redirectPolicy,
    @JsonProperty("language") String language,
    @JsonProperty("disable_saved_methods") Boolean disableSavedMethods) {}
