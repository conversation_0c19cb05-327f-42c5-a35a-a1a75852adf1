package com.scopely.paymentgateway.providers.digitalriver.model.refund;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.scopely.paymentgateway.model.refund.ProviderRefundStatus;
import com.scopely.paymentgateway.model.refund.RefundStatus;
import java.util.Arrays;

public enum DigitalRiverProviderRefundStatus implements ProviderRefundStatus {
  CREATED("created", RefundStatus.CREATED),
  PENDING("pending", RefundStatus.PENDING),
  FAILED("failed", RefundStatus.FAILED),
  SUCCEEDED("succeeded", RefundStatus.COMPLETED),
  @JsonEnumDefaultValue
  UNKNOWN("unknown", RefundStatus.UNKNOWN);

  private final String description;
  private final RefundStatus status;

  DigitalRiverProviderRefundStatus(String description, RefundStatus status) {
    this.description = description;
    this.status = status;
  }

  @Override
  @JsonValue
  public String getDescription() {
    return description;
  }

  @JsonCreator
  public static DigitalRiverProviderRefundStatus fromDescription(String value) {
    return Arrays.stream(DigitalRiverProviderRefundStatus.values())
        .filter(identifier -> identifier.description.equals(value))
        .findFirst()
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    "RefundReason with description " + value + " does not exist"));
  }

  @Override
  public RefundStatus getRefundStatus() {
    return status;
  }

  public boolean greaterThan(ProviderRefundStatus other) {
    // by default we will treat the other like it's at the same level so is equal
    boolean result = false;
    // if it's different we should evaluate it
    if (this != other) {
      // If this is created, any other state will be greater
      // if any of the below is true, just update it because it means this is is lower
      result =
          this != CREATED
              // If the other status is failed or completed, our state is lower because are final
              // states
              && other != FAILED
              && other != SUCCEEDED;
    }
    return result;
  }
}
