package com.scopely.paymentgateway.providers.digitalriver.services;

import com.scopely.paymentgateway.exceptions.CustomerIdException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.services.customer.CustomerOperations;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class AttachSourceToCustomerHandler {
  private final DigitalRiverPaymentProviderService paymentProviderDigitalRiverService;
  private final CustomerOperations customerOperations;

  @Inject
  public AttachSourceToCustomerHandler(
      DigitalRiverPaymentProviderService paymentProviderDigitalRiverService,
      CustomerOperations customerOperations) {
    this.paymentProviderDigitalRiverService = paymentProviderDigitalRiverService;
    this.customerOperations = customerOperations;
  }

  public void execute(Payment payment, String sourceId, ClientConfiguration clientConfiguration)
      throws RequestToProviderException {

    Optional<String> externalId =
        customerOperations.getExternalCustomerId(
            payment.getUserId(), payment.getApiKey(), PaymentProviderIdentifier.DIGITAL_RIVER);

    if (externalId.isPresent()) {
      paymentProviderDigitalRiverService.attachSourceToCustomer(
          externalId.get(), sourceId, clientConfiguration);
    } else {
      throw new CustomerIdException("User not found");
    }
  }
}
