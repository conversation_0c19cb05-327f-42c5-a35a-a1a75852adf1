package com.scopely.paymentgateway.providers.digitalriver.services.countryconversion;

import static com.scopely.paymentgateway.constants.LocationConstants.DEFAULT_COUNTRY;

import com.scopely.paymentgateway.model.countryconversion.DynamicPrice;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PricingMode;
import com.scopely.paymentgateway.services.countryconversion.CountryConversionApplier;
import com.scopely.paymentgateway.services.countryconversion.CountryConversionService;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.javamoney.moneta.Money;

@Singleton
public class DRCountryConversionApplier implements CountryConversionApplier {

  private final CountryConversionService countryConversionService;

  @Inject
  public DRCountryConversionApplier(CountryConversionService countryConversionService) {
    this.countryConversionService = countryConversionService;
  }

  @Override
  public PriceData calculateFromProvider(String countryID, Money originPrice) {

    DynamicPrice newPriceInfo =
        countryConversionService
            .getDynamicPrice(countryID, DEFAULT_COUNTRY, originPrice.getNumberStripped())
            .get();

    return new PriceData.Builder()
        .setPricingMode(PricingMode.EXPLICIT)
        .setTaxRate(newPriceInfo.getCountryConversion().getTaxRate())
        .setConversionFactor(newPriceInfo.getCountryConversion().getConversionFactor())
        .setTaxIncluded(newPriceInfo.getCountryConversion().isTaxIncluded())
        .setBasePrice(originPrice)
        .setLocalPrice(
            newPriceInfo.getConvertedPrice(), newPriceInfo.getCountryConversion().getCurrencyId())
        .build();
  }
}
