package com.scopely.paymentgateway.providers.digitalriver.webhook.events;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = DigitalRiverRefundPendingRequestData.Builder.class)
public abstract class DigitalRiverRefundPendingRequestData implements DigitalRiverRefundData {

  public static class Builder extends DigitalRiverRefundPendingRequestData_Builder {
    public Builder() {}
  }
}
