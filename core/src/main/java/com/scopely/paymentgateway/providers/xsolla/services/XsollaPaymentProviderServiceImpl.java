package com.scopely.paymentgateway.providers.xsolla.services;

import static com.scopely.paymentgateway.utils.ApiClientExceptionHandler.unwrapWebApplicationException;

import com.scopely.paymentgateway.exceptions.PaymentProviderIntentionNotUpdatableException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.mailchimp.EmailType;
import com.scopely.paymentgateway.model.payment.BillingAddress;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.provider.ProviderCheckout;
import com.scopely.paymentgateway.model.refund.ProviderRefundInformation;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.ReturnResponse;
import com.scopely.paymentgateway.providers.xsolla.api.XSollaApi;
import com.scopely.paymentgateway.providers.xsolla.api.XsollaMerchantApi;
import com.scopely.paymentgateway.providers.xsolla.config.XSollaClientConfig;
import com.scopely.paymentgateway.providers.xsolla.model.checkout.CheckoutRequest;
import com.scopely.paymentgateway.providers.xsolla.model.checkout.CheckoutResponse;
import com.scopely.paymentgateway.providers.xsolla.model.checkout.Country;
import com.scopely.paymentgateway.providers.xsolla.model.refund.RefundRequest;
import com.scopely.paymentgateway.providers.xsolla.model.refund.XsollaProviderRefundStatus;
import com.scopely.paymentgateway.providers.xsolla.model.refund.XsollaRefundProviderData;
import com.scopely.paymentgateway.services.payment.PaymentCreationContext;
import com.scopely.paymentgateway.services.translations.TranslationsService;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.javamoney.moneta.Money;

@Singleton
public class XsollaPaymentProviderServiceImpl implements XSollaPaymentProviderService {

  public static final String PAYMENT_ID = "PAYMENT_ID";
  public static final String ORDER_ID = "ORDER_ID";
  public static final String BASIC = "Basic ";
  private static final String AUTH_DELIMITER = ":";
  private static final Map<String, String> LANGUAGE_MAPPINGS =
      Map.of(
          "zh", "cn",
          "zh_Hant", "tw");

  private final XSollaApi api;
  private final XsollaMerchantApi merchantApi;
  private final TranslationsService translationsService;

  @Inject
  public XsollaPaymentProviderServiceImpl(
      XSollaApi xSollaApi, XsollaMerchantApi merchantApi, TranslationsService translationsService) {
    this.api = xSollaApi;
    this.merchantApi = merchantApi;
    this.translationsService = translationsService;
  }

  @Override
  public PaymentProviderIdentifier paymentProviderIdentifier() {
    return PaymentProviderIdentifier.XSOLLA;
  }

  @Override
  public ProviderCheckout createProviderCheckout(
      PaymentCreationContext context, ClientConfiguration clientConfiguration, String customerId)
      throws RequestToProviderException {
    try {
      XSollaClientConfig xsollaClientConfig = (XSollaClientConfig) clientConfiguration;

      String xsollaLanguage = getXsollaLocale(context, xsollaClientConfig.getAllowedLanguages());

      CheckoutRequest checkoutRequest =
          CheckoutRequest.fromRequestData(
              context,
              customerId,
              new Country(context.getPaymentLocation().getCountry(), context.isMultiCurrency()),
              xsollaLanguage,
              xsollaClientConfig.isDisableSavedMethods(),
              xsollaClientConfig.isUseCrossAsCloseButtonIcon());

      CheckoutResponse checkoutResponse =
          api.createCheckout(
              getProjectAuth(xsollaClientConfig),
              xsollaClientConfig.getProjectId(),
              checkoutRequest);

      ProviderCheckout providerCheckout =
          checkoutResponse.toCheckoutInformation(
              context.getPaymentLocation(),
              context.getItemData(),
              context.getTotalPriceData(),
              context.getContextProperties(),
              xsollaLanguage);
      context
          .getLogger()
          .debug("Xsolla checkout created successfully: {}", providerCheckout.getCheckoutId());

      return providerCheckout;

    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      context.getLogger().error(exception, "Error creating XSolla checkout");
      throw exception;
    }
  }

  // Method for locale matching for Chinese in Xsolla PayStation.
  private String getXsollaLocale(PaymentCreationContext context, List<String> allowedLanguages) {
    List<String> allowedLangPlusChinese = new ArrayList<>(allowedLanguages);
    allowedLangPlusChinese.addAll(LANGUAGE_MAPPINGS.keySet());

    String xsollaLanguage =
        translationsService.getValidateLocale(
            context.getRequestLocale(), allowedLangPlusChinese, context.getApiKey());

    return LANGUAGE_MAPPINGS.getOrDefault(xsollaLanguage, xsollaLanguage);
  }

  @Override
  public ProviderCheckout updateProviderCheckout(
      String apiKey,
      Payment payment,
      ClientConfiguration clientConfiguration,
      String customerId,
      String email,
      String sourceId,
      BillingAddress billingAddress,
      PriceData priceDataProvider) {
    throw new PaymentProviderIntentionNotUpdatableException(
        payment.getProviderData().getProvider(), payment.getPaymentId());
  }

  @Override
  public ProviderRefundInformation createFullRefundRequest(
      Payment paymentToBeRefund, RefundReason refundReason, ClientConfiguration clientConfiguration)
      throws RequestToProviderException {
    var logger =
        new PaymentGatewayLogBuilder()
            .addPayment(paymentToBeRefund.getApiKey(), paymentToBeRefund.getPaymentId())
            .build();
    Objects.requireNonNull(paymentToBeRefund.getOrderId());
    try {
      XSollaClientConfig xsollaClientConfig = (XSollaClientConfig) clientConfiguration;
      merchantApi.createRefund(
          getMerchantAuth(xsollaClientConfig),
          xsollaClientConfig.getMerchantId(),
          Long.parseLong(paymentToBeRefund.getOrderId()),
          new RefundRequest(refundReason));
      // FIXME:when the status is not a 204 and it's a 200, the  refundResponse will not be null and
      // will be pending
      // TODO: check the behaviours for XSOLLA PENDING statuses
      // By default Xsolla responds always with a COMPLETE status
      ProviderRefundInformation refundRequested =
          new ProviderRefundInformation.Builder()
              .setProvider(PaymentProviderIdentifier.XSOLLA)
              .setRefundId(UUID.randomUUID().toString())
              .setRefundReason(refundReason)
              .setCreatedTime(Instant.now().toString())
              .setProviderData(
                  new XsollaRefundProviderData.Builder()
                      .setStatus(XsollaProviderRefundStatus.COMPLETED)
                      .setOrderId(Integer.valueOf(paymentToBeRefund.getOrderId()))
                      .build())
              .setRefundedAmount(
                  Money.of(
                      paymentToBeRefund.getPriceData().getBasePrice().getTotalAmount(),
                      paymentToBeRefund.getPriceData().getBasePrice().getCurrency()))
              .setRefundedLocalAmount(
                  Money.of(
                      paymentToBeRefund.getPriceData().getLocalPrice().getTotalAmount(),
                      paymentToBeRefund.getPriceData().getLocalPrice().getCurrency()))
              .setStatus(XsollaProviderRefundStatus.COMPLETED.getRefundStatus())
              .build();
      logger.debug("XSolla refund was created successfully");
      return refundRequested;
    } catch (RuntimeException e) {
      RequestToProviderException exception = unwrapWebApplicationException(e);
      logger.error(exception, "Error {} on Xsolla creating the refund", exception.getHttpStatus());
      throw exception;
    }
  }

  @Override
  public void processRefundReceipt(Refund refund, EmailType type) {
    // do nothing: because xsolla operations are not sending emails
  }

  @Override
  public boolean isValidToPerformProviderRefund(
      Payment payment, ClientConfiguration clientConfiguration) {
    return true;
  }

  private String getMerchantAuth(XSollaClientConfig xsollaClientConfig) {
    return BASIC
        + encode(
            xsollaClientConfig.getMerchantId()
                + AUTH_DELIMITER
                + xsollaClientConfig.getAuthApiKey());
  }

  private String getProjectAuth(XSollaClientConfig xsollaClientConfig) {
    return BASIC
        + encode(
            xsollaClientConfig.getProjectId()
                + AUTH_DELIMITER
                + xsollaClientConfig.getAuthApiKey());
  }

  private String encode(String token) {
    return Base64.getEncoder().encodeToString(token.getBytes(StandardCharsets.UTF_8));
  }

  public String createReturnFullOrder(
      String orderId, RefundReason refundReason, ClientConfiguration clientConfiguration) {
    return null;
  }

  public ReturnResponse updateReturn(String orderId, ClientConfiguration clientConfig) {
    return null;
  }
}
