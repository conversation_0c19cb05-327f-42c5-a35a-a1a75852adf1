package com.scopely.paymentgateway.providers.xsolla.webhook.processors;

import com.scopely.paymentgateway.providers.xsolla.webhook.constants.XsollaEventName;
import com.scopely.paymentgateway.providers.xsolla.webhook.events.XsollaWebhookEvent;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import javax.inject.Inject;
import javax.inject.Singleton;

@SuppressWarnings({"unchecked", "rawtypes"})
@Singleton
public class XsollaWebhookProcessorProvider {
  private final Map<String, XsollaWebhookProcessor> processors = new HashMap<>();

  @Inject
  public XsollaWebhookProcessorProvider(Set<XsollaWebhookProcessor<?>> sets) {
    sets.stream().forEach(this::register);
  }

  private void addProcessor(String identifier, XsollaWebhookProcessor processor) {
    this.processors.put(identifier, processor);
  }

  private <T extends XsollaWebhookEvent> void register(XsollaWebhookProcessor processor) {
    processor
        .getAssociatedEvents()
        .forEach(
            event -> {
              this.addProcessor(((XsollaEventName) event).getEventName(), processor);
            });
  }

  public Optional<XsollaWebhookProcessor> getProcessor(XsollaEventName identifier) {
    return Optional.ofNullable(this.processors.get(identifier.getEventName()));
  }
}
