package com.scopely.paymentgateway.providers.digitalriver.model.checkout;

import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.providers.digitalriver.model.order.Address;
import com.scopely.paymentgateway.providers.digitalriver.model.order.CreditCard;
import com.scopely.paymentgateway.providers.digitalriver.model.order.Owner;

public record SavedSource(
    String id,
    String createdTime,
    String type,
    String currency,
    PaymentBigDecimal amount,
    Boolean reusable,
    String state,
    Owner owner,
    String paymentSessionId,
    String clientSecret,
    CreditCard creditCard) {

  public Address getCountryAddress() {
    return this.owner.address();
  }
}
