package com.scopely.paymentgateway.providers.xsolla.api;

import com.scopely.paymentgateway.providers.xsolla.model.refund.RefundRequest;
import com.scopely.paymentgateway.providers.xsolla.model.refund.RefundResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

@Path("/merchant/v2/merchants/{merchant_id}/reports")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface XsollaMerchantApi {

  @PUT
  @Path("transactions/{transaction_id}/refund")
  RefundResponse createRefund(
      @HeaderParam("Authorization") String auth,
      @PathParam("merchant_id") long merchantId,
      @PathParam("transaction_id") long transactionId,
      RefundRequest checkoutRequest);
}
