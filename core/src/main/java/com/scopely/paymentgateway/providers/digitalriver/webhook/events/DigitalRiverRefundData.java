package com.scopely.paymentgateway.providers.digitalriver.webhook.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.model.refund.RefundType;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.DigitalRiverProviderRefundStatus;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.DigitalRiverRefundProviderData;
import com.scopely.paymentgateway.utils.PaymentAmountCalculator;
import java.time.Instant;
import org.javamoney.moneta.Money;

public interface DigitalRiverRefundData extends DigitalRiverRequestData {
  String getRefundId();

  String getOrderId();

  DigitalRiverProviderRefundStatus getState();

  Instant getCreatedAt();

  // Amount requested to be refunded
  @JsonProperty("amount")
  Money getLocalAmount();

  // total amount refunded
  // DR returns the prices in the local currency of the user
  @JsonProperty("refundedAmount")
  Money getRefundedLocalAmount();

  RefundReason getReason();

  default Refund toRefund(Payment payment) {
    var totalLocalAmount =
        Money.of(
            payment.getPriceData().getLocalPrice().getTotalAmount(),
            payment.getPriceData().getLocalPrice().getCurrency());
    boolean partial = getRefundedLocalAmount().isLessThan(totalLocalAmount);
    return new Refund.Builder()
        .setRefundId(getRefundId())
        .setApiKey(payment.getApiKey())
        .setRefundReason(getReason())
        .setCreatedAt(getCreatedAt())
        .setRefundedLocalAmount(getRefundedLocalAmount())
        .setRefundedAmount(
            PaymentAmountCalculator.calculateBaseAmount(getRefundedLocalAmount(), payment))
        .setPaymentId(payment.getPaymentId())
        .setStatus(getState().getRefundStatus())
        .setProviderData(
            new DigitalRiverRefundProviderData.Builder()
                .setStatus(getState())
                .setOrderId(getOrderId())
                .build())
        .setRefundId(getRefundId())
        .setPaymentMethodUsed(payment.getPaymentMethodUsed())
        .setRefundType(partial ? RefundType.PARTIAL : RefundType.TOTAL)
        .build();
  }
}
