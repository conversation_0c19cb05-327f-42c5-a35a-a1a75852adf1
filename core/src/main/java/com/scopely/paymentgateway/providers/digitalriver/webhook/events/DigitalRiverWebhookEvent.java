package com.scopely.paymentgateway.providers.digitalriver.webhook.events;

import static com.google.common.collect.ImmutableMap.of;

import com.fasterxml.jackson.annotation.JsonValue;
import com.scopely.proteus.logging.Log;

public enum DigitalRiverWebhookEvent {
  // REGISTERED EVENTS
  ORDER_ACCEPTED("order.accepted"),
  ORDER_BLOCKED("order.blocked"),
  ORDER_CHARGE_FAILED("order.charge.failed"),
  ORDER_COMPLETE("order.complete"),
  REFUND_PENDING("refund.pending"),
  REFUND_COMPLETE("refund.complete"),
  REFUND_FAILED("refund.failed"),
  DISPUTE_OPEN("order.dispute"),
  DISPUTE_RESOLVED("order.dispute.resolved"),
  DISPUTE_CHARGEBACK("order.chargeback"),

  // UNREGISTERED EVENTS
  ORDER_CHARGE_REFUND_COMPLETE("order.charge.refund.complete"),
  ORDER_CHARGE_REFUND_FAILED("order.charge.refund.failed"),
  REFUND_PENDING_INFORMATION("refund.pending.information"),
  ORDER_INVOICE_CREATED("order.invoice.created"),
  ORDER_CREDIT_MEMO_CREATED("order.credit.memo.created"),
  ORDER_CANCELLED("order.cancelled"),
  FULFILLMENT_CREATED("fulfillment.created"),
  ORDER_CHARGE_CAPTURE_COMPLETE("order.charge.capture.complete"),
  ORDER_CHARGE_CAPTURE_FAILED("order.charge.capture.failed"),
  ORDER_CHARGE_CANCEL_COMPLETE("order.charge.cancel.complete"),
  ORDER_REVIEW_OPENED("order.review_opened"),
  ORDER_PENDING_PAYMENT("order.pending_payment"),
  UNKNOWN("UNKNOWN");

  private final String eventName;

  DigitalRiverWebhookEvent(String eventName) {
    this.eventName = eventName;
  }

  @JsonValue
  public String getEventName() {
    return eventName;
  }

  public static DigitalRiverWebhookEvent fromValue(final String value) {
    for (DigitalRiverWebhookEvent event : DigitalRiverWebhookEvent.values()) {
      if (event.getEventName().equals(value)) {
        return event;
      }
    }
    Log.withMetadata(of(DigitalRiverWebhookEvent.class.getSimpleName(), value))
        .warn("Event with name {} is not registered, we will use UNKNOWN instead", value);
    // default value to avoid unchecked events
    return UNKNOWN;
  }
}
