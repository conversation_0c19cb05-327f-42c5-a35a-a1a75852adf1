package com.scopely.paymentgateway.providers.xsolla.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public record Subscription(
    @JsonProperty("plan_id") String planId,
    @JsonProperty("subscription_id") Integer subscriptionId,
    @JsonProperty("product_id") String productId,
    List<String> tags,
    @JsonProperty("date_create") String dateCreate,
    @JsonProperty("date_next_charge") String dateNextCharge,
    String currency,
    Integer amount) {}
