package com.scopely.paymentgateway.providers.digitalriver.webhook.events;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = DigitalRiverRefundCompleteRequestData.Builder.class)
public abstract class DigitalRiverRefundCompleteRequestData implements DigitalRiverRefundData {

  public static class Builder extends DigitalRiverRefundCompleteRequestData_Builder {
    public Builder() {}
  }
}
