package com.scopely.paymentgateway.providers.digitalriver.model.checkout;

import com.scopely.paymentgateway.model.fulfillment.FulfillmentItem;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import java.util.Map;

public record Item(
    String id,
    ProductDetails productDetails,
    PaymentBigDecimal amount,
    Integer quantity,
    Tax tax,
    ImporterTax importerTax,
    Duties duties,
    Fees fees,
    Map<String, String> metadata) {

  public FulfillmentItem mapToFulfillmentItem() {
    return new FulfillmentItem.Builder().setItemId(id()).setQuantity(quantity()).build();
  }
}
