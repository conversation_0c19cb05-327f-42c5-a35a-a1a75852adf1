package com.scopely.paymentgateway.providers.xsolla.services;

import com.scopely.paymentgateway.model.payment.PaymentMethod;
import java.util.Map;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class GetPaymentMethodFromXsollaId {

  private static final int CREDIT_CARD_ID = 1380;
  private static final int GOOGLE_PAY_ID = 3431;
  private static final int PAYPAL_ID = 24;
  private static final int APPLE_PAY_ID = 3175;
  private static final int AMAZON_PAY_ID = 235;

  // Harcoded values provided by Xsolla for our Payment Methods
  private static final Map<Integer, PaymentMethod> PAYMENT_METHODS =
      Map.of(
          CREDIT_CARD_ID,
          PaymentMethod.CREDIT_CARD,
          GOOGLE_PAY_ID,
          PaymentMethod.GOOGLE_PAY,
          PAYPAL_ID,
          PaymentMethod.PAYPAL,
          APPLE_PAY_ID,
          PaymentMethod.APPLE_PAY,
          AMAZON_PAY_ID,
          PaymentMethod.AMAZON_PAY);

  @Inject
  public GetPaymentMethodFromXsollaId() {}

  /*
  TODO For now, we only use the known payment method ids until to solve a Xsolla API issue.
   When the payment method API work fine, check for the PR #313 to include the changes.
   */
  public PaymentMethod execute(String apiKey, Integer id) {
    return Optional.ofNullable(PAYMENT_METHODS.get(id)).orElse(PaymentMethod.UNKNOWN);
  }
}
