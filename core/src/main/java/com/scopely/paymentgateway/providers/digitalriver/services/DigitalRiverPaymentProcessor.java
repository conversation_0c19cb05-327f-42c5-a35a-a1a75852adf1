package com.scopely.paymentgateway.providers.digitalriver.services;

import static com.google.common.collect.ImmutableMap.of;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PAYMENT_METHOD;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PRICING_MODE;

import com.scopely.paymentgateway.constants.StatsConstants;
import com.scopely.paymentgateway.exceptions.AmountsNotMatchingException;
import com.scopely.paymentgateway.exceptions.AssociationSourceToCheckoutException;
import com.scopely.paymentgateway.exceptions.ExpiredPaymentException;
import com.scopely.paymentgateway.exceptions.InvalidPaymentException;
import com.scopely.paymentgateway.exceptions.NonRetryablePaymentException;
import com.scopely.paymentgateway.exceptions.OrderAlreadyCreatedException;
import com.scopely.paymentgateway.exceptions.RejectedPaymentException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.exceptions.SourceNotChargeableException;
import com.scopely.paymentgateway.exceptions.SourceNotFoundException;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentMethod;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import com.scopely.paymentgateway.model.payment.ProcessedPaymentItem;
import com.scopely.paymentgateway.model.payment.ProcessedPaymentResponse;
import com.scopely.paymentgateway.model.payment.ProviderStatus;
import com.scopely.paymentgateway.model.payment.SourceInformation;
import com.scopely.paymentgateway.model.payment.SourceState;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.providers.digitalriver.model.payment.DigitalRiverPaymentProviderData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverSourceCreation;
import com.scopely.paymentgateway.repositories.PaymentRepository;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.payment.ExternalIdService;
import com.scopely.paymentgateway.services.payment.PaymentValidator;
import com.scopely.paymentgateway.services.payment.ProcessPaymentError;
import com.scopely.paymentgateway.services.payment.UpdateProviderStatus;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.javamoney.moneta.Money;

@Singleton
public class DigitalRiverPaymentProcessor {

  public static final String PAYMENT_ID = "PAYMENT_ID";
  public static final String SOURCE_CREATION_CALLER_TAG = "source_creation";
  public static final String SOURCE_ID = "SOURCE_ID";
  public static final String USER_ID = "USER_ID";
  public static final String CHECKOUT_ID = "CHECKOUT_ID";
  private final ProcessPaymentError processPaymentError;
  private final OrderCreationHandler orderCreationHandler;
  private final SourceAndEmailAssociationService sourceAndEmailAssociationService;
  private final UpdateProviderStatus updateProviderStatus;
  private final PaymentRepository paymentRepository;
  private final PaymentProcessAnalytics paymentProcessAnalytics;
  private final AcceptedPaymentFinalAction acceptedPaymentFinalAction;
  private final ClientConfigurationService clientConfigurationService;
  private final AttachSourceToCustomerHandler attachSourceToCustomerHandler;
  private final DigitalRiverSourceDetachmentFromUserHandler detachmentFromUserHandler;
  private final DigitalRiverPaymentProviderService digitalRiverPaymentProviderService;
  private final PaymentValidator paymentValidator;
  private final ExternalIdService externalIdService;
  private final StatsDClient statsDClient;

  @Inject
  public DigitalRiverPaymentProcessor(
      ProcessPaymentError processPaymentError,
      SourceAndEmailAssociationService sourceAndEmailAssociationService,
      OrderCreationHandler orderCreationHandler,
      AcceptedPaymentFinalAction acceptedPaymentFinalAction,
      UpdateProviderStatus updateProviderStatus,
      PaymentRepository paymentRepository,
      PaymentProcessAnalytics paymentProcessAnalytics,
      ClientConfigurationService clientConfigurationService,
      AttachSourceToCustomerHandler attachSourceToCustomerHandler,
      DigitalRiverSourceDetachmentFromUserHandler detachmentFromUserHandler,
      DigitalRiverPaymentProviderService digitalRiverPaymentProviderService,
      PaymentValidator paymentValidator,
      StatsDClient statsDClient,
      ExternalIdService externalIdService) {
    this.processPaymentError = processPaymentError;
    this.sourceAndEmailAssociationService = sourceAndEmailAssociationService;
    this.orderCreationHandler = orderCreationHandler;
    this.acceptedPaymentFinalAction = acceptedPaymentFinalAction;
    this.updateProviderStatus = updateProviderStatus;
    this.paymentRepository = paymentRepository;
    this.paymentProcessAnalytics = paymentProcessAnalytics;
    this.clientConfigurationService = clientConfigurationService;
    this.attachSourceToCustomerHandler = attachSourceToCustomerHandler;
    this.detachmentFromUserHandler = detachmentFromUserHandler;
    this.digitalRiverPaymentProviderService = digitalRiverPaymentProviderService;
    this.paymentValidator = paymentValidator;
    this.statsDClient = statsDClient;
    this.externalIdService = externalIdService;
  }

  public ProcessedPaymentResponse execute(DigitalRiverSourceCreation request)
      throws WebhookException,
          NonRetryablePaymentException,
          InvalidPaymentException,
          RequestToProviderException,
          PaymentNotFoundException,
          ExpiredPaymentException,
          RejectedPaymentException {

    Payment payment = paymentRepository.getPaymentUnchecked(request.getPaymentId());

    paymentValidator.validatePayment(payment, SOURCE_CREATION_CALLER_TAG);

    DigitalRiverPaymentProviderData providerData =
        (DigitalRiverPaymentProviderData) payment.getProviderData();

    payment =
        Payment.Builder.from(payment)
            .setPaymentMethodUsed(PaymentMethod.fromDescription(request.getPaymentMethod()))
            .setSavedPaymentMethod(request.isSavedPaymentMethod())
            .setProviderData(
                DigitalRiverPaymentProviderData.Builder.from(providerData)
                    .setSourceId(request.getSourceId())
                    .build())
            .build();

    payment = updateProviderStatus.execute(payment, ProviderStatus.SOURCE_CREATED);

    paymentProcessAnalytics.sendStartPaymentAnalyticEvent(payment);

    DigitalRiverClientConfig digitalRiverClientConfig =
        clientConfigurationService.getConfiguration(
            ConfigurationProviderIdentifier.DIGITAL_RIVER,
            payment.getApiKey(),
            payment.isSandbox());

    var sourceInformation =
        retrieveSourceInformation(request.getSourceId(), digitalRiverClientConfig);

    if (isNotValidToCreateOrder(payment)) {
      throw new OrderAlreadyCreatedException(
          "Already exist an order for the checkoutId: " + providerData.getCheckoutId());
    }

    try {
      if (request.getFutureUse()) {
        attachSourceToCustomerHandler.execute(
            payment, request.getSourceId(), digitalRiverClientConfig);
      }
    } catch (RequestToProviderException exception) {
      Log.withMetadata(of(SOURCE_ID, request.getSourceId(), USER_ID, payment.getUserId()))
          .warn(
              "Unable to associate source id {} to user id {}",
              request.getSourceId(),
              payment.getUserId());
      throw exception;
    }

    try {
      Payment updatedPayment =
          sourceAndEmailAssociationService.attach(
              payment,
              request.getSourceId(),
              sourceInformation.getBillingAddress(),
              sourceInformation.getEmail(),
              digitalRiverClientConfig);

      validateAmount(
          updatedPayment, payment, providerData.getCheckoutId(), request, digitalRiverClientConfig);

      payment = updatedPayment;
    } catch (RequestToProviderException exception) {
      Log.withMetadata(
              of(SOURCE_ID, request.getSourceId(), CHECKOUT_ID, providerData.getCheckoutId()))
          .warn("Unable to associate source to checkout");
      throw new AssociationSourceToCheckoutException(
          String.format(
              "Unable to associate source id %s to checkout id %s",
              request.getSourceId(), providerData.getCheckoutId()));
    }

    try {
      payment = createOrderWithProvider(payment, digitalRiverClientConfig);
    } catch (NonRetryablePaymentException exception) {
      detachCardFromCustomer(request, payment);
      Log.withMetadata(of(PAYMENT_ID, payment.getPaymentId()))
          .info(exception, "Unable to create an order");
      processPaymentError.execute(payment, exception);
      throw exception;
    } catch (Exception exception) {
      Log.withMetadata(of(PAYMENT_ID, payment.getPaymentId()))
          .error(exception, "Unhandled exception creating order");
      throw exception;
    }

    try {
      if (ProviderStatus.ACCEPTED == payment.getProviderStatus()) {
        acceptedPaymentFinalAction.execute(payment, digitalRiverClientConfig);
      }
    } catch (NonRetryablePaymentException exception) {
      Log.withMetadata(of(PAYMENT_ID, payment.getPaymentId()))
          .info(exception, "Unable to finalize the accepted order");
      processPaymentError.execute(payment, exception.getMessage());
      throw exception;
    } catch (Exception exception) {
      Log.withMetadata(of(PAYMENT_ID, payment.getPaymentId()))
          .error(exception, "Unhandled exception finalizing order");
      throw new NonRetryablePaymentException(
          String.format(
              "Unhandled exception finalizing order. ID: %s. Error: %s.",
              payment.getPaymentId(), exception.getMessage()));
    }

    return new ProcessedPaymentResponse.Builder()
        .setUserId(payment.getUserId())
        .setEmail(sourceInformation.getEmail())
        .setPaymentId(payment.getPaymentId())
        .setStatus(payment.getPaymentStatus().toString())
        .addAllItems(getProcessedItems(payment))
        .setCountry(payment.getCountry())
        .setPriceDetail(payment.getPriceData())
        .setClaimed(payment.getClaimed())
        .setErrorMessage(payment.getErrorMessage())
        .build();
  }

  private Payment createOrderWithProvider(
      Payment payment, DigitalRiverClientConfig digitalRiverClientConfig)
      throws NonRetryablePaymentException, RequestToProviderException {
    var previousStatus = payment.getPaymentStatus();
    payment = externalIdService.processWithLockExternalId(payment);
    try {
      return orderCreationHandler.execute(payment, digitalRiverClientConfig);
    } catch (Exception exception) {
      externalIdService.processErrorWithExternalId(payment, exception, previousStatus);
      throw exception;
    }
  }

  private boolean isNotValidToCreateOrder(Payment payment) {
    return !PaymentStatus.INITIATED.equals(payment.getPaymentStatus());
  }

  // This creates a single item list for now.
  private List<ProcessedPaymentItem> getProcessedItems(Payment payment) {
    ProcessedPaymentItem processedPaymentItem =
        new ProcessedPaymentItem.Builder()
            .setAmount(
                Money.of(
                    payment.getPriceData().getBasePrice().getSubtotalAmount(),
                    payment.getPriceData().getBasePrice().getCurrency()))
            .setItemName(payment.getItemData() != null ? payment.getItemData().getName() : "")
            .build();

    return List.of(processedPaymentItem);
  }

  private void validateAmount(
      Payment updatedPayment,
      Payment payment,
      String checkoutId,
      DigitalRiverSourceCreation request,
      DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException {
    if (!updatedPayment
        .getPriceData()
        .getLocalPrice()
        .getTotalAmount()
        .equals(payment.getPriceData().getLocalPrice().getTotalAmount())) {
      this.sourceAndEmailAssociationService.detach(
          checkoutId, request.getSourceId(), digitalRiverClientConfig);
      detachCardFromCustomer(request, payment);
      statsDClient.increment(
          StatsConstants.DD_PAYMENT_MISMATCH_AMOUNT,
          MetricsUtils.buildTags(
              List.of(TAG_PAYMENT_METHOD, TAG_PRICING_MODE),
              List.of(
                  payment.getPaymentMethodUsed().getDescription(),
                  payment.getPriceData().getPricingMode().getDescription())));
      throw new AmountsNotMatchingException();
    }
  }

  private void detachCardFromCustomer(DigitalRiverSourceCreation request, Payment payment)
      throws RequestToProviderException {
    if (request.getFutureUse()
        && !request.isSavedPaymentMethod()
        && request.getPaymentMethod().equals(PaymentMethod.CREDIT_CARD.getDescription())) {
      detachmentFromUserHandler.detach(request.getSourceId(), payment);
    }
  }

  private SourceInformation retrieveSourceInformation(
      String sourceId, DigitalRiverClientConfig digitalRiverClientConfig) {
    try {
      var sourceInformation =
          digitalRiverPaymentProviderService.getSource(sourceId, digitalRiverClientConfig);

      if (sourceInformation.getState() != SourceState.CHARGEABLE) {
        throw new SourceNotChargeableException(
            "Source is not chargeable. State is " + sourceInformation.getState().getDescription());
      }
      return sourceInformation;

    } catch (SourceNotChargeableException e) {
      throw e;
    } catch (Exception e) {
      throw new SourceNotFoundException(
          "An error occurred while retrieving the source from Digital River", e);
    }
  }
}
