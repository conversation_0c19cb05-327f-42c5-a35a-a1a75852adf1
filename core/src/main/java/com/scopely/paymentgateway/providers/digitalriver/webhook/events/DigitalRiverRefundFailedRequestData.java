package com.scopely.paymentgateway.providers.digitalriver.webhook.events;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = DigitalRiverRefundFailedRequestData.Builder.class)
public abstract class DigitalRiverRefundFailedRequestData implements DigitalRiverRefundData {

  public static class Builder extends DigitalRiverRefundFailedRequestData_Builder {
    public Builder() {}
  }
}
