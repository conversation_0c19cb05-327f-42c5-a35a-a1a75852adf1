package com.scopely.paymentgateway.providers.digitalriver.services;

import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.customer.Customer;
import com.scopely.paymentgateway.model.payment.BillingAddress;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentOrder;
import com.scopely.paymentgateway.model.payment.SourceInformation;
import com.scopely.paymentgateway.model.payment.User;
import com.scopely.paymentgateway.model.provider.ProviderCheckout;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.providers.digitalriver.model.order.OrderResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.taxes.DigitalRiverCountryConversionResponse;
import com.scopely.paymentgateway.services.provider.PaymentProviderService;
import java.util.Optional;

public interface DigitalRiverPaymentProviderService extends PaymentProviderService {

  void createCustomer(
      String customerId, String apiKey, User userData, ClientConfiguration clientConfiguration)
      throws RequestToProviderException;

  void updateCustomer(
      String customerId, String apiKey, User userData, ClientConfiguration clientConfiguration)
      throws RequestToProviderException;

  Optional<Customer> getCustomer(String costumerId, ClientConfiguration clientConfiguration);

  void attachSourceToCustomer(
      String customerId, String sourceId, ClientConfiguration clientConfiguration)
      throws RequestToProviderException;

  void detachSourceFromCustomer(
      String customerId, String sourceId, ClientConfiguration clientConfiguration)
      throws RequestToProviderException;

  void detachSourceFromCheckout(
      String checkoutId, String sourceId, ClientConfiguration clientConfiguration)
      throws RequestToProviderException;

  ProviderCheckout updateCheckout(
      Payment payment,
      String email,
      String sourceId,
      BillingAddress billingAddress,
      DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException;

  ProviderCheckout getCheckout(String checkoutId, DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException;

  PaymentOrder createPaymentOrder(
      Payment payment, DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException;

  SourceInformation getSource(String sourceId, DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException;

  PaymentOrder fulfillOrder(Payment payment, DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException;

  OrderResponse getOrder(String orderId, DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException;

  DigitalRiverCountryConversionResponse getCountryCurrencyTaxes(
      String country, String currency, DigitalRiverClientConfig digitalRiverClientConfig)
      throws RequestToProviderException;
}
