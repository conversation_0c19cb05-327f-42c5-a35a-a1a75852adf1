package com.scopely.paymentgateway.providers.digitalriver.services;

import com.scopely.paymentgateway.exceptions.DRProcessHandlerException;
import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.NonRetryablePaymentException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.exceptions.repository.EntityLockingConflictException;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentOrder;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PriceDetail;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.repositories.PaymentRepository;
import jakarta.ws.rs.core.Response;
import java.time.Clock;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class OrderCreationHandler {

  public static final String PAYMENT_ID = "PAYMENT_ID";
  private final DigitalRiverPaymentProviderService paymentProviderDigitalRiverService;
  private final PaymentRepository paymentRepository;
  private final Clock clock;

  @Inject
  public OrderCreationHandler(
      DigitalRiverPaymentProviderService paymentProviderDigitalRiverService,
      PaymentRepository paymentRepository,
      Clock clock) {
    this.paymentProviderDigitalRiverService = paymentProviderDigitalRiverService;
    this.paymentRepository = paymentRepository;
    this.clock = clock;
  }

  public Payment execute(Payment payment, DigitalRiverClientConfig digitalRiverClientConfig)
      throws DRProcessHandlerException, RequestToProviderException, NonRetryablePaymentException {

    try {
      PaymentOrder paymentOrder =
          paymentProviderDigitalRiverService.createPaymentOrder(payment, digitalRiverClientConfig);

      Payment savedPayment = safeSave(payment, paymentOrder);

      return Payment.Builder.from(savedPayment)
          .setFulfillmentItems(paymentOrder.getFulfillmentItems())
          .build();
    } catch (RequestToProviderException exception) {
      if (Response.Status.CONFLICT.getStatusCode() == exception.getHttpStatus()) {
        throw new NonRetryablePaymentException(
            String.format(
                "Unable to create order for payment id %s. Cause: %s",
                payment.getPaymentId(), exception.getMessage()),
            exception);
      }
      throw exception;
    }
  }

  private Payment safeSave(Payment payment, PaymentOrder paymentOrder) {
    Payment orderCreatedPayment =
        Payment.Builder.from(payment)
            .setProviderStatus(paymentOrder.getProviderStatus())
            .setOrderId(paymentOrder.getOrderId())
            .setUpdatedAt(clock.instant())
            .setPriceData(
                PriceData.Builder.from(payment.getPriceData())
                    .setLocalPrice(
                        PriceDetail.Builder.from(payment.getPriceData().getLocalPrice())
                            .setTotalAmount(
                                paymentOrder.getPriceData().getLocalPrice().getTotalAmount())
                            .setSubtotalAmount(
                                paymentOrder.getPriceData().getLocalPrice().getSubtotalAmount())
                            .setTaxAmount(
                                paymentOrder.getPriceData().getLocalPrice().getTaxAmount())
                            .build())
                    .setTaxRate(paymentOrder.getPriceData().getTaxRate())
                    .build())
            .build();

    try {
      return paymentRepository.save(orderCreatedPayment);
    } catch (LockingConflictException exception) {
      var paymentOpt = paymentRepository.getPayment(payment.getPaymentId());
      if (paymentOpt.isPresent()) {
        return safeSave(paymentOpt.get(), paymentOrder);
      }
      throw new EntityLockingConflictException(exception);
    }
  }
}
