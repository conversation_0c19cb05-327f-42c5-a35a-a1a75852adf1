package com.scopely.paymentgateway.providers.xsolla.webhook.processors;

import static com.google.common.collect.ImmutableMap.of;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_MISMATCH_TAXES_CALCULATION_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_XSOLLA_OTHER_PAYMENT_METHOD;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PAYMENT_METHOD;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PROVIDER;
import static com.scopely.paymentgateway.constants.StatsConstants.XSOLLA_WEBHOOK;

import com.scopely.paymentgateway.exceptions.NonRetryablePaymentException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentMethod;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PriceDetail;
import com.scopely.paymentgateway.providers.digitalriver.services.PaymentProcessAnalytics;
import com.scopely.paymentgateway.providers.xsolla.model.PaymentDetails;
import com.scopely.paymentgateway.providers.xsolla.model.Transaction;
import com.scopely.paymentgateway.providers.xsolla.model.payment.XsollaPaymentProviderData;
import com.scopely.paymentgateway.providers.xsolla.services.GetPaymentMethodFromXsollaId;
import com.scopely.paymentgateway.providers.xsolla.webhook.constants.XsollaEventName;
import com.scopely.paymentgateway.providers.xsolla.webhook.events.XsollaPaymentWebhookEvent;
import com.scopely.paymentgateway.repositories.PaymentRepository;
import com.scopely.paymentgateway.services.payment.ExternalIdService;
import com.scopely.paymentgateway.services.payment.MarkPaymentAsCompleted;
import com.scopely.paymentgateway.services.payment.ProcessPaymentError;
import com.scopely.paymentgateway.services.refund.RefundSchedulingService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import java.math.BigDecimal;
import java.time.Clock;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.javamoney.moneta.Money;
import org.jetbrains.annotations.NotNull;

@Singleton
public class PaymentWebhookProcessor implements XsollaWebhookProcessor<XsollaPaymentWebhookEvent> {

  private static final String PROVIDER_ID = "PROVIDER_ID";
  public static final String PAYMENT_ID = "PAYMENT_ID";
  public static final String PRICE = "PRICE";
  public static final String TAXES = "TAXES";
  public static final String TOTAL_PRICE = "TOTAL_PRICE";
  public static final BigDecimal RATE_DIVISOR = new BigDecimal(100);
  public static final String CLIENT_TAX_CALCULATION_LEVEL = "client";
  private final Clock clock;
  private final MarkPaymentAsCompleted markPaymentAsCompleted;
  private final PaymentRepository paymentRepository;
  private final GetPaymentMethodFromXsollaId getPaymentMethodFromXsollaId;

  private final PaymentProcessAnalytics paymentProcessAnalytics;
  private final StatsDClient statsDClient;
  private final ExternalIdService externalIdService;
  private final ProcessPaymentError processPaymentError;
  private final RefundSchedulingService refundSchedulingService;

  @Inject
  public PaymentWebhookProcessor(
      Clock clock,
      MarkPaymentAsCompleted markPaymentAsCompleted,
      PaymentRepository paymentRepository,
      GetPaymentMethodFromXsollaId getPaymentMethodFromXsollaId,
      PaymentProcessAnalytics paymentProcessAnalytics,
      ProcessPaymentError processPaymentError,
      RefundSchedulingService refundSchedulingService,
      ExternalIdService externalIdService,
      StatsDClient statsDClient) {
    this.clock = clock;
    this.markPaymentAsCompleted = markPaymentAsCompleted;
    this.paymentRepository = paymentRepository;
    this.getPaymentMethodFromXsollaId = getPaymentMethodFromXsollaId;
    this.paymentProcessAnalytics = paymentProcessAnalytics;
    this.statsDClient = statsDClient;
    this.processPaymentError = processPaymentError;
    this.externalIdService = externalIdService;
    this.refundSchedulingService = refundSchedulingService;
  }

  @Trace(operationName = XSOLLA_WEBHOOK, resourceName = "payment")
  public void execute(XsollaPaymentWebhookEvent xsollaWebhookRequest) throws WebhookException {
    String paymentId =
        Optional.ofNullable(xsollaWebhookRequest)
            .map(XsollaPaymentWebhookEvent::getTransaction)
            .map(Transaction::externalId)
            .orElseThrow(
                () ->
                    new WebhookException(
                        WebhookException.Error.PAYMENT_NOT_FOUND,
                        PaymentProviderIdentifier.XSOLLA));

    var payment =
        paymentRepository
            .getPayment(paymentId)
            .orElseThrow(
                () ->
                    new WebhookException(
                        WebhookException.Error.PAYMENT_NOT_FOUND,
                        PaymentProviderIdentifier.XSOLLA));

    Transaction transaction = xsollaWebhookRequest.getTransaction();

    // We need to update the payment method in order to send it to analytics
    Integer paymentMethodId = transaction.paymentMethod();
    PaymentMethod paymentMethod =
        getPaymentMethodFromXsollaId.execute(payment.getApiKey(), paymentMethodId);

    payment =
        Payment.Builder.from(payment)
            .setPaymentMethodUsed(paymentMethod)
            .setOrderId(transaction.id().toString())
            .setUpdatedAt(clock.instant())
            .build();

    paymentProcessAnalytics.sendStartPaymentAnalyticEvent(payment);

    try {
      payment = externalIdService.processWithLockExternalId(payment);
    } catch (NonRetryablePaymentException nonRetryablePaymentException) {
      processPaymentError.executeWithoutNotify(payment, nonRetryablePaymentException);
      refundSchedulingService.scheduleRefundForFailedPayment(
          payment, "Payment rejected: Duplicated payment ID");
      throw new WebhookException(
          WebhookException.Error.DUPLICATED_EXTERNAL_ID, PaymentProviderIdentifier.XSOLLA);
    }

    PaymentDetails paymentDetails = xsollaWebhookRequest.getPaymentDetails();

    Money salesTax = paymentDetails.salesTax().getMoneyValue();
    Money vatDirectWHS =
        paymentDetails.vat().getMoneyValue().add(paymentDetails.directWht().getMoneyValue());
    boolean taxIncluded = vatDirectWHS.isGreaterThan(salesTax);
    Money localTotal = paymentDetails.payment().getMoneyValue();
    Money localTaxes = calculateLocalTaxes(paymentDetails);
    Money localSubtotal = calculateNetPrice(localTotal, localTaxes);
    Money storePrice = xsollaWebhookRequest.getPurchase().total().getMoneyValue();

    PriceData updatedPriceData =
        PriceData.Builder.from(payment.getPriceData())
            .setTaxRate(calculateTaxRate(paymentDetails))
            .setTaxIncluded(taxIncluded)
            .setBasePrice(
                payment.getPriceData().getOriginalBasePrice().getNumberStripped(),
                payment.getPriceData().getBasePrice().getCurrency())
            .setStorePrice(
                storePrice.getNumberStripped(), storePrice.getCurrency().getCurrencyCode())
            .setLocalPrice(
                new PriceDetail.Builder()
                    .setTotalAmount(localTotal.getNumberStripped())
                    .setSubtotalAmount(localSubtotal.getNumberStripped())
                    .setTaxAmount(localTaxes.getNumberStripped())
                    .setCurrency(localSubtotal.getCurrency().getCurrencyCode())
                    .build())
            .build();

    validateMismatchPricing(
        paymentId,
        payment.getApiKey(),
        updatedPriceData.getLocalPrice().getSubtotalAmount(),
        updatedPriceData.getLocalPrice().getTaxAmount(),
        updatedPriceData.getLocalPrice().getTotalAmount(),
        CLIENT_TAX_CALCULATION_LEVEL);

    XsollaPaymentProviderData xsollaPaymentProviderData =
        XsollaPaymentProviderData.Builder.from(
                (XsollaPaymentProviderData) payment.getProviderData())
            .setPaymentMethod(paymentMethodId)
            .build();

    Payment newPayment =
        Payment.Builder.from(payment)
            .setPriceData(updatedPriceData)
            .setProviderData(xsollaPaymentProviderData)
            .setUpdatedAt(clock.instant())
            .build();

    newPayment = markPaymentAsCompleted.execute(newPayment);
    if (PaymentMethod.OTHER.equals(paymentMethod) || PaymentMethod.UNKNOWN.equals(paymentMethod)) {
      var paymentMethodDesc = paymentMethodId.toString();
      statsDClient.increment(
          DD_WEBHOOK_XSOLLA_OTHER_PAYMENT_METHOD,
          MetricsUtils.buildTags(
              new String[] {TAG_API_KEY, TAG_PAYMENT_METHOD},
              new String[] {payment.getApiKey(), paymentMethodDesc}));
    }
    paymentProcessAnalytics.sendPaymentFinishedEvents(newPayment);

    Log.withMetadata(of(PROVIDER_ID, transaction.externalId()))
        .debug("Payment webhook processed successfully");
  }

  @NotNull
  private static Money calculateLocalTaxes(PaymentDetails paymentDetails) {
    return paymentDetails
        .salesTax()
        .getMoneyValue()
        .add(paymentDetails.vat().getMoneyValue())
        .add(paymentDetails.directWht().getMoneyValue());
  }

  @NotNull
  private static BigDecimal calculateTaxRate(PaymentDetails paymentDetails) {
    return paymentDetails
        .salesTax()
        .percent()
        .add(paymentDetails.vat().percent())
        .add(paymentDetails.directWht().percent())
        .divide(RATE_DIVISOR)
        .abs();
  }

  @NotNull
  private static Money calculateNetPrice(Money grossPrice, Money totalTaxes) {
    return grossPrice.subtract(totalTaxes).abs();
  }

  // Check if price + taxes != totalPrice
  private void validateMismatchPricing(
      String paymentId,
      String apiKey,
      BigDecimal price,
      BigDecimal taxes,
      BigDecimal totalPrice,
      String taxCalculationLevel) {
    if (!price.add(taxes).equals(totalPrice)) {
      Log.withMetadata(
              of(
                  PAYMENT_ID,
                  paymentId,
                  PRICE,
                  price.toString(),
                  TAXES,
                  taxes.toString(),
                  TOTAL_PRICE,
                  totalPrice.toString()))
          .warn(
              "Mismatch in {} taxes calculation when perform XSolla payment webhook for payment with id {}",
              taxCalculationLevel,
              paymentId);

      statsDClient.increment(
          DD_WEBHOOK_MISMATCH_TAXES_CALCULATION_ERROR,
          MetricsUtils.buildTags(
              List.of(TAG_API_KEY, TAG_PROVIDER),
              List.of(apiKey, PaymentProviderIdentifier.XSOLLA)));
    }
  }

  @Override
  public List<XsollaEventName> getAssociatedEvents() {
    return List.of(XsollaEventName.PAYMENT);
  }

  @Override
  public Class<XsollaPaymentWebhookEvent> getModel() {
    return XsollaPaymentWebhookEvent.class;
  }
}
