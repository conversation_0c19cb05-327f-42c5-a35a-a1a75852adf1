package com.scopely.paymentgateway.providers.digitalriver.webhook.events.body;

import static com.scopely.satellites.Try.tryThis;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverRefundCompleteRequestData;
import java.time.Instant;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = DigitalRiverRefundCompleteRequestBody.Builder.class)
public abstract class DigitalRiverRefundCompleteRequestBody
    implements DigitalRiverRefundBody, DigitalRiverWebhookRequestBody {

  @Override
  public DigitalRiverRefundCompleteRequestData toRequestData() {
    return new DigitalRiverRefundCompleteRequestData.Builder()
        .setLocalAmount(getLocalAmount().getMoneyValue(getCurrency()))
        .setState(getState())
        .setOrderId(getOrderId())
        .setReason(getReason())
        .setCreatedAt(Instant.parse(getCreatedTime()))
        .setRefundedLocalAmount(getRefundedLocalAmount().getMoneyValue(getCurrency()))
        .setRefundId(getId())
        .build();
  }

  public static class Builder extends DigitalRiverRefundCompleteRequestBody_Builder {
    public Builder() {
      super.setReason(RefundReason.OTHER);
    }

    @Override
    public Builder setLocalAmount(PaymentBigDecimal amount) {
      return super.setLocalAmount(amount);
    }

    @Override
    public Builder setRefundedLocalAmount(PaymentBigDecimal refundedAmount) {
      return super.setRefundedLocalAmount(refundedAmount);
    }

    @Override
    public DigitalRiverRefundCompleteRequestBody build() {
      if (!tryThis(this::getLocalAmount).isSuccess()) {
        // retrieve the local amount from the items
        super.setLocalAmount(
            DigitalRiverRefundBody.calculateLocalAmountFromPurchasedItems(super.getItems()));
      }
      return super.build();
    }
  }
}
