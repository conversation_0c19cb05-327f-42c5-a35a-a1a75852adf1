package com.scopely.paymentgateway.providers.digitalriver.model.transaction;

import com.fasterxml.jackson.annotation.JsonValue;

public enum SalesTransactionType {
  SALE("sale"),
  REPLACEMENT("replacement"),
  REPLACEMENT_REFUND("replacement_refund"),
  REPLACEMENT_RETURN("replacement_return"),
  FRAUD_CHARGEBACK("fraud_chargeback"),
  NON_FRAUD_CHARGEBACK("non_fraud_chargeback"),
  REFUND("refund"),
  RETURN("return"),
  FRAUD_DETECTION("fraud_detection"),
  DECLINED_SETTLEMENT("declined_settlement");

  private final String code;

  SalesTransactionType(String code) {
    this.code = code;
  }

  @JsonValue
  public String getCode() {
    return code;
  }
}
