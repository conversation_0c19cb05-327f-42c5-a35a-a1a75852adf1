package com.scopely.paymentgateway.providers.xsolla.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_REDIRECT_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_REDIRECT_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PROVIDER;
import static com.scopely.paymentgateway.constants.StatsConstants.XSOLLA_WEBHOOK;
import static jakarta.ws.rs.core.MediaType.APPLICATION_JSON;

import com.scopely.paymentgateway.exceptions.InvalidRedirectUrlWebhookException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.providers.xsolla.config.XSollaClientConfig;
import com.scopely.paymentgateway.services.api.ApiFactory;
import com.scopely.paymentgateway.services.api.RedirectWebhookApi;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.ApiCallsExecutor;
import com.scopely.proteus.logging.Log;
import com.scopely.proteus.util.ApiConfig;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import jakarta.ws.rs.core.Response;
import java.util.List;
import javax.inject.Singleton;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.apache.logging.log4j.util.Strings;

@Singleton
@SuppressWarnings("PMD.UseLocaleWithCaseConversions")
public class RedirectWebhookProcessor {

  private final ClientConfigurationService clientConfigurationService;
  private final StatsDClient statsDClient;
  private final ApiConfig apiConfig;
  private final ApiCallsExecutor apiCallsExecutor;
  private final ApiFactory apiFactory;

  public RedirectWebhookProcessor(
      ClientConfigurationService clientConfigurationService,
      StatsDClient statsDClient,
      ApiCallsExecutor apiCallsExecutor,
      ApiConfig apiConfig,
      ApiFactory apiFactory) {
    this.clientConfigurationService = clientConfigurationService;
    this.statsDClient = statsDClient;
    this.apiCallsExecutor = apiCallsExecutor;
    this.apiConfig = apiConfig;
    this.apiFactory = apiFactory;
  }

  @Trace(operationName = XSOLLA_WEBHOOK, resourceName = "redirect")
  public Response execute(String authorization, String apiKey, String body)
      throws WebhookException {
    var tags =
        MetricsUtils.buildTags(
            List.of(TAG_PROVIDER, TAG_API_KEY),
            List.of(PaymentProviderIdentifier.XSOLLA.getDescription(), apiKey));
    statsDClient.increment(DD_WEBHOOK_REDIRECT_HITS, tags);

    XSollaClientConfig config =
        clientConfigurationService.getConfiguration(ConfigurationProviderIdentifier.XSOLLA, apiKey);
    // TODO: validate this and throw proper exceptions
    String webhookFallbackURI = config.getWebhookFallbackURI();
    RedirectWebhookApi api = getApi(urlWithSlash(webhookFallbackURI));
    try {
      return apiCallsExecutor.executeCallForOriginalResponse(
          api.redirect(
              authorization,
              APPLICATION_JSON, // TODO: hardcoding "application/json" for Xsolla redirects
              webhookFallbackURI,
              RequestBody.create(MediaType.parse(APPLICATION_JSON), body)),
          tags);
    } catch (Exception exception) {
      Log.error(
          exception, "Unhandled exception when try to redirect xsolla webhook for {}", apiKey);
      sendErrorMetrics(tags);
      throw new WebhookException(
          WebhookException.Error.UNEXPECTED_ERROR, PaymentProviderIdentifier.XSOLLA, exception);
    }
  }

  private void sendErrorMetrics(String[] tags) {
    statsDClient.increment(DD_WEBHOOK_REDIRECT_ERROR, tags);
  }

  private RedirectWebhookApi getApi(String url) {
    return apiFactory.buildNullable(RedirectWebhookApi.class, url, apiConfig);
  }

  private String urlWithSlash(String url) {
    if (Strings.isBlank(url)) {
      throw new InvalidRedirectUrlWebhookException(
          "The url to redirect webhook must not be null or empty");
    }
    return url.trim().endsWith("/") ? url : url.trim() + "/";
  }
}
