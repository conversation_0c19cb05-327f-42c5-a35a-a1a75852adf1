package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute.Builder;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeStatus;
import com.scopely.paymentgateway.utils.PaymentAmountCalculator;
import java.time.Clock;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.javamoney.moneta.Money;

@Singleton
public class DisputeFactory {
  private static final String UNKNOWN_REASON = "unknown";
  private final Clock clock;

  @Inject
  public DisputeFactory(Clock clock) {
    this.clock = clock;
  }

  public Dispute buildDispute(Payment payment, DisputeStatus status, Money localAmount) {
    var now = clock.instant();
    return new Builder()
        .setApiKey(payment.getApiKey())
        .setUserId(payment.getUserId())
        .setPaymentId(payment.getPaymentId())
        .setDisputeId(payment.getOrderId())
        .setDisputeReason(UNKNOWN_REASON)
        .setCreatedAt(now)
        .setUpdatedAt(now)
        .setLocalAmount(localAmount)
        .setAmount(PaymentAmountCalculator.calculateBaseAmount(localAmount, payment))
        .setStatus(status)
        .build();
  }
}
