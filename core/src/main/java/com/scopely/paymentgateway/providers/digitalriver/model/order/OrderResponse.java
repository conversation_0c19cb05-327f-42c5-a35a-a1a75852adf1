package com.scopely.paymentgateway.providers.digitalriver.model.order;

import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.model.payment.PaymentOrder;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PriceDetail;
import com.scopely.paymentgateway.model.payment.ProviderStatus;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.Item;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.PurchaseLocation;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.SellingEntity;
import java.math.BigDecimal;
import java.util.List;

public record OrderResponse(
    String id,
    String createdTime,
    String currency,
    String email,
    BillTo billTo,
    PaymentBigDecimal totalAmount,
    PaymentBigDecimal subtotal,
    PaymentBigDecimal totalFees,
    PaymentBigDecimal totalTax,
    PaymentBigDecimal totalImporterTax,
    PaymentBigDecimal totalDuty,
    PaymentBigDecimal totalDiscount,
    PaymentBigDecimal totalShipping,
    List<Item> items,
    String upstreamId,
    String updatedTime,
    String browserIp,
    String locale,
    PurchaseLocation purchaseLocation,
    String customerType,
    SellingEntity sellingEntity,
    Boolean liveMode,
    Payment payment,
    String state,
    StateTransitions stateTransitions,
    String fraudState,
    FraudStateTransitions fraudStateTransitions,
    Boolean requestToBeForgotten,
    PaymentBigDecimal capturedAmount,
    PaymentBigDecimal cancelledAmount,
    PaymentBigDecimal availableToRefundAmount,
    String checkoutId) {

  public static final BigDecimal ZERO_TAX_RATE = BigDecimal.ZERO;

  public PaymentOrder toPaymentOrder(com.scopely.paymentgateway.model.payment.Payment payment) {
    return new PaymentOrder.Builder()
        .setOrderId(this.id())
        .setPaymentId(payment.getPaymentId())
        .setProviderStatus(ProviderStatus.fromDescription(this.state()))
        .addAllFulfillmentItems(this.items().stream().map(Item::mapToFulfillmentItem))
        .setPriceData(
            PriceData.Builder.from(payment.getPriceData())
                .setTaxRate(getNewTaxRate())
                .setBasePrice(
                    payment.getPriceData().getOriginalBasePrice().getNumberStripped(),
                    payment.getPriceData().getBasePrice().getCurrency())
                .setLocalPrice(
                    PriceDetail.Builder.from(payment.getPriceData().getLocalPrice())
                        .setTotalAmount(this.totalAmount.bigDecimal())
                        .setSubtotalAmount(this.subtotal.bigDecimal())
                        .setTaxAmount(this.totalTax.bigDecimal())
                        .build())
                .build())
        .build();
  }

  private boolean hasTaxes() {
    return !this.totalTax().getMoneyValue(this.currency()).isZero();
  }

  private BigDecimal getNewTaxRate() {
    return this.hasTaxes()
        ? this.items().stream().findFirst().map(item -> item.tax().rate()).orElse(ZERO_TAX_RATE)
        : ZERO_TAX_RATE;
  }
}
