package com.scopely.paymentgateway.providers.digitalriver.webhook.events;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = DigitalRiverWebhookRequest.Builder.class)
public interface DigitalRiverWebhookRequest {
  String FIELD_OBJECT = "object";

  String getId();

  DigitalRiverWebhookEvent getType();

  String getLiveMode();

  String getCreatedTime();

  default PaymentProviderIdentifier getPaymentProviderIdentifier() {
    return PaymentProviderIdentifier.DIGITAL_RIVER;
  }

  JsonNode getData();

  default JsonNode getEventData() {
    return this.getData().get(FIELD_OBJECT);
  }

  public class Builder extends DigitalRiverWebhookRequest_Builder {}
}
