package com.scopely.paymentgateway.providers.digitalriver.services.countryconversion;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_PRICE_EXCHANGE_RATES_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_PRICE_EXCHANGE_RATES_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_COUNTRY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_CURRENCY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PROVIDER;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.exceptions.ParsingParametersException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;
import com.scopely.paymentgateway.model.countryconversion.CountryConversion;
import com.scopely.paymentgateway.model.dto.config.CurrenciesByCountry;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.providers.digitalriver.model.taxes.DigitalRiverCountryConversionResponse;
import com.scopely.paymentgateway.providers.digitalriver.services.DigitalRiverPaymentProviderServiceImpl;
import com.scopely.paymentgateway.repositories.DynamoCountryConversionRepository;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.countryconversion.CountryInfoService;
import com.scopely.paymentgateway.services.countryconversion.ExternalCountryConversionService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.timgroup.statsd.StatsDClient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.openjdk.tools.sjavac.Log;

@Singleton
public class DigitalRiverCountryConversionProvider implements ExternalCountryConversionService {
  private static final int SCALE_2 = 2;

  private final DigitalRiverPaymentProviderServiceImpl digitalRiverPaymentProviderService;
  private final CountryInfoService countryInfoService;
  private final ClientConfigurationService clientConfigurationService;
  private final DynamoCountryConversionRepository dynamoCountryConversionRepository;
  private final ObjectMapper objectMapper;
  private final StatsDClient statsDClient;

  @Inject
  public DigitalRiverCountryConversionProvider(
      DigitalRiverPaymentProviderServiceImpl digitalRiverPaymentProviderService,
      CountryInfoService countryInfoService,
      ClientConfigurationService clientConfigurationService,
      DynamoCountryConversionRepository dynamoCountryConversionRepository,
      ObjectMapper objectMapper,
      StatsDClient statsDClient) {
    this.digitalRiverPaymentProviderService = digitalRiverPaymentProviderService;
    this.countryInfoService = countryInfoService;
    this.clientConfigurationService = clientConfigurationService;
    this.dynamoCountryConversionRepository = dynamoCountryConversionRepository;
    this.objectMapper = objectMapper;
    this.statsDClient = statsDClient;
  }

  public List<CountryConversion> update() {
    // FIXME: no errors thrown in this method? Should we be catching exceptions?
    List<CountryConversion> result = new ArrayList<>();
    List<CurrenciesByCountry> currenciesByCountries = retrieveCommonCountriesAndCurrencies();
    currenciesByCountries.forEach(
        (countryAndCurrencies) ->
            countryAndCurrencies
                .currencies()
                .forEach(
                    currency -> {
                      Optional<CountryConversion> countryConversion =
                          retrieveCountryConversion(countryAndCurrencies.code(), currency);
                      countryConversion.ifPresent(
                          cc -> {
                            dynamoCountryConversionRepository.save(cc);
                            result.add(cc);
                          });
                    }));
    return result;
  }

  @Override
  public Optional<CountryConversion> retrieveCountryConversion(String country, String currency) {
    DigitalRiverClientConfig drConfig = getDRConfig();
    try {
      statsDClient.increment(
          DD_PRICE_EXCHANGE_RATES_HITS,
          buildDDTags(country, currency, PaymentProviderIdentifier.DIGITAL_RIVER.getDescription()));
      DigitalRiverCountryConversionResponse response =
          digitalRiverPaymentProviderService.getCountryCurrencyTaxes(country, currency, drConfig);
      retrieveCommonCountriesAndCurrencies();
      return Optional.of(buildCountryConversion(response));

    } catch (RequestToProviderException e) {
      statsDClient.increment(
          DD_PRICE_EXCHANGE_RATES_ERROR,
          buildDDTags(country, currency, PaymentProviderIdentifier.DIGITAL_RIVER.getDescription()));
      Log.error("Could not retrieve exchange rates for " + country + " " + currency);
      // FIXME: this should not be returning empty, is a crash with DR
      return Optional.empty();
    }
  }

  private CountryConversion buildCountryConversion(DigitalRiverCountryConversionResponse response) {
    DigitalRiverClientConfig drConfig = getDRConfig();
    BigDecimal conversionFeeDR = getConversionFeeDR(response.currencyCode(), drConfig);
    BigDecimal taxRate =
        getTaxRate(
            response.exchangeRate().bigDecimal(),
            response.conversionFactor().bigDecimal(),
            conversionFeeDR);
    boolean taxIncluded = isTaxIncluded(response.countryCode());

    return new CountryConversion.Builder()
        .setCountryId(response.countryCode())
        .setCurrencyId(response.currencyCode())
        .setExchangeRate(response.exchangeRate().bigDecimal())
        .setTaxIncluded(taxIncluded)
        .setTaxRate(taxRate)
        .setConversionFactor(response.exchangeRate().bigDecimal())
        .setUpdatedAt(Instant.now())
        .build();
  }

  private BigDecimal getConversionFeeDR(String currencyId, DigitalRiverClientConfig drConfig) {
    Map<String, String> conversionFees = getConversionFeesFromConfig(drConfig);

    if (conversionFees.containsKey(currencyId)) {
      return new BigDecimal(conversionFees.get(currencyId));
    }
    return new BigDecimal(drConfig.getDefaultConversionFee());
  }

  private boolean isTaxIncluded(String countryId) {
    return countryInfoService
        .getCountryDetails(countryId, PaymentProviderIdentifier.DIGITAL_RIVER)
        .isTaxIncluded();
  }

  private BigDecimal getTaxRate(
      BigDecimal exchangeRateDR, BigDecimal conversionFactorDR, BigDecimal conversionFeeDR) {

    return ((conversionFactorDR.subtract(conversionFactorDR.multiply(conversionFeeDR)))
            .subtract(exchangeRateDR))
        .divide(exchangeRateDR, SCALE_2, RoundingMode.HALF_UP);
  }

  private List<CurrenciesByCountry> retrieveCommonCountriesAndCurrencies() {
    PlaygamiPaymentsClientConfig clientConfiguration =
        clientConfigurationService.getConfiguration(
            ConfigurationProviderIdentifier.PLAYGAMI_PAYMENTS,
            ClientConfigurationService.NO_API_KEY);

    String currenciesByCountryString = clientConfiguration.getCurrenciesByCountry();
    try {
      return Arrays.asList(
          objectMapper.readValue(currenciesByCountryString, CurrenciesByCountry[].class));
    } catch (JsonProcessingException e) {
      throw new ParsingParametersException(e);
    }
  }

  private DigitalRiverClientConfig getDRConfig() {
    return clientConfigurationService.getConfiguration(
        ConfigurationProviderIdentifier.DIGITAL_RIVER, ClientConfigurationService.NO_API_KEY);
  }

  private Map<String, String> getConversionFeesFromConfig(DigitalRiverClientConfig drConfig) {
    String conversionFeesString = drConfig.getConversionFees();
    TypeReference<HashMap<String, String>> typeRef = new TypeReference<>() {};
    Map<String, String> conversionFees;
    try {
      conversionFees = objectMapper.readValue(conversionFeesString, typeRef);
    } catch (JsonProcessingException e) {
      throw new ParsingParametersException(e);
    }

    return conversionFees;
  }

  private String[] buildDDTags(final String country, String currency, String provider) {
    return MetricsUtils.buildTags(
        new String[] {TAG_COUNTRY, TAG_CURRENCY, TAG_PROVIDER},
        new String[] {country, currency, provider});
  }
}
