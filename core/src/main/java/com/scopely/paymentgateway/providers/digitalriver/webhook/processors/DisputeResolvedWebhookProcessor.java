package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_DR_DISPUTE_RESOLVED_WEBHOOK_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_DR_DISPUTE_RESOLVED_WEBHOOK_SUCCESS;
import static com.scopely.paymentgateway.constants.StatsConstants.DR_WEBHOOK;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;

import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeStatus;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverDisputeData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.services.reversal.dispute.DisputePropagator;
import com.scopely.paymentgateway.services.reversal.dispute.DisputeService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DisputeResolvedWebhookProcessor extends BaseDisputeWebhookProcessor {
  private static final String LOST_DISPUTE_STATUS = "suppressed";

  @Inject
  public DisputeResolvedWebhookProcessor(
      PaymentService paymentService,
      DisputeService disputeService,
      DisputePropagator disputePropagator,
      DisputeFactory disputeFactory,
      StatsDClient statsDClient) {
    super(
        paymentService,
        disputeService,
        disputePropagator,
        disputeFactory,
        statsDClient,
        List.of(DigitalRiverWebhookEvent.DISPUTE_RESOLVED));
  }

  @Override
  @Trace(operationName = DR_WEBHOOK, resourceName = "order.dispute.resolved")
  public void execute(DigitalRiverDisputeData requestData)
      throws PaymentNotFoundException, WebhookException {
    var payment =
        getPaymentService()
            .getPaymentByOrderId(requestData.getOrderId())
            .orElseThrow(
                () -> {
                  getStatsDClient().increment(DD_DR_DISPUTE_RESOLVED_WEBHOOK_ERROR);
                  return PaymentNotFoundException.createPaymentNotFoundExceptionByOrderId(
                      requestData.getOrderId());
                });
    var status =
        LOST_DISPUTE_STATUS.equals(requestData.getState()) ? DisputeStatus.LOST : DisputeStatus.WON;
    Dispute dispute = buildDispute(requestData, payment, status);
    saveDispute(dispute, payment, DD_DR_DISPUTE_RESOLVED_WEBHOOK_ERROR);
    sendDisputeToSqs(dispute, payment.getPaymentId(), DD_DR_DISPUTE_RESOLVED_WEBHOOK_ERROR);
    getStatsDClient()
        .increment(
            DD_DR_DISPUTE_RESOLVED_WEBHOOK_SUCCESS,
            MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
    getLogger(payment).debug("DISPUTE_RESOLVED event received: {}", requestData);
  }
}
