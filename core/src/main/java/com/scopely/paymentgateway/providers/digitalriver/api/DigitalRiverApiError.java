package com.scopely.paymentgateway.providers.digitalriver.api;

import java.util.Arrays;
import java.util.Optional;

public enum DigitalRiverApiError {
  POSTAL_CODE_INVALID("bad_request", "postal_code_invalid", "101"),
  UNKNOWN("unknown", "unknown", "0");

  private final String type;
  private final String code;
  private final String internalCode;

  DigitalRiverApiError(String type, String code, String internalCode) {
    this.type = type;
    this.code = code;
    this.internalCode = internalCode;
  }

  public static Optional<DigitalRiverApiError> findByCode(String code) {
    return Arrays.stream(DigitalRiverApiError.values())
        .filter(error -> error.code.equals(code))
        .findFirst();
  }

  public String getType() {
    return type;
  }

  public String getCode() {
    return code;
  }

  public String getInternalCode() {
    return internalCode;
  }
}
