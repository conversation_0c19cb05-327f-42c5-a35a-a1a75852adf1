package com.scopely.paymentgateway.providers.xsolla.webhook.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.providers.xsolla.model.PaymentDetails;
import com.scopely.paymentgateway.providers.xsolla.model.Refund;
import com.scopely.paymentgateway.providers.xsolla.model.Transaction;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = XsollaRefundWebhookEvent.Builder.class)
public interface XsollaRefundWebhookEvent extends XsollaWebhookEvent {

  Transaction getTransaction();

  @JsonProperty("refund_details")
  Refund getRefund();

  @JsonProperty("payment_details")
  PaymentDetails getPaymentDetails();

  class Builder extends XsollaRefundWebhookEvent_Builder {

    @Override
    @JsonProperty("payment_details")
    public Builder setPaymentDetails(PaymentDetails paymentDetails) {
      return super.setPaymentDetails(paymentDetails);
    }
  }
}
