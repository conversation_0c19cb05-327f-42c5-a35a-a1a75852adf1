package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_DR_ORDER_ACCEPTED_WEBHOOK_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_DR_ORDER_ACCEPTED_WEBHOOK_FULFILLED_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_DR_ORDER_ACCEPTED_WEBHOOK_SUCCESS;
import static com.scopely.paymentgateway.constants.StatsConstants.DR_WEBHOOK;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.exceptions.webhook.WebhookException.Error.UNEXPECTED_ERROR;

import com.scopely.paymentgateway.exceptions.NonRetryablePaymentException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.payment.ProviderStatus;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.providers.digitalriver.model.checkout.Item;
import com.scopely.paymentgateway.providers.digitalriver.model.order.OrderResponse;
import com.scopely.paymentgateway.providers.digitalriver.services.AcceptedPaymentFinalAction;
import com.scopely.paymentgateway.providers.digitalriver.services.DigitalRiverPaymentProviderService;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverOrderRequestData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.body.DigitalRiverWebhookOrderRequestBody;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.payment.FindPaymentBySessionId;
import com.scopely.paymentgateway.services.payment.ProcessPaymentError;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class OrderAcceptedWebhookProcessor
    implements DigitalRiverWebhookProcessor<
        DigitalRiverOrderRequestData, DigitalRiverWebhookOrderRequestBody> {

  private final FindPaymentBySessionId findPaymentBySessionId;
  private final DigitalRiverPaymentProviderService digitalRiverPaymentProviderService;
  private final ClientConfigurationService clientConfigurationService;
  private final AcceptedPaymentFinalAction acceptedPaymentFinalAction;
  private final ProcessPaymentError processPaymentError;
  private final StatsDClient statsDClient;

  @Inject
  public OrderAcceptedWebhookProcessor(
      FindPaymentBySessionId findPaymentBySessionId,
      DigitalRiverPaymentProviderService digitalRiverPaymentProviderService,
      ClientConfigurationService clientConfigurationService,
      AcceptedPaymentFinalAction acceptedPaymentFinalAction,
      ProcessPaymentError processPaymentError,
      StatsDClient statsDClient) {
    this.findPaymentBySessionId = findPaymentBySessionId;
    this.digitalRiverPaymentProviderService = digitalRiverPaymentProviderService;
    this.clientConfigurationService = clientConfigurationService;
    this.acceptedPaymentFinalAction = acceptedPaymentFinalAction;
    this.processPaymentError = processPaymentError;
    this.statsDClient = statsDClient;
  }

  @Override
  @Trace(operationName = DR_WEBHOOK, resourceName = "order.accepted")
  public void execute(DigitalRiverOrderRequestData request) throws WebhookException {
    Optional<Payment> optPayment =
        findPaymentBySessionId.execute(
            request.getSessionId(), PaymentProviderIdentifier.DIGITAL_RIVER);

    if (optPayment.isPresent()) {
      var payment = optPayment.get();
      DigitalRiverClientConfig digitalRiverClientConfig =
          clientConfigurationService.getConfiguration(
              ConfigurationProviderIdentifier.DIGITAL_RIVER,
              payment.getApiKey(),
              payment.isSandbox());

      if (isNotCompletedYet(payment)) {
        OrderResponse response;
        try {
          response =
              digitalRiverPaymentProviderService.getOrder(
                  payment.getOrderId(), digitalRiverClientConfig);
        } catch (RequestToProviderException e) {
          statsDClient.increment(
              DD_DR_ORDER_ACCEPTED_WEBHOOK_ERROR,
              MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
          throw new WebhookException(UNEXPECTED_ERROR, PaymentProviderIdentifier.DIGITAL_RIVER, e);
        }

        Payment processedPayment =
            Payment.Builder.from(payment)
                .setFulfillmentItems(
                    response.items().stream()
                        .map(Item::mapToFulfillmentItem)
                        .collect(Collectors.toList()))
                .setProviderStatus(ProviderStatus.ACCEPTED)
                .build();

        var logger =
            new PaymentGatewayLogBuilder()
                .addPayment(payment.getApiKey(), payment.getPaymentId())
                .build();
        try {
          acceptedPaymentFinalAction.execute(processedPayment, digitalRiverClientConfig);
        } catch (NonRetryablePaymentException exception) {
          statsDClient.increment(
              DD_DR_ORDER_ACCEPTED_WEBHOOK_ERROR,
              MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
          processPaymentError.execute(processedPayment, exception);
          logger.info(exception, "Unable to finalize the accepted order");
          return;
        } catch (Exception exception) {

          // TODO: add test cases for this scenario
          findPaymentBySessionId
              .execute(request.getSessionId(), PaymentProviderIdentifier.DIGITAL_RIVER)
              .ifPresent(
                  latestPayment -> {
                    if (isNotCompletedYet(payment)) {
                      statsDClient.increment(
                          DD_DR_ORDER_ACCEPTED_WEBHOOK_ERROR,
                          MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
                      processPaymentError.execute(processedPayment, exception.getMessage());
                    } else {
                      // if this fails after fulfilling the order, we don't send the error email
                      statsDClient.increment(
                          DD_DR_ORDER_ACCEPTED_WEBHOOK_FULFILLED_ERROR,
                          MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
                    }
                    logger.error(
                        exception,
                        "Unhandled exception finalizing accepted order. Last state: {}",
                        latestPayment.getProviderStatus());
                  });
          return;
        }
        statsDClient.increment(
            DD_DR_ORDER_ACCEPTED_WEBHOOK_SUCCESS,
            MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
      }
    }
  }

  private boolean isNotCompletedYet(Payment payment) {
    return payment.getProviderStatus() == ProviderStatus.IN_REVIEW
        || payment.getProviderStatus() == ProviderStatus.PENDING_PAYMENT;
  }

  @Override
  public List<DigitalRiverWebhookEvent> getAssociatedEvents() {
    return List.of(DigitalRiverWebhookEvent.ORDER_ACCEPTED);
  }

  @Override
  public Class<DigitalRiverWebhookOrderRequestBody> getModel() {
    return DigitalRiverWebhookOrderRequestBody.class;
  }
}
