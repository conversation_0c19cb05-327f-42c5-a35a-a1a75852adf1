package com.scopely.paymentgateway.providers.digitalriver.webhook.events.body;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.DigitalRiverProviderRefundStatus;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.RefundItem;
import java.math.BigDecimal;
import java.util.List;
import javax.annotation.Nullable;

public interface DigitalRiverRefundBody {
  String getId();

  String getOrderId();

  DigitalRiverProviderRefundStatus getState();

  String getCreatedTime();

  // Amount requested to be refunded
  @JsonProperty("amount")
  PaymentBigDecimal getLocalAmount();

  // total amount refunded
  // DR returns the prices in the local currency of the user
  @JsonProperty("refundedAmount")
  PaymentBigDecimal getRefundedLocalAmount();

  String getCurrency();

  @Nullable
  RefundReason getReason();

  @Nullable
  List<RefundItem> getItems();

  /**
   * Retrieve all items purchased and add the price as the total of the refund
   *
   * @param items list of items to retrieve the amount
   * @return
   */
  static PaymentBigDecimal calculateLocalAmountFromPurchasedItems(List<RefundItem> items) {
    return new PaymentBigDecimal(
        items.stream().map(item -> item.amount()).reduce(BigDecimal.ZERO, BigDecimal::add));
  }
}
