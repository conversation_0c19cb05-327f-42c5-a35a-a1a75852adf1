package com.scopely.paymentgateway.providers.digitalriver.model.checkout;

public record ProductDetails(
    String id,
    String skuGroupId,
    String name,
    String description,
    String url,
    String countryOfOrigin,
    String image,
    int weight,
    String weightUnit,
    String partNumber) {
  public static final String DEFAULT_VALUE = "";
  public static final int DEFAULT_WEIGHT = 0;
  public static final String VIP = "VIP";

  public ProductDetails(
      String skuGroupId, String productName, String countryOfOrigin, boolean isVip) {
    this(
        DEFAULT_VALUE,
        skuGroupId,
        productName,
        DEFAULT_VALUE,
        DEFAULT_VALUE,
        countryOfOrigin,
        DEFAULT_VALUE,
        DEFAULT_WEIGHT,
        DEFAULT_VALUE,
        // this is the way we tell DR that the user is VIP to leverage fraud detection and avoid
        // false positives
        (isVip ? VIP : DEFAULT_VALUE));
  }
}
