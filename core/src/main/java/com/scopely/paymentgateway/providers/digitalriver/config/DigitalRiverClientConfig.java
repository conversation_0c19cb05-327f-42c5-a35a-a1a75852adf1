package com.scopely.paymentgateway.providers.digitalriver.config;

import static com.scopely.paymentgateway.model.client.ClientConfigAttributes.PUBLIC_KEY_ATTRIBUTE;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.client.ClientConfigAttributes;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.utils.validation.SecuredField;
import com.scopely.paymentgateway.utils.validation.ValidJsonString;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import java.beans.Transient;
import java.util.Map;
import org.inferred.freebuilder.FreeBuilder;

@JsonDeserialize(builder = DigitalRiverClientConfig.Builder.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonTypeName(ClientConfigAttributes.DIGITAL_RIVER_VALUE)
@FreeBuilder
public interface DigitalRiverClientConfig extends ClientConfiguration {

  @SecuredField
  @NotBlank(message = "Secret key cannot be blank")
  String getSecretKey();

  @NotBlank(message = "SKU group id cannot be blank")
  String getSkuGroupId();

  @SecuredField
  @NotBlank(message = "Webhook secret key cannot be blank")
  String getWebhookSecretKey();

  @NotBlank(message = "Public key cannot be blank")
  String getPublicKey();

  @NotBlank(message = "Conversion fees cannot be blank")
  @ValidJsonString(message = "Invalid json conversion fees")
  String getConversionFees();

  @Positive(message = "Default conversion fee must be a >= ZERO decimal")
  String getDefaultConversionFee();

  @JsonIgnore
  @Transient
  @Override
  default Map<String, Object> getPublicConfig() {
    return Map.of(PUBLIC_KEY_ATTRIBUTE, this.getPublicKey());
  }

  class Builder extends DigitalRiverClientConfig_Builder {

    public Builder() {}
  }
}
