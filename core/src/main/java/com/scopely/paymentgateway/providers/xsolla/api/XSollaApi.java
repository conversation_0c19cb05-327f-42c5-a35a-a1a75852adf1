package com.scopely.paymentgateway.providers.xsolla.api;

import com.scopely.paymentgateway.providers.xsolla.model.checkout.CheckoutRequest;
import com.scopely.paymentgateway.providers.xsolla.model.checkout.CheckoutResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

@Path("api/v2/project/{projectId}/admin")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface XSollaApi {

  @POST
  @Path("payment")
  CheckoutResponse createCheckout(
      @HeaderParam("Authorization") String auth,
      @PathParam("projectId") int projectId,
      CheckoutRequest checkoutRequest);
}
