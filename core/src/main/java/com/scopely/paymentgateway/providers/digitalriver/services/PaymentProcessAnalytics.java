package com.scopely.paymentgateway.providers.digitalriver.services;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_DOMAIN_CREATE_PAYMENT_BLOCKED;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_TOTAL_CLAIMS_SUCCESS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_TOTAL_INCOME;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_TOTAL_PAYMENTS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PROVIDER;

import com.scopely.paymentgateway.analytics.events.TitanEventService;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import com.scopely.paymentgateway.model.user.User;
import com.scopely.paymentgateway.model.user.UserBlockActionType;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.timgroup.statsd.StatsDClient;
import java.math.BigDecimal;
import java.util.Map;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class PaymentProcessAnalytics {
  private final TitanEventService titanEventService;
  private final StatsDClient statsDClient;

  @Inject
  public PaymentProcessAnalytics(TitanEventService titanEventService, StatsDClient statsDClient) {
    this.statsDClient = statsDClient;
    this.titanEventService = titanEventService;
  }

  public void sendStartPaymentAnalyticEvent(Payment payment) {
    titanEventService.sendProcessPaymentStartedEvent(payment);
  }

  public void sendPaymentFinishedEvents(Payment payment) {
    var metricsTags =
        MetricsUtils.buildTags(
            new String[] {TAG_API_KEY, TAG_PROVIDER},
            new String[] {
              payment.getApiKey(), payment.getProviderData().getProvider().getDescription()
            });
    statsDClient.increment(DD_TOTAL_PAYMENTS, metricsTags);
    statsDClient.count(
        DD_TOTAL_INCOME,
        payment
            .getPriceData()
            .getBasePrice()
            .getTotalAmount()
            .multiply(new BigDecimal(100))
            .longValueExact(),
        metricsTags);
    titanEventService.sendPaymentTransactionEvent(payment);
    titanEventService.sendFinishedPaymentEvent(payment);
  }

  public void sendClaimedPaymentEvent(Payment payment) {
    statsDClient.increment(
        DD_TOTAL_CLAIMS_SUCCESS,
        MetricsUtils.buildTags(
            new String[] {TAG_API_KEY, TAG_PROVIDER},
            new String[] {payment.getApiKey(), payment.getProviderData().getProvider().name()}));
    titanEventService.sendClaimedPaymentEvent(payment);
  }

  public void sendProviderSelectionEvent(Payment payment) {
    titanEventService.sendProviderSelectionEvent(payment);
  }

  public void sendUpdatePaymentEvent(Payment payment, Map<String, String> processAttributes) {
    titanEventService.sendPaymentUpdateEvent(payment, processAttributes);
  }

  public void sendRefundEvent(Refund refund, Payment payment) {
    titanEventService.sendRefundEvent(refund, payment);
  }

  public void sendDisputeEvent(
      Dispute dispute, Payment payment, boolean isUserVip, boolean isUserBlocked) {
    titanEventService.sendDisputeEvent(dispute, payment, isUserVip, isUserBlocked);
  }

  public void sendUserBlockEvent(
      User user, String paymentId, UserBlockActionType userBlockActionType, boolean isUserVip) {
    titanEventService.sendUserBlockEvent(user, paymentId, userBlockActionType, isUserVip);
  }

  public void sendPaymentBlockedEvent(Payment payment, PaymentProviderIdentifier provider) {
    statsDClient.increment(
        DD_DOMAIN_CREATE_PAYMENT_BLOCKED,
        MetricsUtils.buildTags(
            Map.of(
                TAG_API_KEY, payment.getApiKey(),
                TAG_PROVIDER, provider.getDescription())));
    titanEventService.sendPaymentBlockedEvent(payment);
  }
}
