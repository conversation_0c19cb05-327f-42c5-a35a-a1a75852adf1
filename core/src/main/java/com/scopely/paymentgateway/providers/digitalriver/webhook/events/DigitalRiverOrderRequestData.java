package com.scopely.paymentgateway.providers.digitalriver.webhook.events;

import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
public abstract class DigitalRiverOrderRequestData implements DigitalRiverRequestData {

  public abstract String getSessionId();

  public abstract String getSourceId();

  public abstract String getState();

  public static class Builder extends DigitalRiverOrderRequestData_Builder {
    public Builder() {}
  }
}
