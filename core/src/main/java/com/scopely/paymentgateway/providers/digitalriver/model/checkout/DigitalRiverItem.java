package com.scopely.paymentgateway.providers.digitalriver.model.checkout;

import com.scopely.paymentgateway.model.payment.ProductType;
import java.math.BigDecimal;
import java.util.Map;

public record DigitalRiverItem(
    ProductDetails productDetails,
    Integer quantity,
    BigDecimal price,
    Map<String, String> metadata) {

  public static final String PRODUCT_TYPE = "productType";
  public static final String API_KEY = "apiKey";

  public DigitalRiverItem(
      String itemName,
      ProductType productType,
      String country,
      BigDecimal price,
      String skuGroupId,
      boolean isVip,
      String apiKey,
      int quantity) {
    this(
        new ProductDetails(skuGroupId, itemName, country, isVip),
        quantity,
        price,
        Map.of(API_KEY, apiKey, PRODUCT_TYPE, productType.getDescription()));
  }
}
