package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.DR_WEBHOOK;

import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.model.refund.RefundStatus;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.DigitalRiverRefundProviderData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverRefundFailedRequestData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.body.DigitalRiverRefundFailedRequestBody;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.services.refund.RefundService;
import datadog.trace.api.Trace;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class RefundFailedWebhookProcessor
    implements DigitalRiverWebhookProcessor<
        DigitalRiverRefundFailedRequestData, DigitalRiverRefundFailedRequestBody> {

  private final PaymentService paymentService;
  private final RefundService refundService;

  @Inject
  public RefundFailedWebhookProcessor(PaymentService paymentService, RefundService refundService) {
    this.paymentService = paymentService;
    this.refundService = refundService;
  }

  @Override
  @Trace(operationName = DR_WEBHOOK, resourceName = "refund.failed")
  public void execute(DigitalRiverRefundFailedRequestData request) throws PaymentNotFoundException {
    var payment = getPaymentByRefundRequest(request);
    // retrieve the refund
    var refund =
        refundService
            .optRefundById(request.getRefundId())
            .orElseGet(() -> request.toRefund(payment));
    refundService.saveRefund(updateRefund(refund, request), payment);
  }

  private Refund updateRefund(Refund refund, DigitalRiverRefundFailedRequestData request) {
    var metadata =
        DigitalRiverRefundProviderData.Builder.from(
                (DigitalRiverRefundProviderData) refund.getProviderData())
            .setStatus(request.getState())
            .build();

    // change the status from the refund
    return Refund.Builder.from(refund)
        .setStatus(RefundStatus.FAILED)
        .setFailureReason(
            "The refund failed in the provider side, please perform this refund manually")
        .setProviderData(metadata)
        .build();
  }

  private Payment getPaymentByRefundRequest(DigitalRiverRefundFailedRequestData request)
      throws PaymentNotFoundException {
    return paymentService.getPaymentByOrderIdUnchecked(request.getOrderId());
  }

  @Override
  public List<DigitalRiverWebhookEvent> getAssociatedEvents() {
    return List.of(DigitalRiverWebhookEvent.REFUND_FAILED);
  }

  @Override
  public Class<DigitalRiverRefundFailedRequestBody> getModel() {
    return DigitalRiverRefundFailedRequestBody.class;
  }
}
