package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_DR_CHARGEBACK_WEBHOOK_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_DR_CHARGEBACK_WEBHOOK_QUEUEING_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_DR_CHARGEBACK_WEBHOOK_SUCCESS;
import static com.scopely.paymentgateway.constants.StatsConstants.DR_WEBHOOK;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.exceptions.dispute.ChargebackServiceException;
import com.scopely.paymentgateway.exceptions.dispute.DisputeServiceException;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.reversal.Chargeback;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverChargebackData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookRequest;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.body.DigitalRiverWebhookSalesTransactionRequestBody;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.services.reversal.ChargebackService;
import com.scopely.paymentgateway.services.reversal.dispute.DisputeService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.PaymentAmountCalculator;
import com.scopely.proteus.logging.Log;
import com.scopely.proteus.sqs.publisher.SendMessageException;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import java.time.Clock;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.javamoney.moneta.Money;

@Singleton
public class ChargebackWebhookProcessor
    implements DigitalRiverWebhookProcessor<
        DigitalRiverChargebackData, DigitalRiverWebhookSalesTransactionRequestBody> {
  private final PaymentService paymentService;
  private final ChargebackService chargebackService;
  private final DisputeService disputeService;
  private final StatsDClient statsDClient;
  private final Clock clock;

  @Inject
  public ChargebackWebhookProcessor(
      PaymentService paymentService,
      ChargebackService chargebackService,
      DisputeService disputeService,
      StatsDClient statsDClient,
      Clock clock) {
    this.paymentService = paymentService;
    this.chargebackService = chargebackService;
    this.disputeService = disputeService;
    this.statsDClient = statsDClient;
    this.clock = clock;
  }

  @Override
  public DigitalRiverChargebackData toRequestData(
      DigitalRiverWebhookRequest request, ObjectMapper mapper) {
    var body = mapper.convertValue(request.getEventData(), getModel());
    return new DigitalRiverChargebackData.Builder()
        .setRequestId(request.getId())
        .setChargebackId(body.id())
        .setOrderId(body.orderId())
        .setChargebackAmount(body.amount().getMoneyValue(body.currency()))
        .setChargebackType(body.type())
        .build();
  }

  @Override
  public List<DigitalRiverWebhookEvent> getAssociatedEvents() {
    return List.of(DigitalRiverWebhookEvent.DISPUTE_CHARGEBACK);
  }

  @Override
  public Class<DigitalRiverWebhookSalesTransactionRequestBody> getModel() {
    return DigitalRiverWebhookSalesTransactionRequestBody.class;
  }

  @Override
  @Trace(operationName = DR_WEBHOOK, resourceName = "order.chargeback")
  public void execute(DigitalRiverChargebackData requestData)
      throws PaymentNotFoundException, WebhookException {
    if (requestData.getChargebackAmount().isZero()) {
      new PaymentGatewayLogBuilder()
          .build()
          .debug("Discarding chargeback with zero amount: {}", requestData);
      return;
    }
    var payment =
        paymentService
            .getPaymentByOrderId(requestData.getOrderId())
            .orElseThrow(
                () -> {
                  statsDClient.increment(DD_DR_CHARGEBACK_WEBHOOK_ERROR);
                  return PaymentNotFoundException.createPaymentNotFoundExceptionByOrderId(
                      requestData.getOrderId());
                });
    Chargeback chargeback = buildChargeback(requestData, payment);
    try {
      chargebackService.save(chargeback, payment);
      checkNonContestableChargeback(requestData.getChargebackAmount().abs(), payment);
    } catch (DisputeServiceException | ChargebackServiceException e) {
      statsDClient.increment(
          DD_DR_CHARGEBACK_WEBHOOK_ERROR, MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
      getLogger(payment).error(e, "Couldn't save chargeback: {}", requestData);
      throw new WebhookException(
          "Couldn't save chargeback for payment %s".formatted(payment.getPaymentId()), e);
    }
    statsDClient.increment(
        DD_DR_CHARGEBACK_WEBHOOK_SUCCESS, MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
    getLogger(payment).debug("DISPUTE_CHARGEBACK event received: {}", requestData);
  }

  private Chargeback buildChargeback(DigitalRiverChargebackData request, Payment payment) {
    var now = clock.instant();
    // Always store amounts as positive numbers
    var localAmount = request.getChargebackAmount().abs();
    return new Chargeback.Builder()
        .setApiKey(payment.getApiKey())
        .setUserId(payment.getUserId())
        .setPaymentId(payment.getPaymentId())
        .setChargebackId(request.getChargebackId())
        .setChargebackReason(request.getChargebackType().getCode())
        .setCreatedAt(now)
        .setUpdatedAt(now)
        .setLocalAmount(localAmount)
        .setAmount(PaymentAmountCalculator.calculateBaseAmount(localAmount, payment))
        .build();
  }

  protected void checkNonContestableChargeback(Money chargebackAmount, Payment payment)
      throws WebhookException, DisputeServiceException {
    try {
      disputeService.updateDisputesByChargeback(chargebackAmount, payment);
    } catch (SendMessageException e) {
      statsDClient.increment(
          DD_DR_CHARGEBACK_WEBHOOK_QUEUEING_ERROR,
          MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
      var message =
          "Couldn't enqueue non contestable dispute for payment %s"
              .formatted(payment.getPaymentId());
      throw new WebhookException(message, e);
    }
  }

  private Log.MetadataLog getLogger(Payment payment) {
    return new PaymentGatewayLogBuilder()
        .addPayment(payment.getApiKey(), payment.getPaymentId())
        .build();
  }
}
