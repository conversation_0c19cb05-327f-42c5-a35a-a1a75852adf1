package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.DR_WEBHOOK;
import static java.util.Objects.isNull;

import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.exceptions.model.RefundNotFoundException;
import com.scopely.paymentgateway.model.mailchimp.EmailType;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.DigitalRiverRefundProviderData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverRefundCompleteRequestData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.body.DigitalRiverRefundCompleteRequestBody;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.services.email.ReceiptMailProcessor;
import com.scopely.paymentgateway.services.refund.RefundService;
import datadog.trace.api.Trace;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class RefundCompletedWebhookProcessor
    implements DigitalRiverWebhookProcessor<
        DigitalRiverRefundCompleteRequestData, DigitalRiverRefundCompleteRequestBody> {

  private final PaymentService paymentService;
  private final RefundService refundService;
  private final ReceiptMailProcessor receiptMailProcessor;

  @Inject
  public RefundCompletedWebhookProcessor(
      PaymentService paymentService,
      RefundService refundService,
      ReceiptMailProcessor receiptMailProcessor) {
    this.paymentService = paymentService;
    this.refundService = refundService;
    this.receiptMailProcessor = receiptMailProcessor;
  }

  @Override
  @Trace(operationName = DR_WEBHOOK, resourceName = "refund.complete")
  public void execute(DigitalRiverRefundCompleteRequestData request)
      throws PaymentNotFoundException, RefundNotFoundException {
    var payment = getPaymentByRefundRequest(request);
    // retrieve the refund
    var refund =
        refundService
            .optRefundById(request.getRefundId())
            .orElseGet(() -> request.toRefund(payment));
    // update always the refund with the complete event
    var updatedRefund = refundService.saveRefund(updateRefund(refund, request), payment);
    if (isNull(updatedRefund.getReceiptId())) {
      receiptMailProcessor.processRefundReceipt(updatedRefund, EmailType.SUCCESS);
    }
  }

  private Refund updateRefund(Refund refund, DigitalRiverRefundCompleteRequestData request) {
    var metadata =
        DigitalRiverRefundProviderData.Builder.from(
                (DigitalRiverRefundProviderData) refund.getProviderData())
            .setStatus(request.getState())
            .build();
    return Refund.Builder.from(refund)
        // we only update the refunded local amount because DR doesn't provide the base amount
        .setRefundedLocalAmount(request.getRefundedLocalAmount())
        .setProviderData(metadata)
        .setStatus(request.getState().getRefundStatus())
        .build();
  }

  private Payment getPaymentByRefundRequest(DigitalRiverRefundCompleteRequestData request)
      throws PaymentNotFoundException {
    return paymentService
        .getPaymentByOrderId(request.getOrderId())
        .orElseThrow(
            () ->
                new PaymentNotFoundException(
                    "Payment with provider id " + request.getOrderId() + " not found"));
  }

  @Override
  public List<DigitalRiverWebhookEvent> getAssociatedEvents() {
    return List.of(DigitalRiverWebhookEvent.REFUND_COMPLETE);
  }

  @Override
  public Class<DigitalRiverRefundCompleteRequestBody> getModel() {
    return DigitalRiverRefundCompleteRequestBody.class;
  }
}
