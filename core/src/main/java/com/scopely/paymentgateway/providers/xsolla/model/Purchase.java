package com.scopely.paymentgateway.providers.xsolla.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public record Purchase(
    CurrencyAndAmount checkout,
    CurrencyAndAmount total,
    @JsonProperty("virtual_currency") VirtualCurrency virtualCurrency,
    Subscription subscription,
    @JsonProperty("virtual_items") VirtualItems virtualItems,
    @JsonProperty("pin_codes") PinCodes pinCodes,
    Gift gift,
    List<Promotion> promotions,
    Coupon coupon) {}
