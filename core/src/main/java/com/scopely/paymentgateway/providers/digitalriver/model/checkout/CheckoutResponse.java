package com.scopely.paymentgateway.providers.digitalriver.model.checkout;

import com.scopely.paymentgateway.model.payment.ItemData;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PriceDetail;
import com.scopely.paymentgateway.model.provider.ProviderCheckout;
import com.scopely.paymentgateway.providers.digitalriver.model.order.BillTo;
import com.scopely.paymentgateway.providers.digitalriver.model.order.Item;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

public record CheckoutResponse(
    String id,
    String sourceId,
    String createdTime,
    String currency,
    String email,
    PaymentBigDecimal totalAmount,
    PaymentBigDecimal subtotal,
    PaymentBigDecimal totalFees,
    PaymentBigDecimal totalTax,
    PaymentBigDecimal totalImporterTax,
    PaymentBigDecimal totalDuty,
    PaymentBigDecimal totalDiscount,
    PaymentBigDecimal totalShipping,
    List<Item> items,
    String upstreamId,
    String updatedTime,
    String browserIp,
    String locale,
    PurchaseLocation purchaseLocation,
    String customerType,
    SellingEntity sellingEntity,
    Boolean liveMode,
    Payment payment,
    BillTo billTo) {

  private static final BigDecimal ZERO_TAX_RATE = BigDecimal.ZERO;

  public ProviderCheckout toProviderCheckout(
      ItemData itemData, PriceData priceData, String locale) {
    return new ProviderCheckout.Builder()
        .setSessionId(Optional.ofNullable(this.payment().session()).map(Session::id).orElse(null))
        .setCheckoutId(this.id())
        .setPriceData(
            PriceData.Builder.from(priceData)
                .setTaxRate(getNewTaxRate())
                .setBasePrice(
                    priceData.getOriginalBasePrice().getNumberStripped(),
                    priceData.getBasePrice().getCurrency())
                .setLocalPrice(
                    PriceDetail.Builder.from(priceData.getLocalPrice())
                        .setTotalAmount(this.totalAmount.bigDecimal())
                        .setSubtotalAmount(this.subtotal.bigDecimal())
                        .setTaxAmount(this.totalTax.bigDecimal())
                        .build())
                .build())
        .setItemData(itemData)
        .setLocale(locale)
        .setEmail(
            Optional.ofNullable(billTo())
                .map(BillTo::email)
                .filter(Predicate.not(String::isBlank))
                .orElse(this.email()))
        .setCountry(
            this.billTo() != null && this.billTo().address() != null
                ? this.billTo.address().country()
                : this.purchaseLocation.country())
        .setBrowserIP(this.browserIp)
        .build();
  }

  public ProviderCheckout toProviderCheckout(
      com.scopely.paymentgateway.model.payment.Payment payment) {
    return new ProviderCheckout.Builder()
        .setSessionId(Optional.ofNullable(this.payment().session()).map(Session::id).orElse(null))
        .setCheckoutId(this.id())
        .setPriceData(
            PriceData.Builder.from(payment.getPriceData())
                .setTaxRate(getNewTaxRate())
                .setBasePrice(
                    payment.getPriceData().getOriginalBasePrice().getNumberStripped(),
                    payment.getPriceData().getBasePrice().getCurrency())
                .setLocalPrice(
                    PriceDetail.Builder.from(payment.getPriceData().getLocalPrice())
                        .setTotalAmount(this.totalAmount.bigDecimal())
                        .setSubtotalAmount(this.subtotal.bigDecimal())
                        .setTaxAmount(this.totalTax.bigDecimal())
                        .build())
                .build())
        .setItemData(payment.getItemData())
        .setLocale(payment.getLocale())
        .setEmail(
            Optional.ofNullable(billTo())
                .map(BillTo::email)
                .filter(Predicate.not(String::isBlank))
                .orElse(this.email()))
        .setCountry(payment.getCountry())
        .setCompleteUserName(this.billTo() != null ? this.billTo.name() : null)
        .setBrowserIP(this.browserIp)
        .build();
  }

  public ProviderCheckout toProviderCheckout() {
    return new ProviderCheckout.Builder()
        .setSessionId(Optional.ofNullable(this.payment().session()).map(Session::id).orElse(null))
        .setCheckoutId(this.id())
        .setPriceData(
            new PriceData.Builder()
                .setLocalPrice(
                    new PriceDetail.Builder()
                        .setCurrency(this.currency())
                        .setTotalAmount(this.totalAmount.bigDecimal())
                        .setSubtotalAmount(this.subtotal.bigDecimal())
                        .setTaxAmount(this.totalTax.bigDecimal())
                        .build())
                .setBasePrice(
                    new PriceDetail.Builder()
                        .setCurrency(this.currency())
                        .setTotalAmount(this.totalAmount.bigDecimal())
                        .setSubtotalAmount(this.subtotal.bigDecimal())
                        .setTaxAmount(this.totalTax.bigDecimal())
                        .build())
                .setTaxRate(getNewTaxRate())
                .build())
        .setItemData(new ItemData.Builder().build())
        .setCountry(
            this.billTo() != null && this.billTo().address() != null
                ? this.billTo.address().country()
                : this.purchaseLocation.country())
        .setEmail(
            Optional.ofNullable(billTo())
                .map(BillTo::email)
                .filter(Predicate.not(String::isBlank))
                .orElse(this.email()))
        .setCompleteUserName(this.billTo() != null ? this.billTo.name() : null)
        .setBrowserIP(this.browserIp)
        .build();
  }

  private boolean hasTaxes() {
    return !this.totalTax().getMoneyValue(this.currency).isZero();
  }

  private BigDecimal getNewTaxRate() {
    return this.hasTaxes()
        ? this.items().stream().findFirst().map(item -> item.tax().rate()).orElse(ZERO_TAX_RATE)
        : ZERO_TAX_RATE;
  }
}
