package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_DR_DISPUTE_OPEN_WEBHOOK_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_DR_DISPUTE_OPEN_WEBHOOK_SUCCESS;
import static com.scopely.paymentgateway.constants.StatsConstants.DR_WEBHOOK;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;

import com.scopely.paymentgateway.exceptions.dispute.DisputeServiceException;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeStatus;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverDisputeData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.services.reversal.dispute.DisputePropagator;
import com.scopely.paymentgateway.services.reversal.dispute.DisputeService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DisputeOpenedWebhookProcessor extends BaseDisputeWebhookProcessor {

  @Inject
  public DisputeOpenedWebhookProcessor(
      PaymentService paymentService,
      DisputeService disputeService,
      DisputePropagator disputePropagator,
      DisputeFactory disputeFactory,
      StatsDClient statsDClient) {
    super(
        paymentService,
        disputeService,
        disputePropagator,
        disputeFactory,
        statsDClient,
        List.of(DigitalRiverWebhookEvent.DISPUTE_OPEN));
  }

  @Override
  @Trace(operationName = DR_WEBHOOK, resourceName = "order.dispute")
  public void execute(DigitalRiverDisputeData requestData)
      throws PaymentNotFoundException, WebhookException {
    var payment =
        getPaymentService()
            .getPaymentByOrderId(requestData.getOrderId())
            .orElseThrow(
                () -> {
                  getStatsDClient().increment(DD_DR_DISPUTE_OPEN_WEBHOOK_ERROR);
                  return PaymentNotFoundException.createPaymentNotFoundExceptionByOrderId(
                      requestData.getOrderId());
                });
    Dispute dispute = buildDispute(requestData, payment, DisputeStatus.NEW);
    var logger = getLogger(payment);
    var optPreviousDispute = retrievePreviousDispute(dispute.getDisputeId(), logger);
    if (optPreviousDispute.isEmpty()) {
      saveDispute(dispute, payment, DD_DR_DISPUTE_OPEN_WEBHOOK_ERROR);
      sendDisputeToSqs(dispute, payment.getPaymentId(), DD_DR_DISPUTE_OPEN_WEBHOOK_ERROR);
    } else {
      var previous = optPreviousDispute.get();
      // We only update the dispute if it was in non-contestable status
      if (previous.getStatus() == DisputeStatus.NON_CONTESTABLE) {
        saveDispute(mergeToLost(previous, dispute), payment, DD_DR_DISPUTE_OPEN_WEBHOOK_ERROR);
        // No need to send the dispute to SQS queue
      }
    }
    getStatsDClient()
        .increment(
            DD_DR_DISPUTE_OPEN_WEBHOOK_SUCCESS,
            MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
    logger.debug("DISPUTE_OPEN event received: {}", requestData);
  }

  private Optional<Dispute> retrievePreviousDispute(String disputeId, Log.MetadataLog logger)
      throws WebhookException {
    try {
      return getDisputeService().getById(disputeId);
    } catch (DisputeServiceException e) {
      getStatsDClient().increment(DD_DR_DISPUTE_OPEN_WEBHOOK_ERROR);
      logger.error("Couldn't retrieve previous dispute: {}", disputeId);
      throw new WebhookException("Couldn't retrieve previous dispute " + disputeId, e);
    }
  }

  private Dispute mergeToLost(Dispute previous, Dispute update) {
    return Dispute.Builder.from(update)
        .setCreatedAt(previous.getCreatedAt())
        .setVersion(previous.getVersion())
        .setStatus(DisputeStatus.LOST)
        .build();
  }
}
