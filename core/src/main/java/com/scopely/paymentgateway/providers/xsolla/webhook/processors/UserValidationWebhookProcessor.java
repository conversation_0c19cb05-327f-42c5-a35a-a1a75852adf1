package com.scopely.paymentgateway.providers.xsolla.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.XSOLLA_WEBHOOK;

import com.scopely.paymentgateway.providers.xsolla.webhook.constants.XsollaEventName;
import com.scopely.paymentgateway.providers.xsolla.webhook.events.XsollaUserValidationWebhookEvent;
import datadog.trace.api.Trace;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class UserValidationWebhookProcessor
    implements XsollaWebhookProcessor<XsollaUserValidationWebhookEvent> {

  @Inject
  public UserValidationWebhookProcessor() {}

  @Override
  @Trace(operationName = XSOLLA_WEBHOOK, resourceName = "user_validation")
  public void execute(XsollaUserValidationWebhookEvent xsollaWebhookRequest) {
    // TODO Validate that the user has an initiated payment and this payment is not expired
  }

  @Override
  public List<XsollaEventName> getAssociatedEvents() {
    return List.of(XsollaEventName.USER_VALIDATION);
  }

  @Override
  public Class<XsollaUserValidationWebhookEvent> getModel() {
    return XsollaUserValidationWebhookEvent.class;
  }
}
