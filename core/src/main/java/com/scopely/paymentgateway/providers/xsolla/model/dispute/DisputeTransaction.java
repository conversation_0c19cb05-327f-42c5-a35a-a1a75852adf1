package com.scopely.paymentgateway.providers.xsolla.model.dispute;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scopely.paymentgateway.providers.xsolla.model.CurrencyAndAmount;
import java.time.Instant;

public record DisputeTransaction(
    int id,
    @JsonProperty("date_create") Instant creationDate,
    CurrencyAndAmount total,
    @JsonProperty("payment_method") String paymentMethod,
    @JsonProperty("country_code") String countryCode,
    @JsonProperty("external_id") String externalId) {}
