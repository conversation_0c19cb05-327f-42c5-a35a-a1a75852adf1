package com.scopely.paymentgateway.providers.xsolla.model.checkout;

import com.scopely.paymentgateway.model.payment.ItemData;
import com.scopely.paymentgateway.model.payment.PaymentLocation;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PriceDetail;
import com.scopely.paymentgateway.model.provider.ProviderCheckout;

public record CheckoutResponse(String token) {

  public ProviderCheckout toCheckoutInformation(
      PaymentLocation paymentLocation,
      ItemData product,
      PriceData priceData,
      String contextProperties,
      String locale) {
    return new ProviderCheckout.Builder()
        .setToken(this.token())
        .setCheckoutId(token())
        .setPriceData(
            new PriceData.Builder()
                .setPricingMode(priceData.getPricingMode())
                .setLocalPrice(
                    new PriceDetail.Builder()
                        .setTotalAmount(priceData.getLocalPrice().getTotalAmount())
                        .setSubtotalAmount(priceData.getLocalPrice().getSubtotalAmount())
                        .setTaxAmount(priceData.getLocalPrice().getTaxAmount())
                        .setCurrency(priceData.getLocalPrice().getCurrency())
                        .build())
                .setBasePrice(
                    new PriceDetail.Builder()
                        .setTotalAmount(priceData.getBasePrice().getTotalAmount())
                        .setSubtotalAmount(priceData.getBasePrice().getSubtotalAmount())
                        .setTaxAmount(priceData.getBasePrice().getTaxAmount())
                        .setCurrency(priceData.getBasePrice().getCurrency())
                        .build())
                .setTaxIncluded(priceData.isTaxIncluded())
                .build())
        .setCountry(paymentLocation.getCountry())
        .setLocale(locale)
        .setItemData(product)
        .setBrowserIP(paymentLocation.getIpAddress())
        .setContextProperties(contextProperties)
        .build();
  }
}
