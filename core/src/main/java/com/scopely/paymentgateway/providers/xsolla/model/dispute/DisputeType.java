package com.scopely.paymentgateway.providers.xsolla.model.dispute;

import com.fasterxml.jackson.annotation.JsonValue;

public enum DisputeType {
  FIRST_TIME_CHARGEBACK("1st_time_chargeback"),
  SECOND_TIME_CHARGEBACK("2nd_time_chargeback"),
  ARBITRATION("arbitration"),
  RETRIEVAL("retrieval"),
  REPRESENTMENT("representment"),
  CHARGEBACK_REVERSAL("chargeback_reversal"),
  REPRESENTMENT_REVERSAL("representment_reversal"),
  REIMBURSEMENT("reimbursement"),
  DISPUTE("dispute"),
  CHARGEBACK("chargeback"),
  CLAIM("claim"),
  REIMBURSEMENT_REVERSAL("reimbursement_reversal"),
  INQUIRY("inquiry"),
  OTHER("other");

  private final String code;

  DisputeType(String code) {
    this.code = code;
  }

  @JsonValue
  public String getCode() {
    return code;
  }
}
