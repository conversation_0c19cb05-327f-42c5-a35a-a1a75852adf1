package com.scopely.paymentgateway.providers.xsolla.model.checkout;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record Header(
    @JsonProperty("close_button") Boolean closeButton,
    @JsonProperty("visible_logo") Boolean visibleLogo,
    @JsonProperty("close_button_icon") String closeButtonIcon) {}
