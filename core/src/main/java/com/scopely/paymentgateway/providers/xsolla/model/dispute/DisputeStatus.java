package com.scopely.paymentgateway.providers.xsolla.model.dispute;

import com.fasterxml.jackson.annotation.JsonValue;

public enum DisputeStatus {
  NEW("new"),
  ACCEPTED("accepted"),
  NO_ACTIONS_REQUIRED("no_actions_required"),
  WON("won"),
  LOST("lost");

  private final String code;

  DisputeStatus(String code) {
    this.code = code;
  }

  @JsonValue
  public String getCode() {
    return code;
  }
}
