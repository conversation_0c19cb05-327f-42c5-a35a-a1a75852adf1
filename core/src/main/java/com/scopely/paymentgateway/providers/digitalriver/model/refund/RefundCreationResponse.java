package com.scopely.paymentgateway.providers.digitalriver.model.refund;

import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.refund.ProviderRefundInformation;
import com.scopely.paymentgateway.model.refund.RefundReason;
import javax.annotation.Nullable;
import org.javamoney.moneta.Money;

public record RefundCreationResponse(
    String id,
    String createdTime,
    String orderId,
    String currency,
    PaymentBigDecimal amount,
    PaymentBigDecimal refundedAmount,
    RefundReason reason,
    @Nullable String failureReason,
    DigitalRiverProviderRefundStatus state) {

  public ProviderRefundInformation toRefundProviderInformation(Payment paymentToBeRefund) {

    return new ProviderRefundInformation.Builder()
        .setProvider(PaymentProviderIdentifier.DIGITAL_RIVER)
        .setRefundId(id())
        .setCreatedTime(createdTime())
        .setRefundReason(reason())
        .setFailureReason(failureReason())
        .setRefundedLocalAmount(refundedAmount().getMoneyValue(currency()))
        .setRefundedAmount(
            Money.of(
                paymentToBeRefund.getPriceData().getBasePrice().getTotalAmount(),
                paymentToBeRefund.getPriceData().getBasePrice().getCurrency()))
        .setStatus(state().getRefundStatus())
        .setProviderData(
            new DigitalRiverRefundProviderData.Builder()
                .setStatus(state())
                .setOrderId(orderId())
                .build())
        .build();
  }
}
