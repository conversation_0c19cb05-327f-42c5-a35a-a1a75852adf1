package com.scopely.paymentgateway.providers.digitalriver.services.countryconversion;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.exceptions.ParsingParametersException;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import java.util.HashMap;
import java.util.Map;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class PostalCodeManageService {

  private final ObjectMapper mapper;
  private final ClientConfigurationService clientConfigurationService;

  @Inject
  public PostalCodeManageService(
      ObjectMapper mapper, ClientConfigurationService clientConfigurationService) {
    this.mapper = mapper;
    this.clientConfigurationService = clientConfigurationService;
  }

  public String managePostalCode(
      String baseZipCode, String countryCode, PaymentProviderIdentifier provider, String apiKey) {
    Map<String, String> postalCodeString =
        getPostalCodesFromConfig(
            ConfigurationProviderIdentifier.fromPaymentProvider(provider), apiKey);

    if (postalCodeString.containsKey(countryCode)) {
      return postalCodeString.get(countryCode);
    }
    return baseZipCode;
  }

  public String getInitialPostalCode(
      String countryCode, PaymentProviderIdentifier provider, String apiKey) {
    Map<String, String> postalCodeString =
        getPostalCodesFromConfig(
            ConfigurationProviderIdentifier.fromPaymentProvider(provider), apiKey);

    if (postalCodeString.containsKey(countryCode)) {
      return postalCodeString.get(countryCode);
    }
    return null;
  }

  private Map<String, String> getPostalCodesFromConfig(
      ConfigurationProviderIdentifier provider, String apiKey) {
    TypeReference<HashMap<String, String>> typeRef = new TypeReference<>() {};
    Map<String, String> postalCodes;

    ClientConfiguration clientConfiguration =
        clientConfigurationService.getConfiguration(provider, apiKey);

    try {
      postalCodes = mapper.readValue(clientConfiguration.getPostalCodes(), typeRef);
    } catch (JsonProcessingException e) {
      throw new ParsingParametersException(e);
    } catch (Exception e) {
      return new HashMap<>();
    }

    return postalCodes;
  }
}
