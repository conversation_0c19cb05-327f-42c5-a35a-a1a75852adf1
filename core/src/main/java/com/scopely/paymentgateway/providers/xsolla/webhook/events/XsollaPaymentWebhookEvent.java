package com.scopely.paymentgateway.providers.xsolla.webhook.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.providers.xsolla.model.PaymentDetails;
import com.scopely.paymentgateway.providers.xsolla.model.Purchase;
import com.scopely.paymentgateway.providers.xsolla.model.Settings;
import com.scopely.paymentgateway.providers.xsolla.model.Transaction;
import com.scopely.paymentgateway.providers.xsolla.model.User;
import javax.annotation.Nullable;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = XsollaPaymentWebhookEvent.Builder.class)
public interface XsollaPaymentWebhookEvent extends XsollaWebhookEvent {

  @Nullable
  Settings getSettings();

  @Nullable
  Purchase getPurchase();

  @Nullable
  User getUser();

  Transaction getTransaction();

  @JsonProperty("payment_details")
  PaymentDetails getPaymentDetails();

  class Builder extends XsollaPaymentWebhookEvent_Builder {
    public Builder() {}

    @Override
    @JsonProperty("payment_details")
    public Builder setPaymentDetails(PaymentDetails paymentDetails) {
      return super.setPaymentDetails(paymentDetails);
    }
  }
}
