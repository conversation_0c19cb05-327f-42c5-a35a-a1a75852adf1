package com.scopely.paymentgateway.providers.digitalriver.services;

import com.scopely.paymentgateway.exceptions.CustomerIdException;
import com.scopely.paymentgateway.exceptions.ExpiredPaymentException;
import com.scopely.paymentgateway.exceptions.InvalidPaymentException;
import com.scopely.paymentgateway.exceptions.RejectedPaymentException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.repositories.PaymentRepository;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.customer.CustomerOperations;
import com.scopely.paymentgateway.services.payment.PaymentValidator;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DigitalRiverSourceDetachmentFromUserHandler {

  public static final String DETACH_SOURCE_CUSTOMER_CALLER_TAG = "detach_source_customer";
  private static final int NOT_FOUND = 404;
  private final DigitalRiverPaymentProviderService paymentProviderDigitalRiverService;
  private final PaymentRepository paymentRepository;
  private final CustomerOperations customerOperations;
  private final ClientConfigurationService clientConfigurationService;
  private final PaymentValidator paymentValidator;

  @Inject
  public DigitalRiverSourceDetachmentFromUserHandler(
      DigitalRiverPaymentProviderService paymentProviderDigitalRiverService,
      PaymentRepository paymentRepository,
      CustomerOperations customerOperations,
      ClientConfigurationService clientConfigurationService,
      PaymentValidator paymentValidator) {
    this.paymentProviderDigitalRiverService = paymentProviderDigitalRiverService;
    this.paymentRepository = paymentRepository;
    this.customerOperations = customerOperations;
    this.clientConfigurationService = clientConfigurationService;
    this.paymentValidator = paymentValidator;
  }

  public void detach(String sourceId, String paymentId)
      throws InvalidPaymentException,
          RequestToProviderException,
          PaymentNotFoundException,
          ExpiredPaymentException,
          RejectedPaymentException {
    Payment payment = paymentRepository.getPaymentUnchecked(paymentId);

    paymentValidator.validatePayment(payment, DETACH_SOURCE_CUSTOMER_CALLER_TAG);
    this.detach(sourceId, payment);
  }

  public void detach(String sourceId, Payment payment) throws RequestToProviderException {

    Optional<String> externalIdOpt =
        customerOperations.getExternalCustomerId(
            payment.getUserId(), payment.getApiKey(), PaymentProviderIdentifier.DIGITAL_RIVER);

    String externalId =
        externalIdOpt.orElseThrow(
            () -> new CustomerIdException("User not found, could not detach source from customer"));

    this.detach(externalId, sourceId, payment.getApiKey(), payment.isSandbox());
  }

  public void detach(String externalCustomerId, String sourceId, String apiKey, boolean sandbox)
      throws RequestToProviderException {

    DigitalRiverClientConfig digitalRiverClientConfig =
        clientConfigurationService.getConfiguration(
            ConfigurationProviderIdentifier.DIGITAL_RIVER, apiKey, sandbox);

    try {
      paymentProviderDigitalRiverService.detachSourceFromCustomer(
          externalCustomerId, sourceId, digitalRiverClientConfig);
    } catch (RequestToProviderException e) {
      if (e.getHttpStatus() == NOT_FOUND) {
        throw new CustomerIdException(
            "User not found in Provider, could not detach source from customer", e);
      }
      throw e;
    }
  }
}
