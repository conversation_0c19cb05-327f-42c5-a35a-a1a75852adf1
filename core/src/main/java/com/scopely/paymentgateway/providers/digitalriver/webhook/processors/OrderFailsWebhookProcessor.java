package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.*;
import static com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent.*;

import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverOrderRequestData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.body.DigitalRiverWebhookOrderRequestBody;
import com.scopely.paymentgateway.services.payment.FindPaymentBySessionId;
import com.scopely.paymentgateway.services.payment.ProcessPaymentError;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class OrderFailsWebhookProcessor
    implements DigitalRiverWebhookProcessor<
        DigitalRiverOrderRequestData, DigitalRiverWebhookOrderRequestBody> {

  public static final String ORDER_FAILED = "Order Failed. Request status: ";

  private final FindPaymentBySessionId findPaymentBySessionId;
  private final ProcessPaymentError processPaymentError;
  private final StatsDClient statsDClient;

  @Inject
  public OrderFailsWebhookProcessor(
      FindPaymentBySessionId findPaymentBySessionId,
      ProcessPaymentError processPaymentError,
      StatsDClient statsDClient) {
    this.findPaymentBySessionId = findPaymentBySessionId;
    this.processPaymentError = processPaymentError;
    this.statsDClient = statsDClient;
  }

  @Trace(operationName = DR_WEBHOOK, resourceName = "order.charge.failed_order.blocked")
  public void execute(DigitalRiverOrderRequestData request) {
    Optional<Payment> optPayment =
        findPaymentBySessionId.execute(
            request.getSessionId(), PaymentProviderIdentifier.DIGITAL_RIVER);
    optPayment.ifPresent(
        payment -> {
          if (payment.getPaymentStatus() != PaymentStatus.FAILED) {
            processPaymentError.execute(payment, ORDER_FAILED.concat(request.getState()));
            statsDClient.increment(
                DD_DR_ORDER_FAIL_WEBHOOK_SUCCESS,
                MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
          }
        });
  }

  @Override
  public List<DigitalRiverWebhookEvent> getAssociatedEvents() {
    return List.of(ORDER_CHARGE_FAILED, ORDER_BLOCKED);
  }

  @Override
  public Class<DigitalRiverWebhookOrderRequestBody> getModel() {
    return DigitalRiverWebhookOrderRequestBody.class;
  }
}
