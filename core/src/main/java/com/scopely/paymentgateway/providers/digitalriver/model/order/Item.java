package com.scopely.paymentgateway.providers.digitalriver.model.order;

import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;

public record Item(
    String id,
    String skuId,
    PaymentBigDecimal amount,
    int quantity,
    Object metadata,
    String state,
    StateTransitions stateTransitions,
    Tax tax,
    ImporterTax importerTax,
    Duties duties,
    PaymentBigDecimal availableToRefundAmount,
    Fees fees) {}
