package com.scopely.paymentgateway.providers.digitalriver.webhook.events;

import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
public abstract class DigitalRiverSourceRequestData implements DigitalRiverRequestData {

  public abstract String getSessionId();

  public abstract String getSourceId();

  public abstract String getState();

  public abstract String getPaymentMethod();

  public static class Builder extends DigitalRiverSourceRequestData_Builder {
    public Builder() {}
  }
}
