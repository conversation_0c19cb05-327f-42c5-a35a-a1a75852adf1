package com.scopely.paymentgateway.providers.xsolla.model.paymentmethods;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scopely.paymentgateway.model.deserializer.BigDecimalCustomSerializer;
import java.math.BigDecimal;

public record Price(
    @JsonSerialize(using = BigDecimalCustomSerializer.class) BigDecimal fee_percent,
    @JsonSerialize(using = BigDecimalCustomSerializer.class) BigDecimal fee_fixed_value,
    @JsonProperty("fee_fixed_currency") String feeFixedCurrency,
    String currencies,
    String description) {}
