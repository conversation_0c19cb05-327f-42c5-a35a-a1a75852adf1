package com.scopely.paymentgateway.providers.digitalriver.webhook.events.body;

import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.providers.digitalriver.model.transaction.PayoutAmounts;
import com.scopely.paymentgateway.providers.digitalriver.model.transaction.SalesTransactionType;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;

public record DigitalRiverWebhookSalesTransactionRequestBody(
    String id,
    Instant createdTime,
    Instant updatedTime,
    Instant saleTime,
    String salesSummaryId,
    String currency,
    PaymentBigDecimal amount,
    SalesTransactionType type,
    String orderId,
    String skuId,
    BigDecimal quantity,
    PayoutAmounts payoutAmounts,
    String payerId,
    String payerName,
    String payeeId,
    String payeeName,
    boolean liveMode,
    String orderUpstreamId,
    String skuTaxCode,
    String shipFromCountry,
    String shipToCountry,
    String billToCountry,
    String shipFromState,
    String shipToState,
    String billToState,
    String paymentType,
    String lineItemId,
    Map<String, String> orderMetadata,
    Map<String, String> lineItemMetadata)
    implements DigitalRiverWebhookRequestBody {}
