package com.scopely.paymentgateway.providers.digitalriver.services.countryconversion;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.client.ClientConfiguration;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import java.util.Map;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DisclosureLinksManageService {

  private final ObjectMapper mapper;
  private final ClientConfigurationService clientConfigurationService;

  private static final String DEFAULT_LINKS = "DEFAULT";
  private static final Map<String, String> HARDCODED_LINKS =
      Map.of(
          "merchantId",
          "Digital River Ireland, Ltd.",
          "termsSale",
          "https://store.digitalriver.com/store/defaults/en_US/DisplayDRTermsAndConditionsPage/eCommerceProvider.Digital%20River%20Ireland%20Ltd.",
          "privacyPolicy",
          "https://store.digitalriver.com/store/defaults/en_US/DisplayDRPrivacyPolicyPage/eCommerceProvider.Digital%20River%20Ireland%20Ltd.",
          "resellerDisclosure",
          "https://store.digitalriver.com/store/defaults/en_US/DisplayDRAboutDigitalRiverPage/eCommerceProvider.Digital%20River%20Ireland%20Ltd.",
          "cookies",
          "https://store.digitalriver.com/store/defaults/en_US/DisplayDRCookiesPolicyPage/eCommerceProvider.Digital%20River%20Ireland%20Ltd.",
          "cancellationRight",
          "https://store.digitalriver.com/store/defaults/en_US/DisplayDRTermsAndConditionsPage/eCommerceProvider.Digital%20River%20Ireland%20Ltd.#cancellationRight",
          "legalNotice",
          "https://store.digitalriver.com/store/defaults/en_US/DisplayDRContactInformationPage/eCommerceProvider.Digital%20River%20Ireland%20Ltd.");

  @Inject
  public DisclosureLinksManageService(
      ObjectMapper mapper, ClientConfigurationService clientConfigurationService) {
    this.mapper = mapper;
    this.clientConfigurationService = clientConfigurationService;
  }

  public Map<String, String> getDisclosureLinks(
      String countryCode, PaymentProviderIdentifier provider, String apiKey) {
    Map<String, Map<String, String>> disclosureLinksMap =
        getDisclosureLinksFromConfig(provider, apiKey);

    if (disclosureLinksMap.isEmpty() || !disclosureLinksMap.containsKey(DEFAULT_LINKS)) {
      new PaymentGatewayLogBuilder()
          .addProvider(provider)
          .build()
          .warn("Couldn't find the disclosure links for " + provider.getDescription());
      return HARDCODED_LINKS;
    }

    return disclosureLinksMap.getOrDefault(countryCode, disclosureLinksMap.get(DEFAULT_LINKS));
  }

  private Map<String, Map<String, String>> getDisclosureLinksFromConfig(
      PaymentProviderIdentifier provider, String apiKey) {
    var log = new PaymentGatewayLogBuilder().addProvider(provider).build();
    ClientConfiguration clientConfiguration =
        clientConfigurationService.getConfiguration(
            ConfigurationProviderIdentifier.fromPaymentProvider(provider), apiKey);

    try {
      return mapper.readValue(clientConfiguration.getDisclosureLinks(), new TypeReference<>() {});
    } catch (JsonProcessingException e) {
      log.error(e, "Couldn't processing json disclosureLinks for provider {}", provider);
    } catch (Exception e) {
      log.error(e, "There was an error getting disclosureLinks");
    }
    return Map.of();
  }
}
