package com.scopely.paymentgateway.providers.digitalriver.webhook.processors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.exceptions.model.RefundNotFoundException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverRequestData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookEvent;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverWebhookRequest;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.body.DigitalRiverWebhookRequestBody;
import java.util.List;

public interface DigitalRiverWebhookProcessor<
    T extends DigitalRiverRequestData, U extends DigitalRiverWebhookRequestBody> {

  @SuppressWarnings("unchecked")
  default T toRequestData(DigitalRiverWebhookRequest request, ObjectMapper mapper) {
    return (T) mapper.convertValue(request.getEventData(), getModel()).toRequestData();
  }

  void execute(T requestData)
      throws WebhookException, PaymentNotFoundException, RefundNotFoundException;

  List<DigitalRiverWebhookEvent> getAssociatedEvents();

  Class<U> getModel();
}
