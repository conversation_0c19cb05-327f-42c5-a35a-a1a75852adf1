package com.scopely.paymentgateway.providers.digitalriver.services;

import com.scopely.paymentgateway.exceptions.DRProcessHandlerException;
import com.scopely.paymentgateway.exceptions.PaymentProviderIntentionNotUpdatableException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.model.payment.BillingAddress;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.ProviderStatus;
import com.scopely.paymentgateway.model.provider.ProviderCheckout;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.services.payment.UpdatePaymentService;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class SourceAndEmailAssociationService {

  private final DigitalRiverPaymentProviderService paymentProviderDigitalRiverService;
  private final UpdatePaymentService updatePaymentService;

  @Inject
  public SourceAndEmailAssociationService(
      DigitalRiverPaymentProviderService paymentProviderDigitalRiverService,
      UpdatePaymentService updatePaymentService) {
    this.paymentProviderDigitalRiverService = paymentProviderDigitalRiverService;
    this.updatePaymentService = updatePaymentService;
  }

  public Payment attach(
      Payment payment,
      String sourceId,
      BillingAddress billingAddress,
      String email,
      DigitalRiverClientConfig digitalRiverClientConfig)
      throws DRProcessHandlerException, RequestToProviderException, PaymentNotFoundException {
    ProviderCheckout providerCheckout =
        paymentProviderDigitalRiverService.updateCheckout(
            payment, email, sourceId, billingAddress, digitalRiverClientConfig);

    return updatePaymentService.updateDataWithProviderCheckout(
        payment, providerCheckout, ProviderStatus.SOURCE_ATTACHED);
  }

  public void detach(
      String checkoutId, String sourceId, DigitalRiverClientConfig digitalRiverClientConfig) {
    try {
      this.paymentProviderDigitalRiverService.detachSourceFromCheckout(
          checkoutId, sourceId, digitalRiverClientConfig);
    } catch (RequestToProviderException e) {
      throw new PaymentProviderIntentionNotUpdatableException(
          "unable to detach source from checkout");
    }
  }
}
