package com.scopely.paymentgateway.providers.xsolla.webhook.events;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.providers.xsolla.model.Settings;
import com.scopely.paymentgateway.providers.xsolla.model.User;
import javax.annotation.Nullable;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = XsollaUserValidationWebhookEvent.Builder.class)
public interface XsollaUserValidationWebhookEvent extends XsollaWebhookEvent {

  @Nullable
  Settings getSettings();

  @Nullable
  User getUser();

  class Builder extends XsollaUserValidationWebhookEvent_Builder {
    public Builder() {}
  }
}
