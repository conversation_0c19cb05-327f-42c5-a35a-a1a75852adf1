package com.scopely.paymentgateway.providers.digitalriver.model.order;

import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import java.util.ArrayList;

public record Charge(
    String id,
    String createdTime,
    String currency,
    PaymentBigDecimal amount,
    String state,
    boolean captured,
    ArrayList<Capture> captures,
    boolean refunded,
    String sourceId,
    String type) {}
