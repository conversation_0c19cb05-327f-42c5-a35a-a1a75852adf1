package com.scopely.paymentgateway.providers.xsolla.model.checkout;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scopely.paymentgateway.constants.XsollaConstants;
import com.scopely.paymentgateway.services.payment.PaymentCreationContext;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import org.javamoney.moneta.Money;

public record CheckoutRequest(
    User user,
    Settings settings,
    Purchase purchase,
    @JsonProperty("custom_parameters") CustomParameters customParameters) {

  public static CheckoutRequest fromRequestData(
      PaymentCreationContext context,
      String customerId,
      Country country,
      String language,
      boolean disableSavedMethods,
      boolean useCrossAsCloseButtonIcon) {
    String currency =
        context.getTotalPriceData().getOriginalLocalPrice().getCurrency().getCurrencyCode();
    String unitPrice =
        Optional.ofNullable(context.getItemData().getUnitLocalPrice())
            .map(Money::getNumberStripped)
            .map(BigDecimal::toPlainString)
            .orElse("");
    String closeButtonIcon =
        useCrossAsCloseButtonIcon
            ? XsollaConstants.CLOSE_BUTTON_ICON_CROSS
            : XsollaConstants.CLOSE_BUTTON_ICON_ARROW;
    return new CheckoutRequest(
        new User(new Id(customerId), country),
        new Settings(
            context.isSandbox(),
            context.getPaymentId(),
            new UISettings(
                new DeviceSettings(new Header(true, true, closeButtonIcon)),
                new DeviceSettings(new Header(true, null, closeButtonIcon))),
            new RedirectPolicy(ManualRedirectionEnum.POST_MESSAGE),
            language,
            disableSavedMethods),
        new Purchase(
            new Checkout(
                context.getTotalPriceData().getOriginalLocalPrice().getNumberStripped(), currency),
            new Description(
                List.of(
                    new XsollaItem(
                        context.getItemData().getName(),
                        new Price(unitPrice),
                        context.getItemData().getQuantity())))),
        new CustomParameters(XsollaConstants.PLAYGAMI, context.getPaymentId()));
  }
}
