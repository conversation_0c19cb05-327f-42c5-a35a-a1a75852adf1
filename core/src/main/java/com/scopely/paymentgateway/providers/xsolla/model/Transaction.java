package com.scopely.paymentgateway.providers.xsolla.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public record Transaction(
    Integer id,
    @JsonProperty("external_id") String externalId,
    @JsonProperty("payment_method_order_id") String paymentMethodOrderId,
    @JsonProperty("dry_run") Integer dryRun,
    Integer agreement,
    @JsonProperty("payment_date") String paymentDate,
    @JsonProperty("payment_method") Integer paymentMethod) {}
