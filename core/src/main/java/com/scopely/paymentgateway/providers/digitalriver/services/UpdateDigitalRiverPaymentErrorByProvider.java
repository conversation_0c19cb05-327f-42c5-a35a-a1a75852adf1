package com.scopely.paymentgateway.providers.digitalriver.services;

import com.scopely.paymentgateway.exceptions.LockingConflictException;
import com.scopely.paymentgateway.exceptions.repository.EntityLockingConflictException;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import com.scopely.paymentgateway.repositories.PaymentRepository;
import com.scopely.paymentgateway.services.payment.UpdatePaymentErrorByProvider;
import java.time.Clock;

public class UpdateDigitalRiverPaymentErrorByProvider implements UpdatePaymentErrorByProvider {

  private final PaymentRepository paymentRepository;

  private final Clock clock;

  public UpdateDigitalRiverPaymentErrorByProvider(
      PaymentRepository paymentRepository, Clock clock) {
    this.paymentRepository = paymentRepository;
    this.clock = clock;
  }

  @Override
  public Payment updatePaymentError(Payment payment, String errorMessage) {
    try {
      return paymentRepository.save(
          Payment.Builder.from(payment)
              .setPaymentStatus(PaymentStatus.FAILED)
              .setUpdatedAt(clock.instant())
              .setErrorMessage(errorMessage)
              .build());
    } catch (LockingConflictException exception) {
      var paymentOpt = paymentRepository.getPayment(payment.getPaymentId());
      if (paymentOpt.isPresent()) {
        return updatePaymentError(paymentOpt.get(), errorMessage);
      }
      throw new EntityLockingConflictException(exception);
    }
  }
}
