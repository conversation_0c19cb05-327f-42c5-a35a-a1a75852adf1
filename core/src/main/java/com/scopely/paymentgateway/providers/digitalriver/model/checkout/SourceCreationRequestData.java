package com.scopely.paymentgateway.providers.digitalriver.model.checkout;

import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverSourceCreation;

public record SourceCreationRequestData(
    String paymentId,
    String sourceId,
    String paymentMethod,
    Boolean futureUse,
    boolean isSavedPaymentMethod) {
  public DigitalRiverSourceCreation toSourceCreationData() {
    return new DigitalRiverSourceCreation.Builder()
        .setPaymentId(this.paymentId())
        .setSourceId(this.sourceId())
        .setPaymentMethod(this.paymentMethod())
        .setFutureUse(this.futureUse())
        .setSavedPaymentMethod(this.isSavedPaymentMethod())
        .build();
  }
}
