package com.scopely.paymentgateway.providers.xsolla.model.refund;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.scopely.paymentgateway.model.refund.ProviderRefundStatus;
import com.scopely.paymentgateway.model.refund.RefundStatus;
import java.util.Arrays;

public enum XsollaProviderRefundStatus implements ProviderRefundStatus {
  FAILED("failed", RefundStatus.FAILED),
  PENDING("pending", RefundStatus.PENDING),
  COMPLETED("completed", RefundStatus.COMPLETED),
  @JsonEnumDefaultValue
  UNKNOWN("unknown", RefundStatus.UNKNOWN);

  private final String description;
  private final RefundStatus status;

  XsollaProviderRefundStatus(String description, RefundStatus status) {
    this.description = description;
    this.status = status;
  }

  @Override
  @JsonValue
  public String getDescription() {
    return description;
  }

  @JsonCreator
  public static XsollaProviderRefundStatus fromDescription(String value) {
    return Arrays.stream(XsollaProviderRefundStatus.values())
        .filter(identifier -> identifier.description.equals(value))
        .findFirst()
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    "RefundReason with description " + value + " does not exist"));
  }

  @Override
  public RefundStatus getRefundStatus() {
    return status;
  }

  public boolean greaterThan(ProviderRefundStatus other) {
    // by default, we will treat the other like it's at the same level so is equal
    boolean result = false;
    // if it's different we should evaluate it
    if (this != other) {
      // If other is COMPLETED, any other state will be lower
      // if the other is not the same and is not completed, this is a greater status
      result = other != COMPLETED;
    }
    return result;
  }
}
