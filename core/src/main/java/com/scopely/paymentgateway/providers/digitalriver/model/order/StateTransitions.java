package com.scopely.paymentgateway.providers.digitalriver.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;

public record StateTransitions(
    String created,
    String fulfilled,
    String accepted,
    String complete,
    @JsonProperty("pending_payment") String pendingPayment,
    @JsonProperty("in_review") String inReview,
    String dispute,
    String cancelled,
    String blocked,
    String returned) {}
