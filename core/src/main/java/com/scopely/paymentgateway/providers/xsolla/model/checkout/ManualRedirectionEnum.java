package com.scopely.paymentgateway.providers.xsolla.model.checkout;

import com.fasterxml.jackson.annotation.JsonValue;

public enum ManualRedirectionEnum {
  REDIRECT("redirect"),
  POST_MESSAGE("postmessage"),
  ;
  private final String description;

  ManualRedirectionEnum(String description) {
    this.description = description;
  }

  @JsonValue
  public String getDescription() {
    return description;
  }
}
