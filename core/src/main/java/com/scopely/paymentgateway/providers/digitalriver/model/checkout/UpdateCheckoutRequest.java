package com.scopely.paymentgateway.providers.digitalriver.model.checkout;

import static java.util.Objects.nonNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.scopely.paymentgateway.model.payment.BillingAddress;
import com.scopely.paymentgateway.providers.digitalriver.model.order.Address;
import com.scopely.paymentgateway.providers.digitalriver.model.order.BillTo;

@SuppressWarnings("PMD.NullAssignment")
public record UpdateCheckoutRequest(
    @JsonInclude(JsonInclude.Include.NON_NULL) String sourceId,
    @JsonInclude(JsonInclude.Include.NON_NULL) BillTo billTo,
    @JsonInclude(JsonInclude.Include.NON_NULL) String email) {

  public UpdateCheckoutRequest(BillingAddress billingAddress, String email, String sourceId) {
    this(
        sourceId,
        nonNull(billingAddress)
            ? new BillTo(
                new Address(
                    billingAddress.getAddressLine1(),
                    billingAddress.getCity(),
                    billingAddress.getPostalCode(),
                    billingAddress.getState(),
                    billingAddress.getCountry()),
                null,
                null,
                null,
                null)
            : null,
        email);
  }
}
