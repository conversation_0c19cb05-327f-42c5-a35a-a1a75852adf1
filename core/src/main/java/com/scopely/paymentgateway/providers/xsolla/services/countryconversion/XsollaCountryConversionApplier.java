package com.scopely.paymentgateway.providers.xsolla.services.countryconversion;

import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PricingMode;
import com.scopely.paymentgateway.services.countryconversion.CountryConversionApplier;
import javax.inject.Singleton;
import org.javamoney.moneta.Money;

@Singleton
public class XsollaCountryConversionApplier implements CountryConversionApplier {

  @Override
  public PriceData calculateFromProvider(String countryID, Money originPrice) {
    return new PriceData.Builder()
        .setBasePrice(originPrice)
        .setLocalPrice(originPrice)
        .setPricingMode(PricingMode.EXPLICIT)
        .build();
  }
}
