package com.scopely.paymentgateway.providers.xsolla.webhook.processors;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_XSOLLA_CHARGEBACK_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_XSOLLA_CHARGEBACK_SUCCESS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_XSOLLA_DISPUTE_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.XSOLLA_WEBHOOK;

import com.scopely.paymentgateway.exceptions.dispute.ChargebackServiceException;
import com.scopely.paymentgateway.exceptions.dispute.DisputeServiceException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.model.refund.RefundType;
import com.scopely.paymentgateway.model.reversal.Chargeback;
import com.scopely.paymentgateway.providers.xsolla.model.XsollaRefundCode;
import com.scopely.paymentgateway.providers.xsolla.model.refund.XsollaProviderRefundStatus;
import com.scopely.paymentgateway.providers.xsolla.model.refund.XsollaRefundProviderData;
import com.scopely.paymentgateway.providers.xsolla.webhook.constants.XsollaEventName;
import com.scopely.paymentgateway.providers.xsolla.webhook.events.XsollaRefundWebhookEvent;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.services.refund.RefundService;
import com.scopely.paymentgateway.services.reversal.ChargebackService;
import com.scopely.paymentgateway.services.reversal.dispute.DisputeService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.utils.PaymentAmountCalculator;
import com.scopely.proteus.logging.Log;
import com.scopely.proteus.sqs.publisher.SendMessageException;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import java.time.Clock;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import javax.inject.Inject;
import org.javamoney.moneta.Money;

public class RefundWebhookProcessor implements XsollaWebhookProcessor<XsollaRefundWebhookEvent> {
  private final PaymentService paymentService;
  private final RefundService refundService;
  private final DisputeService disputeService;
  private final ChargebackService chargebackService;
  private final StatsDClient statsDclient;
  private final Clock clock;

  @Inject
  public RefundWebhookProcessor(
      PaymentService paymentService,
      RefundService refundService,
      DisputeService disputeService,
      ChargebackService chargebackService,
      StatsDClient statsDclient,
      Clock clock) {
    this.paymentService = paymentService;
    this.refundService = refundService;
    this.disputeService = disputeService;
    this.chargebackService = chargebackService;
    this.statsDclient = statsDclient;
    this.clock = clock;
  }

  @Override
  @Trace(operationName = XSOLLA_WEBHOOK, resourceName = "refund")
  public void execute(XsollaRefundWebhookEvent request) throws WebhookException {
    // because Xsolla doesn't give us any kind of identifier for the refund, we will retrieve a
    // pending refund
    // retrieve the payment by order id
    var payment =
        paymentService
            .getPaymentByOrderId(request.getTransaction().id().toString())
            .orElseThrow(
                () ->
                    new WebhookException(
                        WebhookException.Error.PAYMENT_NOT_FOUND,
                        PaymentProviderIdentifier.XSOLLA));

    var eventType = XsollaEventName.fromValue(request.getEventName());
    if (request.getRefund().getCode() == XsollaRefundCode.CHARGEBACK) {
      processChargeback(request, payment);
    } else if (eventType == XsollaEventName.PARTIAL_REFUND) {
      processPartialRefund(request, payment);
    } else {
      processRefund(request, payment);
    }
  }

  protected void processChargeback(XsollaRefundWebhookEvent request, Payment payment)
      throws WebhookException {
    Chargeback chargeback = buildChargeback(request, payment);
    var logger = getLogger(payment);
    try {
      chargebackService.save(chargeback, payment);
      checkNonContestableChargeback(chargeback.getLocalAmount(), payment);
    } catch (DisputeServiceException | ChargebackServiceException e) {
      statsDclient.increment(
          DD_WEBHOOK_XSOLLA_CHARGEBACK_ERROR,
          MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
      logger.error(e, "Couldn't save chargeback: {}", request);
      throw new WebhookException(
          "Couldn't save chargeback for payment %s".formatted(payment.getPaymentId()), e);
    }
    statsDclient.increment(
        DD_WEBHOOK_XSOLLA_CHARGEBACK_SUCCESS,
        MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
    logger.debug("Chargeback event received: {}", chargeback);
  }

  protected Chargeback buildChargeback(XsollaRefundWebhookEvent request, Payment payment) {
    var now = clock.instant();
    var localAmount = request.getPaymentDetails().payment().getMoneyValue();
    return new Chargeback.Builder()
        .setApiKey(payment.getApiKey())
        .setUserId(payment.getUserId())
        .setPaymentId(payment.getPaymentId())
        .setChargebackId(UUID.randomUUID().toString())
        .setChargebackReason(request.getRefund().getReason())
        .setCreatedAt(now)
        .setUpdatedAt(now)
        .setLocalAmount(localAmount)
        .setAmount(PaymentAmountCalculator.calculateBaseAmount(localAmount, payment))
        .build();
  }

  private void checkNonContestableChargeback(Money chargebackAmount, Payment payment)
      throws WebhookException, DisputeServiceException {
    try {
      disputeService.updateDisputesByChargeback(chargebackAmount, payment);
    } catch (SendMessageException e) {
      statsDclient.increment(
          DD_WEBHOOK_XSOLLA_DISPUTE_ERROR,
          MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
      var message =
          "Couldn't enqueue non contestable dispute for payment %s"
              .formatted(payment.getPaymentId());
      throw new WebhookException(message, e);
    }
  }

  private void processRefund(XsollaRefundWebhookEvent request, Payment payment) {
    // Discard API refunds for now because xsolla always returns a completed status for a refund
    if (!request.getRefund().getAuthor().equals("API")) {
      // filter over the refunds by this payment to retrieve the pending refund
      // if we don't have any refund pending, we will try to generate one
      var refund =
          refundService.getRefundsByPaymentId(payment.getPaymentId()).stream()
              .filter(refundToFilter -> refundToFilter.getRefundType() == RefundType.TOTAL)
              .findFirst()
              .map(this::updateRefund)
              .orElseGet(() -> buildTotalRefund(request, payment));
      refundService.saveRefund(refund, payment);
    } else {
      getLogger(payment)
          .debug(
              "Refund with payment id ({}) event reached. Refunds made by API author will not processed by webhook events",
              payment.getPaymentId());
    }
  }

  private void processPartialRefund(XsollaRefundWebhookEvent request, Payment payment) {
    var refundLocalPrice = request.getPaymentDetails().payment().getMoneyValue();
    var refund =
        getRefundBuilder(request, payment)
            .setRefundType(RefundType.PARTIAL)
            .setRefundedAmount(
                PaymentAmountCalculator.calculateBaseAmount(refundLocalPrice, payment))
            .build();
    refundService.saveRefund(refund, payment);
  }

  private Refund buildTotalRefund(XsollaRefundWebhookEvent request, Payment payment) {
    var basePrice = payment.getPriceData().getBasePrice();
    return getRefundBuilder(request, payment)
        .setRefundType(RefundType.TOTAL)
        .setRefundedAmount(Money.of(basePrice.getTotalAmount(), basePrice.getCurrency()))
        .build();
  }

  private Refund.Builder getRefundBuilder(XsollaRefundWebhookEvent request, Payment payment) {
    return new com.scopely.paymentgateway.model.refund.Refund.Builder()
        // Xsolla doesn't provide any kind of refund id
        .setRefundId(UUID.randomUUID().toString())
        .setApiKey(payment.getApiKey())
        .setRefundReason(RefundReason.fromDescription(request.getRefund().getReason()))
        // Xsolla doesn't provide a refund date
        .setCreatedAt(Instant.now())
        .setRefundedLocalAmount(request.getPaymentDetails().payment().getMoneyValue())
        .setPaymentId(payment.getPaymentId())
        .setStatus(XsollaProviderRefundStatus.COMPLETED.getRefundStatus())
        .setProviderData(
            new XsollaRefundProviderData.Builder()
                .setStatus(XsollaProviderRefundStatus.COMPLETED)
                .setAuthor(request.getRefund().getAuthor())
                .setOrderId(request.getTransaction().id())
                .build())
        .setPaymentMethodUsed(payment.getPaymentMethodUsed());
  }

  private Refund updateRefund(Refund refund) {
    var metadata =
        XsollaRefundProviderData.Builder.from((XsollaRefundProviderData) refund.getProviderData())
            .setStatus(XsollaProviderRefundStatus.COMPLETED)
            .build();
    return Refund.Builder.from(refund)
        .setStatus(XsollaProviderRefundStatus.COMPLETED.getRefundStatus())
        .setProviderData(metadata)
        .build();
  }

  @Override
  public List<XsollaEventName> getAssociatedEvents() {
    return List.of(XsollaEventName.REFUND, XsollaEventName.PARTIAL_REFUND);
  }

  @Override
  public Class<XsollaRefundWebhookEvent> getModel() {
    return XsollaRefundWebhookEvent.class;
  }

  private Log.MetadataLog getLogger(Payment payment) {
    return new PaymentGatewayLogBuilder()
        .addPayment(payment.getApiKey(), payment.getPaymentId())
        .build();
  }
}
