package com.scopely.paymentgateway.providers.xsolla.model;

import com.fasterxml.jackson.annotation.JsonValue;

public enum XsollaRefundCode {
  CANCELLATION(1, "Cancellation initiated from Publisher Account."),
  CHARGEBACK(2, "Transaction chargeback requested."),
  INTEGRATION_ERROR(3, "Issues in integration between Xsolla and the game."),
  POTENTIAL_FRAUD(4, "Fraud suspected."),
  TEST_PAYMENT(5, "Test transaction followed by cancellation."),
  USER_INVOICE_EXPIRED(6, "Invoice overdue (used for postpaid model)."),
  FRAUD_NOTIFICATION(7, "Payment refused by payment system. Potential fraud detected by PS."),
  CANCELLATION_BY_PS(8, "Cancellation requested by payment system."),
  CANCELLATION_BY_USER(
      9, "The user was not satisfied with the game or the purchase for any reason."),
  CANCELLATION_BY_GAME(10, "Cancellation requested by the game."),
  FRAUD_REPORTED(11, "The account owner states that they didn’t make the transaction."),
  FRIENDLY_FRAUD(12, "Friendly fraud reported."),
  DUPLICATE(13, "Duplicate transaction for the same invoice."),
  UNKNOWN(0, "Unknown code");

  private final Integer code;
  private final String description;

  XsollaRefundCode(Integer code, String description) {
    this.code = code;
    this.description = description;
  }

  @JsonValue
  public Integer getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }
}
