package com.scopely.paymentgateway.providers.digitalriver.model.refund;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.math.BigDecimal;

public record RefundItem(
    String itemId,
    int quantity,
    @JsonInclude(JsonInclude.Include.NON_NULL) String skuId,
    @JsonInclude(JsonInclude.Include.NON_NULL) BigDecimal amount) {
  public RefundItem(String itemId, int quantity) {
    this(itemId, quantity, null, null);
  }
}
