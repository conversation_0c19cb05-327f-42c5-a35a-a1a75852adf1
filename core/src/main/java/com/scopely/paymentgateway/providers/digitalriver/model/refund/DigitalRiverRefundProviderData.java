package com.scopely.paymentgateway.providers.digitalriver.model.refund;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.refund.RefundProviderdata;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonIgnoreProperties
@JsonDeserialize(builder = DigitalRiverRefundProviderData.Builder.class)
public interface DigitalRiverRefundProviderData extends RefundProviderdata {

  DigitalRiverProviderRefundStatus getStatus();

  String getOrderId();

  PaymentProviderIdentifier getProvider();

  class Builder extends DigitalRiverRefundProviderData_Builder {
    public Builder() {
      setProvider(PaymentProviderIdentifier.DIGITAL_RIVER);
    }
  }
}
