package com.scopely.paymentgateway.providers.digitalriver.model.checkout;

import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;
import com.scopely.paymentgateway.providers.digitalriver.model.tokenization.Owner;

public record SourceResponse(
    String id,
    String createdTime,
    String type,
    String currency,
    PaymentBigDecimal amount,
    boolean reusable,
    Owner owner,
    String state
    // there is more info about the Source payment method used on this response, but is not relevant
    // now.
    // See
    // https://www.digitalriver.com/docs/digital-river-api-reference/#tag/Sources/operation/retrieveSources.

    ) {}
