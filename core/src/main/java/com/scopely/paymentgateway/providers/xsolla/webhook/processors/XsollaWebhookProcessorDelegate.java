package com.scopely.paymentgateway.providers.xsolla.webhook.processors;

import static com.google.common.collect.ImmutableMap.of;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_AUTO_FORWARD_EVENT;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_PROCESSOR_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_EVENT_NAME;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PROVIDER;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_WEBHOOK_EVENT_NAME;
import static com.scopely.paymentgateway.constants.StatsConstants.XSOLLA_WEBHOOK;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.providers.xsolla.config.XSollaClientConfig;
import com.scopely.paymentgateway.providers.xsolla.webhook.constants.XsollaEventName;
import com.scopely.paymentgateway.providers.xsolla.webhook.events.XsollaWebhookEvent;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import java.util.List;
import java.util.Map;
import javax.inject.Inject;
import javax.inject.Singleton;

@SuppressWarnings({"unchecked", "rawtypes"})
@Singleton
public class XsollaWebhookProcessorDelegate {
  private final StatsDClient statsDClient;
  private final ObjectMapper mapper;
  private final XsollaWebhookProcessorProvider xsollaWebhookProcessorProvider;
  private final ClientConfigurationService clientConfigurationService;

  @Inject
  public XsollaWebhookProcessorDelegate(
      XsollaWebhookProcessorProvider xsollaWebhookProcessorProvider,
      ClientConfigurationService clientConfigurationService,
      final StatsDClient statsDClient,
      ObjectMapper mapper) {
    this.clientConfigurationService = clientConfigurationService;
    this.statsDClient = statsDClient;
    this.mapper = mapper;
    this.xsollaWebhookProcessorProvider = xsollaWebhookProcessorProvider;
  }

  @Trace(operationName = XSOLLA_WEBHOOK, resourceName = "event_forwarder")
  public void execute(XsollaWebhookEvent data) throws WebhookException, PaymentNotFoundException {
    xsollaWebhookProcessorProvider
        .getProcessor(XsollaEventName.fromValue(data.getEventName()))
        .orElseThrow(
            () ->
                new WebhookException(
                    WebhookException.Error.PAYMENT_NOT_FOUND, PaymentProviderIdentifier.XSOLLA))
        .execute(data);
  }

  private XsollaEventName retrieveEventType(String bodyRequest) throws WebhookException {
    XsollaEventName eventType = XsollaEventName.UNKNOWN;
    try {
      XsollaWebhookEvent webhookRequestType =
          mapper.readValue(bodyRequest, XsollaWebhookEvent.class);
      return XsollaEventName.fromValue(webhookRequestType.getEventName());
    } catch (Exception e) {
      Log.error("Parsing error for event type {} for XSolla webhook", eventType);
      throw new WebhookException(
          WebhookException.Error.PARSING_EVENT_EXCEPTION, PaymentProviderIdentifier.XSOLLA, e);
    }
  }

  private XsollaWebhookEvent parseRequestData(
      String bodyRequest, XsollaWebhookProcessor eventProcessor, XsollaEventName eventType)
      throws WebhookException {
    try {
      return (XsollaWebhookEvent) mapper.readValue(bodyRequest, eventProcessor.getModel());
    } catch (Exception e) {
      Log.error("Parsing error for event type {} for XSolla webhook", eventType);
      throw new WebhookException(
          WebhookException.Error.PARSING_EVENT_EXCEPTION, PaymentProviderIdentifier.XSOLLA, e);
    }
  }

  public XsollaEventName execute(String apiKey, String body)
      throws WebhookException, PaymentNotFoundException {
    var eventType = retrieveEventType(body);
    // check if this event should be forwarded
    forwardEventIfNecessary(apiKey, eventType);
    var processor = xsollaWebhookProcessorProvider.getProcessor(eventType);
    if (processor.isPresent()) {
      var eventProcessor = processor.get();
      var request = parseRequestData(body, eventProcessor, eventType);
      Log.withMetadata(of(TAG_WEBHOOK_EVENT_NAME, request.getEventName()))
          .debug(
              "Notification type with id {} and body {} received successfully",
              request.getEventName(),
              body);
      eventProcessor.execute(request);
    } else {
      Log.withMetadata(
              Map.of(
                  TAG_PROVIDER,
                  PaymentProviderIdentifier.XSOLLA.getDescription(),
                  TAG_API_KEY,
                  apiKey,
                  TAG_API_EVENT_NAME,
                  eventType.getEventName()))
          .warn("Event with name {} is not registered", eventType);
      statsDClient.increment(
          DD_WEBHOOK_PROCESSOR_ERROR,
          MetricsUtils.buildTags(
              List.of(TAG_PROVIDER, TAG_API_KEY, TAG_API_EVENT_NAME),
              List.of(PaymentProviderIdentifier.XSOLLA.getDescription(), apiKey, eventType)));
      throw new WebhookException(
          WebhookException.Error.EVENT_NOT_FOUND, PaymentProviderIdentifier.XSOLLA);
    }
    return eventType;
  }

  private void forwardEventIfNecessary(String apiKey, XsollaEventName eventType)
      throws WebhookException {
    XSollaClientConfig xsollaConfig =
        clientConfigurationService.getConfiguration(ConfigurationProviderIdentifier.XSOLLA, apiKey);
    if (xsollaConfig.getEventsToForward() != null
        && xsollaConfig.getEventsToForward().contains(eventType.getEventName())) {
      Log.withMetadata(
              Map.of(
                  TAG_PROVIDER,
                  PaymentProviderIdentifier.XSOLLA.getDescription(),
                  TAG_API_KEY,
                  apiKey,
                  TAG_API_EVENT_NAME,
                  eventType.getEventName()))
          .info("Event {} will be automatically forwarded", eventType.getEventName());
      statsDClient.increment(
          DD_WEBHOOK_AUTO_FORWARD_EVENT,
          MetricsUtils.buildTags(
              List.of(TAG_PROVIDER, TAG_API_KEY, TAG_API_EVENT_NAME),
              List.of(
                  PaymentProviderIdentifier.XSOLLA.getDescription(),
                  apiKey,
                  eventType.getEventName())));
      throw new WebhookException(
          WebhookException.Error.EVENT_TO_BE_FORWARDED, PaymentProviderIdentifier.XSOLLA);
    }
  }
}
