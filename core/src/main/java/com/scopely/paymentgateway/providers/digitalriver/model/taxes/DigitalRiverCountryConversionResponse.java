package com.scopely.paymentgateway.providers.digitalriver.model.taxes;

import com.scopely.paymentgateway.model.payment.PaymentBigDecimal;

public record DigitalRiverCountryConversionResponse(
    String countryCode,
    String currencyCode,
    Boolean taxInclusivePrices,
    Boolean dutyInclusivePrices,
    Boolean supportsFixedPrices,
    Boolean operatedByDr,
    PaymentBigDecimal conversionFactor,
    PaymentBigDecimal exchangeRate) {}
