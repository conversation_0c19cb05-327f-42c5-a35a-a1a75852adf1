package com.scopely.paymentgateway.providers.digitalriver.webhook.events.body;

import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverRequestData;

public interface DigitalRiverWebhookRequestBody {

  @Deprecated
  default DigitalRiverRequestData toRequestData() {
    return new EmptyDigitalRiverRequestData();
  }

  class EmptyDigitalRiverRequestData implements DigitalRiverRequestData {}
}
