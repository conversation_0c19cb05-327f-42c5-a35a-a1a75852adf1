package com.scopely.paymentgateway.providers.digitalriver.api;

import com.scopely.paymentgateway.providers.digitalriver.model.checkout.*;
import com.scopely.paymentgateway.providers.digitalriver.model.fulfillment.FulfillmentRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.fulfillment.FulfillmentResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.order.OrderCreationRequest;
import com.scopely.paymentgateway.providers.digitalriver.model.order.OrderResponse;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.*;
import com.scopely.paymentgateway.providers.digitalriver.model.taxes.DigitalRiverCountryConversionResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

@SuppressWarnings("PMD.NullAssignment")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface DigitalRiverApi {

  String AUTHORIZATION = "Authorization";
  String CUSTOMER_ID = "customerId";
  String SOURCE_ID = "sourceId";
  String CHECKOUT_ID = "checkoutId";

  @POST
  @Path("customers")
  CreateCustomerResponse createCustomer(
      @HeaderParam(AUTHORIZATION) String auth, CreateCustomerRequest createCustomerRequest);

  @POST
  @Path("/customers/{customerId}")
  CreateCustomerResponse updateCustomer(
      @HeaderParam(AUTHORIZATION) String auth,
      @PathParam(CUSTOMER_ID) String customerId,
      UpdateCustomerRequest updateCustomerRequest);

  @GET
  @Path("/customers/{customerId}")
  GetCustomerResponse getCustomer(
      @HeaderParam(AUTHORIZATION) String auth, @PathParam(CUSTOMER_ID) String customerId);

  @POST
  @Path("/customers/{customerId}/sources/{sourceId}")
  AttachSourceToCustomerResponse attachSourceToCustomer(
      @HeaderParam(AUTHORIZATION) String auth,
      @PathParam(CUSTOMER_ID) String customerId,
      @PathParam(SOURCE_ID) String sourceId);

  @DELETE
  @Path("/customers/{customerId}/sources/{sourceId}")
  Void detachSourceFromCustomer(
      @HeaderParam(AUTHORIZATION) String auth,
      @PathParam(CUSTOMER_ID) String customerId,
      @PathParam(SOURCE_ID) String sourceId);

  @GET
  @Path("/sources/{sourceId}")
  SourceResponse getSource(
      @HeaderParam(AUTHORIZATION) String auth, @PathParam(SOURCE_ID) String sourceId);

  @POST
  @Path("refunds")
  RefundCreationResponse requestRefund(
      @HeaderParam(AUTHORIZATION) String auth, CreateRefundRequest createRefundRequest);

  @POST
  @Path("returns")
  ReturnResponse createReturn(
      @HeaderParam(AUTHORIZATION) String auth, ReturnCreationRequest returnCreationRequest);

  @POST
  @Path("returns/{id}")
  ReturnResponse updateReturn(
      @HeaderParam(AUTHORIZATION) String auth,
      @PathParam("id") String id,
      ReturnUpdate returnUpdate);

  @POST
  @Path("checkouts")
  CheckoutResponse createCheckout(
      @HeaderParam(AUTHORIZATION) String auth, CheckoutRequest checkoutRequest);

  @POST
  @Path("checkouts/{checkoutId}")
  CheckoutResponse updateCheckout(
      @HeaderParam(AUTHORIZATION) String auth,
      @PathParam(CHECKOUT_ID) String checkoutId,
      UpdateCheckoutRequest associationRequest);

  @GET
  @Path("checkouts/{checkoutId}")
  CheckoutResponse getCheckout(
      @HeaderParam(AUTHORIZATION) String auth, @PathParam(CHECKOUT_ID) String checkoutId);

  @DELETE
  @Path("checkouts/{checkoutId}")
  CheckoutResponse deleteCheckout(
      @HeaderParam(AUTHORIZATION) String auth, @PathParam(CHECKOUT_ID) String checkoutId);

  @DELETE
  @Path("checkouts/{checkoutId}/sources/{sourceId}")
  Void detachSource(
      @HeaderParam(AUTHORIZATION) String auth,
      @PathParam(CHECKOUT_ID) String checkoutId,
      @PathParam(SOURCE_ID) String sourceId);

  @POST
  @Path("orders")
  OrderResponse createOrder(
      @HeaderParam(AUTHORIZATION) String auth, OrderCreationRequest associationRequest);

  @POST
  @Path("fulfillments")
  FulfillmentResponse fulfillOrder(
      @HeaderParam(AUTHORIZATION) String auth, FulfillmentRequest fulfillmentRequest);

  @GET
  @Path("orders/{orderId}")
  OrderResponse getOrder(
      @HeaderParam(AUTHORIZATION) String auth, @PathParam("orderId") String orderId);

  @GET
  @Path("dynamic-pricing/country-currency/countries/{country}/currencies/{currency}")
  DigitalRiverCountryConversionResponse retrieveTaxesForSpecificCountryAndCurrency(
      @HeaderParam(AUTHORIZATION) String auth,
      @PathParam("country") String country,
      @PathParam("currency") String currency);
}
