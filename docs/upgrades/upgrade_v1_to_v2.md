## Upgrade to v1.0.0
>The module version v1.0.0 uses the new [context module](https://app.terraform.io/app/scopely-playgami/registry/modules/private/scopely-playgami/module-context/tf/0.0.4) hosted in our private terraform cloud registry. This module declares a single "tags" variable for required and suggested tags. The module returns tags in multiple formats to use with different providers.
>The new context module is named as "context" instead of "this".

* Modify your call to the module by grouping all the tags variables in the `tags`key:


```
## Before

module "s3" "this" {
  bucket_name = "my-bucket"

  environment = "preview"
  allocation  = "playgami-infrastructure"
  affinity    = "none"
  product     = "tests"
  team        = "playgami-devops"
  org_unit    = "playgami"

}
```

```
## This is how it should be for the updated modules
module "s3" "this" {
  bucket_name = "my-bucket"

  tags = {
    environment = "preview"
    allocation  = "playgami-infrastructure"
    affinity    = "none"
    product     = "tests"
    team        = "playgami-devops"
    org_unit    = "playgami"
  }

}
```