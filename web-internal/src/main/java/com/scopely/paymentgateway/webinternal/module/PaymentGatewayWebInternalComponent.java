package com.scopely.paymentgateway.webinternal.module;

import com.scopely.paymentgateway.modules.ActionsModule;
import com.scopely.paymentgateway.modules.RepositoriesModule;
import com.scopely.paymentgateway.modules.ServicesModule;
import com.scopely.paymentgateway.web.module.PaymentGatewayWebComponent;
import com.scopely.paymentgateway.web.v1.controller.InternalToolsController;
import com.scopely.proteus.config.ProteusModule;
import com.scopely.proteus.monitoring.MonitoringModule;
import dagger.Component;
import javax.inject.Singleton;

@Singleton
@Component(
    modules = {
      PaymentGatewayWebInternalModule.class,
      ProteusModule.class,
      MonitoringModule.class,
      RepositoriesModule.class,
      ServicesModule.class,
      ActionsModule.class
    })
public interface PaymentGatewayWebInternalComponent extends PaymentGatewayWebComponent {

  InternalToolsController getInternalToolsController();

  static PaymentGatewayWebInternalComponent create() {
    return DaggerPaymentGatewayWebInternalComponent.create();
  }
}
