package com.scopely.paymentgateway.webinternal.module;

import com.scopely.paymentgateway.analytics.events.TitanEventService;
import com.scopely.paymentgateway.config.PaymentGatewayConfig;
import com.scopely.paymentgateway.modules.CoreModule;
import com.scopely.paymentgateway.web.service.PaymentGatewayWebService;
import com.scopely.paymentgateway.web.v1.controller.InternalToolsController;
import com.scopely.paymentgateway.web.v1.controller.PaymentSecuredController;
import com.scopely.paymentgateway.web.v1.controller.PaymentStatusController;
import com.scopely.paymentgateway.web.v1.controller.StartupController;
import com.scopely.paymentgateway.web.v1.controller.UserController;
import com.scopely.proteus.core.service.ProteusService;
import com.scopely.proteus.monitoring.StatsDHeartbeater;
import com.scopely.proteus.server.BaseHttpServiceSettings;
import com.scopely.proteus.server.resources.StatusController;
import dagger.Module;
import dagger.Provides;
import java.time.Duration;
import java.util.Set;
import javax.inject.Singleton;

@Module(includes = {CoreModule.class})
public class PaymentGatewayWebInternalModule {

  @Provides
  @Singleton
  ProteusService webService(
      PaymentGatewayConfig config,
      TitanEventService titanEventService,
      StatsDHeartbeater statsDHeartbeater,
      PaymentGatewayWebInternalComponent component) {

    var settings =
        new BaseHttpServiceSettings.Builder(config.webServicePort(), config.webServiceThreadCount())
            .withResources(getControllers())
            .withSleepTimeOnStopRequested(
                Duration.ofSeconds(config.terminationGracePeriodSeconds()))
            .withProviders(PaymentGatewayWebService.getProviders())
            .build();

    return new PaymentGatewayWebService(settings, titanEventService, statsDHeartbeater, component);
  }

  private static Set<Class<?>> getControllers() {
    return Set.of(
        // APIs
        StatusController.class,
        StartupController.class,
        PaymentSecuredController.class,
        UserController.class,
        InternalToolsController.class,
        PaymentStatusController.class);
  }
}
