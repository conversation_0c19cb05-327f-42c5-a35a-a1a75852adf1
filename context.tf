#
# ONLY EDIT THIS FILE IN github.com/scopely/tf-module-context
# All other instances of this file should be a copy of that one
#
# IMPORTANT: Ensure that this context.tf file version matches
# with the used context module version tag!
#
# Copy this file from https://github.com/scopely/tf-module-context/blob/master/examples/context.tf
# and then place it in your Terraform module to automatically get
# Scopely's standard configuration inputs suitable for passing
# to Scopely modules.
#
# <PERSON><PERSON><PERSON> should access the whole context as `module.this.context`
# to get the input variables with nulls for defaults,
# for example `context = module.this.context`,
# and access individual variables as `module.this.<var>`,
# with final values filled in.
#
# For example, when using defaults, `module.this.context.namespace`
# will be null, and `module.this.namespace` will be `scopely`.
#
# Work based on CloudPosse's context module: github.com/cloudposse/terraform-null-label
#
module "this" {
  #Remember to set the correct context module version tag after copying this file
  source = "**************:scopely/tf-module-context.git?ref=v0.0.8"

  enabled             = var.enabled
  namespace           = var.namespace
  org_unit            = var.org_unit
  environment         = var.environment
  name                = var.name
  tags                = var.tags
  additional_tag_map  = var.additional_tag_map
  regex_replace_chars = var.regex_replace_chars
  label_key_case      = var.label_key_case
  label_value_case    = var.label_value_case
  affinity            = var.affinity
  allocation          = var.allocation
  product             = var.product
  service             = var.service
  purpose             = var.purpose
  streamrole          = var.streamrole
  team                = var.team
  module_name         = var.module_name
  module_version      = var.module_version
  iac_repo            = var.iac_repo
  iac_repo_path       = var.iac_repo_path

  context = var.context
}

variable "context" {
  type = any
  default = {
    enabled             = true
    namespace           = "scopely"
    org_unit            = null
    environment         = null
    name                = null
    tags                = {}
    additional_tag_map  = {}
    regex_replace_chars = null
    label_key_case      = "lower"
    label_value_case    = "lower"
    affinity            = null
    allocation          = null
    product             = null
    service             = null
    purpose             = null
    streamrole          = null
    team                = null
    module_name         = null
    module_version      = null
    iac_repo            = null
    iac_repo_path       = null
  }
  description = <<-EOT
    Single object for setting entire context at once.
    See description of individual variables for details.
    Leave string and numeric variables as `null` to use default value.
    Individual variable settings (non-null) override settings in context object,
    except for tags, and additional_tag_map, which are merged.
  EOT

  validation {
    condition     = lookup(var.context, "label_key_case", null) == null ? true : contains(["lower", "title", "upper"], var.context["label_key_case"])
    error_message = "Allowed values: `lower`, `title`, `upper`."
  }

  validation {
    condition     = lookup(var.context, "label_value_case", null) == null ? true : contains(["lower", "title", "upper", "none"], var.context["label_value_case"])
    error_message = "Allowed values: `lower`, `title`, `upper`, `none`."
  }
}

### Scopely

variable "org_unit" {
  type        = string
  default     = null
  description = "Contains Scopely's organization unit name, e.g. 'playgami'"
}

variable "affinity" {
  type        = string
  default     = null
  description = "Contains the game, or group of games"
}

variable "allocation" {
  type        = string
  default     = null
  description = "Contains a single, unique cost category, used to identify a group of services, group of components, etc."
}

variable "product" {
  type        = string
  default     = null
  description = "Contains the name of the product"
}

variable "service" {
  type        = string
  default     = null
  description = "Contains the name of the service"
}

variable "purpose" {
  type        = string
  default     = null
  description = "Contains for what was this resource created"
}

variable "streamrole" {
  type        = string
  default     = null
  description = "Used for grouping the Kinesis streams"
}

variable "team" {
  type        = string
  default     = null
  description = "Used to attribute the resource to a team and to know the associated costs by areas"
}

variable "module_version" {
  type        = string
  default     = null
  description = "Scopely Module version used for creation of infrastructure"
}

variable "module_name" {
  type        = string
  default     = null
  description = "Scopely Module name used for creation of infrastructure"
}

variable "iac_repo" {
  type        = string
  default     = null
  description = "Scopely IaC Git repo where the state definition is in"
}

variable "iac_repo_path" {
  type        = string
  default     = null
  description = "Path in the 'iac_repo' where the state definition for the module is in"
}

### Cloudposse

variable "enabled" {
  type        = bool
  default     = null
  description = "Set to false to prevent the module from creating any resources"
}

variable "namespace" {
  type        = string
  default     = null
  description = "Namespace, which could be your organization name or abbreviation, e.g. 'eg' or 'cp'"
}

variable "environment" {
  type        = string
  default     = null
  description = "Environment, e.g. 'uw2', 'us-west-2', OR 'prod', 'staging', 'dev', 'UAT'"
}

variable "name" {
  type        = string
  default     = null
  description = "Solution name, e.g. 'app' or 'jenkins'"
}

variable "tags" {
  type        = map(string)
  default     = {}
  description = "Additional tags (e.g. `map('BusinessUnit','XYZ')`"
}

variable "additional_tag_map" {
  type        = map(string)
  default     = {}
  description = "Additional tags for appending to tags_as_list_of_maps. Not added to `tags`."
}

variable "regex_replace_chars" {
  type        = string
  default     = null
  description = <<-EOT
    Regex to replace chars with empty string in `namespace`, `environment`, `stage` and `name`.
    If not set, `"/[^a-zA-Z0-9-]/"` is used to remove all characters other than hyphens, letters and digits.
  EOT
}

variable "label_key_case" {
  type        = string
  default     = "lower"
  description = <<-EOT
    The letter case of label keys (`tag` names) (i.e. `name`, `namespace`, `environment`) to use in `tags`.
    Possible values: `lower`, `title`, `upper`.
    Default value: `title`.
  EOT

  validation {
    condition     = var.label_key_case == null ? true : contains(["lower", "title", "upper"], var.label_key_case)
    error_message = "Allowed values: `lower`, `title`, `upper`."
  }
}

variable "label_value_case" {
  type        = string
  default     = "lower"
  description = <<-EOT
    The letter case of output label values (also used in `tags` and `id`).
    Possible values: `lower`, `title`, `upper` and `none` (no transformation).
    Default value: `lower`.
  EOT

  validation {
    condition     = var.label_value_case == null ? true : contains(["lower", "title", "upper", "none"], var.label_value_case)
    error_message = "Allowed values: `lower`, `title`, `upper`, `none`."
  }
}