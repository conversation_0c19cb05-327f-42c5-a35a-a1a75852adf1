package com.scopely.paymentgateway.webhook.xsolla.authentication.filter;

import com.scopely.paymentgateway.exceptions.webhook.WebhookAuthException;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.providers.xsolla.config.XSollaClientConfig;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.proteus.logging.Log;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class XsollaAuthenticationValidator {
  public static final String SHA_1 = "SHA-1";
  public static final String SIGNATURE = "Signature ";

  private final ClientConfigurationService clientConfigurationService;

  @Inject
  public XsollaAuthenticationValidator(
      final ClientConfigurationService clientConfigurationService) {
    this.clientConfigurationService = clientConfigurationService;
  }

  public void validateSignature(String authorization, String apiKey, String requestBody)
      throws WebhookAuthException {
    // FIXME: ADD METRICS HERE
    try {
      MessageDigest md = MessageDigest.getInstance(SHA_1);
      isSignatureValid(authorization, apiKey, requestBody, md);
    } catch (NoSuchAlgorithmException exception) {
      Log.warn(exception, "Error processing Authentication signature");
      throw new WebhookAuthException(exception);
    } catch (WebhookAuthException exception) {
      Log.error(exception, "Bad Authentication signature");
      throw exception;
    }
  }

  private void isSignatureValid(
      String authorization, String apiKey, String requestBody, MessageDigest md)
      throws WebhookAuthException {

    String processedSignature = getProcessedSignature(requestBody, apiKey, md, false);
    String processedSandboxSignature = getProcessedSignature(requestBody, apiKey, md, true);

    if (!authorization.equals(processedSignature)
        && (!authorization.equals(processedSandboxSignature))) {
      throw new WebhookAuthException(PaymentProviderIdentifier.XSOLLA);
    }
  }

  private String getProcessedSignature(
      String requestBody, String apiKey, MessageDigest md, boolean sandbox) {
    String signature = requestBody + getSecretKey(apiKey, sandbox);
    byte[] messageDigest = md.digest(signature.getBytes(StandardCharsets.UTF_8));
    String processedSignature = SIGNATURE + this.bytesToHex(messageDigest);
    return processedSignature;
  }

  private String bytesToHex(byte[] hash) {
    StringBuilder sb = new StringBuilder();
    for (byte b : hash) {
      sb.append(String.format("%02x", b));
    }
    return sb.toString();
  }

  private String getSecretKey(String apiKey, boolean sandbox) {
    return ((XSollaClientConfig)
            clientConfigurationService.getConfiguration(
                ConfigurationProviderIdentifier.XSOLLA, apiKey, sandbox))
        .getWebhookSecretKey();
  }
}
