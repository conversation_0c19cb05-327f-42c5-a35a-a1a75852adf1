package com.scopely.paymentgateway.webhook.xsolla.v1.controller;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_PROCESSOR_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_PROCESSOR_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_REDIRECT_URL_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_WEBHOOK_UNAUTHORIZED;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_PROVIDER;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_WEBHOOK_EVENT_NAME;

import com.scopely.paymentgateway.exceptions.InvalidRedirectUrlWebhookException;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookAuthException;
import com.scopely.paymentgateway.exceptions.webhook.WebhookException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.providers.xsolla.webhook.constants.XsollaEventName;
import com.scopely.paymentgateway.providers.xsolla.webhook.processors.RedirectWebhookProcessor;
import com.scopely.paymentgateway.providers.xsolla.webhook.processors.XsollaWebhookProcessorDelegate;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.webhook.xsolla.authentication.filter.XsollaAuthenticationValidator;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import jakarta.ws.rs.BadRequestException;
import jakarta.ws.rs.ClientErrorException;
import jakarta.ws.rs.InternalServerErrorException;
import jakarta.ws.rs.NotAuthorizedException;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.SecurityContext;
import java.util.List;
import java.util.Map;
import javax.inject.Inject;

@SuppressWarnings("unchecked")
public class WebhookXSollaController implements WebhookXSollaEndpoint {
  @Context SecurityContext securityContext;
  @Context HttpHeaders httpHeaders;
  private final XsollaWebhookProcessorDelegate xsollaWebhookProcessorDelegate;
  private final XsollaAuthenticationValidator xsollaAuthenticationValidator;
  private final RedirectWebhookProcessor redirectWebhookProcessor;
  private final StatsDClient statsDClient;

  @Inject
  public WebhookXSollaController(
      XsollaWebhookProcessorDelegate xsollaWebhookProcessorDelegate,
      XsollaAuthenticationValidator xsollaAuthenticationValidator,
      RedirectWebhookProcessor redirectWebhookProcessor,
      StatsDClient statsDClient) {
    this.xsollaWebhookProcessorDelegate = xsollaWebhookProcessorDelegate;
    this.xsollaAuthenticationValidator = xsollaAuthenticationValidator;
    this.redirectWebhookProcessor = redirectWebhookProcessor;
    this.statsDClient = statsDClient;
  }

  @Override
  public Response xsollaWebhook(String authorization, String apiKey, String body) {
    var tags =
        MetricsUtils.buildTags(
            List.of(TAG_PROVIDER, TAG_API_KEY), List.of(PaymentProviderIdentifier.XSOLLA, apiKey));

    var logger =
        new PaymentGatewayLogBuilder()
            .addProvider(PaymentProviderIdentifier.XSOLLA)
            .addApiKey(apiKey)
            .build();

    try {
      // FIXME: Add all headers using HttpHeaders
      xsollaAuthenticationValidator.validateSignature(authorization, apiKey, body);
      XsollaEventName xsollaEventName = xsollaWebhookProcessorDelegate.execute(apiKey, body);
      String eventName =
          (xsollaEventName != null)
              ? xsollaEventName.toString()
              : XsollaEventName.UNKNOWN.toString();
      tags =
          MetricsUtils.buildTags(
              List.of(TAG_PROVIDER, TAG_API_KEY, TAG_WEBHOOK_EVENT_NAME),
              List.of(PaymentProviderIdentifier.XSOLLA.getDescription(), apiKey, eventName));

      statsDClient.increment(DD_WEBHOOK_PROCESSOR_HITS, tags);
      return Response.status(200).build();
    } catch (WebhookAuthException exception) {
      statsDClient.increment(DD_WEBHOOK_UNAUTHORIZED, tags);
      return redirectWebhook(
          authorization,
          apiKey,
          body,
          new NotAuthorizedException(
              exception.getMessage(), Response.Status.BAD_REQUEST, exception));
    } catch (WebhookException webhookException) {
      if (webhookException.getError() == WebhookException.Error.DUPLICATED_EXTERNAL_ID) {
        logger.warn(
            webhookException,
            "Unable to process webhook due to duplicated external id for apiKey {}",
            apiKey);
        throw new BadRequestException(webhookException.getMessage());
      }
      logger.warn(webhookException, "Unable to process webhook for apiKey {}", apiKey);
      return redirectWebhook(
          authorization,
          apiKey,
          body,
          new ClientErrorException(
              webhookException.getMessage(), Response.Status.BAD_REQUEST, webhookException));
    } catch (PaymentNotFoundException exception) {
      logger.warn(exception, "Unable to find payment in webhook for apiKey {}", apiKey);
      return redirectWebhook(
          authorization,
          apiKey,
          body,
          new ClientErrorException(exception.getMessage(), Response.Status.BAD_REQUEST, exception));
    } catch (Exception exception) {
      logger.warn(exception, "Unhandled error processing payment webhook for apiKey {}", apiKey);
      return redirectWebhook(
          authorization,
          apiKey,
          body,
          new InternalServerErrorException(exception.getMessage(), exception));
    }
  }

  private Object retrieveErrorCode(Exception exception) {
    if (exception instanceof PaymentNotFoundException) {
      return WebhookException.Error.PAYMENT_NOT_FOUND;
    } else if (exception instanceof WebhookException) {
      return ((WebhookException) exception).getError();
    } else {
      return WebhookException.Error.UNEXPECTED_ERROR;
    }
  }

  private Response redirectWebhook(
      String authorization, String apiKey, String body, WebApplicationException exception) {
    var tags =
        MetricsUtils.buildTags(
            List.of(TAG_PROVIDER, TAG_API_KEY),
            List.of(PaymentProviderIdentifier.XSOLLA.getDescription(), apiKey));
    var log =
        Log.withMetadata(
            Map.of(
                TAG_PROVIDER,
                PaymentProviderIdentifier.XSOLLA.getDescription(),
                TAG_API_KEY,
                apiKey));
    try {
      log.info(exception, "Forwarding xsolla event for the apiKey {}", apiKey);
      return redirectWebhookProcessor.execute(authorization, apiKey, body);
    } catch (InvalidRedirectUrlWebhookException e) {
      log.error(e, "Invalid url to redirect for apiKey {}", apiKey);
      statsDClient.increment(DD_WEBHOOK_REDIRECT_URL_ERROR, tags);
      sendMetrics(tags, exception);
      throw exception;
    } catch (Exception e) {
      log.error(exception, "Unhandled error redirecting webhook");
      sendMetrics(tags, exception);
      throw exception;
    }
  }

  private void sendMetrics(String[] tags, Exception exception) {
    statsDClient.increment(
        DD_WEBHOOK_PROCESSOR_ERROR,
        MetricsUtils.mergeTags(
            tags, MetricsUtils.buildTags(TAG_RESULT, retrieveErrorCode(exception))));
  }
}
