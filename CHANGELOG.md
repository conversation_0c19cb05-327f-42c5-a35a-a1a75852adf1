## v2.0.26 (2024-06-18)

### Feat

- **eks-irsa**: added conditions to additional statements

## v2.0.25 (2024-06-07)

### Feat

- **waf**: deprecate old staging rule for cloudfront, update logging (#79)

## v2.0.24 (2024-06-07)

## v2.0.23 (2024-06-06)

### Feat

- **waf**: disambiguate cloudwatch metric for datadog ingestion (#77)

## v2.0.22 (2024-06-05)

### Feat

- **eks-irsa**: added additional satetements in the trust relationship

## v2.0.21 (2024-05-24)

### Feat

- **rds**: added db_apply_immediately variable to avoid maintenance windows

## v2.0.20 (2024-05-24)

### Fix

- **rds**: reduce the default instance class size to avoid overprovisioning

## v2.0.19 (2024-05-23)

### Feat

- **rds**: added output parameters with relevant connection information for RDS instance

## v2.0.6 (2023-12-21)

### Feat

- **ec2-lb-health-check**: removed is not being used

## v2.0.5 (2023-12-14)

### Feat

- **core**: removed unused tests

## v2.0.4 (2023-12-05)

### Feat

- **ec2-lb-health-check**: added full lb creation besides the rule

## v2.0.3 (2023-12-04)

## v2.0.2 (2023-12-01)

### Feat

- **modules/ec2-lb-health-check**: new module to create alb health check rules

## v2.0.1 (2023-11-29)

## v2.0.0 (2023-11-20)

### Feat

- **module-context**: udpated module context to new module (#49)

## v1.0.3 (2023-11-17)

## v1.0.2 (2023-11-16)

### Feat

- **rds**: added variable for backup retention period

## v1.0.1 (2023-10-31)

### Feat

- **route53**: updated context module (#47)

## v1.0.0 (2023-09-18)

### BREAKING CHANGE

- the key of the password for the aws secrets to use for rds is now lowercase
- you will need to update the source path in which you were using the module to get the  latest version
- You need to change the terraform/terragrunt if you were using this output
- experiment support known issue for terraform 1.3.x https://github.com/hashicorp/terraform/issues/31692
- experiment support known issue for terraform 1.3.x https://github.com/hashicorp/terraform/issues/31692
- experiment support known issue for terraform 1.3.x https://github.com/hashicorp/terraform/issues/31692

### Feat

- **iam-role**: added description var for tf cloud migration
- **iam-role**: update context module for tf-cloud
- **rds**: update context for tf cloud
- **eks-irsa**: added new context for tf-cloud
- **ecr**: update context for tf cloud
- **secretsmanager**: updated context module for tf-cloud
- **ssm-parameter**: update context module
- **modules/rds**: applied naming convention to the secrets
- **s3-module**: updated context module version (#36)
- **iam-role**: fix variable name to be consistent
- **spinnaker**: added extra statement for ecr access
- **spinnaker**: basic module to add spinnaker recessary resources for integrations
- **iam-role**: basic module to create a iam role
- **s3-module**: updated s3 module
- **s3-bucket**: outputs now the id of the bucket in the variable bucket_id
- **eks-role**: added inline policies variables to allow added policies directly
- **rds**: upgraded context module to v0.0.8
- **ecr**: update context module to v0.0.8
- **eks-role**: compatibility with terraform 1.3.x for optional attribute variables
- **eks-role**: upgraded context module to v0.0.8
- **ssm-parameter**: compatibility with terraform 1.3.x for optional attribute variables
- **ssm-parameter**: upgraded context module to v0.0.8
- **secretsmanager**: compatibility with terraform 1.3.x for optional attribute variables
- **rds**: variable renamed and added variable for switch the deletion protection
- **rds**: first implementation of the rds module, outputs to be defined
- **eks-role**: new terraform module to created federated roles into for eks clusters
- **ssm_parameter**: added new parameter store module with additional role for eks integration
- **secretsmanager**: added iam role creation for accessing the secrets
- **secretsmanager**: allow dict of secrets to create multiple secrets
- **secretsmanager**: new module for secrets creation in the secretsmanager service

### Fix

- **spinnaker**: remove flag to avoid complexity
- **eks-role**: change output name to follow naming convention
- **rds**: added default value for delete protection and autogen readme
- **core**: wrong module creation
- **ssm-parameter**: resource is an array
- **ssm-parameter**: added more things to the access policy
- **ssm-parameter**: fixing resources arn template
- **eks-role**: variable policies_arn is already a list
- **ssm-parameter**: fix access to object property
- **ssm-parameter**: naming convention
- **secretsmanager**: addded reference to access policy
- **secretsmanager**: identifiers is a list
- **secretsmanager**: added assume role with the eks oidc provider
- **secretsmanager**: conditions values are arry
- **secretsmanager**: another variable name wrong
- **secretsmanager**: fix wrong named variable
- **secretsmanager**: map from list of objects
- **secretsmanager**: toset for the list of secrets
- **secretsmanager**: addde experimenta flag for optional attrs
- **secretsmanager**: name better than name_prefix
- **ecr**: wrong outputs description
- **ecr**: added playgami additional tags

### Refactor

- **spinnaker**: move spinnaker submodule to its own module
- **eks-irsa**: rename eks-role module to eks-irsa to be more accurate and self-explanatory
- **secretsmanager**: remove eks-role creation
- **ssm-parameter**: extract eks role creationg
- **ssm-parameter**: renaming variables and re-format files

## v0.0.3 (2022-09-23)

### Feat

- **ecr**: documentation improvements and force_delete added
- **ecr**: adding basic ecr repo creation module

### Fix

- **ecr**: wrong prefix in the outputs
- **ecr**: forget to add the outputs

## v0.0.2 (2022-07-05)

## v0.0.1 (2022-07-05)
