package com.scopely.paymentgateway.worker.services.backfill.jobs;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_BACKFILL_UNCLAIMED_ERRORS_ITEMS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_BACKFILL_UNCLAIMED_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;

import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.repositories.daos.BackfillPage;
import com.scopely.paymentgateway.repositories.daos.PaginationToken;
import com.scopely.paymentgateway.repositories.daos.PaymentDAO;
import com.scopely.paymentgateway.repositories.daos.mapper.PaymentDAOMapper;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.worker.services.backfill.BackfillType;
import com.scopely.paymentgateway.worker.services.backfill.PaymentBackfill;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class UnclaimedPaymentsByIdBackfill implements PaymentBackfill<PaymentDAO, Payment> {

  // Add here the Payment IDs to be marked as Unclaimed
  private static final List<String> PAYMENTS_IDS = List.of();

  private final StatsDClient statsDClient;
  private final PaymentService paymentService;

  @Inject
  public UnclaimedPaymentsByIdBackfill(StatsDClient statsDClient, PaymentService paymentService) {
    this.statsDClient = statsDClient;
    this.paymentService = paymentService;
  }

  @Override
  public BackfillType getBackfillType() {
    return BackfillType.PAYMENTS_UNCLAIMED_BY_ID;
  }

  @Override
  public BackfillPage<PaymentDAO> getData(PaginationToken currentPaginationToken) {
    List<PaymentDAO> items = new ArrayList<>();
    Log.info("Total Payments requested: {} ", PAYMENTS_IDS.size());
    for (String paymentId : PAYMENTS_IDS) {
      try {
        items.add(PaymentDAOMapper.paymentToPaymentDao(paymentService.getPaymentById(paymentId)));
      } catch (PaymentNotFoundException e) {
        Log.error(e, "Payment with id " + paymentId + " not found", paymentId);
      }
    }
    Log.info("Total Payments founds to backfill: {} ", items.size());
    return new BackfillPage<>(currentPaginationToken, items);
  }

  @Override
  public List<Payment> transform(List<PaymentDAO> input) {
    return input.stream().map(PaymentDAOMapper::paymentDaoToPayment).toList();
  }

  @Override
  public void backfill(Payment payment) {
    try {
      // Because this process only needs a re-save of the payment, we are doing it directly here
      paymentService.systemSave(Payment.Builder.from(payment).setClaimed(false).build());
      Log.info("Resave {}", payment.getPaymentId());
      statsDClient.increment(
          DD_BACKFILL_UNCLAIMED_HITS, MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
    } catch (Exception e) {
      new PaymentGatewayLogBuilder()
          .addPayment(payment.getPaymentId())
          .addApiKey(payment.getApiKey())
          .build()
          .error(e, "Error processing payment backfill");
      statsDClient.increment(
          DD_BACKFILL_UNCLAIMED_ERRORS_ITEMS,
          MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
    }
  }
}
