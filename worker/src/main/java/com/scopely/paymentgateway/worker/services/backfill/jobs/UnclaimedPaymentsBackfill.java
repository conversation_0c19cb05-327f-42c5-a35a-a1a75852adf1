package com.scopely.paymentgateway.worker.services.backfill.jobs;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_BACKFILL_UNCLAIMED_ERRORS_ITEMS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_BACKFILL_UNCLAIMED_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;

import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.repositories.PaymentRepository;
import com.scopely.paymentgateway.repositories.daos.BackfillPage;
import com.scopely.paymentgateway.repositories.daos.PaginationToken;
import com.scopely.paymentgateway.repositories.daos.PaymentDAO;
import com.scopely.paymentgateway.repositories.daos.mapper.PaymentDAOMapper;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.worker.services.backfill.BackfillType;
import com.scopely.paymentgateway.worker.services.backfill.PaymentBackfill;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class UnclaimedPaymentsBackfill implements PaymentBackfill<PaymentDAO, Payment> {

  private final StatsDClient statsDClient;
  private final PaymentRepository paymentRepository;
  private final PaymentService paymentService;

  @Inject
  public UnclaimedPaymentsBackfill(
      StatsDClient statsDClient,
      PaymentService paymentService,
      PaymentRepository paymentRepository) {
    this.statsDClient = statsDClient;
    this.paymentRepository = paymentRepository;
    this.paymentService = paymentService;
  }

  @Override
  public BackfillType getBackfillType() {
    return BackfillType.PAYMENTS_UNCLAIMED;
  }

  @Override
  public BackfillPage<PaymentDAO> getData(PaginationToken currentPaginationToken) {
    return paymentRepository.getUnclaimedPaymentsPaginated(currentPaginationToken);
  }

  @Override
  public List<Payment> transform(List<PaymentDAO> input) {
    return input.stream().map(PaymentDAOMapper::paymentDaoToPayment).toList();
  }

  @Override
  public void backfill(Payment payment) {
    try {
      // Because this process only needs a re-save of the payment, we are doing it directly here
      paymentService.systemSave(payment);
      Log.info("Resave {}", payment.getPaymentId());
      statsDClient.increment(
          DD_BACKFILL_UNCLAIMED_HITS, MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
    } catch (Exception e) {
      new PaymentGatewayLogBuilder()
          .addPayment(payment.getPaymentId())
          .addApiKey(payment.getApiKey())
          .build()
          .error(e, "Error processing payment backfill");
      statsDClient.increment(
          DD_BACKFILL_UNCLAIMED_ERRORS_ITEMS,
          MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
    }
  }
}
