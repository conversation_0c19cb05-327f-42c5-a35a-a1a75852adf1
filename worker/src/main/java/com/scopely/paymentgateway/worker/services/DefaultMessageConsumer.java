package com.scopely.paymentgateway.worker.services;

import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.worker.CommandBasedQueueMessageProcessor;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DefaultMessageConsumer implements CommandBasedQueueMessageProcessor<QueueMessage> {

  public static final String DEFAULT_CONSUMER = "default";

  @Inject
  public DefaultMessageConsumer() {}

  @Override
  public boolean acceptsMessage(QueueMessage message) {
    return message.type().equals(DEFAULT_CONSUMER);
  }

  @Override
  public void process(QueueMessage queueMessage)
      throws RetriableMessageProcessException, NonRetriableMessageProcessException {
    throw new NonRetriableMessageProcessException(
        "No consumer found for message type: " + queueMessage.type());
  }
}
