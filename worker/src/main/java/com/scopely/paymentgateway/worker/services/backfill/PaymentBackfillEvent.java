package com.scopely.paymentgateway.worker.services.backfill;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scopely.paymentgateway.repositories.daos.PaginationToken;
import org.inferred.freebuilder.FreeBuilder;

@FreeBuilder
@JsonDeserialize(builder = PaymentBackfillEvent.Builder.class)
public interface PaymentBackfillEvent {

  Long getProcessedItems();

  PaginationToken getCurrentToken();

  BackfillType getType();

  class Builder extends PaymentBackfillEvent_Builder {
    public Builder() {
      super.setCurrentToken(new PaginationToken(null));
      super.setProcessedItems(0L);
    }

    public Builder setCurrentToken(PaginationToken token) {
      if (token.token() != null && !token.token().isEmpty()) {
        super.setCurrentToken(token);
      }
      return this;
    }
  }
}
