package com.scopely.paymentgateway.worker.services;

import com.scopely.proteus.core.service.ProteusService;
import com.scopely.proteus.sqs.consumer.QueueConsumerService;
import java.util.Set;
import javax.inject.Inject;

public class PaymentGatewayWorkerService implements ProteusService {
  private final Set<QueueConsumerService<?>> consumers;

  @Inject
  public PaymentGatewayWorkerService(Set<QueueConsumerService<?>> consumers) {
    this.consumers = consumers;
  }

  @Override
  public void start() {
    consumers.forEach(QueueConsumerService::start);
  }

  @Override
  public void stop() {
    consumers.forEach(QueueConsumerService::stop);
  }
}
