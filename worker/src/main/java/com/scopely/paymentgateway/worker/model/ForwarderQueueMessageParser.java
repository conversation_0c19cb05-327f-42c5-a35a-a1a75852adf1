package com.scopely.paymentgateway.worker.model;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_QUEUE_PARSER_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_QUEUE_PARSER_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SERVICE;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.proteus.logging.Log;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.QueueMessageParser;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.timgroup.statsd.StatsDClient;

public class ForwarderQueueMessageParser implements QueueMessageParser<QueueMessage> {
  private final ObjectMapper objectMapper;
  private final StatsDClient statsDClient;

  public ForwarderQueueMessageParser(StatsDClient statsDClient, ObjectMapper objectMapper) {
    this.objectMapper = objectMapper;
    this.statsDClient = statsDClient;
  }

  @Override
  public QueueMessage parse(String json)
      throws RetriableMessageProcessException, NonRetriableMessageProcessException {
    try {
      sendMetrics(DD_QUEUE_PARSER_HITS);
      Log.debug("Mapping json: {}", json);
      return objectMapper.readValue(json, QueueMessage.class);
    } catch (JsonMappingException e) {
      Log.error(e, "Error deserializing queue message: {}", json);
      sendMetrics(DD_QUEUE_PARSER_ERROR);
      throw new NonRetriableMessageProcessException(e.getMessage());
    } catch (Exception e) {
      Log.error(e, "Unexpected error: {}", json);
      sendMetrics(DD_QUEUE_PARSER_ERROR);
      throw new RetriableMessageProcessException(e.getMessage());
    }
  }

  private void sendMetrics(String metricName) {
    statsDClient.increment(
        metricName,
        MetricsUtils.buildTags(TAG_SERVICE, ForwarderQueueMessageParser.class.getSimpleName()));
  }
}
