package com.scopely.paymentgateway.worker;

import com.scopely.paymentgateway.modules.ActionsModule;
import com.scopely.paymentgateway.worker.module.WorkerModule;
import com.scopely.paymentgateway.worker.services.PaymentGatewayWorkerService;
import com.scopely.proteus.config.ProteusModule;
import com.scopely.proteus.core.service.BootstrappingProteusComponent;
import com.scopely.proteus.monitoring.MonitoringModule;
import dagger.Component;
import javax.inject.Singleton;

@Singleton
@Component(
    modules = {
      WorkerModule.class,
      ProteusModule.class,
      MonitoringModule.class,
      ActionsModule.class
    })
public interface PaymentGatewayWorkerComponent extends BootstrappingProteusComponent {

  PaymentGatewayWorkerService getService();

  static PaymentGatewayWorkerComponent create() {
    return DaggerPaymentGatewayWorkerComponent.create();
  }
}
