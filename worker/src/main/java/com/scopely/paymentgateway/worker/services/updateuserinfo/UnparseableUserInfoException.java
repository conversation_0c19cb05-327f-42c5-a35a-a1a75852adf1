package com.scopely.paymentgateway.worker.services.updateuserinfo;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayException;
import java.io.Serial;

public class UnparseableUserInfoException extends PaymentGatewayException {

  @Serial private static final long serialVersionUID = -5000646725926635698L;

  protected UnparseableUserInfoException(String message) {
    super(message);
  }

  protected UnparseableUserInfoException(Exception e) {
    super(e);
  }

  public UnparseableUserInfoException(String message, Exception ex) {
    super(message, ex);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.UNPARSEABLE_USER_INFO;
  }
}
