package com.scopely.paymentgateway.worker.services.backfill.jobs;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_BACKFILL_REVERSED_AT_ERRORS_ITEMS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_BACKFILL_REVERSED_AT_ITEMS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;

import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.model.refund.RefundStatus;
import com.scopely.paymentgateway.repositories.PaymentRepository;
import com.scopely.paymentgateway.repositories.RefundRepository;
import com.scopely.paymentgateway.repositories.daos.BackfillPage;
import com.scopely.paymentgateway.repositories.daos.PaginationToken;
import com.scopely.paymentgateway.repositories.daos.PaymentIndexByUnclaimed;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.worker.services.backfill.BackfillType;
import com.scopely.paymentgateway.worker.services.backfill.PaymentBackfill;
import com.scopely.proteus.logging.Log;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import java.util.Objects;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class ReversedAtBackfill implements PaymentBackfill<PaymentIndexByUnclaimed, Payment> {

  private final StatsDClient statsDClient;
  private final PaymentRepository paymentRepository;
  private final RefundRepository refundRepository;
  private final PaymentService paymentService;

  @Inject
  public ReversedAtBackfill(
      StatsDClient statsDClient,
      PaymentService paymentService,
      PaymentRepository paymentRepository,
      RefundRepository refundRepository) {
    this.statsDClient = statsDClient;
    this.paymentRepository = paymentRepository;
    this.refundRepository = refundRepository;
    this.paymentService = paymentService;
  }

  @Override
  public BackfillType getBackfillType() {
    return BackfillType.PAYMENTS_REVERSED_AT;
  }

  @Override
  public BackfillPage<PaymentIndexByUnclaimed> getData(PaginationToken paginationToken) {
    return paymentRepository.getUnclaimedPaymentsFromIndexPaginated(paginationToken);
  }

  @Override
  public List<Payment> transform(List<PaymentIndexByUnclaimed> input) {
    return input.stream()
        .map(this::getCompletedRefund)
        .filter(Objects::nonNull)
        .map(this::setReversedAtInPayment)
        .filter(Objects::nonNull)
        .toList();
  }

  @Override
  public void backfill(Payment payment) {
    try {
      paymentService.systemSave(payment);
      Log.info("Payment {} updated for apiKey: {}", payment.getPaymentId(), payment.getApiKey());
      statsDClient.increment(
          DD_BACKFILL_REVERSED_AT_ITEMS, MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
    } catch (Exception e) {
      new PaymentGatewayLogBuilder()
          .addPayment(payment.getPaymentId())
          .addApiKey(payment.getApiKey())
          .build()
          .error(e, "Error processing payment backfill");
      statsDClient.increment(
          DD_BACKFILL_REVERSED_AT_ERRORS_ITEMS,
          MetricsUtils.buildTags(TAG_API_KEY, payment.getApiKey()));
    }
  }

  private Refund getCompletedRefund(PaymentIndexByUnclaimed paymentIndexByUnclaimed) {
    var refunds = refundRepository.getRefunds(paymentIndexByUnclaimed.getPaymentId());
    var completeRefund =
        refunds.stream().filter(refund -> refund.getStatus() == RefundStatus.COMPLETED).findFirst();
    return completeRefund.orElse(null);
  }

  private Payment setReversedAtInPayment(Refund refund) {
    return paymentRepository
        .getPayment(refund.getPaymentId())
        .map(payment -> Payment.Builder.from(payment).setReversedAt(refund.getUpdatedAt()).build())
        .orElseGet(
            () -> {
              Log.info("Payment {} not found", refund.getPaymentId());
              return null;
            });
  }
}
