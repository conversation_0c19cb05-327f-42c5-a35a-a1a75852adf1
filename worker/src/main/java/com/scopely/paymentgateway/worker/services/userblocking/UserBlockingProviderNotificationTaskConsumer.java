package com.scopely.paymentgateway.worker.services.userblocking;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_FRAUD_NOTIFICATION_TO_PROVIDER_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_FRAUD_NOTIFICATION_TO_PROVIDER_HITS;

import com.scopely.paymentgateway.model.queue.QueueEventType;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.services.fraudnotification.FraudNotificationService;
import com.scopely.paymentgateway.worker.CommandBasedQueueMessageProcessor;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.timgroup.statsd.StatsDClient;
import javax.inject.Inject;
import javax.inject.Singleton;

/**
 * This consumer will send a Slack notification to provider with the list of blocked users. It will
 * be triggered by a scheduled event.
 */
@Singleton
public class UserBlockingProviderNotificationTaskConsumer
    implements CommandBasedQueueMessageProcessor<QueueMessage> {
  private final FraudNotificationService fraudNotificationService;
  private final StatsDClient statsDClient;

  @Inject
  public UserBlockingProviderNotificationTaskConsumer(
      FraudNotificationService fraudNotificationService, StatsDClient statsDClient) {
    this.fraudNotificationService = fraudNotificationService;
    this.statsDClient = statsDClient;
  }

  @Override
  public boolean acceptsMessage(QueueMessage message) {
    return message.type().equals(QueueEventType.USER_BLOCKING_PROVIDER_NOTIFICATION.getType());
  }

  @Override
  public void process(QueueMessage queueMessage)
      throws RetriableMessageProcessException, NonRetriableMessageProcessException {
    try {
      statsDClient.incrementCounter(DD_FRAUD_NOTIFICATION_TO_PROVIDER_HITS);
      fraudNotificationService.sendAlertToProvider();
    } catch (Exception e) {
      statsDClient.increment(DD_FRAUD_NOTIFICATION_TO_PROVIDER_ERROR);
      throw new RetriableMessageProcessException("Failed to send fraud notification to provider.");
    }
  }
}
