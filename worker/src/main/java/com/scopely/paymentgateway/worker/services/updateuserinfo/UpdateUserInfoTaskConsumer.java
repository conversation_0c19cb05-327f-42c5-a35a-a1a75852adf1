package com.scopely.paymentgateway.worker.services.updateuserinfo;

import static com.scopely.proteus.logging.Log.error;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.model.queue.QueueEventType;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.model.user.User;
import com.scopely.paymentgateway.services.rules.PlayerProfileService;
import com.scopely.paymentgateway.worker.CommandBasedQueueMessageProcessor;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.timgroup.statsd.StatsDClient;
import java.util.Locale;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class UpdateUserInfoTaskConsumer implements CommandBasedQueueMessageProcessor<QueueMessage> {
  private final StatsDClient statsDClient;

  private final ObjectMapper mapper;
  private final PlayerProfileService playerProfileService;

  @Inject
  public UpdateUserInfoTaskConsumer(
      StatsDClient statsDClient, ObjectMapper mapper, PlayerProfileService playerProfileService) {
    this.statsDClient = statsDClient;
    this.mapper = mapper;
    this.playerProfileService = playerProfileService;
  }

  @Override
  public boolean acceptsMessage(QueueMessage message) {
    return message.type().equals(getType());
  }

  @Override
  public void process(QueueMessage queueMessage)
      throws RetriableMessageProcessException, NonRetriableMessageProcessException {
    try {
      var updateRequest = parseMessage(queueMessage);
      playerProfileService.updateUserSegmentForProvider(
          updateRequest.getApiKey(), updateRequest.getUserId(), updateRequest.getProvider());
    } catch (Exception e) {
      evaluateSqsMessageError(e, queueMessage);
    }
  }

  private void evaluateSqsMessageError(Throwable err, QueueMessage task)
      throws NonRetriableMessageProcessException, RetriableMessageProcessException {
    var errorCode = ErrorConstants.UNEXPECTED_WORKER_ERROR;
    // try to unwrap the throwable
    var cause = err.getCause() != null ? err.getCause() : err;

    error(
        cause,
        "[{}] Failed to update user info to PlayerProfile with message {},  for {}",
        errorCode.toString().toLowerCase(Locale.getDefault()),
        task);

    throw new RetriableMessageProcessException(cause.getMessage(), cause);
  }

  private User parseMessage(QueueMessage sqsEventTask) throws UnparseableUserInfoException {
    try {
      return mapper.convertValue(sqsEventTask.payload(), User.class);
    } catch (Exception e) {
      throw new UnparseableUserInfoException(e);
    }
  }

  public String getType() {
    return QueueEventType.UPDATE_USER_INFO.getType();
  }
}
