package com.scopely.paymentgateway.worker.services.emailorder;

import com.scopely.paymentgateway.exceptions.PaymentGatewayException;
import java.io.Serial;

public abstract class EmailOrderTaskConsumerException extends PaymentGatewayException {

  @Serial private static final long serialVersionUID = -4405547982944065229L;

  protected EmailOrderTaskConsumerException(String message) {
    super(message);
  }

  protected EmailOrderTaskConsumerException(Exception e) {
    super(e);
  }

  protected EmailOrderTaskConsumerException(String message, Exception ex) {
    super(message, ex);
  }
}
