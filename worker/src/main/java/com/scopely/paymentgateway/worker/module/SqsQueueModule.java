package com.scopely.paymentgateway.worker.module;

import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.worker.CommandBasedQueueMessageProcessor;
import com.scopely.paymentgateway.worker.providers.digitalriver.services.fees.LoadFeesTaskConsumer;
import com.scopely.paymentgateway.worker.providers.digitalriver.services.taxes.LoadTaxesTaskConsumer;
import com.scopely.paymentgateway.worker.services.DefaultMessageConsumer;
import com.scopely.paymentgateway.worker.services.backfill.PaymentBackfillConsumer;
import com.scopely.paymentgateway.worker.services.emailorder.EmailOrderTaskConsumer;
import com.scopely.paymentgateway.worker.services.updateuserinfo.UpdateUserInfoTaskConsumer;
import com.scopely.paymentgateway.worker.services.userblocking.UserBlockingCustomerSupportNotificationTaskConsumer;
import com.scopely.paymentgateway.worker.services.userblocking.UserBlockingProviderNotificationTaskConsumer;
import dagger.Module;
import dagger.Provides;
import java.util.Set;
import javax.inject.Named;

@Module
public class SqsQueueModule {

  public static final String EMAIL_QUEUE_CONSUMERS = "email-order-task-consumers";
  public static final String USERS_QUEUE_CONSUMERS = "update-user-info-task-consumers";
  public static final String SCHEDULED_EVENTS_QUEUE_CONSUMERS = "scheduled-events-task-consumers";

  @Provides
  @Named(EMAIL_QUEUE_CONSUMERS)
  Set<CommandBasedQueueMessageProcessor<QueueMessage>> emailConsumers(
      EmailOrderTaskConsumer emailOrderTaskConsumer) {
    return Set.of(emailOrderTaskConsumer);
  }

  @Provides
  @Named(SCHEDULED_EVENTS_QUEUE_CONSUMERS)
  Set<CommandBasedQueueMessageProcessor<QueueMessage>> scheduledEventsConsumers(
      LoadTaxesTaskConsumer loadTaxesTaskConsumer,
      LoadFeesTaskConsumer loadFeesTaskConsumer,
      PaymentBackfillConsumer paymentBackfillConsumer,
      DefaultMessageConsumer defaultMessageConsumer,
      UserBlockingProviderNotificationTaskConsumer userBlockingProviderNotificationTaskConsumer,
      UserBlockingCustomerSupportNotificationTaskConsumer
          userBlockingCustomerSupportNotificationTaskConsumer) {
    return Set.of(
        loadTaxesTaskConsumer,
        loadFeesTaskConsumer,
        defaultMessageConsumer,
        paymentBackfillConsumer,
        userBlockingProviderNotificationTaskConsumer,
        userBlockingCustomerSupportNotificationTaskConsumer);
  }

  @Provides
  @Named(USERS_QUEUE_CONSUMERS)
  Set<CommandBasedQueueMessageProcessor<QueueMessage>> userInfoConsumers(
      UpdateUserInfoTaskConsumer updateUserInfoTaskConsumer) {
    return Set.of(updateUserInfoTaskConsumer);
  }
}
