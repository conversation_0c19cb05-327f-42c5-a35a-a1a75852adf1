package com.scopely.paymentgateway.worker.services.userblocking;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_FRAUD_NOTIFICATION_TO_CUSTOMER_SUPPORT_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_FRAUD_NOTIFICATION_TO_CUSTOMER_SUPPORT_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_KEY;

import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;
import com.scopely.paymentgateway.model.queue.QueueEventType;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.services.fraudnotification.FraudNotificationService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.worker.CommandBasedQueueMessageProcessor;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.timgroup.statsd.StatsDClient;
import javax.inject.Inject;
import javax.inject.Singleton;

/**
 * This consumer will send a Slack notification to game customer support with the list of blocked
 * users. It will be triggered by a scheduled event.
 */
@Singleton
public class UserBlockingCustomerSupportNotificationTaskConsumer
    implements CommandBasedQueueMessageProcessor<QueueMessage> {
  private final FraudNotificationService fraudNotificationService;
  private final ClientConfigurationService clientConfigurationService;
  private final StatsDClient statsDClient;

  @Inject
  public UserBlockingCustomerSupportNotificationTaskConsumer(
      FraudNotificationService fraudNotificationService,
      ClientConfigurationService clientConfigurationService,
      StatsDClient statsDClient) {
    this.fraudNotificationService = fraudNotificationService;
    this.clientConfigurationService = clientConfigurationService;
    this.statsDClient = statsDClient;
  }

  @Override
  public boolean acceptsMessage(QueueMessage message) {
    return message.type().equals(QueueEventType.USER_BLOCKING_CS_NOTIFICATION.getType());
  }

  @Override
  public void process(QueueMessage queueMessage)
      throws RetriableMessageProcessException, NonRetriableMessageProcessException {
    PlaygamiPaymentsClientConfig clientConfig =
        clientConfigurationService.getConfiguration(
            ConfigurationProviderIdentifier.PLAYGAMI_PAYMENTS,
            ClientConfigurationService.NO_API_KEY);
    var gameApiKeys = clientConfig.getFraudNotificationTargetGames();

    if (gameApiKeys == null || gameApiKeys.isEmpty()) {
      var logger = new PaymentGatewayLogBuilder().build();
      logger.debug("Target games for fraud notification is not set in parameter store.");
    } else {
      for (var apiKey : gameApiKeys) {
        try {
          statsDClient.increment(
              DD_FRAUD_NOTIFICATION_TO_CUSTOMER_SUPPORT_HITS,
              MetricsUtils.buildTags(TAG_API_KEY, apiKey));
          fraudNotificationService.sendAlertToCustomerSupport(apiKey);
        } catch (Exception e) {
          statsDClient.increment(
              DD_FRAUD_NOTIFICATION_TO_CUSTOMER_SUPPORT_ERROR,
              MetricsUtils.buildTags(TAG_API_KEY, apiKey));
          throw new RetriableMessageProcessException(
              String.format("Failed to send fraud notification to customer support: %s", apiKey));
        }
      }
    }
  }
}
