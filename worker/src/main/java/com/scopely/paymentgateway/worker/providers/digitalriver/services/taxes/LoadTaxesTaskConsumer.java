package com.scopely.paymentgateway.worker.providers.digitalriver.services.taxes;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_TAXES_TASK_LOAD_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_TAXES_TASK_LOAD_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.proteus.logging.Log.error;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.model.queue.QueueEventType;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.providers.digitalriver.services.countryconversion.DigitalRiverCountryConversionProvider;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.worker.CommandBasedQueueMessageProcessor;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.timgroup.statsd.StatsDClient;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class LoadTaxesTaskConsumer implements CommandBasedQueueMessageProcessor<QueueMessage> {
  private final DigitalRiverCountryConversionProvider digitalRiverCountryConversionProvider;
  private final StatsDClient statsDClient;

  @Inject
  public LoadTaxesTaskConsumer(
      StatsDClient statsDClient,
      DigitalRiverCountryConversionProvider digitalRiverCountryConversionProvider) {
    this.statsDClient = statsDClient;
    this.digitalRiverCountryConversionProvider = digitalRiverCountryConversionProvider;
  }

  @Override
  public boolean acceptsMessage(QueueMessage message) {
    return message.type().equals(QueueEventType.LOAD_TAXES.toString());
  }

  /**
   * Call digital river conversion provider to update the taxes. If we have a result, result an ok
   * Otherwise we will return the error and not ack the message
   *
   * @param queueMessage the queueMessage to consume from the queue
   * @return the result of the task execution and if the message should be ack
   */
  @Override
  public void process(QueueMessage queueMessage)
      throws RetriableMessageProcessException, NonRetriableMessageProcessException {
    try {
      statsDClient.increment(DD_TAXES_TASK_LOAD_HITS);
      digitalRiverCountryConversionProvider.update();
    } catch (Exception err) {
      var error = ErrorConstants.UNEXPECTED_WORKER_ERROR;
      error(
          err,
          "[{}] Failed to process email notification batch job with message {} for {}",
          error,
          QueueEventType.LOAD_TAXES.toString());
      statsDClient.increment(DD_TAXES_TASK_LOAD_ERROR, MetricsUtils.buildTags(TAG_RESULT, error));
      throw new RetriableMessageProcessException(error.toString());
    }
  }
}
