package com.scopely.paymentgateway.worker.services.refunds;

import com.scopely.paymentgateway.exceptions.RefundPaymentRequestException;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.logging.PaymentProcess;
import com.scopely.paymentgateway.model.refund.RefundEvent;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.services.refund.StartFullRefundProcess;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.QueueMessageProcessor;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import javax.inject.Inject;

public class ScheduledRefundsProcessor implements QueueMessageProcessor<RefundEvent> {

  private final StartFullRefundProcess startFullRefundProcess;

  @Inject
  public ScheduledRefundsProcessor(StartFullRefundProcess startFullRefundProcess) {
    this.startFullRefundProcess = startFullRefundProcess;
  }

  @Override
  public void process(RefundEvent refundEvent)
      throws NonRetriableMessageProcessException, RetriableMessageProcessException {
    var logger =
        new PaymentGatewayLogBuilder()
            .addPayment(refundEvent.apiKey(), refundEvent.paymentId())
            .addProcess(PaymentProcess.REFUND)
            .build();

    logger.info("Processing auto refund event");
    try {
      if (refundEvent.refundReason() == RefundReason.FAILED_PAYMENT) {
        var refund =
            startFullRefundProcess.initRefundForUncompletedPayment(
                refundEvent.paymentId(), refundEvent.refundReason(), refundEvent.comment());
        logger.info("Auto refund created: " + refund.getRefundId());
      }
      // If we need to add more refunds through the worker we need to check how to create the refund
      // according to the type
    } catch (Exception e) {
      if (e instanceof RefundPaymentRequestException) {
        logger.error(e, "Auto refund failed with Retryable error");
        throw new RetriableMessageProcessException("Unable to do auto refund");
      }

      logger.error(e, "Auto refund failed with Non Retryable error");
      throw new NonRetriableMessageProcessException("Unable to do auto refund");
    }
  }
}
