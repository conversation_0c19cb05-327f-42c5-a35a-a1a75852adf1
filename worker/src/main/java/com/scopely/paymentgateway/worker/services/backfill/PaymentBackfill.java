package com.scopely.paymentgateway.worker.services.backfill;

import com.scopely.paymentgateway.repositories.daos.BackfillPage;
import com.scopely.paymentgateway.repositories.daos.PaginationToken;
import java.util.List;

public interface PaymentBackfill<T, R> {

  BackfillType getBackfillType();

  BackfillPage<T> getData(PaginationToken paginationToken);

  List<R> transform(List<T> input);

  void backfill(R input);
}
