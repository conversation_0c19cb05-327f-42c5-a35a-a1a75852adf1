package com.scopely.paymentgateway.worker.services.emailorder;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayException;
import java.io.Serial;

public class UnparseableEmailEventException extends PaymentGatewayException {

  @Serial private static final long serialVersionUID = -4892353706442891074L;

  protected UnparseableEmailEventException(String message) {
    super(message);
  }

  protected UnparseableEmailEventException(Exception e) {
    super(e);
  }

  public UnparseableEmailEventException(String message, Exception ex) {
    super(message, ex);
  }

  @Override
  public ErrorConstants getErrorCode() {
    return ErrorConstants.UNPARSEABLE_EMAIL_EVENT;
  }
}
