package com.scopely.paymentgateway.worker.services;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_QUEUE_FORWARDER_EMPTY;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_QUEUE_FORWARDER_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_QUEUE_FORWARDER_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_QUEUE_PROCESSOR_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_QUEUE_PROCESSOR_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_API_EVENT_NAME;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_SERVICE;

import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.worker.CommandBasedQueueMessageProcessor;
import com.scopely.proteus.logging.Log;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.QueueMessageProcessor;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.inject.Inject;

public class ForwarderQueueMessageProcessor implements QueueMessageProcessor<QueueMessage> {
  private final Set<CommandBasedQueueMessageProcessor<QueueMessage>> forwardProcessors;
  private final StatsDClient statsDClient;

  @Inject
  public ForwarderQueueMessageProcessor(
      Set<CommandBasedQueueMessageProcessor<QueueMessage>> forwardProcessors,
      StatsDClient statsDClient) {
    this.forwardProcessors = forwardProcessors;
    this.statsDClient = statsDClient;
  }

  @Override
  public void process(QueueMessage messageEvent)
      throws RetriableMessageProcessException, NonRetriableMessageProcessException {
    try {
      statsDClient.increment(
          DD_QUEUE_FORWARDER_HITS, MetricsUtils.buildTags(TAG_API_EVENT_NAME, messageEvent.type()));
      var processorsToForward =
          forwardProcessors.stream()
              .filter(processor -> processor.acceptsMessage(messageEvent))
              .toList();
      if (processorsToForward.isEmpty()) {
        Log.withMetadata(Map.of(TAG_API_EVENT_NAME, messageEvent.type()))
            .error("No consumer found to process queued event");
        statsDClient.increment(
            DD_QUEUE_FORWARDER_EMPTY,
            MetricsUtils.buildTags(TAG_API_EVENT_NAME, messageEvent.type()));
      }
      for (CommandBasedQueueMessageProcessor<QueueMessage> processor : processorsToForward) {
        statsDClient.increment(
            DD_QUEUE_PROCESSOR_HITS,
            MetricsUtils.buildTags(
                List.of(TAG_API_EVENT_NAME, TAG_SERVICE),
                List.of(messageEvent.type(), processor.getClass().getSimpleName())));
        executeProcessor(messageEvent, processor);
      }
    } catch (RetriableMessageProcessException | NonRetriableMessageProcessException e) {
      Log.withMetadata(Map.of(TAG_API_EVENT_NAME, messageEvent.type()))
          .error(e, "The queued event was not finished successfully");
      statsDClient.increment(
          DD_QUEUE_FORWARDER_ERROR,
          MetricsUtils.buildTags(TAG_API_EVENT_NAME, messageEvent.type()));
      throw e;
    } catch (Exception e) {
      Log.withMetadata(Map.of(TAG_API_EVENT_NAME, messageEvent.type()))
          .error(e, "Unexpected error processing queued event");
      statsDClient.increment(
          DD_QUEUE_FORWARDER_ERROR,
          MetricsUtils.buildTags(TAG_API_EVENT_NAME, messageEvent.type()));
      throw new NonRetriableMessageProcessException(e.getMessage(), e);
    }
  }

  private void executeProcessor(
      QueueMessage messageEvent, CommandBasedQueueMessageProcessor<QueueMessage> processor)
      throws RetriableMessageProcessException, NonRetriableMessageProcessException {
    try {
      processor.process(messageEvent);
    } catch (Exception e) {
      statsDClient.increment(
          DD_QUEUE_PROCESSOR_ERROR,
          MetricsUtils.buildTags(
              List.of(TAG_API_EVENT_NAME, TAG_SERVICE),
              List.of(messageEvent.type(), processor.getClass().getSimpleName())));
      throw e;
    }
  }
}
