package com.scopely.paymentgateway.worker.module;

import static com.scopely.paymentgateway.worker.module.SqsQueueModule.EMAIL_QUEUE_CONSUMERS;
import static com.scopely.paymentgateway.worker.module.SqsQueueModule.SCHEDULED_EVENTS_QUEUE_CONSUMERS;
import static com.scopely.paymentgateway.worker.module.SqsQueueModule.USERS_QUEUE_CONSUMERS;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.affinity.AffinityHelper;
import com.scopely.paymentgateway.config.WorkerQueuesConfig;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.model.refund.RefundEvent;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeEvent;
import com.scopely.paymentgateway.modules.CoreModule;
import com.scopely.paymentgateway.modules.RepositoriesModule;
import com.scopely.paymentgateway.modules.ServicesModule;
import com.scopely.paymentgateway.worker.CommandBasedQueueMessageProcessor;
import com.scopely.paymentgateway.worker.model.ForwarderQueueMessageParser;
import com.scopely.paymentgateway.worker.services.ForwarderQueueMessageProcessor;
import com.scopely.paymentgateway.worker.services.disputesmanagement.UserDisputesManagementTaskProcessor;
import com.scopely.paymentgateway.worker.services.refunds.ScheduledRefundsProcessor;
import com.scopely.proteus.service.ProteusServiceConstants;
import com.scopely.proteus.sqs.consumer.FailedMessageHandlerResult;
import com.scopely.proteus.sqs.consumer.QueueConsumerService;
import com.scopely.proteus.sqs.consumer.QueueConsumerServiceBuilder;
import com.timgroup.statsd.StatsDClient;
import dagger.Module;
import dagger.Provides;
import dagger.multibindings.IntoSet;
import java.time.Duration;
import java.util.Set;
import javax.inject.Named;
import javax.inject.Singleton;
import software.amazon.awssdk.services.sqs.SqsClient;

@Module(
    includes = {
      CoreModule.class,
      SqsQueueModule.class,
      ServicesModule.class,
      RepositoriesModule.class
    })
public class WorkerModule {

  public static final String SCHEDULER_QUEUE_MESSAGE_FORWARDER = "SchedulerQueueMessageForwarder";
  public static final String USER_QUEUE_MESSAGE_FORWARDER = "UserQueueMessageForwarder";
  public static final String EMAIL_QUEUE_MESSAGE_FORWARDER = "EmailQueueMessageForwarder";

  @Provides
  @Named(ProteusServiceConstants.SERVICE_NAME)
  String serviceName() {
    return "payment-gateway-worker";
  }

  @Provides
  @Named(USER_QUEUE_MESSAGE_FORWARDER)
  static ForwarderQueueMessageProcessor userForwarderProcessor(
      StatsDClient statsDClient,
      @Named(USERS_QUEUE_CONSUMERS)
          Set<CommandBasedQueueMessageProcessor<QueueMessage>> consumers) {
    return new ForwarderQueueMessageProcessor(consumers, statsDClient);
  }

  @Provides
  @Named(SCHEDULER_QUEUE_MESSAGE_FORWARDER)
  static ForwarderQueueMessageProcessor schedulerQueueMessageForwarder(
      StatsDClient statsDClient,
      @Named(SCHEDULED_EVENTS_QUEUE_CONSUMERS)
          Set<CommandBasedQueueMessageProcessor<QueueMessage>> consumers) {
    return new ForwarderQueueMessageProcessor(consumers, statsDClient);
  }

  @Provides
  @Named(EMAIL_QUEUE_MESSAGE_FORWARDER)
  static ForwarderQueueMessageProcessor emailForwarderProcessor(
      StatsDClient statsDClient,
      @Named(EMAIL_QUEUE_CONSUMERS)
          Set<CommandBasedQueueMessageProcessor<QueueMessage>> consumers) {
    return new ForwarderQueueMessageProcessor(consumers, statsDClient);
  }

  @Provides
  @IntoSet
  static QueueConsumerService<?> createSchedulerQueueForwarder(
      WorkerQueuesConfig queueConfig,
      StatsDClient statsDClient,
      SqsClient sqsClient,
      @Named(SCHEDULER_QUEUE_MESSAGE_FORWARDER)
          ForwarderQueueMessageProcessor forwarderQueueMessageProcessor,
      ObjectMapper mapper,
      AffinityHelper affinityHelper) {
    return new QueueConsumerServiceBuilder<QueueMessage>()
        .withQueueName(
            affinityHelper.getQueueNameForCurrentAffinity(queueConfig.scheduleQueueUrlPattern()))
        .withMessageParser(new ForwarderQueueMessageParser(statsDClient, mapper))
        .withMessageReadBatchSize(10)
        .withMessageReadWaitTime(Duration.ofSeconds(20))
        .withKeepInFlightTime(Duration.ofHours(1))
        .withNumProcessingThreads(25)
        .withProcessWorkQueueSize(20)
        .withTerminationGracePeriod(Duration.ofSeconds(30))
        .withSqsClient(sqsClient)
        .withMessageProcessor(forwarderQueueMessageProcessor)
        .withMaxProcessingRetries(2)
        .withProcessRetryInitialDelay(Duration.ofMinutes(1))
        .withProcessRetryDelayExponentialFactor(1)
        .withFailedMessageHandler((body, receipt) -> FailedMessageHandlerResult.SHOULD_KEEP)
        .build();
  }

  @Provides
  @IntoSet
  static QueueConsumerService<?> createUserQueueForwarder(
      WorkerQueuesConfig queueConfig,
      StatsDClient statsDClient,
      SqsClient sqsClient,
      @Named(USER_QUEUE_MESSAGE_FORWARDER)
          ForwarderQueueMessageProcessor forwarderQueueMessageProcessor,
      ObjectMapper mapper,
      AffinityHelper affinityHelper) {
    return new QueueConsumerServiceBuilder<QueueMessage>()
        .withQueueName(
            affinityHelper.getQueueNameForCurrentAffinity(queueConfig.userQueueUrlPattern()))
        .withMessageParser(new ForwarderQueueMessageParser(statsDClient, mapper))
        .withMessageReadBatchSize(10)
        .withMessageReadWaitTime(Duration.ofSeconds(20))
        .withKeepInFlightTime(Duration.ofMinutes(15))
        .withNumProcessingThreads(25)
        .withProcessWorkQueueSize(20)
        .withTerminationGracePeriod(Duration.ofSeconds(30))
        .withSqsClient(sqsClient)
        .withMessageProcessor(forwarderQueueMessageProcessor)
        .build();
  }

  @Provides
  @IntoSet
  static QueueConsumerService<?> createEmailQueueForwarder(
      WorkerQueuesConfig queueConfig,
      StatsDClient statsDClient,
      SqsClient sqsClient,
      @Named(EMAIL_QUEUE_MESSAGE_FORWARDER)
          ForwarderQueueMessageProcessor forwarderQueueMessageProcessor,
      ObjectMapper mapper,
      AffinityHelper affinityHelper) {
    return new QueueConsumerServiceBuilder<QueueMessage>()
        .withQueueName(
            affinityHelper.getQueueNameForCurrentAffinity(queueConfig.emailQueueUrlPattern()))
        .withMessageParser(new ForwarderQueueMessageParser(statsDClient, mapper))
        .withMessageReadBatchSize(10)
        .withMessageReadWaitTime(Duration.ofSeconds(20))
        .withKeepInFlightTime(Duration.ofMinutes(15))
        .withNumProcessingThreads(25)
        .withProcessWorkQueueSize(20)
        .withTerminationGracePeriod(Duration.ofSeconds(30))
        .withSqsClient(sqsClient)
        .withMessageProcessor(forwarderQueueMessageProcessor)
        .build();
  }

  @Provides
  @Singleton
  @IntoSet
  static QueueConsumerService<?> disputeQueueMessageProcessor(
      WorkerQueuesConfig config,
      ObjectMapper objectMapper,
      SqsClient sqsClient,
      UserDisputesManagementTaskProcessor processor,
      AffinityHelper affinityHelper) {
    return new QueueConsumerServiceBuilder<DisputeEvent>()
        .withQueueName(
            affinityHelper.getQueueNameForCurrentAffinity(config.disputeQueueUrlPattern()))
        .withMessageReadBatchSize(10)
        .withMessageReadWaitTime(Duration.ofSeconds(20))
        .withKeepInFlightTime(Duration.ofMinutes(15))
        .withNumProcessingThreads(25)
        .withProcessWorkQueueSize(20)
        .withTerminationGracePeriod(Duration.ofSeconds(30))
        .withJsonMessageParser(objectMapper, DisputeEvent.class)
        .withSqsClient(sqsClient)
        .withMessageProcessor(processor)
        .withFailedMessageHandler((body, receipt) -> FailedMessageHandlerResult.SHOULD_KEEP)
        .build();
  }

  @Provides
  @Singleton
  @IntoSet
  static QueueConsumerService<?> refundQueueMessageProcessor(
      WorkerQueuesConfig config,
      ObjectMapper objectMapper,
      ScheduledRefundsProcessor processor,
      SqsClient sqsClient,
      AffinityHelper affinityHelper) {
    return new QueueConsumerServiceBuilder<RefundEvent>()
        .withQueueName(
            affinityHelper.getQueueNameForCurrentAffinity(config.refundQueueUrlPattern()))
        .withMessageReadBatchSize(10)
        .withMessageReadWaitTime(Duration.ofSeconds(20))
        .withKeepInFlightTime(Duration.ofMinutes(15))
        .withNumProcessingThreads(25)
        .withProcessWorkQueueSize(20)
        .withTerminationGracePeriod(Duration.ofSeconds(30))
        .withJsonMessageParser(objectMapper, RefundEvent.class)
        .withSqsClient(sqsClient)
        .withMessageProcessor(processor)
        .build();
  }
}
