package com.scopely.paymentgateway.worker.services.disputesmanagement;

import com.scopely.paymentgateway.exceptions.segmentation.UnableToRetrieveSegments;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeEvent;
import com.scopely.paymentgateway.services.reversal.dispute.DisputeService;
import com.scopely.proteus.sqs.consumer.QueueMessageProcessor;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class UserDisputesManagementTaskProcessor implements QueueMessageProcessor<DisputeEvent> {
  private final DisputeService disputeService;

  @Inject
  public UserDisputesManagementTaskProcessor(DisputeService disputeService) {
    this.disputeService = disputeService;
  }

  @Override
  public void process(DisputeEvent disputeEvent) throws RetriableMessageProcessException {
    new PaymentGatewayLogBuilder()
        .addFullPaymentData(disputeEvent.apiKey(), disputeEvent.paymentId(), disputeEvent.userId())
        .build()
        .info("Processing user dispute event: {}", disputeEvent);
    try {
      disputeService.checkAutoblockUser(
          disputeEvent.apiKey(), disputeEvent.userId(), disputeEvent.paymentId());
    } catch (UnableToRetrieveSegments e) {
      throw new RetriableMessageProcessException("Unable to retrieve VIP segment", e);
    }
  }
}
