package com.scopely.paymentgateway.worker.services.emailorder;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_SENDING_EMAIL_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_PAYMENT_SENDING_EMAIL_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_ERROR_CODE;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_TYPE;
import static com.scopely.proteus.logging.Log.error;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.PaymentGatewayException;
import com.scopely.paymentgateway.exceptions.email.RejectedEmailException;
import com.scopely.paymentgateway.exceptions.model.EntityNotFoundException;
import com.scopely.paymentgateway.model.queue.QueueEventType;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.model.queue.emailnotifications.SendEmailRequest;
import com.scopely.paymentgateway.services.email.EmailServiceDispatcher;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.worker.CommandBasedQueueMessageProcessor;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import java.util.Locale;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class EmailOrderTaskConsumer implements CommandBasedQueueMessageProcessor<QueueMessage> {
  private final StatsDClient statsDClient;
  private final EmailServiceDispatcher emailServiceDispatcher;
  private final ObjectMapper mapper;

  @Inject
  public EmailOrderTaskConsumer(
      ObjectMapper mapper,
      StatsDClient statsDClient,
      EmailServiceDispatcher emailServiceDispatcher) {
    this.statsDClient = statsDClient;
    this.emailServiceDispatcher = emailServiceDispatcher;
    this.mapper = mapper;
  }

  @Override
  public boolean acceptsMessage(QueueMessage message) {
    return message.type().equals(getType());
  }

  @Override
  public void process(QueueMessage queueMessage)
      throws RetriableMessageProcessException, NonRetriableMessageProcessException {
    // send metrics for every hit to the email service
    statsDClient.increment(DD_PAYMENT_SENDING_EMAIL_HITS);

    try {
      var emailRequest = parseMessage(queueMessage);
      emailServiceDispatcher.getService(emailRequest.getEmailTemplate()).sendEmail(emailRequest);
    } catch (Exception e) {
      // send metrics for every error to the email service
      evaluateSqsMessageError(e, queueMessage);
    }
  }

  private void evaluateSqsMessageError(Throwable err, QueueMessage task)
      throws NonRetriableMessageProcessException, RetriableMessageProcessException {
    var errorCode = ErrorConstants.UNEXPECTED_WORKER_ERROR;
    // try to unwrap the throwable
    var cause = err.getCause() != null ? err.getCause() : err;

    // capture the error here to perform logging and metrics
    if (cause instanceof PaymentGatewayException paymentGatewayException) {
      errorCode = paymentGatewayException.getErrorCode();
    }
    error(
        cause,
        "[{}] Failed to process email notification batch job with message {},  for {}",
        errorCode.toString().toLowerCase(Locale.getDefault()),
        task);
    statsDClient.increment(
        DD_PAYMENT_SENDING_EMAIL_ERROR,
        MetricsUtils.buildTags(List.of(TAG_TYPE, TAG_ERROR_CODE), List.of(task.type(), errorCode)));

    // if we got a rejected email or an entity not found, we should not retry
    if (cause instanceof EntityNotFoundException || cause instanceof RejectedEmailException) {
      throw new NonRetriableMessageProcessException(cause.getMessage(), cause);
    }
    throw new RetriableMessageProcessException(cause.getMessage(), cause);
  }

  private SendEmailRequest parseMessage(QueueMessage queueMessage)
      throws UnparseableEmailEventException {
    try {
      return mapper.convertValue(queueMessage.payload(), SendEmailRequest.class);
    } catch (Exception e) {
      // In this scenario, the task body is not parseable to an SendEmailRequest
      throw new UnparseableEmailEventException(e);
    }
  }

  public String getType() {
    return QueueEventType.EMAIL_NOTIFICATION.getType();
  }
}
