package com.scopely.paymentgateway.worker.services.backfill;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_BACKFILL_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_BACKFILL_ITEMS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_BACKFILL_ITEMS_ERRORS;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_BACKFILL_STOPPED;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.affinity.AffinityHelper;
import com.scopely.paymentgateway.config.WorkerQueuesConfig;
import com.scopely.paymentgateway.logging.PaymentGatewayLogBuilder;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;
import com.scopely.paymentgateway.model.queue.QueueEventType;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.repositories.QueueRepository;
import com.scopely.paymentgateway.repositories.daos.BackfillPage;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.worker.CommandBasedQueueMessageProcessor;
import com.scopely.paymentgateway.worker.services.backfill.jobs.ReversedAtBackfill;
import com.scopely.paymentgateway.worker.services.backfill.jobs.UnclaimedPaymentsBackfill;
import com.scopely.paymentgateway.worker.services.backfill.jobs.UnclaimedPaymentsByIdBackfill;
import com.scopely.proteus.logging.Log;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.timgroup.statsd.StatsDClient;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import javax.inject.Inject;
import javax.inject.Singleton;

@SuppressWarnings({"rawtypes", "unchecked"})
@Singleton
public class PaymentBackfillConsumer implements CommandBasedQueueMessageProcessor<QueueMessage> {
  public static final String ERROR_PROCESSING_PAYMENT_BACKFILL =
      "Error processing payment backfill";
  private final WorkerQueuesConfig queuesConfig;
  private final StatsDClient statsDClient;
  private final ObjectMapper mapper;
  private final QueueRepository queueRepository;
  private final Set<PaymentBackfill> paymentBackfills;
  private final ClientConfigurationService clientConfigurationService;
  private final AffinityHelper affinityHelper;

  @Inject
  public PaymentBackfillConsumer(
      WorkerQueuesConfig queuesConfig,
      ObjectMapper mapper,
      QueueRepository queueRepository,
      ClientConfigurationService clientConfigurationService,
      StatsDClient statsDClient,
      UnclaimedPaymentsByIdBackfill unclaimedPaymentsByIdBackfill,
      UnclaimedPaymentsBackfill unclaimedPaymentsBackfill,
      ReversedAtBackfill reversedAtBackfill,
      AffinityHelper affinityHelper) {
    this.queuesConfig = queuesConfig;
    this.statsDClient = statsDClient;
    this.mapper = mapper;
    this.queueRepository = queueRepository;
    this.clientConfigurationService = clientConfigurationService;
    this.paymentBackfills =
        Set.of(unclaimedPaymentsByIdBackfill, unclaimedPaymentsBackfill, reversedAtBackfill);
    this.affinityHelper = affinityHelper;
  }

  @Override
  public boolean acceptsMessage(QueueMessage message) {
    return message.type().equals(QueueEventType.PAYMENT_BACKFILL.toString());
  }

  @Override
  public void process(QueueMessage queueMessage)
      throws RetriableMessageProcessException, NonRetriableMessageProcessException {
    Log.info("Consuming payment backfill event");
    statsDClient.increment(DD_BACKFILL_HITS);
    PlaygamiPaymentsClientConfig clientConfig =
        clientConfigurationService.getConfiguration("global");
    if (clientConfig.isBackfillServiceEnabled()) {
      var paymentBackfillEvent = parseMessage(queueMessage);
      Log.info("Processing backfill, page {}", paymentBackfillEvent.getCurrentToken().token());

      var paymentsBackfill = getBackfillInstance(paymentBackfillEvent.getType());
      var backfillPage = paymentsBackfill.getData(paymentBackfillEvent.getCurrentToken());
      // FIXME: items will be backfilled with no guarantees of fully migration, if any item fails
      // it will be skipped
      iterateAndExecute(paymentsBackfill, backfillPage);
      scheduleNextIfNecessary(backfillPage, paymentBackfillEvent);

      Log.info(
          "Total backfilled: {} ",
          paymentBackfillEvent.getProcessedItems() + backfillPage.items().size());
    } else {
      statsDClient.increment(DD_BACKFILL_STOPPED);
      Log.info("Backfill stopped abrubtly, backfill service is disabled.");
    }
  }

  private PaymentBackfillEvent parseMessage(QueueMessage sqsEventTask)
      throws NonRetriableMessageProcessException {
    try {
      return mapper.convertValue(sqsEventTask.payload(), PaymentBackfillEvent.class);
    } catch (Exception e) {
      // In this scenario, the task body is not parseable
      throw new NonRetriableMessageProcessException(
          "Couldn't parse task body: " + sqsEventTask.payload());
    }
  }

  private void scheduleNextIfNecessary(
      BackfillPage backfillPage, PaymentBackfillEvent paymentBackfillEvent) {
    if (backfillPage.token() != null && backfillPage.token().hasMoreItems()) {
      var nextEvent =
          new PaymentBackfillEvent.Builder()
              .setProcessedItems(
                  paymentBackfillEvent.getProcessedItems() + backfillPage.items().size())
              .setType(paymentBackfillEvent.getType())
              .setCurrentToken(backfillPage.token())
              .build();
      // enqueue next page
      Log.info("Enqueue next page");
      this.queueRepository.queueTask(
          affinityHelper.getQueueNameForCurrentAffinity(queuesConfig.scheduleQueueUrlPattern()),
          new QueueMessage(QueueEventType.PAYMENT_BACKFILL.toString(), nextEvent));
    } else {
      Log.info("No need to enqueue another page");
    }
  }

  private PaymentBackfill getBackfillInstance(BackfillType backfillType) {
    return this.paymentBackfills.stream()
        .filter(backfill -> backfill.getBackfillType() == backfillType)
        .findFirst()
        .orElseThrow();
  }

  private void iterateAndExecute(PaymentBackfill paymentBackfill, BackfillPage backfillPage) {
    Log.info("Retrieved {} items", backfillPage.items().size());
    var filteredAndTransformedItems = paymentBackfill.transform(backfillPage.items());

    Log.info("Filtered {} items", filteredAndTransformedItems.size());
    var itemCount = new AtomicInteger();
    filteredAndTransformedItems.forEach(
        itemToBackfill -> {
          try {
            paymentBackfill.backfill(itemToBackfill);
            itemCount.getAndIncrement();
            statsDClient.increment(DD_BACKFILL_ITEMS);
          } catch (Exception e) {
            new PaymentGatewayLogBuilder().build().error(e, ERROR_PROCESSING_PAYMENT_BACKFILL);
            statsDClient.increment(DD_BACKFILL_ITEMS_ERRORS);
          }
        });

    Log.info("Backfilled {} items successfully", itemCount);
  }
}
