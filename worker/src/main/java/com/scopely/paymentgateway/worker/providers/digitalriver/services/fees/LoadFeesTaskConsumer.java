package com.scopely.paymentgateway.worker.providers.digitalriver.services.fees;

import static com.scopely.paymentgateway.constants.StatsConstants.DD_FEES_TASK_LOAD_ERROR;
import static com.scopely.paymentgateway.constants.StatsConstants.DD_FEES_TASK_LOAD_HITS;
import static com.scopely.paymentgateway.constants.StatsConstants.TAG_RESULT;
import static com.scopely.proteus.logging.Log.error;

import com.scopely.paymentgateway.constants.ErrorConstants;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.model.queue.QueueEventType;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.services.fees.FeesByProviderService;
import com.scopely.paymentgateway.stats.MetricsUtils;
import com.scopely.paymentgateway.worker.CommandBasedQueueMessageProcessor;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.timgroup.statsd.StatsDClient;
import javax.inject.Inject;
import javax.inject.Singleton;

/**
 * This consumer will populate the fees table with the fees for each provider. It will be triggered
 * by a scheduled event.
 */
@Singleton
public class LoadFeesTaskConsumer implements CommandBasedQueueMessageProcessor<QueueMessage> {
  private final StatsDClient statsDClient;
  private final FeesByProviderService feesByProviderService;

  @Inject
  public LoadFeesTaskConsumer(
      StatsDClient statsDClient, FeesByProviderService feesByProviderService) {
    this.statsDClient = statsDClient;
    this.feesByProviderService = feesByProviderService;
  }

  @Override
  public boolean acceptsMessage(QueueMessage message) {
    return message.type().equals(QueueEventType.LOAD_FEES.getType());
  }

  @Override
  public void process(QueueMessage queueMessage)
      throws RetriableMessageProcessException, NonRetriableMessageProcessException {
    try {
      statsDClient.increment(DD_FEES_TASK_LOAD_HITS);
      feesByProviderService.update();
    } catch (Exception err) {
      var error =
          err instanceof RequestToProviderException
              ? ((RequestToProviderException) err).getErrorCode()
              : ErrorConstants.UNEXPECTED_WORKER_ERROR;
      error(
          err,
          "Failed to populate the fees table with the fees for each provider. Error message: {}",
          error);
      statsDClient.increment(DD_FEES_TASK_LOAD_ERROR, MetricsUtils.buildTags(TAG_RESULT, error));
      throw new RetriableMessageProcessException(error.toString());
    }
  }
}
