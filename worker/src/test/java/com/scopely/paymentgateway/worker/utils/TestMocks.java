package com.scopely.paymentgateway.worker.utils;

import static jakarta.ws.rs.core.Response.Status.BAD_REQUEST;

import com.scopely.paymentgateway.constants.LocationConstants;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.model.checkout.Item;
import com.scopely.paymentgateway.model.checkout.ProviderItem;
import com.scopely.paymentgateway.model.client.config.ConfigurationProviderIdentifier;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;
import com.scopely.paymentgateway.model.createpayment.CreatePaymentRequestData;
import com.scopely.paymentgateway.model.customer.Customer;
import com.scopely.paymentgateway.model.fulfillment.FulfillmentItem;
import com.scopely.paymentgateway.model.payment.GetPaymentsByDashboardFilterRequest;
import com.scopely.paymentgateway.model.payment.ItemData;
import com.scopely.paymentgateway.model.payment.Payment;
import com.scopely.paymentgateway.model.payment.PaymentAggregation;
import com.scopely.paymentgateway.model.payment.PaymentLocation;
import com.scopely.paymentgateway.model.payment.PaymentMethod;
import com.scopely.paymentgateway.model.payment.PaymentOrder;
import com.scopely.paymentgateway.model.payment.PaymentProviderIdentifier;
import com.scopely.paymentgateway.model.payment.PaymentStatus;
import com.scopely.paymentgateway.model.payment.PlatformType;
import com.scopely.paymentgateway.model.payment.PriceData;
import com.scopely.paymentgateway.model.payment.PriceDetail;
import com.scopely.paymentgateway.model.payment.PriceProvider;
import com.scopely.paymentgateway.model.payment.PricingMode;
import com.scopely.paymentgateway.model.payment.ProductType;
import com.scopely.paymentgateway.model.payment.Properties;
import com.scopely.paymentgateway.model.payment.ProviderStatus;
import com.scopely.paymentgateway.model.payment.User;
import com.scopely.paymentgateway.model.provider.ProviderCheckout;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.model.refund.RefundStatus;
import com.scopely.paymentgateway.model.reversal.Chargeback;
import com.scopely.paymentgateway.model.reversal.dispute.Dispute;
import com.scopely.paymentgateway.model.reversal.dispute.DisputeStatus;
import com.scopely.paymentgateway.model.user.UserAutoblockOption;
import com.scopely.paymentgateway.model.user.UserBlockedReason;
import com.scopely.paymentgateway.providers.digitalriver.config.DigitalRiverClientConfig;
import com.scopely.paymentgateway.providers.digitalriver.model.payment.DigitalRiverPaymentProviderData;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.DigitalRiverProviderRefundStatus;
import com.scopely.paymentgateway.providers.digitalriver.model.refund.DigitalRiverRefundProviderData;
import com.scopely.paymentgateway.providers.digitalriver.webhook.events.DigitalRiverSourceCreation;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.assertj.core.util.Lists;
import org.javamoney.moneta.Money;

public class TestMocks {
  public static final String PAYMENT_ID = "payment-id-1";
  public static final String SESSION_ID = "SESSION_ID";
  public static final String CHECKOUT_ID = UUID.randomUUID().toString();
  public static final String SKU_ID = "SKU_ID";
  public static final String USER_ID = "USER_ID";
  public static final String DEVICE_TOKEN = "DEVICE_TOKEN";
  public static final String API_KEY = "API_KEY";
  public static final String CREDIT_CARD = "creditCard";
  public static final PaymentProviderIdentifier PROVIDER = PaymentProviderIdentifier.DIGITAL_RIVER;
  private static final String US = "US";
  public static final String USD = "USD";
  private static final String ES = "ES";
  public static final String EUR = "EUR";
  public static final String ID = "ID";
  private static final Money PRICE = Money.of(9, USD);
  private static final Money TOTAL = Money.of(11, USD);
  private static final Money TAXES = Money.of(0.1, USD);

  private static final Money LOCAL_PRICE = Money.of(10, EUR);
  private static final Money LOCAL_TOTAL = Money.of(12, EUR);
  private static final Money LOCAL_TAXES = Money.of(0.1, EUR);

  public static final String EMAIL = "email";
  public static final String LOCALE = LocationConstants.DEFAULT_LOCALE;
  public static final String CLIENT_SECRET = "clientSecret";
  public static final String ITEM_NAME = "ITEM_NAME";
  public static final String PRODUCT_ID = "PRODUCT_ID";
  public static final String TRACKING_ID = "TRACKING_ID";
  public static final String ORDER_ID = "ORDER_ID";
  public static final String SOURCE_ID = "SOURCE_ID";
  public static final int QUANTITY = 1;
  public static final String ITEM_ID = "ITEM_ID";
  public static final String DECLINED = "declined";
  public static final String USER_IP = "*******";
  private static final String PUBLIC_KEY = "PUBLIC_KEY";
  private static final String POSTAL_CODES = "POSTAL_CODES";
  private static final String SECRET_KEY = "SECRET_KEY_PLACEHOLDER";
  private static final String SKU_GROUP_ID = "SKU_GROUP_ID";

  private static final String MAIL_CHIMP_SUBJECT_PAYMENT_SUCCESS = "mailChimpSubjectPaymentSuccess";
  private static final String MAIL_CHIMP_SUBJECT_PAYMENT_ERROR = "mailChimpSubjectPaymentError";
  private static final String MAIL_CHIMP_SUBJECT_REFUND_SUCCESS = "mailChimpSubjectRefundSuccess";
  private static final String CURRENCIES_BY_COUNTRY =
      "[{\"code\": \"US\"  , \"currencies\":  [\"USD\"]}]";
  public static final String GAME_NAME = "Scopely payments";

  public static final String SKU_NAME = "SKU_NAME";
  public static final String EXTERNAL_ID = "EXTERNAL_ID";
  public static final String FIRST_NAME = "FIRST_NAME";
  public static final String LAST_NAME = "LAST_NAME";
  private static final String CONVERSION_FEES = "CONVERSION_FEES";
  private static final String DEFAULT_CONVERSION_FEE = "DEFAULT_CONVERSION_FEE";
  public static final ProviderItem PROVIDER_ITEM =
      new ProviderItem.Builder().setProvider(PriceProvider.PLAYGAMI).setSku(SKU_ID).build();

  public static final GetPaymentsByDashboardFilterRequest FILTER_WITH_REVERSAL =
      new GetPaymentsByDashboardFilterRequest(
          API_KEY, PAYMENT_ID, ORDER_ID, USER_ID, PROVIDER.getDescription(), true);
  public static final GetPaymentsByDashboardFilterRequest FILTER_WITHOUT_REVERSAL =
      new GetPaymentsByDashboardFilterRequest(
          API_KEY, PAYMENT_ID, ORDER_ID, USER_ID, PROVIDER.getDescription(), false);
  public static final ItemData PAYMENT_ITEM_DATA =
      new ItemData.Builder()
          .setInternalSku(PRODUCT_ID)
          .setProviderItem(PROVIDER_ITEM)
          .setName(SKU_NAME)
          .build();

  public static final ItemData PRODUCT_DETAIL =
      new ItemData.Builder()
          .setInternalSku(PRODUCT_ID)
          .setName(ITEM_NAME)
          .setProviderItem(PROVIDER_ITEM)
          .setProductType(ProductType.IAP)
          .build();

  public static final PriceData PRICE_DATA =
      new PriceData.Builder()
          .setLocalPrice(
              new PriceDetail.Builder()
                  .setTotalAmount(TOTAL.getNumberStripped())
                  .setSubtotalAmount(PRICE.getNumberStripped())
                  .setTaxAmount(TAXES.getNumberStripped())
                  .setCurrency(PRICE.getCurrency().getCurrencyCode())
                  .build())
          .setBasePrice(
              new PriceDetail.Builder()
                  .setTotalAmount(TOTAL.getNumberStripped())
                  .setSubtotalAmount(PRICE.getNumberStripped())
                  .setTaxAmount(TAXES.getNumberStripped())
                  .setCurrency(PRICE.getCurrency().getCurrencyCode())
                  .build())
          .build();

  public static final Payment PAYMENT =
      new Payment.Builder()
          .setPaymentId(PAYMENT_ID)
          .setPaymentStatus(PaymentStatus.INITIATED)
          .setItemData(PAYMENT_ITEM_DATA)
          .setUserId(USER_ID)
          .setDeviceToken(DEVICE_TOKEN)
          .setApiKey(API_KEY)
          .setPaymentMethodUsed(PaymentMethod.CREDIT_CARD)
          .setClaimed(false)
          .setClaimedAt(null)
          .setReceiptSentAt(null)
          .setCountry(US)
          .setLocale("en")
          .setCreatedAt(Instant.now())
          .setUpdatedAt(Instant.now())
          .setTrackingId(TRACKING_ID)
          .setFulfillmentItems(Lists.emptyList())
          .setErrorMessage(DECLINED)
          .setSavedPaymentMethod(false)
          .setVip(false)
          .setProviderData(
              new DigitalRiverPaymentProviderData.Builder()
                  .setSessionId(SESSION_ID)
                  .setCheckoutId(CHECKOUT_ID)
                  .setProvider(PROVIDER)
                  .build())
          .setPriceData(PRICE_DATA)
          .setOrderId(ORDER_ID)
          .build();

  public static final Payment REJECTED_PAYMENT =
      new Payment.Builder()
          .setPaymentId(PAYMENT_ID)
          .setPaymentStatus(PaymentStatus.REJECTED)
          .setItemData(PAYMENT_ITEM_DATA)
          .setUserId(USER_ID)
          .setDeviceToken(DEVICE_TOKEN)
          .setApiKey(API_KEY)
          .setClaimed(false)
          .setCountry(US)
          .setCreatedAt(Instant.now())
          .setUpdatedAt(Instant.now())
          .setTrackingId(TRACKING_ID)
          .setVip(false)
          .setPriceData(PRICE_DATA)
          .build();

  public static final Refund REFUND =
      new Refund.Builder()
          .setStatus(RefundStatus.COMPLETED)
          .setApiKey(API_KEY)
          .setRefundId(UUID.randomUUID().toString())
          .setPaymentId(PAYMENT_ID)
          .setUpdatedAt(Instant.now())
          .setCreatedAt(Instant.now())
          .setRefundReason(RefundReason.ACCIDENTAL_PURCHASE)
          .setRefundedAmount(PRICE)
          .setRefundedLocalAmount(PRICE)
          .setProviderData(
              new DigitalRiverRefundProviderData.Builder()
                  .setStatus(DigitalRiverProviderRefundStatus.CREATED)
                  .setOrderId(ORDER_ID)
                  .build())
          .setPaymentMethodUsed(PaymentMethod.CREDIT_CARD)
          .build();
  public static final PaymentAggregation PAYMENT_AGGREGATION =
      new PaymentAggregation.Builder()
          .setPayment(PAYMENT)
          .setRefunds(Collections.singletonList(REFUND))
          .build();
  public static final Dispute DISPUTE =
      new Dispute.Builder()
          .setApiKey(API_KEY)
          .setUserId(USER_ID)
          .setPaymentId(PAYMENT_ID)
          .setDisputeId(UUID.randomUUID().toString())
          .setDisputeReason("any-reason")
          .setCreatedAt(Instant.now())
          .setUpdatedAt(Instant.now())
          .setAmount(PRICE)
          .setLocalAmount(PRICE)
          .setStatus(DisputeStatus.LOST)
          .build();
  public static final Chargeback CHARGEBACK =
      new Chargeback.Builder()
          .setApiKey(API_KEY)
          .setUserId(USER_ID)
          .setPaymentId(PAYMENT_ID)
          .setChargebackId(UUID.randomUUID().toString())
          .setChargebackReason("any-reason")
          .setCreatedAt(Instant.now())
          .setUpdatedAt(Instant.now())
          .setAmount(PRICE)
          .setLocalAmount(PRICE)
          .build();
  public static final Properties PROPERTIES =
      new Properties.Builder()
          .setUser(
              new User.Builder()
                  .setUserId(USER_ID)
                  .setFirstName(FIRST_NAME)
                  .setLastName(LAST_NAME)
                  .setEmail(EMAIL)
                  .setVip(false))
          .setUserIp(USER_IP)
          .setPricingMode(PricingMode.EXPLICIT)
          .setSandbox(true)
          .setLocale(LOCALE)
          .build();

  public static final PaymentLocation PAYMENT_LOCATION =
      new PaymentLocation.Builder()
          .setIpAddress(USER_IP)
          .setCountry(US)
          .setContinent("NA")
          .setCurrency(USD)
          .setTaxIncluded(false)
          .build();

  public static final PaymentLocation PAYMENT_LOCATION_ES =
      new PaymentLocation.Builder()
          .setIpAddress(USER_IP)
          .setCountry(ES)
          .setContinent("EU")
          .setCurrency(EUR)
          .setTaxIncluded(true)
          .build();

  public static final com.scopely.paymentgateway.model.user.User USER =
      new com.scopely.paymentgateway.model.user.User.Builder()
          .setApiKey(API_KEY)
          .setUserId(USER_ID)
          .setBlocked(false)
          .setProvider(PaymentProviderIdentifier.DIGITAL_RIVER)
          .setUpdatedAt(Instant.now())
          .build();

  public static final com.scopely.paymentgateway.model.user.User USER_NOT_PROVIDER =
      new com.scopely.paymentgateway.model.user.User.Builder()
          .setApiKey(API_KEY)
          .setUserId(USER_ID)
          .setBlocked(false)
          .setUpdatedAt(Instant.now())
          .build();

  public static final com.scopely.paymentgateway.model.user.User BLOCKED_USER =
      new com.scopely.paymentgateway.model.user.User.Builder()
          .setApiKey(API_KEY)
          .setUserId(USER_ID)
          .setProvider(PROVIDER)
          .setBlocked(true)
          .setLastBlockedAt(Instant.now())
          .setBlockedReason(UserBlockedReason.TOO_MANY_DISPUTES)
          .setUpdatedAt(Instant.now())
          .build();
  public static final CreatePaymentRequestData PAYMENT_REQUEST_DATA_EXPLICIT_WITHOUT_EXCHANGE =
      new CreatePaymentRequestData.Builder()
          .setApiKey(API_KEY)
          .setItem(
              new Item.Builder()
                  .setSku(PRODUCT_ID)
                  .setName(SKU_NAME)
                  .setBasePrice(TOTAL)
                  .setLocalizedPrice(LOCAL_TOTAL)
                  .addAllProviders(
                      List.of(
                          new ProviderItem.Builder()
                              .setProvider(PriceProvider.PLAYGAMI)
                              .setSku(SKU_ID)
                              .build()))
                  .build())
          .setProperties(
              new Properties.Builder()
                  .setUser(
                      new User.Builder()
                          .setUserId(USER_ID)
                          .setFirstName(FIRST_NAME)
                          .setLastName(LAST_NAME)
                          .setEmail(EMAIL)
                          .setVip(false))
                  .setUserIp(USER_IP)
                  .setSandbox(true)
                  .setPricingMode(PricingMode.EXPLICIT)
                  .setLocale(LOCALE)
                  .build())
          .setDeviceToken(DEVICE_TOKEN)
          .setTrackingId(TRACKING_ID)
          .build();

  public static final CreatePaymentRequestData PAYMENT_REQUEST_DATA_EXPLICIT_EXCHANGE =
      new CreatePaymentRequestData.Builder()
          .setApiKey(API_KEY)
          .setItem(
              new Item.Builder()
                  .setSku(PRODUCT_ID)
                  .setName(SKU_NAME)
                  .setBasePrice(TOTAL)
                  .addAllProviders(
                      List.of(
                          new ProviderItem.Builder()
                              .setProvider(PriceProvider.TEST)
                              .setSku(SKU_ID)
                              .build()))
                  .build())
          .setProperties(
              new Properties.Builder()
                  .setUser(
                      new User.Builder()
                          .setUserId(USER_ID)
                          .setFirstName(FIRST_NAME)
                          .setLastName(LAST_NAME)
                          .setEmail(EMAIL)
                          .setVip(false))
                  .setUserIp(USER_IP)
                  .setSandbox(true)
                  .setPricingMode(PricingMode.EXPLICIT)
                  .setLocale(LOCALE)
                  .build())
          .setDeviceToken(DEVICE_TOKEN)
          .setTrackingId(TRACKING_ID)
          .build();

  public static final CreatePaymentRequestData PAYMENT_REQUEST_DATA_SKU =
      new CreatePaymentRequestData.Builder()
          .setApiKey(API_KEY)
          .setItem(
              new Item.Builder()
                  .setSku(PRODUCT_ID)
                  .setName(SKU_NAME)
                  .addAllProviders(
                      List.of(
                          new ProviderItem.Builder()
                              .setProvider(PriceProvider.PLAYGAMI)
                              .setSku(SKU_ID)
                              .build()))
                  .build())
          .setProperties(
              new Properties.Builder()
                  .setUser(
                      new User.Builder()
                          .setUserId(USER_ID)
                          .setFirstName(FIRST_NAME)
                          .setLastName(LAST_NAME)
                          .setEmail(EMAIL)
                          .setVip(false))
                  .setUserIp(USER_IP)
                  .setSandbox(true)
                  .setPricingMode(PricingMode.SKU)
                  .setCountry(US)
                  .setLocale(LOCALE)
                  .build())
          .setTrackingId(TRACKING_ID)
          .build();

  public static final CreatePaymentRequestData PAYMENT_REQUEST_DATA_PC_PAYMENT =
      new CreatePaymentRequestData.Builder()
          .setApiKey(API_KEY)
          .setItem(
              new Item.Builder()
                  .setSku(PRODUCT_ID)
                  .setName(SKU_NAME)
                  .setBasePrice(TOTAL)
                  .setLocalizedPrice(LOCAL_TOTAL)
                  .addAllProviders(
                      List.of(
                          new ProviderItem.Builder()
                              .setProvider(PriceProvider.TEST)
                              .setSku(SKU_ID)
                              .build()))
                  .build())
          .setProperties(
              new Properties.Builder()
                  .setUser(
                      new User.Builder()
                          .setUserId(USER_ID)
                          .setFirstName(FIRST_NAME)
                          .setLastName(LAST_NAME)
                          .setEmail(EMAIL)
                          .setVip(false))
                  .setUserIp(USER_IP)
                  .setSandbox(true)
                  .setPricingMode(PricingMode.EXPLICIT)
                  .setLocale(LOCALE)
                  .build())
          .setDeviceToken(DEVICE_TOKEN)
          .setTrackingId(TRACKING_ID)
          .setPlatform(PlatformType.PC)
          .build();

  public static final DigitalRiverClientConfig DR_CLIENT_CONFIG =
      new DigitalRiverClientConfig.Builder()
          .setApiKey(API_KEY)
          .setPaymentProviderIdentifier(ConfigurationProviderIdentifier.DIGITAL_RIVER)
          .setPublicKey(PUBLIC_KEY)
          .setSecretKey(SECRET_KEY)
          .setSkuGroupId(SKU_GROUP_ID)
          .setWebhookSecretKey(API_KEY)
          .setPostalCodes(POSTAL_CODES)
          .setConversionFees(CONVERSION_FEES)
          .setDefaultConversionFee(DEFAULT_CONVERSION_FEE)
          .build();

  public static final PlaygamiPaymentsClientConfig PAYMENTS_CLIENT_CONFIG =
      new PlaygamiPaymentsClientConfig.Builder()
          .setApiKey(API_KEY)
          .setPaymentProviderIdentifier(ConfigurationProviderIdentifier.PLAYGAMI_PAYMENTS)
          .setEmailOrderReceiptFrom("emailOrder")
          .setEmailOrderSenderAddress("emailSenderAddress")
          .setSendGridApiKey("sendgrid-test-api-key")
          .setGameName(GAME_NAME)
          .setCurrenciesByCountry(CURRENCIES_BY_COUNTRY)
          .setAutoblockUsersOption(UserAutoblockOption.BLOCK_USERS)
          .setFlagVIPUsersEnabled(true)
          .setFraudNotificationSystemDestination("test-channel")
          .build();

  public static final DigitalRiverSourceCreation DIGITAL_RIVER_SOURCE_CREATION =
      new DigitalRiverSourceCreation.Builder()
          .setPaymentId(PAYMENT_ID)
          .setSourceId(SOURCE_ID)
          .setPaymentMethod(PaymentMethod.CREDIT_CARD.getDescription())
          .setFutureUse(false)
          .setSavedPaymentMethod(false)
          .build();

  public static final FulfillmentItem FULFILLMENT_ITEM =
      new FulfillmentItem.Builder().setItemId(ITEM_ID).setQuantity(QUANTITY).build();

  public static final PaymentOrder PAYMENT_ORDER =
      new PaymentOrder.Builder()
          .setPaymentId(PAYMENT_ID)
          .setOrderId(ORDER_ID)
          .setProviderStatus(ProviderStatus.SOURCE_CREATED)
          .addAllFulfillmentItems(List.of(FULFILLMENT_ITEM))
          .setPriceData(PRICE_DATA)
          .build();

  public static final ProviderCheckout PROVIDER_CHECKOUT =
      new ProviderCheckout.Builder()
          .setSessionId(SESSION_ID)
          .setCheckoutId(CHECKOUT_ID)
          .setCountry(US)
          .setItemData(PAYMENT_ITEM_DATA)
          .setLocale(LOCALE)
          .setEmail(EMAIL)
          .setPriceData(PRICE_DATA)
          .setBrowserIP(USER_IP)
          .build();

  private static final String ERROR_BODY =
      "{\"type\":\"conflict\",\"errors\":[{\"code\":\"declined\","
          + "\"message\":\"Failed to charge source.\"}]}";

  public static final String FRAUD_ERROR_BODY =
      "{\"type\":\"conflict\",\"errors\":[{\"code\":\"order-fraud-failure\","
          + "\"message\":\"Fraud\"}]}";

  public static final RequestToProviderException REQUEST_TO_PROVIDER_EXCEPTION_RETROFIT =
      new RequestToProviderException(BAD_REQUEST.getStatusCode(), "declined");

  public static final Customer CUSTOMER =
      new Customer.Builder()
          .setId(USER_ID)
          .addAllSavedCards(List.of())
          .setExternalId(EXTERNAL_ID)
          .setEmail(EMAIL)
          .setFirstName(FIRST_NAME)
          .setLastName(LAST_NAME)
          .build();
}
