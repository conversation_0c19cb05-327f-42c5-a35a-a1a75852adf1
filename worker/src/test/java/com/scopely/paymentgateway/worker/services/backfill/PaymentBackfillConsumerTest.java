package com.scopely.paymentgateway.worker.services.backfill;

import static com.scopely.paymentgateway.model.queue.QueueEventType.PAYMENT_BACKFILL;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.affinity.AffinityHelper;
import com.scopely.paymentgateway.config.WorkerQueuesConfig;
import com.scopely.paymentgateway.model.client.config.PlaygamiPaymentsClientConfig;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.repositories.QueueRepository;
import com.scopely.paymentgateway.repositories.daos.BackfillPage;
import com.scopely.paymentgateway.repositories.daos.PaginationToken;
import com.scopely.paymentgateway.repositories.daos.PaymentDAO;
import com.scopely.paymentgateway.repositories.daos.mapper.PaymentDAOMapper;
import com.scopely.paymentgateway.services.clientconfig.ClientConfigurationService;
import com.scopely.paymentgateway.worker.services.backfill.jobs.ReversedAtBackfill;
import com.scopely.paymentgateway.worker.services.backfill.jobs.UnclaimedPaymentsBackfill;
import com.scopely.paymentgateway.worker.services.backfill.jobs.UnclaimedPaymentsByIdBackfill;
import com.scopely.paymentgateway.worker.utils.TestMocks;
import com.timgroup.statsd.StatsDClient;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PaymentBackfillConsumerTest {
  private static final PaginationToken FIRST_PAGE = new PaginationToken(null);
  private static final QueueMessage FIRST_PAGE_MESSAGE =
      new QueueMessage(
          PAYMENT_BACKFILL.name(),
          new PaymentBackfillEvent.Builder().setType(BackfillType.PAYMENTS_UNCLAIMED).build());
  private static final PaginationToken SECOND_PAGE =
      new PaginationToken(Map.of("paymentId", new AttributeValue("SECOND_PAGINATION")));
  private static final PaymentDAO PAYMENT_DAO_A =
      PaymentDAOMapper.paymentToPaymentDao(TestMocks.PAYMENT);

  @Mock WorkerQueuesConfig workerQueuesConfig;
  @Mock QueueRepository queueRepository;
  @Mock UnclaimedPaymentsBackfill unclaimedPaymentsBackfill;
  @Mock UnclaimedPaymentsByIdBackfill unclaimedPaymentsByIdBackfill;
  @Mock ReversedAtBackfill reversedAtBackfill;
  @Mock ClientConfigurationService clientConfigurationService;
  @Mock StatsDClient statsDClient;
  @Mock AffinityHelper affinityHelper;

  private PaymentBackfillConsumer paymentBackfillConsumer;
  @Mock private PlaygamiPaymentsClientConfig clientConfig;

  @BeforeEach
  public void setup() {
    paymentBackfillConsumer =
        new PaymentBackfillConsumer(
            workerQueuesConfig,
            new ObjectMapper(),
            queueRepository,
            clientConfigurationService,
            statsDClient,
            unclaimedPaymentsByIdBackfill,
            unclaimedPaymentsBackfill,
            reversedAtBackfill,
            affinityHelper);
  }

  @Test
  void consumeButDontExecuteBecauseServiceDisabled() {
    when(clientConfigurationService.getConfiguration("global")).thenReturn(clientConfig);
    when(clientConfig.isBackfillServiceEnabled()).thenReturn(false);
    Assertions.assertDoesNotThrow(() -> paymentBackfillConsumer.process(FIRST_PAGE_MESSAGE));
  }

  @Test
  void consumeAndExecuteAllStepsButNotCreateAnotherSqs() {
    when(clientConfigurationService.getConfiguration("global")).thenReturn(clientConfig);
    when(clientConfig.isBackfillServiceEnabled()).thenReturn(true);
    when(unclaimedPaymentsBackfill.getBackfillType()).thenReturn(BackfillType.PAYMENTS_UNCLAIMED);
    when(unclaimedPaymentsBackfill.getData(FIRST_PAGE))
        .thenReturn(new BackfillPage<>(new PaginationToken(null), List.of(PAYMENT_DAO_A)));
    when(unclaimedPaymentsBackfill.transform(List.of(PAYMENT_DAO_A)))
        .thenReturn(List.of(TestMocks.PAYMENT));

    Assertions.assertDoesNotThrow(() -> paymentBackfillConsumer.process(FIRST_PAGE_MESSAGE));

    verify(queueRepository, never()).queueTask(any(), any(QueueMessage.class));
    verify(unclaimedPaymentsBackfill).backfill(TestMocks.PAYMENT);
  }

  @Test
  void consumeAndExecuteAllStepsAndCreateAnotherSqs() {
    when(clientConfigurationService.getConfiguration("global")).thenReturn(clientConfig);
    when(clientConfig.isBackfillServiceEnabled()).thenReturn(true);
    when(unclaimedPaymentsBackfill.getBackfillType()).thenReturn(BackfillType.PAYMENTS_UNCLAIMED);
    when(unclaimedPaymentsBackfill.getData(FIRST_PAGE))
        .thenReturn(new BackfillPage<>(SECOND_PAGE, List.of(PAYMENT_DAO_A)));
    when(unclaimedPaymentsBackfill.transform(List.of(PAYMENT_DAO_A)))
        .thenReturn(List.of(TestMocks.PAYMENT));

    Assertions.assertDoesNotThrow(() -> paymentBackfillConsumer.process(FIRST_PAGE_MESSAGE));

    verify(queueRepository).queueTask(any(), any(QueueMessage.class));
    verify(unclaimedPaymentsBackfill).backfill(TestMocks.PAYMENT);
  }

  @Test
  void consumeAndFail() {
    when(clientConfigurationService.getConfiguration("global")).thenReturn(clientConfig);
    when(clientConfig.isBackfillServiceEnabled()).thenReturn(true);
    when(unclaimedPaymentsBackfill.getBackfillType()).thenReturn(BackfillType.PAYMENTS_UNCLAIMED);
    when(unclaimedPaymentsBackfill.getData(FIRST_PAGE)).thenThrow(RuntimeException.class);

    Assertions.assertThrows(
        RuntimeException.class, () -> paymentBackfillConsumer.process(FIRST_PAGE_MESSAGE));
  }
}
