package com.scopely.paymentgateway.worker.services;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DefaultMessageConsumerTest {

  @InjectMocks private DefaultMessageConsumer defaultMessageConsumer;

  @Test
  void executeDefaultAckBecauseTypeIsMissing() {
    Assertions.assertThrows(
        NonRetriableMessageProcessException.class,
        () -> defaultMessageConsumer.process(new QueueMessage("default", "{}")));
  }

  @Test
  void acceptsMessage() {
    assertTrue(defaultMessageConsumer.acceptsMessage(new QueueMessage("default", "{}")));
    assertFalse(defaultMessageConsumer.acceptsMessage(new QueueMessage("default_2", "{}")));
  }
}
