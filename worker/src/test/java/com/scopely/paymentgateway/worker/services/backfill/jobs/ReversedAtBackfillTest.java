package com.scopely.paymentgateway.worker.services.backfill.jobs;

import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;

import com.scopely.paymentgateway.repositories.PaymentRepository;
import com.scopely.paymentgateway.repositories.daos.PaginationToken;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.worker.utils.TestMocks;
import com.timgroup.statsd.StatsDClient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ReversedAtBackfillTest {
  @Mock PaymentService paymentService;
  @Mock PaymentRepository paymentRepository;
  @Mock StatsDClient statsDClient;
  @InjectMocks ReversedAtBackfill reversedAtBackfill;

  @Test
  void testGetData() {
    var paginationToken = new PaginationToken(null);
    reversedAtBackfill.getData(paginationToken);
    verify(paymentRepository, timeout(1)).getUnclaimedPaymentsFromIndexPaginated(paginationToken);
  }

  @Test
  void testBackfillOk() {
    reversedAtBackfill.backfill(TestMocks.PAYMENT);
    verify(paymentService).systemSave(TestMocks.PAYMENT);
  }
}
