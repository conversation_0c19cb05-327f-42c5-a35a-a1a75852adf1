package com.scopely.paymentgateway.worker.providers.digitalriver.services.taxes;

import static com.scopely.paymentgateway.model.queue.QueueEventType.LOAD_FEES;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.services.fees.FeesByProviderService;
import com.scopely.paymentgateway.worker.providers.digitalriver.services.fees.LoadFeesTaskConsumer;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.timgroup.statsd.StatsDClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LoadFeesTaskConsumerTest {
  @Mock private StatsDClient statsDClient;
  @Mock private FeesByProviderService feesByProviderService;
  @InjectMocks private LoadFeesTaskConsumer loadFeesTaskConsumer;

  @Test
  void executeLoadTaxes() {
    Assertions.assertDoesNotThrow(
        () -> loadFeesTaskConsumer.process(new QueueMessage(LOAD_FEES.getType(), "{}")));
  }

  @Test
  void acceptsMessage() {
    assertTrue(loadFeesTaskConsumer.acceptsMessage(new QueueMessage(LOAD_FEES.getType(), "{}")));
    assertFalse(loadFeesTaskConsumer.acceptsMessage(new QueueMessage("default_2", "{}")));
  }

  @Test
  void executeLoadTaxesRuntimeErrorAckAndReturnFalse() throws RequestToProviderException {
    Mockito.doThrow(new RequestToProviderException("error", 500))
        .when(feesByProviderService)
        .update();
    Assertions.assertThrows(
        RetriableMessageProcessException.class,
        () -> loadFeesTaskConsumer.process(new QueueMessage(LOAD_FEES.getType(), "{}")));
  }
}
