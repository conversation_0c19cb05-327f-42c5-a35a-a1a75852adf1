package com.scopely.paymentgateway.worker.com.scopely.paymentgateway.worker.model;

import static com.scopely.paymentgateway.model.queue.QueueEventType.EMAIL_NOTIFICATION;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.worker.model.ForwarderQueueMessageParser;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.timgroup.statsd.StatsDClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ForwarderQueueMessageParserTest {

  private static final String RAW_EVENT =
      "{\"type\":\"EMAIL_NOTIFICATION\",\"payload\":{\"resourceId\":\"XPXKDD7X\",\"paymentProviderIdentifier\":\"DIGITAL_RIVER\",\"emailTemplate\":\"PAYMENT\",\"emailType\":\"SUCCESS\",\"apiKey\":\"API_KEY\"}}";
  private static final String RAW_UNDEFINED_EVENT =
      "{\"typee\":\"EMAIL_NOTIFICATION_2\",\"payload\":{\"resourceId\":\"XPXKDD7X\",\"paymentProviderIdentifier\":\"DIGITAL_RIVER\",\"emailTemplate\":\"PAYMENT\",\"emailType\":\"SUCCESS\",\"apiKey\":\"API_KEY\"}}";

  private ObjectMapper objectMapper;
  @Mock private StatsDClient statsDClient;

  private ForwarderQueueMessageParser forwarderQueueMessageParser;

  @BeforeEach
  void setUp() {
    objectMapper = spy(new ObjectMapper());
    forwarderQueueMessageParser = new ForwarderQueueMessageParser(statsDClient, objectMapper);
  }

  @Test
  void parseCorrectMessage()
      throws NonRetriableMessageProcessException, RetriableMessageProcessException {
    var event = forwarderQueueMessageParser.parse(RAW_EVENT);
    Assertions.assertNotNull(event);
    Assertions.assertEquals(EMAIL_NOTIFICATION.getType(), event.type());
  }

  @Test
  void parseMessageWithoutPayload()
      throws NonRetriableMessageProcessException, RetriableMessageProcessException {
    var event = forwarderQueueMessageParser.parse("{\"type\":\"EMAIL_NOTIFICATION\"}");
    Assertions.assertNotNull(event);
    Assertions.assertEquals(EMAIL_NOTIFICATION.getType(), event.type());
  }

  @Test
  void throwsRetriableWhenParsingError() {
    Assertions.assertThrows(
        NonRetriableMessageProcessException.class,
        () -> forwarderQueueMessageParser.parse(RAW_UNDEFINED_EVENT));
  }

  @Test
  void throwsNonRetriableWhenTheMessageIsWrong() throws JsonProcessingException {
    when(objectMapper.readValue(RAW_EVENT, QueueMessage.class))
        .thenThrow(JsonMappingException.class);
    Assertions.assertThrows(
        NonRetriableMessageProcessException.class,
        () -> forwarderQueueMessageParser.parse(RAW_EVENT));
  }

  @Test
  void throwsRetriableWhenUnexpectedException() throws JsonProcessingException {
    when(objectMapper.readValue(RAW_EVENT, QueueMessage.class))
        .thenThrow(NullPointerException.class);
    Assertions.assertThrows(
        RetriableMessageProcessException.class, () -> forwarderQueueMessageParser.parse(RAW_EVENT));
  }
}
