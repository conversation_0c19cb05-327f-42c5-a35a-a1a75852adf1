package com.scopely.paymentgateway.worker.services;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.scopely.paymentgateway.exceptions.PaymentNotCompletedException;
import com.scopely.paymentgateway.exceptions.RefundPaymentRequestException;
import com.scopely.paymentgateway.exceptions.RequestToProviderException;
import com.scopely.paymentgateway.exceptions.model.PaymentNotFoundException;
import com.scopely.paymentgateway.model.refund.Refund;
import com.scopely.paymentgateway.model.refund.RefundEvent;
import com.scopely.paymentgateway.model.refund.RefundReason;
import com.scopely.paymentgateway.model.refund.RefundStatus;
import com.scopely.paymentgateway.services.refund.StartFullRefundProcess;
import com.scopely.paymentgateway.worker.services.refunds.ScheduledRefundsProcessor;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import java.time.Instant;
import org.javamoney.moneta.Money;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ScheduleRefundProcessorTest {

  private static final String PAYMENT_ID = "payment-id";
  private static final String API_KEY = "api-key";
  private static final String COMMENT = "comment";
  private static final String REFUND_ID = "refund-id";
  private static final Money MONEY = Money.of(10, "USD");

  private static final Refund REFUND =
      new Refund.Builder()
          .setStatus(RefundStatus.CREATED)
          .setApiKey(API_KEY)
          .setRefundId(REFUND_ID)
          .setPaymentId(PAYMENT_ID)
          .setUpdatedAt(Instant.now())
          .setCreatedAt(Instant.now())
          .setRefundReason(RefundReason.FAILED_PAYMENT)
          .setRefundedAmount(MONEY)
          .setRefundedLocalAmount(MONEY)
          .build();

  @Mock private StartFullRefundProcess startFullRefundProcess;

  @InjectMocks private ScheduledRefundsProcessor scheduledRefundsProcessor;

  @Test
  void consumeMessageForTypeFailedWithSuccess()
      throws NonRetriableMessageProcessException,
          RetriableMessageProcessException,
          PaymentNotCompletedException,
          PaymentNotFoundException,
          RequestToProviderException {
    when(startFullRefundProcess.initRefundForUncompletedPayment(any(), any(), any()))
        .thenReturn(REFUND);
    scheduledRefundsProcessor.process(
        new RefundEvent(PAYMENT_ID, API_KEY, COMMENT, RefundReason.FAILED_PAYMENT));
    verify(startFullRefundProcess, times(1))
        .initRefundForUncompletedPayment(
            eq(PAYMENT_ID), eq(RefundReason.FAILED_PAYMENT), eq(COMMENT));
  }

  @Test
  void consumeMessageForTypeNotValidWithSuccess()
      throws PaymentNotCompletedException, PaymentNotFoundException, RequestToProviderException {
    assertDoesNotThrow(
        () ->
            scheduledRefundsProcessor.process(
                new RefundEvent(PAYMENT_ID, API_KEY, COMMENT, RefundReason.OTHER)));
    verify(startFullRefundProcess, never()).initRefundForUncompletedPayment(any(), any(), any());
  }

  @Test
  void consumeMessageForTypeFailedWithFailedNonRetryable()
      throws PaymentNotCompletedException, PaymentNotFoundException, RequestToProviderException {
    when(startFullRefundProcess.initRefundForUncompletedPayment(any(), any(), any()))
        .thenThrow(new PaymentNotCompletedException(""));
    assertThrows(
        NonRetriableMessageProcessException.class,
        () ->
            scheduledRefundsProcessor.process(
                new RefundEvent(PAYMENT_ID, API_KEY, COMMENT, RefundReason.FAILED_PAYMENT)));
    verify(startFullRefundProcess, times(1))
        .initRefundForUncompletedPayment(
            eq(PAYMENT_ID), eq(RefundReason.FAILED_PAYMENT), eq(COMMENT));
  }

  @Test
  void consumeMessageForTypeFailedWithFailedRetryable()
      throws PaymentNotCompletedException, PaymentNotFoundException, RequestToProviderException {
    when(startFullRefundProcess.initRefundForUncompletedPayment(any(), any(), any()))
        .thenThrow(new RefundPaymentRequestException(""));
    assertThrows(
        RetriableMessageProcessException.class,
        () ->
            scheduledRefundsProcessor.process(
                new RefundEvent(PAYMENT_ID, API_KEY, COMMENT, RefundReason.FAILED_PAYMENT)));
    verify(startFullRefundProcess, times(1))
        .initRefundForUncompletedPayment(
            eq(PAYMENT_ID), eq(RefundReason.FAILED_PAYMENT), eq(COMMENT));
  }
}
