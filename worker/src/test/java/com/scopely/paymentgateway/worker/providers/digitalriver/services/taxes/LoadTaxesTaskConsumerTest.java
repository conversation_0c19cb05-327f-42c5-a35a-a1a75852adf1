package com.scopely.paymentgateway.worker.providers.digitalriver.services.taxes;

import static com.scopely.paymentgateway.model.queue.QueueEventType.LOAD_TAXES;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.providers.digitalriver.services.countryconversion.DigitalRiverCountryConversionProvider;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.timgroup.statsd.StatsDClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LoadTaxesTaskConsumerTest {
  @Mock private StatsDClient statsDClient;
  @Mock private DigitalRiverCountryConversionProvider digitalRiverCountryConversionProvider;
  @InjectMocks private LoadTaxesTaskConsumer consumer;

  @Test
  void executeLoadTaxes() {
    Assertions.assertDoesNotThrow(
        () -> consumer.process(new QueueMessage(LOAD_TAXES.getType(), "{}")));
  }

  @Test
  void acceptsMessage() {
    assertTrue(consumer.acceptsMessage(new QueueMessage(LOAD_TAXES.getType(), "{}")));
    assertFalse(consumer.acceptsMessage(new QueueMessage("default_2", "{}")));
  }

  @Test
  void executeLoadTaxesRuntimeErrorAckAndReturnFalse() {
    Mockito.doThrow(new RuntimeException("error"))
        .when(digitalRiverCountryConversionProvider)
        .update();
    Assertions.assertThrows(
        RetriableMessageProcessException.class,
        () -> consumer.process(new QueueMessage(LOAD_TAXES.getType(), "{}")));
  }
}
