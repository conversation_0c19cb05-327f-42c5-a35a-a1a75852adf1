package com.scopely.paymentgateway.worker.services.updateuserinfo;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.model.queue.QueueEventType;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.parsing.ObjectMapperFactory;
import com.scopely.paymentgateway.services.rules.PlayerProfileService;
import com.scopely.paymentgateway.worker.utils.TestMocks;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.timgroup.statsd.StatsDClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class UpdateUserInfoTaskConsumerTest {

  @Mock private StatsDClient statsDClient;
  private ObjectMapper mapper;
  @Mock private PlayerProfileService playerProfileService;

  private UpdateUserInfoTaskConsumer updateUserInfoTaskConsumer;

  @BeforeEach
  public void setUp() {
    mapper = ObjectMapperFactory.get();
    updateUserInfoTaskConsumer =
        new UpdateUserInfoTaskConsumer(statsDClient, mapper, playerProfileService);
  }

  @Test
  void acceptsMessage() {
    assertTrue(
        updateUserInfoTaskConsumer.acceptsMessage(
            new QueueMessage(updateUserInfoTaskConsumer.getType(), "{}")));
    assertFalse(updateUserInfoTaskConsumer.acceptsMessage(new QueueMessage("default_2", "{}")));
  }

  @Test
  void getTypeIsTheExpectedOne() {
    assertEquals(QueueEventType.UPDATE_USER_INFO.getType(), updateUserInfoTaskConsumer.getType());
  }

  @Test
  void anyErrorWillRetry() {
    Assertions.assertThrows(
        RetriableMessageProcessException.class,
        () ->
            updateUserInfoTaskConsumer.process(
                new QueueMessage(QueueEventType.UPDATE_USER_INFO.getType(), "{}")));
  }

  @Test
  void updateUser() {
    Assertions.assertDoesNotThrow(
        () ->
            updateUserInfoTaskConsumer.process(
                new QueueMessage(QueueEventType.UPDATE_USER_INFO.getType(), TestMocks.USER)));
  }
}
