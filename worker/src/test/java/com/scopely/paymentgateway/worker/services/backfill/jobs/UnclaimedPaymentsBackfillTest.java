package com.scopely.paymentgateway.worker.services.backfill.jobs;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;

import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.worker.utils.TestMocks;
import com.timgroup.statsd.StatsDClient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class UnclaimedPaymentsBackfillTest {
  @Mock PaymentService paymentService;
  @Mock StatsDClient statsDClient;
  @InjectMocks UnclaimedPaymentsBackfill unclaimedPaymentsBackfill;

  @Test
  void performBackfill() {
    unclaimedPaymentsBackfill.backfill(TestMocks.PAYMENT);
    verify(paymentService).systemSave(TestMocks.PAYMENT);
    verifyNoMoreInteractions(paymentService);
  }
}
