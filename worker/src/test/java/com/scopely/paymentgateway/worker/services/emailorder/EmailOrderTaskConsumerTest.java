package com.scopely.paymentgateway.worker.services.emailorder;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.spy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scopely.paymentgateway.exceptions.email.EmailServiceException;
import com.scopely.paymentgateway.exceptions.email.RejectedEmailException;
import com.scopely.paymentgateway.exceptions.email.SendEmailException;
import com.scopely.paymentgateway.exceptions.model.EntityNotFoundException;
import com.scopely.paymentgateway.model.queue.QueueMessage;
import com.scopely.paymentgateway.services.PaymentService;
import com.scopely.paymentgateway.services.email.EmailSender;
import com.scopely.paymentgateway.services.email.EmailServiceDispatcher;
import com.scopely.paymentgateway.services.email.PaymentEmailService;
import com.scopely.paymentgateway.services.email.PaymentEmailTemplateProcessor;
import com.scopely.paymentgateway.services.email.RefundEmailService;
import com.scopely.paymentgateway.services.email.RefundEmailTemplateProcessor;
import com.scopely.paymentgateway.services.refund.RefundService;
import com.scopely.proteus.sqs.consumer.NonRetriableMessageProcessException;
import com.scopely.proteus.sqs.consumer.RetriableMessageProcessException;
import com.scopely.proteus.util.JacksonMapper;
import com.timgroup.statsd.StatsDClient;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class EmailOrderTaskConsumerTest {
  private static final String SQS_MESSAGE_EMAIl_NOTIFICATION =
      "{\"type\":\"EMAIL_NOTIFICATION\",\"payload\":{\"resourceId\":\"XPXKDD7X\",\"paymentProviderIdentifier\":\"DIGITAL_RIVER\",\"emailTemplate\":\"PAYMENT\",\"emailType\":\"SUCCESS\",\"apiKey\":\"API_KEY\"}}";

  @Mock private StatsDClient statsDClient;
  @Mock private PaymentService paymentService;
  @Mock private RefundService refundService;
  @Mock private PaymentEmailTemplateProcessor emailTemplateProcessor;
  @Mock private RefundEmailTemplateProcessor refundEmailTemplateProcessor;
  @Mock private EmailSender emailSender;

  private PaymentEmailService paymentEmailService;
  private RefundEmailService refundEmailService;
  private EmailServiceDispatcher emailServiceDispatcher;
  private QueueMessage emailNotificationMessage;

  @Spy
  private ObjectMapper mapper =
      JacksonMapper.MAPPER
          .copy()
          // disable exceptions when parsing old properties
          .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
          // enable default enum values
          .enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE)
          // avoid nulls on save
          .setSerializationInclusion(JsonInclude.Include.NON_NULL);

  private EmailOrderTaskConsumer consumer;

  @BeforeEach
  public void setup() throws JsonProcessingException {
    emailNotificationMessage = mapper.readValue(SQS_MESSAGE_EMAIl_NOTIFICATION, QueueMessage.class);
    paymentEmailService =
        spy(new PaymentEmailService(paymentService, emailTemplateProcessor, emailSender));
    refundEmailService =
        spy(
            new RefundEmailService(
                paymentService, refundService, refundEmailTemplateProcessor, emailSender));
    emailServiceDispatcher =
        spy(
            new EmailServiceDispatcher(
                Set.of(paymentEmailService, refundEmailService), statsDClient));
    consumer = new EmailOrderTaskConsumer(mapper, statsDClient, emailServiceDispatcher);
  }

  @Test
  void consumeSqsMessageEmailNotification() throws EmailServiceException, EntityNotFoundException {
    doNothing().when(paymentEmailService).sendEmail(any());
    Assertions.assertDoesNotThrow(() -> consumer.process(emailNotificationMessage));
  }

  @Test
  void noConsumeSqsMessageEmailNotificationWhenSentEmailException()
      throws EmailServiceException, EntityNotFoundException {
    // throw an email exception sending the email
    doThrow(SendEmailException.class).when(paymentEmailService).sendEmail(any());
    Assertions.assertThrows(
        RetriableMessageProcessException.class, () -> consumer.process(emailNotificationMessage));
  }

  @Test
  void notConsumeSqsMessageEmailNotificationWhenSentResultIsUnexpected()
      throws EmailServiceException, EntityNotFoundException {
    // throw an email exception sending the email
    doThrow(RuntimeException.class).when(paymentEmailService).sendEmail(any());
    Assertions.assertThrows(
        RetriableMessageProcessException.class, () -> consumer.process(emailNotificationMessage));
  }

  @Test
  void consumeSqsMessageRejectedSendEmail() throws EmailServiceException, EntityNotFoundException {
    doThrow(RejectedEmailException.class).when(paymentEmailService).sendEmail(any());
    Assertions.assertThrows(
        NonRetriableMessageProcessException.class,
        () -> consumer.process(emailNotificationMessage));
  }
}
