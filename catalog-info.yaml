apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: iac-tf-module-aws-utils
  description: Small terraform modules with small submodules that we use as utilities
    and may or may not became a module in the future.
  annotations:
    github.com/project-slug: scopely/iac-tf-module-aws-utils
    scopely.io/backstage-cli: 1.1.37
    backstage.io/techdocs-ref: dir:.
  insights:
    dependencies:
    - name: github.com/scopely/tf-module-context
      version: 0.0.8
    tfProviders:
    - name: registry.terraform.io/hashicorp/aws
      constraints:
      - '>=4.13.0'
  tags:
  - hcl
  - shell
  - go
  links: []
spec:
  owner: playgami-devops-security
  type: terraform-module
  lifecycle: experimental
  system: playgami
  subcomponentOf: component:default/iac-tf-module-template
